{"fqn": "gateway_widgets.gateway_configuration", "name": "Gateway Configuration", "deprecated": false, "image": "tb-image;/api/images/system/gateway_configuration_system_widget_image.png", "description": "Allows to define configuration for a single gateway.", "descriptor": {"type": "static", "sizeX": 8, "sizeY": 6.5, "resources": [{"url": "tb-resource;/api/resource/js_module/system/gateway-management-extension.js", "isModule": true}], "templateHtml": "<tb-gateway-form\n    [ctx]=\"ctx\">\n</tb-gateway-form>\n", "templateCss": "", "controllerScript": "self.onInit = function() {\n}\n", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-gateway-config-widget-settings", "defaultConfig": "{\"datasources\":[{\"type\":\"static\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgb(255, 255, 255)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"widgetTitle\":\"Gateway Configuration\",\"archiveFileName\":\"configurationGateway\"},\"title\":\"Gateway Configuration\",\"dropShadow\":true,\"showTitleIcon\":false,\"titleIcon\":\"more_horiz\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"enableFullscreen\":true,\"widgetStyle\":{},\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"showLegend\":false,\"actions\":{}}"}, "externalId": null, "tags": ["router", "bridge", "hub", "access point", "relay", "opc ua", "opc-ua", "modbus", "bacnet", "odbc", "ftp", "snmp", "mqtt", "xmpp", "ocpp", "ble", "bluetooth"]}