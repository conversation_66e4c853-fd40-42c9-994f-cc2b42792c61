{"fqn": "rainfall_card", "name": "Rainfall card", "deprecated": false, "image": "tb-image;/api/images/system/rainfall_card_system_widget_image.png", "description": "Displays the latest rainfall telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'rainfall', label: 'Rainfall', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Rainfall \",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 8) {\\n\\tvalue = 8;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:weather-pouring\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#7191EF\"},{\"from\":0,\"to\":2.5,\"color\":\"#4B70DD\"},{\"from\":2.5,\"to\":7.6,\"color\":\"#305AD7\"},{\"from\":7.6,\"to\":null,\"color\":\"#234CC7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#7191EF\"},{\"from\":0,\"to\":2.5,\"color\":\"#4B70DD\"},{\"from\":2.5,\"to\":7.6,\"color\":\"#305AD7\"},{\"from\":7.6,\"to\":null,\"color\":\"#234CC7\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Rainfall card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"mm\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "rain", "precipitation", "downpour", "rain shower", "drizzle", "raindrop", "cloudburst", "rainwater"], "resources": [{"link": "/api/images/system/rainfall_card_system_widget_image.png", "title": "\"Rainfall card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "rainfall_card_system_widget_image.png", "publicResourceKey": "hqeINNbEWqTw9vbkEObiQqLArPu7ArWC", "mediaType": "image/png", "data": "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", "public": true}]}