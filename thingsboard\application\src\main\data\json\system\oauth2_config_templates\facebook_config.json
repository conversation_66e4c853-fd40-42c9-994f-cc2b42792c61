{"providerId": "Facebook", "accessTokenUri": "https://graph.facebook.com/v2.8/oauth/access_token", "authorizationUri": "https://www.facebook.com/v2.8/dialog/oauth", "scope": ["email", "public_profile"], "jwkSetUri": null, "userInfoUri": "https://graph.facebook.com/me?fields=id,name,first_name,last_name,email", "clientAuthenticationMethod": "BASIC", "userNameAttributeName": "email", "mapperConfig": {"type": "BASIC", "basic": {"emailAttributeKey": "email", "firstNameAttributeKey": "first_name", "lastNameAttributeKey": "last_name", "tenantNameStrategy": "DOMAIN"}}, "comment": null, "loginButtonIcon": "facebook-logo", "loginButtonLabel": "Facebook", "helpLink": "https://developers.facebook.com/docs/facebook-login/web#logindialog"}