{"fqn": "simple_pump_vibration_chart_card", "name": "Simple vibration chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_vibration_chart_card_(1).svg", "description": "Displays historical vibration values as a simplified chart. Optionally may display the corresponding latest vibration value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'vibration', label: 'Vibration', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'vibration', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 3.3 - 1.7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 3.3 - 1.7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2.8,\"color\":\"#3FA71A\"},{\"from\":2.8,\"to\":4.5,\"color\":\"#FFA600\"},{\"from\":4.5,\"to\":7.1,\"color\":\"#F36900\"},{\"from\":7.1,\"to\":null,\"color\":\"#F04022\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Vibration\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"waves\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":1,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"units\":\"mm/s\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true},\"margin\":\"0px\"}"}, "tags": ["vibration", "pump vibration", "motion", "resonance", "dynamic balancing", "mechanical integrity", "pulsation", "frequency"], "resources": [{"link": "/api/images/system/simple_vibration_chart_card_(1).svg", "title": "simple_vibration_chart_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_vibration_chart_card_(1).svg", "publicResourceKey": "N1RWw2VlHFDQwc3Hxmo1qwu7uOHAnllV", "mediaType": "image/svg+xml", "data": "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", "public": true}]}