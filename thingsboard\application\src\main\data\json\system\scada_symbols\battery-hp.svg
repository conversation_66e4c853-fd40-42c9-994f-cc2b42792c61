<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="600" height="200" fill="none" version="1.1" viewBox="0 0 600 200"><tb:metadata xmlns=""><![CDATA[{
  "title": "HP Battery",
  "description": "Battery with various states and scalable quantity.",
  "searchTags": [
    "energy",
    "power",
    "rechargeable",
    "storage",
    "lithium-ion",
    "ev"
  ],
  "widgetSizeX": 3,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.runningColor;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "critical",
      "stateRenderFunction": "element.attr({fill: ctx.properties.criticalColor});\nif (ctx.values.critical) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = ctx.values.critical && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'criticalClick');"
        }
      }
    },
    {
      "tag": "indicator",
      "stateRenderFunction": "if (ctx.values.running) {\n    element.fill(ctx.properties.runningIndicatorColor);\n} else {\n    element.fill(ctx.properties.stoppedIndicatorColor);\n}",
      "actions": null
    },
    {
      "tag": "label",
      "stateRenderFunction": "ctx.api.font(element, ctx.properties.labelFont, ctx.properties.labelColor);\nctx.api.text(element, ctx.properties.label);",
      "actions": null
    },
    {
      "tag": "left-bottom-connector",
      "stateRenderFunction": "if (!ctx.properties.leftBottomConnector) {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "left-connector",
      "stateRenderFunction": "if (!ctx.properties.leftConnector) {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "left-top-connector",
      "stateRenderFunction": "if (!ctx.properties.leftTopConnector) {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "right-bottom-connector",
      "stateRenderFunction": "if (!ctx.properties.rightBottomConnector) {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "right-connector",
      "stateRenderFunction": "if (!ctx.properties.rightConnector) {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "right-top-connector",
      "stateRenderFunction": "if (!ctx.properties.rightTopConnector) {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "warning",
      "stateRenderFunction": "element.attr({fill: ctx.properties.warningColor});\nvar warning = ctx.values.warning && !(ctx.values.warning && ctx.values.critical)\nif (warning) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = warning && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'warningClick');"
        }
      }
    }
  ],
  "behavior": [
    {
      "id": "running",
      "name": "{i18n:scada.symbol.running}",
      "hint": "{i18n:scada.symbol.running-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.running}",
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "running"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warning",
      "name": "{i18n:scada.symbol.warning}",
      "hint": "{i18n:scada.symbol.warning-state-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.warning}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "MAJOR",
            "MINOR",
            "WARNING",
            "INDETERMINATE"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warningClick",
      "name": "{i18n:scada.symbol.warning-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "critical",
      "name": "{i18n:scada.symbol.critical}",
      "hint": "{i18n:scada.symbol.critical-state-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.critical}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "CRITICAL"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "criticalClick",
      "name": "{i18n:scada.symbol.critical-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "criticalAnimation",
      "name": "{i18n:scada.symbol.warning-critical-state-animation}",
      "hint": "{i18n:scada.symbol.warning-critical-state-animation-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.animation}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    }
  ],
  "properties": [
    {
      "id": "runningColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#FFFFFF",
      "subLabel": "{i18n:scada.symbol.running}",
      "divider": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "stoppedColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#666666",
      "subLabel": "{i18n:scada.symbol.stopped}",
      "disabled": false,
      "visible": true
    },
    {
      "id": "warningColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#FAA405",
      "subLabel": "{i18n:scada.symbol.warning}",
      "divider": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "criticalColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#D12730",
      "subLabel": "{i18n:scada.symbol.critical}",
      "disabled": false,
      "visible": true
    },
    {
      "id": "runningIndicatorColor",
      "name": "{i18n:scada.symbol.indicator-colors}",
      "type": "color",
      "default": "#198038",
      "subLabel": "{i18n:scada.symbol.running}",
      "divider": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "stoppedIndicatorColor",
      "name": "{i18n:scada.symbol.indicator-colors}",
      "type": "color",
      "default": "#DEDEDE",
      "subLabel": "{i18n:scada.symbol.stopped}",
      "disabled": false,
      "visible": true
    },
    {
      "id": "label",
      "name": "{i18n:scada.symbol.label}",
      "type": "text",
      "default": "ON",
      "fieldClass": "medium-width",
      "disabled": false,
      "visible": true
    },
    {
      "id": "labelFont",
      "name": "{i18n:scada.symbol.label}",
      "type": "font",
      "default": {
        "size": 30,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "400",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "labelColor",
      "name": "{i18n:scada.symbol.label}",
      "type": "color",
      "default": "#1A1A1A",
      "disabled": false,
      "visible": true
    },
    {
      "id": "leftConnector",
      "name": "{i18n:scada.symbol.left-connector}",
      "group": "{i18n:scada.symbol.connectors-positions}",
      "type": "switch",
      "default": false,
      "disabled": false,
      "visible": true
    },
    {
      "id": "leftTopConnector",
      "name": "{i18n:scada.symbol.left-top-connector}",
      "group": "{i18n:scada.symbol.connectors-positions}",
      "type": "switch",
      "default": false,
      "disabled": false,
      "visible": true
    },
    {
      "id": "leftBottomConnector",
      "name": "{i18n:scada.symbol.left-bottom-connector}",
      "group": "{i18n:scada.symbol.connectors-positions}",
      "type": "switch",
      "default": false,
      "disabled": false,
      "visible": true
    },
    {
      "id": "rightConnector",
      "name": "{i18n:scada.symbol.right-connector}",
      "group": "{i18n:scada.symbol.connectors-positions}",
      "type": "switch",
      "default": false,
      "disabled": false,
      "visible": true
    },
    {
      "id": "rightTopConnector",
      "name": "{i18n:scada.symbol.right-top-connector}",
      "group": "{i18n:scada.symbol.connectors-positions}",
      "type": "switch",
      "default": false,
      "disabled": false,
      "visible": true
    },
    {
      "id": "rightBottomConnector",
      "name": "{i18n:scada.symbol.right-bottom-connector}",
      "group": "{i18n:scada.symbol.connectors-positions}",
      "type": "switch",
      "default": false,
      "disabled": false,
      "visible": true
    }
  ]
}]]></tb:metadata>
<g fill="#fff" tb:tag="background">
  <rect x="1" y="41" width="598" height="120" rx="6" stroke="#000" stroke-width="2"/>
  <circle cx="468" cy="102.5" r="12"/>
  <circle cx="468" cy="101.5" r="11" stroke="#000" stroke-opacity=".87" stroke-width="2"/>
  <circle cx="84" cy="101.5" r="11" stroke="#1a1a1a" stroke-width="2"/>
  <circle cx="516" cy="102.5" r="12"/>
  <circle cx="516" cy="101.5" r="11" stroke="#000" stroke-opacity=".87" stroke-width="2"/>
  <circle cx="132" cy="101.5" r="11" stroke="#1a1a1a" stroke-width="2"/>
 </g><circle cx="204" cy="102" r="10" fill="#198038" tb:tag="indicator"/><text x="221" y="105.37933" fill="#000000" font-family="Roboto, sans-serif" font-size="30px" font-weight="400" tb:tag="label" xml:space="preserve"><tspan dominant-baseline="middle">ON</tspan></text><path d="m103 200h-6s7.858-27.047 14-44c6.324-17.455 18-44 18-44h6s-11.676 26.545-18 44c-6.142 16.953-14 44-14 44z" fill="#1a1a1a" tb:tag="left-bottom-connector"/><path d="m103 0h-6s-6.4394 26.077-9.5 43c-3.2977 18.234-6.5 47-6.5 47h6s2.7023-26.766 6-45c3.0606-16.923 10-45 10-45z" fill="#1a1a1a" tb:tag="left-top-connector"/><path d="m497 0h6s6.439 26.077 9.5 43c3.298 18.234 6.5 47 6.5 47h-6s-2.702-26.766-6-45c-3.061-16.923-10-45-10-45z" fill="#1a1a1a" tb:tag="right-top-connector"/><path d="m0 100h73" stroke="#1a1a1a" stroke-width="6" tb:tag="left-connector"/><path d="m527 100h73" stroke="#1a1a1a" stroke-width="6" tb:tag="right-connector"/><path d="m497 200h6s-7.858-27.047-14-44c-6.324-17.455-18-44-18-44h-6s11.676 26.545 18 44c6.142 16.953 14 44 14 44z" fill="#1a1a1a" tb:tag="right-bottom-connector"/><path d="m201.8 40s-201.8 0-201.8 20.435v100.15c0 0.80861 5.3727 1.4153 12 1.4153h576c6.627 0 12-0.60671 12-1.4153v-100.15c0-20.435-198.21-20.435-198.21-20.435h-101.79zm201.21 24.766c-3.8661 0-6.9999 0.38235-6.9999 0.854v91.622c0 0.47165 3.1341 0.854 6.9999 0.854h43.998c3.8661 0 6.9999-0.38235 6.9999-0.854v-91.622c0-0.47165-3.1341-0.854-6.9999-0.854z" fill="#000" fill-opacity="0" tb:tag="clickArea"/><g fill="#d12730" style="display: none;" tb:tag="critical">
  <rect width="84" height="84" rx="4" fill="#fff" style=""/>
  <rect width="84" height="84" rx="4" style=""/>
  <rect x="2" y="2" width="80" height="80" rx="2" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 27.562-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g><g fill="#faa405" style="display: none;" tb:tag="warning">
  <path d="m35.955 2.2112c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" fill="#fff" style=""/>
  <path d="m35.955 2.2112c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" style=""/>
  <path d="m37.72 3.1061c0.7371-1.4741 2.8407-1.4741 3.5778-1e-5l35.528 71.056c0.6649 1.3298-0.3021 2.8944-1.7888 2.8944h-71.056c-1.4868 0-2.4538-1.5646-1.7889-2.8944z" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m42.092 32.618-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g>
</svg>