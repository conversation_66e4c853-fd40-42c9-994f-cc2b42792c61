#
# Copyright © 2016-2025 The Thingsboard Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

name: License header format

on:
  push:
    branches:
      - 'master'
      - 'develop/3*'
      - 'hotfix/3*'

jobs:
  license-format:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'corretto' # https://github.com/actions/setup-java?tab=readme-ov-file#supported-distributions
          java-version: '21'
          cache: 'maven' # https://github.com/actions/setup-java?tab=readme-ov-file#caching-sbt-dependencies

      - name: License header format
        run: mvn -T 1C license:format

      - name: License header format (msa/black-box-tests/)
        run: mvn -T 1C license:format -f msa/black-box-tests/

      - name: Set Git user information
        run: |
          git config user.name "ThingsBoard Bot"
          git config user.email "<EMAIL>"

      - name: Check and push changes
        run: |
          git diff --exit-code || git commit -am "License header format" && git push
