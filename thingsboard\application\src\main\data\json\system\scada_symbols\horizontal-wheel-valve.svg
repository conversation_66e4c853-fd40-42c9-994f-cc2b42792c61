<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="400" height="200" fill="none" version="1.1" viewBox="0 0 400 200"><tb:metadata xmlns=""><![CDATA[{
  "title": "Horizontal wheel valve",
  "description": "Horizontal wheel valve with open/close animation and state colors.",
  "searchTags": [
    "valve",
    "wheel"
  ],
  "widgetSizeX": 2,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "var opened = ctx.values.opened;\nvar openAnimate = element.remember('openAnimate');\nvar color = opened ? ctx.properties.openedColor : ctx.properties.closedColor;\nif (!openAnimate) {\n    element.attr({fill: color});\n} else {\n    ctx.api.cssAnimate(element, 500).attr({fill: color});\n    element.remember('openAnimate', false);\n}\n",
      "actions": null
    },
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": " ctx.tags.wheel.forEach(e => {\n     e.remember('openAnimate', true);\n });\n ctx.tags.background.forEach(e => {\n     e.remember('openAnimate', true);\n });\n\n\nvar opened = ctx.values.opened;\nvar action = opened ? 'close' : 'open';\n\nctx.api.callAction(event, action, undefined, {\n  next: () => {\n     ctx.api.setValue('opened', !opened);\n  }\n});"
        }
      }
    },
    {
      "tag": "wheel",
      "stateRenderFunction": "var opened = ctx.values.opened;\nvar openAnimate = element.remember('openAnimate');\nvar angle = opened ? ctx.properties.openedRotationAngle : ctx.properties.closedRotationAngle;\nif (!openAnimate) {\n    element.transform({rotate: angle});\n} else {\n    ctx.api.cssAnimate(element, 500).transform({rotate: angle});\n    element.remember('openAnimate', false);\n}\n",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "opened",
      "name": "{i18n:scada.symbol.opened}",
      "hint": "{i18n:scada.symbol.opened-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.opened}",
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "open"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "open",
      "name": "{i18n:scada.symbol.open}",
      "hint": "{i18n:scada.symbol.open-hint}",
      "group": null,
      "type": "action",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": {
        "action": "SET_ATTRIBUTE",
        "executeRpc": {
          "method": "setState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "setAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "open"
        },
        "putTimeSeries": {
          "key": "state"
        },
        "valueToData": {
          "type": "CONSTANT",
          "constantValue": true,
          "valueToDataFunction": "/* Convert input boolean value to RPC parameters or attribute/time-series value */\nreturn value;"
        }
      },
      "defaultWidgetActionSettings": null
    },
    {
      "id": "close",
      "name": "{i18n:scada.symbol.close}",
      "hint": "{i18n:scada.symbol.close-hint}",
      "group": null,
      "type": "action",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": {
        "action": "SET_ATTRIBUTE",
        "executeRpc": {
          "method": "setState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "setAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "open"
        },
        "putTimeSeries": {
          "key": "state"
        },
        "valueToData": {
          "type": "CONSTANT",
          "constantValue": false,
          "valueToDataFunction": "/* Convert input boolean value to RPC parameters or attribute/time-series value */\nreturn value;"
        }
      },
      "defaultWidgetActionSettings": null
    }
  ],
  "properties": [
    {
      "id": "openedColor",
      "name": "{i18n:scada.symbol.opened-color}",
      "type": "color",
      "default": "#1C943E",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "closedColor",
      "name": "{i18n:scada.symbol.closed-color}",
      "type": "color",
      "default": "#696969",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "openedRotationAngle",
      "name": "{i18n:scada.symbol.opened-rotation-angle}",
      "type": "number",
      "default": 0,
      "required": true,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": -179,
      "max": 179,
      "step": null
    },
    {
      "id": "closedRotationAngle",
      "name": "{i18n:scada.symbol.closed-rotation-angle}",
      "type": "number",
      "default": 90,
      "required": true,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": -179,
      "max": 179,
      "step": null
    }
  ]
}]]></tb:metadata>
<path d="m200 29.489c-28.161 0-44.15 0.82311-72 5-28.662 4.2988-72 17-72 17v-13.99c0-4.4182-3.5817-8-8-8h-26c-4.4183 0-8 3.5818-8 8v126c0 4.418 3.5817 8 8 8h26c4.4183 0 8-3.582 8-8v-14.01s43.238 12.711 72 17c27.834 4.151 43.859 5.195 72 5 28.16-0.19499 44.15-1.823 72-6 28.663-4.299 72-16 72-16v14.01c0 4.418 3.582 8 8 8h26c4.418 0 8-3.582 8-8v-126c0-4.4182-3.582-8-8-8h-26c-4.418 0-8 3.5818-8 8v13.99s-43.337-12.701-72-17c-27.85-4.1769-43.839-5-72-5z" fill="#1c943e" tb:tag="background"/><rect x="1.5" y="52.5" width="11" height="97" rx="5.5" fill="#d9d9d9" stroke="#727171" stroke-width="3"/><rect x="14" y="30" width="42" height="142" rx="8" fill="url(#paint0_linear_1343_53957)" style="fill:url(#paint0_linear_1343_53957)"/><rect x="15.5" y="31.5" width="39" height="139" rx="6.5" stroke="#000" stroke-opacity=".12" stroke-width="3"/><path d="m56 51.988s43.338-12.701 72-17c27.85-4.1769 43.839-5 72-5s44.15 0.8231 72 5c28.663 4.2988 72 17 72 17v98s-43.337 11.701-72 16c-27.85 4.177-43.84 5.806-72 6-28.141 0.195-44.166-0.849-72-5-28.762-4.289-72-17-72-17z" fill="url(#paint1_linear_1343_53957)" style="fill:url(#paint1_linear_1343_53957)"/><path d="m341.67 52.876c0.314 0.09 0.591 0.1699 0.831 0.239v95.722c-0.233 0.062-0.501 0.133-0.802 0.212-1.238 0.328-3.04 0.801-5.292 1.382-4.505 1.163-10.809 2.758-18.008 4.487-14.409 3.46-32.357 7.448-46.62 9.587-27.798 4.169-43.715 5.789-71.788 5.983-28.068 0.194-44.015-0.844-71.769-4.983-14.3-2.133-32.237-6.366-46.632-10.078-7.1905-1.854-13.484-3.574-17.98-4.83-2.2476-0.629-4.0454-1.141-5.2807-1.496-0.3128-0.09-0.5896-0.17-0.8284-0.239v-95.747c0.2396-0.0691 0.5174-0.149 0.8314-0.239 1.2379-0.3548 3.0393-0.8668 5.2909-1.4947 4.5034-1.2558 10.806-2.9751 18.003-4.8284 14.408-3.7103 32.345-7.9439 46.596-10.081 27.761-4.1635 43.674-4.9834 71.778-4.9834s44.017 0.8199 71.778 4.9834c14.251 2.1374 32.189 6.371 46.596 10.081 7.197 1.8533 13.5 3.5726 18.004 4.8284 2.251 0.6279 4.053 1.1399 5.291 1.4947z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><rect x="344" y="30" width="42" height="142" rx="8" fill="url(#paint2_linear_1343_53957)" style="fill:url(#paint2_linear_1343_53957)"/><rect x="345.5" y="31.5" width="39" height="139" rx="6.5" stroke="#000" stroke-opacity=".12" stroke-width="3"/><rect x="387.5" y="52.5" width="11" height="97" rx="5.5" fill="#d9d9d9" stroke="#727171" stroke-width="3"/><g tb:tag="wheel">
  <path d="m300 100c0 55.229-44.772 100-100 100s-100-44.771-100-100c0-55.228 44.772-100 100-100s100 44.772 100 100zm-175.01 0c0 41.428 33.584 75.012 75.012 75.012s75.012-33.584 75.012-75.012-33.584-75.012-75.012-75.012-75.012 33.584-75.012 75.012z" fill="url(#paint3_radial_1343_53957)" style="fill:url(#paint3_radial_1343_53957)"/>
  <path d="m194.12 74.998c-1.726 0-3.125-1.3991-3.125-3.125v-46.625c0-3.4517 2.798-6.2499 6.25-6.2499h5.5c3.452 0 6.25 2.7982 6.25 6.2499v46.625c0 1.7259-1.399 3.125-3.125 3.125z" fill="url(#paint4_linear_1343_53957)" style="fill:url(#paint4_linear_1343_53957)"/>
  <path d="m222.98 88.918c-0.612-1.6137 0.2-3.418 1.814-4.0301l43.595-16.534c3.227-1.2241 6.836 0.3999 8.06 3.6273l2.217 5.8439c1.224 3.2274-0.4 6.8361-3.628 8.0602l-43.595 16.535c-1.613 0.612-3.418-0.2-4.03-1.814l-4.433-11.688z" fill="url(#paint5_linear_1343_53957)" style="fill:url(#paint5_linear_1343_53957)"/>
  <path d="m190.37 125.55c1.37 1.05 1.63 3.012 0.58 4.382l-28.362 37.006c-2.1 2.74-6.023 3.259-8.762 1.159l-4.961-3.802c-2.74-2.099-3.259-6.022-1.159-8.762l28.362-37.007c1.05-1.37 3.011-1.629 4.381-0.579z" fill="url(#paint6_linear_1343_53957)" style="fill:url(#paint6_linear_1343_53957)"/>
  <path d="m211.85 125.55c-1.37 1.05-1.63 3.012-0.58 4.382l28.362 37.006c2.1 2.74 6.023 3.259 8.762 1.159l4.961-3.802c2.74-2.099 3.259-6.022 1.159-8.762l-28.362-37.007c-1.05-1.37-3.011-1.629-4.381-0.579z" fill="url(#paint7_linear_1343_53957)" style="fill:url(#paint7_linear_1343_53957)"/>
  <path d="m173.44 97.473c-0.506 1.6506-2.253 2.5776-3.903 2.0716l-44.578-13.664c-3.3-1.0116-5.156-4.507-4.144-7.8072l1.832-5.9756c1.011-3.3002 4.507-5.1555 7.807-4.1439l44.578 13.664c1.65 0.5058 2.577 2.2535 2.072 3.9036l-3.664 11.951z" fill="url(#paint8_linear_1343_53957)" style="fill:url(#paint8_linear_1343_53957)"/>
 </g><g filter="url(#filter0_ii_1343_53957)">
  <circle cx="200" cy="100.99" r="30" fill="#d9d9d9"/>
 </g><path d="m223 100.99c0 12.703-10.297 23-23 23s-23-10.297-23-23c0-12.702 10.297-23 23-23s23 10.297 23 23z" fill="#000" fill-opacity=".05"/><g filter="url(#filter1_i_1343_53957)">
  <path d="m223 101c0 12.703-10.297 23-23 23s-23-10.297-23-23c0-12.702 10.297-23 23-23s23 10.298 23 23z" fill="#1c943e" tb:tag="background"/>
 </g><path d="m222 101c0 12.15-9.85 22-22 22s-22-9.85-22-22 9.85-22 22-22 22 9.8497 22 22z" stroke="#fff" stroke-width="2"/><path d="m197.28 91.614c0-0.647-0.611-1.1214-1.216-0.8901-4.132 1.5826-7.067 5.5867-7.067 10.276 0 6.075 4.925 11 11 11s11-4.925 11-11c0-4.5645-2.78-8.4795-6.739-10.144-0.609-0.2563-1.244 0.2209-1.244 0.882 0 0.4223 0.269 0.7938 0.655 0.9649 3.181 1.4097 5.399 4.5944 5.399 8.2976 0 5.01-4.061 9.071-9.071 9.071s-9.071-4.061-9.071-9.071c0-3.8069 2.344-7.0659 5.668-8.4119 0.402-0.1627 0.686-0.5409 0.686-0.9745z" clip-rule="evenodd" fill="#fff" fill-rule="evenodd"/><path d="m199.51 88.637c0-0.3519 0.285-0.6372 0.637-0.6372s0.637 0.2853 0.637 0.6372v10.401c0 0.3519-0.285 0.6372-0.637 0.6372s-0.637-0.2853-0.637-0.6372z" fill="#fff"/><defs>
  <filter id="filter0_ii_1343_53957" x="166.88" y="67.863" width="66.25" height="66.25" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
   <feFlood flood-opacity="0" result="BackgroundImageFix"/>
   <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="3.12497" dy="-3.12497"/>
   <feGaussianBlur stdDeviation="3.12497"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
   <feBlend in2="shape" result="effect1_innerShadow_1343_53957"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="-3.12497" dy="3.12497"/>
   <feGaussianBlur stdDeviation="3.12497"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
   <feBlend in2="effect1_innerShadow_1343_53957" result="effect2_innerShadow_1343_53957"/>
  </filter>
  <filter id="filter1_i_1343_53957" x="172" y="78" width="51" height="51" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
   <feFlood flood-opacity="0" result="BackgroundImageFix"/>
   <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="-5" dy="5"/>
   <feGaussianBlur stdDeviation="4"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
   <feBlend in2="shape" result="effect1_innerShadow_1343_53957"/>
  </filter>
  <linearGradient id="paint0_linear_1343_53957" x1="24.92" x2="21.891" y1="30" y2="171.95" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint1_linear_1343_53957" x1="130.88" x2="130.44" y1="29.988" y2="172.02" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint2_linear_1343_53957" x1="354.92" x2="351.89" y1="30" y2="171.95" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <radialGradient id="paint3_radial_1343_53957" cx="0" cy="0" r="1" gradientTransform="matrix(0,100,-100,0,200,100)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" offset=".75112"/>
   <stop stop-color="#fff" offset=".87301"/>
   <stop stop-color="#727171" offset="1"/>
  </radialGradient>
  <linearGradient id="paint4_linear_1343_53957" x1="191" x2="209" y1="44.877" y2="44.877" gradientTransform="translate(0 -.99)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#fff" offset=".49829"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint5_linear_1343_53957" x1="250.96" x2="257.61" y1="75.954" y2="93.485" gradientTransform="translate(0 -.99)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#fff" offset=".49829"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint6_linear_1343_53957" x1="173.92" x2="159.04" y1="153.14" y2="141.73" gradientTransform="translate(0 -.99)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#fff" offset=".49829"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint7_linear_1343_53957" x1="228.3" x2="243.18" y1="153.14" y2="141.73" gradientTransform="translate(0 -.99)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#fff" offset=".49829"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint8_linear_1343_53957" x1="142.78" x2="148.28" y1="92.333" y2="74.407" gradientTransform="translate(0 -.99)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#fff" offset=".49829"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
 </defs><rect x="100" width="200" height="200" fill="#000" fill-opacity="0" stroke-width="0" tb:tag="clickArea"/>
</svg>