{"fqn": "flooding_level_progress_bar", "name": "Flooding level progress bar", "deprecated": false, "image": "tb-image;/api/images/system/flooding_level_progress_bar_system_widget_image.png", "description": "Displays flooding level reading as a horizontal progress bar. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'flooding', label: 'Flooding level', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Flooding level\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 2 - 1;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 5) {\\n\\tvalue = 5;\\n}\\nreturn value;\\n\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"#234CC7\"},{\"from\":1,\"to\":3,\"color\":\"#F36900\"},{\"from\":3,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":5,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"#234CC7\"},{\"from\":1,\"to\":3,\"color\":\"#F36900\"},{\"from\":3,\"to\":null,\"color\":\"#D81838\"}]},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Flooding level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"m\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"flood\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["weather", "environment", "flood", "flooding", "water height", "flood depth", "flood stage", "inundation level", "water rise", "overflow level", "flood peak", "high water mark"], "resources": [{"link": "/api/images/system/flooding_level_progress_bar_system_widget_image.png", "title": "\"Flooding level progress bar\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "flooding_level_progress_bar_system_widget_image.png", "publicResourceKey": "vLwn3pxiIdY7TIIHNnpKP7QvXNa35uxN", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAnFBMVEXg4ODf39/g4ODg4OAAAAD////g4OD19fUjTMchISGsrKw9PT3j4+PIyMh0dHSQkJDx8fEvLy+CgoLI0vGenp7k6PhaeNVYWFjy9Pu6urqtvOqRpeM+Ys7V1dUwV8vv7+9KSkqsu+qDmt/X3vS6x+5LbdF1j9yGhoZmZmb39/efseePj4+5ubmpqamXl5dohNkxV8rCwsLj6fjk6fgljp4SAAAABXRSTlPvIL+vAC9A4IoAAARWSURBVHja7M9JEQAwCAPAUI43DvDvsjYCk3WwCHuo7dwChsn1GgbvPGAeKk/AlUgpQkYRNoqwUYSNImwUYaMIG0U+u2WvKjkMQ+HqFEeWfzGYFIaUKfL+z7eRcwMhy+xslaS4H8ygWFKsD2zI2/gVeRu/Im/jHyKrDBSO+IjlfFB8IFLwmUB/h4jjQL6KLFxfLuJ1I30V0Y6Xi+gRYENCmWD4EkT3oKjl1qjosdvjWA5Faz2LWAe2EuuXuHdOD4mkmTkz2GwWOAWEdCT3eYRbAXO3/FaQ3UlkdMxI2w9qf8EW4o0iVUT8LlJs44UCpUuIbLbs0fMhYstWpCZZeRLRPVXRqBBWCBeg0d972dsuMjMBngsKZeSSPVhwiERgYkBlBXAWGR3KZaQWKho7zOjmy27BMRndz+aNOrFcRTyDjX4RCRw0pNyQm/UM4iN3JGdgnPLA9RAJ/ytSZGOyrpVynFnpj4g021PG0YmAZgdlA5CvIlaDdBapLAAU1p+pVj8B6HhExNPVamMkxyIzo7kFWXgRsXyQdhYZKzELkPKQ78xVAvsjIhBHOttXZzLXn6DNVxGoI8NZBNpIlgSrFGx4N15xl8gVVewkTUeAv0nQpJwxuHYA5853f/220n1jxBfeLxK5EfCN94v8YYcOUgCCwgAIr/56iZWUkA3W7n87CsUGy3k13w1moqyKMj5lEPKLIYbkwhAaQ2gMoTGExhAaQ2gMoTGExhAaQ2gMoTGExhAaQ2gMoTGE5iNkqusmHppLGyivIfOYdusjZUmnPlDeQrp0GO7zxwxDNvbroMVBGAjD8Gnwuw+GIYZAownUS5f2//+3TVdddNl1m5MjzXOp0Etfpk7UwTNJt/3NwM1+6UmVnZABSNMMPujbXd0o/g8JAM+fPS0YGEijnRADtJSNwGU9Jpa2JXX2Q+jJrkM6IHrAqft/lYYEzAzpUhpiox9FrAMsqVIYsuB5JLYdu4G46w6/a/ZD5K+QZSfDhQQTO+eEDrQfcpu3VvfbxnUAZc9vQo6Qw5fyC+dIWp0jj5TsZiKcQ83z4kIH2g9hIFDm4GghcwAD5jQh5ICRJEw9g3ftNJ5bS+wAe54QXj/9mukhSxwmgc4TQhwB+CSUjfA9ZffkAfiRzhRCJNzLcik06/nwU+N939nPo4ZoU0O0qSHa1BBtaog2NUSbNwh5GKzFvlGhPCRiyzQqXItD8FOjQ3GIwVZsVCifyD1izei4R65vvLVOpoZoU0M+27t7FIZhGAzDzs/nQEihlCwejEZhjA2+/+XqExS8fXH1nkAPGjSKLYOw9QPScg1gqqlqG4eICirVukoUSeOQeAOBaiX1EmAc8ipskKxZZQbIBZznDJCer+MQ+Ry4I3g6+kCtjEMQNFcwFTRnwTgEKYEs+e/L/qwMwpZB2DIIWwZhyyBsGYQtg7BlELYMwpZB2JoI4jBF3q0TvJ4G3ptb3ATPp6Uz9mXzT8+ty/4FsaeuueShPysAAAAASUVORK5CYII=", "public": true}]}