{"fqn": "power_consumption_chart_card", "name": "Power consumption chart card", "deprecated": false, "image": "tb-image;/api/images/system/power_consumption_chart_card.svg", "description": "Displays power consumption data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'powerConsumption', label: 'Power consumption', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'kW', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'powerConsumption', 'kW', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Power consumption\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"kW\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":5,\"color\":\"#3FA71A\"},{\"from\":5,\"to\":15,\"color\":\"#F36900\"},{\"from\":15,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"kW\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"#000000DE\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"kW\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Power consumption\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"bolt\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"margin\":\"0px\"}"}, "tags": ["power", "energy", "energy usage", "electric load", "electricity", "power efficiency", "load profile"], "resources": [{"link": "/api/images/system/power_consumption_chart_card.svg", "title": "power_consumption_chart_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "power_consumption_chart_card.svg", "publicResourceKey": "YHaZQJa2JmWMqb03aCktLih81X7zLEVt", "mediaType": "image/svg+xml", "data": "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", "public": true}]}