{"fqn": "simple_efficiency_chart_card_with_background", "name": "Simple efficiency chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_efficiency_chart_card_with_background.svg", "description": "Displays historical efficiency values as a simplified chart with background. Optionally may display the corresponding efficiency value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'efficiency', label: 'Efficiency', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'efficiency', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Efficiency\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 30;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 30;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":60,\"color\":\"#F89E0D\"},{\"from\":60,\"to\":80,\"color\":\"#3B911C\"},{\"from\":80,\"to\":null,\"color\":\"#2B54CE\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/simple_efficiency_chart_card_background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Efficiency\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"trending_up\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"units\":\"%\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true},\"margin\":\"0px\"}"}, "tags": ["productivity", "effectiveness", "performance", "capability"], "resources": [{"link": "/api/images/system/simple_efficiency_chart_card_background.png", "title": "simple_efficiency_chart_card_background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_efficiency_chart_card_background.png", "publicResourceKey": "ed4SdIDDS2Tm45iFLO7qYOryhcc1D1H8", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_efficiency_chart_card_with_background.svg", "title": "simple_efficiency_chart_card_with_background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_efficiency_chart_card_with_background.svg", "publicResourceKey": "yG5spy1p8WnqsODSVHGFVNlcldYc4WUZ", "mediaType": "image/svg+xml", "data": "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", "public": true}]}