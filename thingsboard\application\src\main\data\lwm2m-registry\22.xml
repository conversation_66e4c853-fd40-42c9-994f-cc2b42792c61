<?xml version="1.0" encoding="UTF-8"?>

<!--
FILE INFORMATION

OMNA LwM2M Registry
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 22.xml

NORMATIVE INFORMATION

  Information about this file can be found in:

  https://www.openmobilealliance.org/documents/guidelines/OMA-Guidelines-LwM2M_VirtualObserveNotify-V1_1-20211130-A.pdf

  Send comments to https://github.com/OpenMobileAlliance/OMA_LWM2M_for_Developers/issues

LEGAL DISCLAIMER

  Copyright 2021 Open Mobile Alliance.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

  1. Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
  3. Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived
  from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.

  The above license is used as a license under copyright only.  Please
  reference the OMA IPR Policy for patent licensing terms:
  https://www.omaspecworks.org/about/intellectual-property-rights/

-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.openmobilealliance.org/tech/profiles/LWM2M-v1_1.xsd">
	<Object ObjectType="MODefinition">
		<Name>Virtual Observe Notify</Name>
		<Description1><![CDATA[The Virtual Observe Notify Object provides a main function to notify multiple resources to a LwM2M Server in one notification message whether the resources are in one object/object instance or across multiple objects. The Virtual Observe Notify Object also provides some functions to implement more efficient multi-resource report.]]></Description1>
		<ObjectID>22</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:22:1.1</ObjectURN>
		<LWM2MVersion>1.1</LWM2MVersion>
		<ObjectVersion>1.1</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>ObserveLinks</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Corelnk</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Array of Core Links where each element includes a Resource ID or Object Instance ID to be observed and its observation criteria (lt, gt, st, pmin, pmax).

When the LwM2M Server write the resources or object instances to this Resource, the LwM2M Client starts to observe these resources.
When the LwM2M Server write empty string to this Resource, the LwM2M Client stop to observe resources.]]></Description>
			</Item>
			<Item ID="1">
				<Name>Report</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource is populated by the LwM2M Client each time an observed resource from ObserveLinks meets its observation criteria, thereby resulting in a notification to a LwM2M Server that is observing Report resource. The type MUST be SenML JSON]]></Description>
			</Item>
			<Item ID="2">
				<Name>ResourceFilter</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Used by the LwM2M Server to indicate the LwM2M Client whether send all or changed resources.
False: report all Resources,
True: report only changed Resources.
When it is absent, the LwM2M Client shall report all Resources as default.]]></Description>
			</Item>
			<Item ID="3">
				<Name>ReportLinks</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Corelnk</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Array of Core Links where each element includes the Resources ID to be included in the notification.

If ReportLinks Resource is absent, the notification sent to the LwM2M Server will only include the Resources in ObserveLinks  .

If the ReportLinks Resource is present, the notification sent to the LwM2M Server will only include the Resources that are listed in ReportLinks and not include the value of the Resource ObserveLinks.

To provide maximum flexibility to the server the Notifications will not comprise the union of resources in ObserveLinks and ReportLinks when both present.]]></Description>
			</Item>
			<Item ID="4">
				<Name>ObserveRelation</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Used by the server to indicate the AND/OR logic relationship combination between or among resources included in ObserveLinks Resource.
0: OR
1: AND
2: reserved value.]]></Description>
			</Item>
			<Item ID="5">
				<Name>Virtual Observe Report Format</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[If present, it specifies the content format LwM2M Client will use to populate Virtual Observe Report Resource.
Values will be either as per Tables 7.4.1 and 7.4.2 of LwM2M Core TS or another IANA registered Media Type. Default value of SenML JSON is assumed when this resource is not present.]]></Description>
			</Item>
			<Item ID="6">
				<Name>Virtual Observe Report</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[If present this resource is populated by the LwM2M Client following exactly the same rules as those used to populate Resource 4, Report Resource.
The only difference is that this resource is formatted as defined by Resource 5]]></Description>
			</Item>
		</Resources>
		<Description2><![CDATA[Notes:
(1)	When the ResourceFilter is set to TRUE, this condition takes precedence over any pmax. Namely, when the LwM2M Server set the Resource ResourceFilter to TRUE, whatever the pmax attribute is attached to any resource included in the ObserveLinks, the Report is only populated by the LwM2M Client each time the resources included in the ObserveLinks are changed.
(2)	Any default pmax and pmin values which have been configured in the Server Object should not apply to VirtualObserveNotify and each instance of this object should use explicit pmin and pmax as requested by the LwM2M Server when setting up notifications.]]></Description2>
	</Object>
</LWM2M>
