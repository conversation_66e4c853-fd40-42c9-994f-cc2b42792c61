<?xml version="1.0" encoding="UTF-8"?>

<!--
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-XML_8-V1_0_3-20210119-A.xml
   Path: http://www.openmobilealliance.org/release/LWM2M_LOCKWIPE/

OMNA LwM2M Registry
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 8.xml

NORMATIVE INFORMATION

  Information about this file can be found in the latest revision of

    OMA-TS-LightweightM2M-V1_0_1

  This is available at http://www.openmobilealliance.org/release/

  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues


LEGAL DISCLAIMER

  Copyright 2021 Open Mobile Alliance.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

  1. Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
  3. Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived
  from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.

  The above license is used as a license under copyright only.  Please
  reference the OMA IPR Policy for patent licensing terms:
  https://www.omaspecworks.org/about/intellectual-property-rights/
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>Lock and Wipe</Name>
		<Description1><![CDATA[This LWM2M objects provides the resources needed to perform the lock and wipe operations.]]></Description1>
		<ObjectID>8</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:8</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Single</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0"><Name>State</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..2</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[State of the device:
0: unlocked state Normal operation.
1: partially locked state
To render the device inoperable the device has been partially locked. The "lock target" resource allows specifying the target(s) for this operation.
2: fully locked state
To render the device fully inoperable the device has been fully locked.]]></Description>
			</Item>
			<Item ID="1"><Name>Lock target</Name>
				<Operations>W</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[To specify one or several targets for the lock operation. This allows partially locking the device by selecting specific components or interfaces to be locked.]]></Description>
			</Item>
			<Item ID="2"><Name>Wipe item</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates which data can be wiped from the device. This resource could be e.g. representing a directory.]]></Description>
			</Item>
			<Item ID="3"><Name>Wipe</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[To permanently erase data from the device.]]></Description>
			</Item>
			<Item ID="4"><Name>Wipe target</Name>
				<Operations>W</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[To specify one or several targets for the wipe operation. This allows selecting specific data, or, memory areas for the wipe operation.]]></Description>
			</Item><Item ID="5"><Name>Lock or Wipe Operation Result</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..8</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Contains the result of a lock and wipe operation
0: Default
1: Partially Lock operation successful
2: Fully Lock operation successful
3: Unlock operation successful
4: Wipe operation successful
5: Partially Lock operation failed
6: Fully Lock operation failed
7: Unlock operation failed 
8: Wipe operation failed
This Resource MAY be reported by sending Observe operation.]]></Description>
			</Item></Resources>
		<Description2 />
	</Object>
</LWM2M>
