---
name: "\U0001F41E Bug report"
about: Create a report to help us improve
title: "Your title here"
labels: ['bug', 'unconfirmed']
assignees: Ultrazombie

---

**Describe the bug**
A clear and concise description of what the bug is.

**Your Server Environment**
<!-- 🔅🔅🔅🔅🔅🔅🔅 Choose one of the following or write your own 🔅🔅🔅🔅🔅🔅🔅-->
* [Live Demo](https://demo.thingsboard.io)
* [ThingsBoard Cloud](https://thingsboard.cloud)
* own setup
  * Deployment: monolith or microservices
  * Deployment type: deb, rpm, exe, docker-compose, k8s, ami
  * ThingsBoard Version
  * Community or Professional Edition
  * OS Name and Version

**Your Client Environment**
<!-- 🔅🔅🔅🔅🔅🔅🔅 Choose one of the following or write your own 🔅🔅🔅🔅🔅🔅🔅-->
**Desktop (please complete the following information):**

* OS: [e.g. iOS]
* Browser [e.g. chrome, safari]
* Version [e.g. 22]

**Smartphone (please complete the following information):**
* Device: [e.g. iPhone6]
* OS: [e.g. iOS8.1]
* Browser [e.g. stock browser, safari]
* Version [e.g. 22]

**Your Device**

* Connectivity
  * MQTT
  * HTTP
  * CoAP
  * Gateway
  * Integration: (Specify name) 
* Device vendor and model

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, please add screenshots to help explain your problem.

**Additional context**
Please feel free to add any other context about the problem here.

________________________________________________________________
**Disclaimer** 

We appreciate your contribution whether it is a bug report, feature request, or pull request with improvement (hopefully). Please comply with the [Community ethics policy](https://docs.github.com/en/site-policy/acceptable-use-policies/github-acceptable-use-policies), and do not expect us to answer your requests immediately. Also, do not treat *GitHub issues* as a support channel. 
