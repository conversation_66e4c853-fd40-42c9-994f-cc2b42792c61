{"fqn": "cards.horizontal_value_card", "name": "Horizontal value card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_value_card_system_widget_image.png", "description": "Displays a single entity attribute or the latest telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"thermostat\",\"iconColor\":{\"type\":\"constant\",\"color\":\"#5469FF\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal value card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"°C\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "resources": [{"link": "/api/images/system/horizontal_value_card_system_widget_image.png", "title": "\"Horizontal value card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_value_card_system_widget_image.png", "publicResourceKey": "vTapIfpV68OmBeL5bNEy3esFNX6Ysd7B", "mediaType": "image/png", "data": "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", "public": true}]}