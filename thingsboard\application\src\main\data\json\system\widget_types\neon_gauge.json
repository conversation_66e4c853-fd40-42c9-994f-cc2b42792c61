{"fqn": "digital_gauges.neon_gauge_justgage", "name": "Neon gauge", "deprecated": false, "image": "tb-image;/api/images/system/neon_gauge_system_widget_image.png", "description": "Preconfigured gauge to display any value reading as an arc. Allows to configure value range, gradient colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 3, "resources": [], "templateHtml": "<canvas id=\"digitalGauge\"></canvas>", "templateCss": "#gauge {\n    text-align: center;\n   /* margin-left: -100px;\n    margin-right: -100px;*/\n    /*margin-top: -50px;*/\n    \n}\n", "controllerScript": "self.onInit = function() {\n    self.ctx.gauge = new TbCanvasDigitalGauge(self.ctx, 'digitalGauge');    \n}\n\nself.onDataUpdated = function() {\n    self.ctx.gauge.update();\n}\n\nself.onResize = function() {\n    self.ctx.gauge.resize();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        supportsUnitConversion: true\n    };\n}\n\nself.onMobileModeChanged = function() {\n    self.ctx.gauge.mobileModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.gauge.destroy();\n}\n\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-digital-gauge-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-digital-simple-gauge-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temp\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.7282710489093589,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#000000\",\"color\":\"rgba(255, 254, 254, 0.87)\",\"padding\":\"0px\",\"settings\":{\"maxValue\":100,\"minValue\":0,\"donutStartAngle\":90,\"showValue\":true,\"showMinMax\":true,\"gaugeWidthScale\":0.75,\"levelColors\":[],\"titleFont\":{\"family\":\"Roboto\",\"size\":12,\"style\":\"normal\",\"weight\":\"500\"},\"labelFont\":{\"family\":\"Roboto\",\"size\":8,\"style\":\"normal\",\"weight\":\"500\"},\"valueFont\":{\"family\":\"Segment7Standard\",\"style\":\"normal\",\"weight\":\"500\",\"size\":32},\"minMaxFont\":{\"family\":\"Segment7Standard\",\"size\":12,\"style\":\"normal\",\"weight\":\"500\"},\"neonGlowBrightness\":70,\"dashThickness\":1,\"gaugeType\":\"arc\",\"animation\":true,\"animationDuration\":500,\"animationRule\":\"linear\"},\"title\":\"Neon gauge\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"widgetStyle\":{},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{},\"configMode\":\"basic\"}"}, "resources": [{"link": "/api/images/system/neon_gauge_system_widget_image.png", "title": "\"Neon gauge\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "neon_gauge_system_widget_image.png", "publicResourceKey": "ow7gJp5luQ1YmQnrqEb3m9wSrV69istx", "mediaType": "image/png", "data": "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", "public": true}]}