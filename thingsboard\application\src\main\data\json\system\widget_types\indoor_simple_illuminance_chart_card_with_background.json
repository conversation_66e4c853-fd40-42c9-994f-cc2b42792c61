{"fqn": "indoor_simple_illuminance_chart_card_with_background", "name": "Indoor simple illuminance chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/indoor_simple_illuminance_chart_card_with_background_system_widget_image.png", "description": "Displays historical indoor illuminance values as a simplified chart with background. Optionally may display the corresponding latest indoor illuminance value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'illuminance', label: 'Illuminance', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'illuminance', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Illuminance\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 400 - 200;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 400 - 200;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":100,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":100,\"to\":300,\"color\":\"#F89E0D\"},{\"from\":300,\"to\":500,\"color\":\"#F77410\"},{\"from\":500,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/indoor_simple_illuminance_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Illuminance\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:lightbulb-on\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"lx\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["environment", "illuminance", "indoor", "brightness", "luminance", "luminosity", "light", "light level", "light intensity", "lux", "candela", "foot-candle"], "resources": [{"link": "/api/images/system/indoor_simple_illuminance_chart_card_with_background_system_widget_background.png", "title": "\"Indoor simple illuminance chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_simple_illuminance_chart_card_with_background_system_widget_background.png", "publicResourceKey": "SW4x4laEx8F7NdUb2FEOwjezetyYxErI", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/indoor_simple_illuminance_chart_card_with_background_system_widget_image.png", "title": "\"Indoor simple illuminance chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_simple_illuminance_chart_card_with_background_system_widget_image.png", "publicResourceKey": "H62BIlGDjS6XHI0ZLVoPqFGOC86t62bP", "mediaType": "image/png", "data": "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", "public": true}]}