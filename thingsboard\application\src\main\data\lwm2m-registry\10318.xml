<?xml version="1.0" encoding="utf-8"?>
<!--BSD 3-Clause License

Copyright (c) 2019, Cloudminds
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>EC<PERSON>L, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON>
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>RCU PM</Name>
    <Description1><![CDATA[This LWM2M Object includes RCU(Robotic Control Unit) PM information(counter and guage).]]></Description1>
    <ObjectID>10318</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10318</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Single</MultipleInstances>
    <Mandatory>Mandatory</Mandatory>
    <Resources>
	  <Item ID="1">
        <Name>CPU Usage</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>0..100</RangeEnumeration>
        <Units>/100</Units>
        <Description><![CDATA[The CPU usage of the RCU.]]></Description>
      </Item>
	  <Item ID="2">
        <Name>Max CPU Usage</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>0..100</RangeEnumeration>
        <Units>/100</Units>
        <Description><![CDATA[Max CPU Usage of the RCU.]]></Description>
      </Item>
      <Item ID="3">
        <Name>Memory Usage</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>0..100</RangeEnumeration>
        <Units>/100</Units>
        <Description><![CDATA[Memory Usage of the RCU.]]></Description>
      </Item>
      <Item ID="4">
        <Name>Storage Usage</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>0..100</RangeEnumeration>
        <Units>/100</Units>
        <Description><![CDATA[Storage Usage of the RCU.]]></Description>
      </Item>
	  
	  <Item ID="51">
        <Name>Battery Level</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>0..100</RangeEnumeration>
        <Units>/100</Units>
        <Description><![CDATA[The Battery Level of the RCU.]]></Description>
      </Item>
      <Item ID="52">
        <Name>Network Bandwidth</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Float</Type>
        <RangeEnumeration/>
        <Units>Mbit/s</Units>
        <Description><![CDATA[Network Bandwidth of the RCU.]]></Description>
      </Item>
      <Item ID="53">
        <Name>Mobile Signal</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[Mobile Signal of the RCU.]]></Description>
      </Item>
      <Item ID="54">
        <Name>GPS Signal</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[GPS Signal of the RCU.]]></Description>
      </Item>
      <Item ID="55">
        <Name>Wi-Fi Signal</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[Wi-Fi Signal of the RCU.]]></Description>
      </Item>
      <Item ID="56">
        <Name>UpLink Rate</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Float</Type>
        <RangeEnumeration/>
        <Units>Mbit/s</Units>
        <Description><![CDATA[UpLink Rate of the network used by the RCU.]]></Description>
      </Item>	  
      <Item ID="57">
        <Name>DownLink Rate</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Float</Type>
        <RangeEnumeration/>
        <Units>Mbit/s</Units>
        <Description><![CDATA[DownLink Rate of the network used by the RCU.]]></Description>
      </Item>
      <Item ID="58">
        <Name>Packet Loss Rate</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>0..100</RangeEnumeration>
        <Units>/100</Units>
        <Description><![CDATA[Packet Loss Rate in percentage.]]></Description>
      </Item>
      <Item ID="59">
        <Name>Network Latency</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration/>
        <Units>ms</Units>
        <Description><![CDATA[Network Latency in ms.]]></Description>
      </Item>
	  
      <Item ID="300">
        <Name>Battery Temperature</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Float</Type>
        <RangeEnumeration/>
        <Units>Cel</Units>
        <Description><![CDATA[Battery Temperature.]]></Description>
      </Item>
    </Resources>
    <Description2 />
  </Object>
</LWM2M>
