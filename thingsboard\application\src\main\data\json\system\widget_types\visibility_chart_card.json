{"fqn": "visibility_chart_card", "name": "Visibility chart card", "deprecated": false, "image": "tb-image;/api/images/system/visibility_chart_card_system_widget_image.png", "description": "Displays a visibility data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'visibility', label: 'Visibility', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'km', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'visibility', 'km', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Visibility\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"km\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":1,\"color\":\"#D81838\"},{\"from\":1,\"to\":4,\"color\":\"#FFA600\"},{\"from\":4,\"to\":null,\"color\":\"#80C32C\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"km\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -4) {\\n\\tvalue = -4;\\n} else if (value > 4) {\\n\\tvalue = 4;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"km\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Visibility\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"visibility\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "visibility", "sight", "view", "clarity", "transparency", "perceptibility", "discernibility", "range of view", "clearness"], "resources": [{"link": "/api/images/system/visibility_chart_card_system_widget_image.png", "title": "\"Visibility chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "visibility_chart_card_system_widget_image.png", "publicResourceKey": "1eX4sEUb6pjRxDzCiEPNACvL7MufaeYi", "mediaType": "image/png", "data": "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", "public": true}]}