{"fqn": "horizontal_vibration_card_with_background", "name": "Horizontal vibration card with background", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_vibration_card_with_background_system_widget_image.png", "description": "Displays the latest vibration telemetry in a scalable horizontal layout with the background image.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'vibration', label: 'Vibration', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Vibration\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"let factor = 1000;\\nif (prevValue < 1) {\\n    factor = 1;\\n} else if (prevValue < 10) {\\n    factor = 10;\\n} else if (prevValue < 100) {\\n    factor = 100;\\n}\\nlet value = prevValue + Math.random() * factor;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"vibration\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0.1,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0.1,\"to\":1,\"color\":\"#F89E0D\"},{\"from\":1,\"to\":10,\"color\":\"#F77410\"},{\"from\":10,\"to\":100,\"color\":\"#F04022\"},{\"from\":100,\"to\":1000,\"color\":\"#DE2343\"},{\"from\":1000,\"to\":null,\"color\":\"#791541\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":0.1,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0.1,\"to\":1,\"color\":\"#F89E0D\"},{\"from\":1,\"to\":10,\"color\":\"#F77410\"},{\"from\":10,\"to\":100,\"color\":\"#F04022\"},{\"from\":100,\"to\":1000,\"color\":\"#DE2343\"},{\"from\":1000,\"to\":null,\"color\":\"#791541\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/horizontal_vibration_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal vibration card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"m/s²\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "vibration", "tremor", "shake", "quiver", "jolt", "oscillation", "pulsation", "resonance"], "resources": [{"link": "/api/images/system/horizontal_vibration_card_with_background_system_widget_background.png", "title": "\"Horizontal vibration card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_vibration_card_with_background_system_widget_background.png", "publicResourceKey": "B5KlhQKoptTSO2wh54y3vLdhELmBANdW", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/horizontal_vibration_card_with_background_system_widget_image.png", "title": "\"Horizontal vibration card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_vibration_card_with_background_system_widget_image.png", "publicResourceKey": "J0xw4V3Qzcmv2dOdX7hUo8YFpCVz9YnC", "mediaType": "image/png", "data": "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", "public": true}]}