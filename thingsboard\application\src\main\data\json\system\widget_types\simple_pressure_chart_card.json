{"fqn": "simple_pressure_chart_card", "name": "Simple pressure chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_pressure_chart_card_system_widget_image.png", "description": "Displays historical pressure values as a simplified chart. Optionally may display the corresponding latest pressure value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pressure', label: 'Pressure', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'pressure', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < 980) {\\n\\tvalue = 980;\\n} else if (value > 1040) {\\n\\tvalue = 1040;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < 980) {\\n\\tvalue = 980;\\n} else if (value > 1040) {\\n\\tvalue = 1040;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1000,\"color\":\"#D81838\"},{\"from\":1000,\"to\":1020,\"color\":\"#80C32C\"},{\"from\":1020,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Pressure\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"compress\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"hPa\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "barometry"], "resources": [{"link": "/api/images/system/simple_pressure_chart_card_system_widget_image.png", "title": "\"Simple pressure chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_pressure_chart_card_system_widget_image.png", "publicResourceKey": "wliVlWPQHV6VyX13ZfB6qmRVaF7OIvl1", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAgVBMVEUAAADg4ODf39/g4ODg4OD////g4ODYGDghISHj4+M8PDziUmnHx8eQkJDsi5tYWFjx8fH1xc3dNVH64uZ0dHQvLy/nb4LwqbRKSkraJkSenp6CgoL98fKsrKzumqj409rV1dXzt8HpfY+rq6tmZmbkYHbgRF26urq5ubnfQ1398fNuhph/AAAABXRSTlMA7yC/r1EOHTEAAAS8SURBVHja7M9JAcAgDAAwytE3/tUOGdAlDtIAAAAAAAB+p4/I181+HrHX83b0Ngs8zmS0XCVElUiKXEbkNh87dqzqMAxDYXg6IAkJebDBJBgMDSTv/4Q3bUNDB8OdgkP9D0YaPzAkeEB6a0B6a0B6a0B66wcgy4o71YJUz7OHc1czmxj91oKwEBnOLMUopOi2FgQsBpyZAHDHVreKUAoD0LIC+7LyfgRwCVz4Nay4tDakquqqe/wFWTI8pYklOSnmFMVR05KFmRRKrJSEV8qScWVtSJSj7bxapRgZ3ANmCfAFMkPjSxj1A5kAiXiuF9aGFDuqHwiJZAM8Ap7cRWDkM6OKRMUH8hyzOxkurA2Z4pF+XS28IYuoagX4IQJgi6RfEFNVxjW1ISem4uwLMlGFPgJN+xQWR0hToBn2hsAzB+sHYpRqA4KZKG0wEjKwpOQMI/IDwrLP3UA0SRa0Cvw+A/b4WPCJL2e0IWAvPX/I/w9Bvdeb8A/8/d6sAemtAemtAemtAemtAemtAemtriHmRCny3SGcyWeLQo+bQ5weh0dvDfljx9yWk4WBAHy1syGRBIKcEX7w0Nr3f8CfZWOpI6XWC0Y6fhdkXaKz3yQbGN9EwUEgzapFDhIchfDWLBJvwREIf8UiG/E+dku2QhEv4LEUHPDe2qxNpIiFOHgUZQYY3lsrE9kJs/NjGQC8Xz09jHx6kX+74su2kdnQHdIvYy593FvPLRIY0WO8zwXZD3JGxGUAcLW3nltkK3wv2McZMJmEbzCx97BI1QAT1rod003aXNJ1CA6FCTiqcKC574gtaHgXb+7jHr7Bi6VHQymlKbzfiIQq11yYwh7rCtM5oh58LKdvREJkbAo/4rsjNja8PnKml6QwWyPiQ3+Ru3tFoi5HVzB0mOvaoq24YDwnLc3I8VT36byaEDkrpSxiDT9hzEWooOtsI3ilkZkfUCRFGdwhwsUoyyIfiGl/zbFmqRCcHplFdPNWJKLvnUlynkCULsiE3GbCwJ34Qnr3rUhdgULt6qJ6EjyOdVM6bWkgWycSJkqlowi0iA1ApY+UnmYzHqp7I80e7mYjs7ub3YnUmNOg8UyxqtKx8Qez1k22SKSjSIMYQsRpBRPwW8iDBMFvRTRaN1DqRL1jIyCasD3h6TIZddRYVKNI3Y8kWFHUzrTIwzwuckZUtc4p5OJRVeDio1s9EmnDsNVIqTZteAPOtcjyIhYVN0FIqTo5InZfj98UkW47FO+wVGsSmWuR5UU45QY+hNMJkWOSJN0gGykkpkW2MSwoknKzd6gmROCIyYRIBBcsnkL+qakFKZcUaaguV2nCxw8tw9F2rjfmRD54F06KbGIZLCbCZXeDTst9DKCp0g7zaEincyIRoqbMhMheZB4sKlIj2pN7FHT9eBrKiiyHFma3luJZdPeaQmQBLCsCqUXMuwoITXFNUZRgH+pqToRn5brD8807xiGAJUSuiaLqS3wTzlIN373x2MLIev/E9ofz6g+IHHy6/gER4iXyElkLL5Fn4yXybLxEno2XyLPxEvnfzn0TAAACQRB80tf4V4sCesKMgxNwexpDTpNRxhMy2l33io3Z34kgRe15u9JqAAAAAAAA/GYBBOmvbsxu/swAAAAASUVORK5CYII=", "public": true}]}