<?xml version="1.0" encoding="utf-8"?>

<!-- BSD-3 Clause License

Copyright 2019 oneM2M Partners. 

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON><PERSON>NTIA<PERSON> DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>CmdhLimits</Name>
		<Description1><![CDATA[This Object represents limits for CMDH related parameter values. ]]></Description1>
		<ObjectID>2053</ObjectID>
		<ObjectURN>urn:oma:lwm2m:ext:2053</ObjectURN><LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion><MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0"><Name>Order</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Contains index indicating in which order the concerned CmdhLimits Object Instance will be treated by the CSE to determine a value for the limit parameters.]]></Description>
			</Item>
			<Item ID="1"><Name>RequestOrigin</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[List of zero or more Local AE-IDs, App-IDs, or the strings "localAE" or "thisCSE"]]></Description>
			</Item>
			
			
			
			
			
			
			<Item ID="2"><Name>RequestContext</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Represents the Dynamic Context condition under which CMDH parameter limits defined inside the concerned CmdhLimits Object Instance is applicable.]]></Description>
			</Item>
			
			<Item ID="3"><Name>RequestContextNotificatio</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Contains true/false flag indicating whether or not notification procedures apply.]]></Description>
			</Item>
			<Item ID="4"><Name>RequestCharacteristics</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Represents conditions pertaining to the request itself, (e.g. the requested Response Type  ) than needs to be matched]]></Description>
			</Item>
			<Item ID="5"><Name>LimitsEventCategory</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Allowed values for the Event Category parameter in a request of any of the Originators indicated in the requestOrigin attribute.]]></Description>
			</Item>
			<Item ID="6"><Name>LimitsRequestExpTime </Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>2 Instances</RangeEnumeration>
				<Units>ms</Units>
				<Description><![CDATA[Defines a range of values for the Request Expiration Time parameter for a request of any Originator indicated in the requestOrigin Resource
Inst 0 : minTime
Inst 1:  maxTime
 ]]></Description>
			</Item>
			<Item ID="7"><Name>LimitsResultExpTime</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>2 Instances</RangeEnumeration>
				<Units>ms</Units>
				<Description><![CDATA[Defines a range of values for the Result Expiration Time parameter for a request of any Originator indicated in the requestOrigin Resource
Inst 0 : minTime
Inst 1:  maxTime
]]></Description>
			</Item>
			<Item ID="8"><Name>LimitsOptExpTime</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>2 Instances</RangeEnumeration>
				<Units>ms</Units>
				<Description><![CDATA[Defines a range of values for the Operation Expiration Time parameter for a request of any Originator indicated in the requestOrigin Resource
Inst 0 : minTime
Inst 1:  maxTime
]]></Description>
			</Item>
			<Item ID="9"><Name>LimitsRespPersistence</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>2 Instances</RangeEnumeration>
				<Units>ms</Units>
				<Description><![CDATA[Defines a range of values for the Result Persistence parameter for a request of any Originator indicated in the requestOrigin Resource
Inst 0 : minTime
Inst 1:  maxTime
]]></Description>
			</Item>
			<Item ID="10"><Name>LimitsDelAggregation</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Contains the permitted settings of the DeliveryAggregation parameter of request primitives. 
'0' means 'False' '1' means 'True' '0 1' means 'False' or 'True'
]]></Description>
			</Item></Resources>
		<Description2 />
	</Object>
</LWM2M>
