<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="1000" height="1200" fill="none" version="1.1" viewBox="0 0 1000 1200"><tb:metadata xmlns=""><![CDATA[{
  "title": "HP Oil pump",
  "description": "Oil pump with various states.",
  "searchTags": [
    "extraction"
  ],
  "widgetSizeX": 5,
  "widgetSizeY": 6,
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.runningColor;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "critical",
      "stateRenderFunction": "element.attr({fill: ctx.properties.criticalColor});\nif (ctx.values.critical) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = ctx.values.critical && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'criticalClick');"
        }
      }
    },
    {
      "tag": "warning",
      "stateRenderFunction": "element.attr({fill: ctx.properties.warningColor});\nvar warning = ctx.values.warning && !(ctx.values.warning && ctx.values.critical)\nif (warning) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = warning && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'warningClick');"
        }
      }
    }
  ],
  "behavior": [
    {
      "id": "running",
      "name": "{i18n:scada.symbol.running}",
      "hint": "{i18n:scada.symbol.running-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.running}",
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "running"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warning",
      "name": "{i18n:scada.symbol.warning}",
      "hint": "{i18n:scada.symbol.warning-state-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.warning}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "MAJOR",
            "MINOR",
            "WARNING",
            "INDETERMINATE"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warningClick",
      "name": "{i18n:scada.symbol.warning-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "critical",
      "name": "{i18n:scada.symbol.critical}",
      "hint": "{i18n:scada.symbol.critical-state-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.critical}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "CRITICAL"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "criticalClick",
      "name": "{i18n:scada.symbol.critical-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "criticalAnimation",
      "name": "{i18n:scada.symbol.warning-critical-state-animation}",
      "hint": "{i18n:scada.symbol.warning-critical-state-animation-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.animation}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    }
  ],
  "properties": [
    {
      "id": "runningColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": "{i18n:scada.symbol.running}",
      "divider": true,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null,
      "disabled": false,
      "visible": true
    },
    {
      "id": "stoppedColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#666666",
      "required": null,
      "subLabel": "{i18n:scada.symbol.stopped}",
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null,
      "disabled": false,
      "visible": true
    },
    {
      "id": "warningColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#FAA405",
      "required": null,
      "subLabel": "{i18n:scada.symbol.warning}",
      "divider": true,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null,
      "disabled": false,
      "visible": true
    },
    {
      "id": "criticalColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#D12730",
      "required": null,
      "subLabel": "{i18n:scada.symbol.critical}",
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null,
      "disabled": false,
      "visible": true
    }
  ]
}]]></tb:metadata>
<rect x="1" y="1161" width="998" height="38" rx="3" stroke="#1A1A1A" stroke-width="2"/><path d="m315.2 1161 151.18-840.74 63.841-21.443 154.58 862.18h-369.61z" stroke="#1A1A1A" stroke-width="2"/><path d="m900 541v384" stroke="#000" stroke-width="6"/><path d="m315 1160 313-313" stroke="#1A1A1A" stroke-width="2"/><path d="m685 1160-313-313" stroke="#1A1A1A" stroke-width="2"/><path d="m628 847-217-217" stroke="#1A1A1A" stroke-width="2"/><path d="m589 630-151-151" stroke="#1A1A1A" stroke-width="2"/><path d="m562 479-106-106" stroke="#1A1A1A" stroke-width="2"/><path d="m438 479 106-106" stroke="#1A1A1A" stroke-width="2"/><path d="m411 630 151-151" stroke="#1A1A1A" stroke-width="2"/><path d="m372 847 217-217" stroke="#1A1A1A" stroke-width="2"/><path d="m372 847h256" stroke="#1A1A1A" stroke-width="2"/><path d="m410 630h180" stroke="#1A1A1A" stroke-width="2"/><path d="m438 479h124" stroke="#1A1A1A" stroke-width="2"/><path d="m114 451 154 564" stroke="#1A1A1A" stroke-width="2"/><rect x="861" y="1143" width="78" height="18" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="881" y="925" width="38" height="218" stroke="#1A1A1A" stroke-width="2"/><path d="m920 1100h80" stroke="#1A1A1A" stroke-width="6"/><g fill="#fff" stroke="#1A1A1A" stroke-width="2" tb:tag="background">
  <path d="m91.339 388.56c0.0845-1.178 0.8541-2.197 1.9665-2.601l563.4-204.78c1.558-0.567 3.28 0.236 3.847 1.791l0.939-0.343-0.939 0.343 23.263 63.828c0.566 1.553-0.235 3.272-1.793 3.839l-592 215.18c-2.0459 0.744-4.1755-0.866-4.0202-3.029l5.3305-74.221z"/>
  <path d="m682.58 4.8723-69.104 147.33c-0.776 1.654-0.587 3.599 0.493 5.073l279.32 381.28c0.926 1.265 2.393 2.021 3.961 2.041l31.595 0.409c2.586 0.033 4.775-1.947 5.019-4.523 24.171-254.85-96.897-453.81-161.98-523.16-0.794-0.8464-1.821-1.3367-2.973-1.4758l-81.21-9.8032c-2.138-0.25813-4.201 0.88532-5.116 2.8362z"/>
  <path d="m341.58 903.6c0.934-0.934 2.338-0.737 2.922 0.302 4.821 8.575 17.591 33.981 20.073 66.303 2.478 32.286-5.299 71.486-41.597 107.78-36.297 36.3-75.496 44.08-107.78 41.6-32.322-2.48-57.728-15.25-66.303-20.07-1.039-0.59-1.236-1.99-0.302-2.93l192.98-192.98z"/>
  <path d="m24.767 832.67c0.5636-1.6 2.0759-2.671 3.7729-2.671h152.67c1.791 0 3.364 1.191 3.85 2.915l18.592 66c0.199 0.71 0.199 1.46 0 2.17l-18.592 66c-0.486 1.724-2.059 2.915-3.85 2.915h-152.67c-1.697 0-3.2093-1.071-3.7729-2.671l-23.239-66c-0.30274-0.86-0.30274-1.798 0-2.658l23.239-66z"/>
  <rect x="65" y="970" width="76" height="191"/>
  <path d="m134.96 889.96c6.62-6.619 17.351-6.619 23.97 0l124.1 124.11c6.619 6.61 6.619 17.35 0 23.97-6.62 6.61-17.351 6.61-23.97 0l-124.1-124.11c-6.619-6.619-6.619-17.35 0-23.97z"/>
 </g><path d="m278.34 1033.8c-4.296 4.29-11.261 4.29-15.557 0-4.295-4.3-4.295-11.26 0-15.56 4.296-4.29 11.261-4.29 15.557 0 4.296 4.3 4.296 11.26 0 15.56z" stroke="#1A1A1A" stroke-width="2"/><path d="m155.75 910.75c-4.296 4.296-11.261 4.296-15.556 0-4.296-4.296-4.296-11.261 0-15.556 4.295-4.296 11.26-4.296 15.556 0 4.296 4.295 4.296 11.26 0 15.556z" stroke="#1A1A1A" stroke-width="2"/><path d="m336.32 0s-336.32 0-336.32 201v985.08c0 7.9536 8.9545 13.921 20 13.921h960c11.045 0 20-5.9676 20-13.921v-985.08c0-201-330.35-201-330.35-201h-169.65zm335.35 243.6c-6.4435 0-11.666 3.7608-11.666 8.4v901.2c0 4.6392 5.2235 8.4 11.666 8.4h73.33c6.4435 0 11.666-3.7608 11.666-8.4v-901.2c0-4.6392-5.2235-8.4-11.666-8.4z" fill="#000" fill-opacity="0" tb:tag="clickArea"/><g transform="translate(0,1116)" fill="#d12730" style="display: none;" tb:tag="critical">
  <rect width="84" height="84" rx="4" fill="#fff" style=""/>
  <rect width="84" height="84" rx="4" style=""/>
  <rect x="2" y="2" width="80" height="80" rx="2" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 27.562-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g><g transform="translate(0 1116)" fill="#faa405" style="display: none;" tb:tag="warning">
  <path d="m38.422 7.1554c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" fill="#fff" style=""/>
  <path d="m38.422 7.1554c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" style=""/>
  <path d="m40.211 8.0498c0.7371-1.4741 2.8407-1.4741 3.5778-1e-5l35.528 71.056c0.6649 1.3298-0.3021 2.8944-1.7888 2.8944h-71.056c-1.4868 0-2.4538-1.5646-1.7889-2.8944z" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 37.562-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g>
</svg>