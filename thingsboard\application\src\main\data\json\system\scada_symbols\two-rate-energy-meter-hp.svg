<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="400" height="400" fill="none" version="1.1" viewBox="0 0 400 400"><tb:metadata xmlns=""><![CDATA[{
  "title": "HP Two-rate energy meter",
  "description": "Two-rate energy meter with various states.",
  "searchTags": [
    "power",
    "energy"
  ],
  "widgetSizeX": 2,
  "widgetSizeY": 2,
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.runningColor;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "critical",
      "stateRenderFunction": "element.attr({fill: ctx.properties.criticalColor});\nif (ctx.values.critical) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = ctx.values.critical && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'criticalClick');"
        }
      }
    },
    {
      "tag": "day-label",
      "stateRenderFunction": "if (ctx.properties.showDayLabel) {\n    element.show();\n    ctx.api.font(element, ctx.properties.dayLabelFont, ctx.properties.dayLabelColor);\n    ctx.api.text(element, ctx.properties.dayLabel);\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "day-rate",
      "stateRenderFunction": "ctx.api.font(element, ctx.properties.dayValueFont, ctx.properties.dayValueColor);\nctx.api.text(element, ctx.api.formatValue(ctx.values.dayRate, {units: ctx.properties.units, decimals: 0, ignoreUnitSymbol: true}));",
      "actions": null
    },
    {
      "tag": "night-label",
      "stateRenderFunction": "if (ctx.properties.showNightLabel) {\n    element.show();\n    ctx.api.font(element, ctx.properties.nightLabelFont, ctx.properties.nightLabelColor);\n    ctx.api.text(element, ctx.properties.nightLabel);\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "night-rate",
      "stateRenderFunction": "ctx.api.font(element, ctx.properties.nightValueFont, ctx.properties.nightValueColor);\nctx.api.text(element, ctx.api.formatValue(ctx.values.nightRate, {units: ctx.properties.units, decimals: 0, ignoreUnitSymbol: true}));",
      "actions": null
    },
    {
      "tag": "units",
      "stateRenderFunction": "if (ctx.properties.showUnits) {\n    element.show();\n    ctx.api.font(element, ctx.properties.unitsFont, ctx.properties.unitsColor);\n    ctx.api.text(element, ctx.api.unitSymbol(ctx.properties.units));\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "value-box-day",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.dayValueBoxBackground;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "value-box-night",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.nightValueBoxBackground;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "warning",
      "stateRenderFunction": "element.attr({fill: ctx.properties.warningColor});\nvar warning = ctx.values.warning && !(ctx.values.warning && ctx.values.critical)\nif (warning) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = warning && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'warningClick');"
        }
      }
    }
  ],
  "behavior": [
    {
      "id": "running",
      "name": "{i18n:scada.symbol.running}",
      "hint": "{i18n:scada.symbol.running-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.running}",
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "running"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "dayRate",
      "name": "{i18n:scada.symbol.day-rate}",
      "hint": "{i18n:scada.symbol.measured-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_TIME_SERIES",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "dayRate"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "nightRate",
      "name": "{i18n:scada.symbol.night-rate}",
      "hint": "{i18n:scada.symbol.measured-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_TIME_SERIES",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "nightRate"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warning",
      "name": "{i18n:scada.symbol.warning}",
      "hint": "{i18n:scada.symbol.warning-state-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.warning}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "MAJOR",
            "MINOR",
            "WARNING",
            "INDETERMINATE"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warningClick",
      "name": "{i18n:scada.symbol.warning-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "critical",
      "name": "{i18n:scada.symbol.critical}",
      "hint": "{i18n:scada.symbol.critical-state-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.critical}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "CRITICAL"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "criticalClick",
      "name": "{i18n:scada.symbol.critical-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "criticalAnimation",
      "name": "{i18n:scada.symbol.warning-critical-state-animation}",
      "hint": "{i18n:scada.symbol.warning-critical-state-animation-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.animation}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    }
  ],
  "properties": [
    {
      "id": "runningColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#FFFFFF",
      "subLabel": "{i18n:scada.symbol.running}",
      "divider": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "stoppedColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#666666",
      "subLabel": "{i18n:scada.symbol.stopped}",
      "disabled": false,
      "visible": true
    },
    {
      "id": "warningColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#FAA405",
      "subLabel": "{i18n:scada.symbol.warning}",
      "divider": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "criticalColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#D12730",
      "subLabel": "{i18n:scada.symbol.critical}",
      "disabled": false,
      "visible": true
    },
    {
      "id": "showDayLabel",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.day-rate}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "dayLabel",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.day-rate}",
      "type": "text",
      "default": "T1",
      "disableOnProperty": "showDayLabel",
      "fieldClass": "medium-width",
      "disabled": true,
      "visible": true
    },
    {
      "id": "dayLabelFont",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.day-rate}",
      "type": "font",
      "default": {
        "size": 36,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disableOnProperty": "showDayLabel",
      "disabled": true,
      "visible": true
    },
    {
      "id": "dayLabelColor",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.day-rate}",
      "type": "color",
      "default": "#000000",
      "disableOnProperty": "showDayLabel",
      "disabled": true,
      "visible": true
    },
    {
      "id": "dayValueFont",
      "name": "{i18n:scada.symbol.value}",
      "group": "{i18n:scada.symbol.day-rate}",
      "type": "font",
      "default": {
        "size": 48,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "dayValueColor",
      "name": "{i18n:scada.symbol.value}",
      "group": "{i18n:scada.symbol.day-rate}",
      "type": "color",
      "default": "#002878",
      "disabled": false,
      "visible": true
    },
    {
      "id": "dayValueBoxBackground",
      "name": "{i18n:scada.symbol.value-box-background}",
      "group": "{i18n:scada.symbol.day-rate}",
      "type": "color",
      "default": "#DEDEDE",
      "disabled": false,
      "visible": true
    },
    {
      "id": "showNightLabel",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "nightLabel",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "text",
      "default": "T2",
      "disableOnProperty": "showNightLabel",
      "fieldClass": "medium-width",
      "disabled": true,
      "visible": true
    },
    {
      "id": "nightLabelFont",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "font",
      "default": {
        "size": 36,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disableOnProperty": "showNightLabel",
      "disabled": true,
      "visible": true
    },
    {
      "id": "nightLabelColor",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "color",
      "default": "#000",
      "disableOnProperty": "showNightLabel",
      "disabled": true,
      "visible": true
    },
    {
      "id": "nightValueFont",
      "name": "{i18n:scada.symbol.value}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "font",
      "default": {
        "size": 48,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "nightValueColor",
      "name": "{i18n:scada.symbol.value}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "color",
      "default": "#002878",
      "disabled": false,
      "visible": true
    },
    {
      "id": "nightValueBoxBackground",
      "name": "{i18n:scada.symbol.value-box-background}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "color",
      "default": "#DEDEDE",
      "disabled": false,
      "visible": true
    },
    {
      "id": "showUnits",
      "name": "{i18n:scada.symbol.units}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "units",
      "name": "{i18n:scada.symbol.units}",
      "type": "units",
      "default": "kWh",
      "supportsUnitConversion": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "unitsFont",
      "name": "{i18n:scada.symbol.units}",
      "type": "font",
      "default": {
        "size": 36,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disableOnProperty": "showUnits",
      "disabled": true,
      "visible": true
    },
    {
      "id": "unitsColor",
      "name": "{i18n:scada.symbol.units}",
      "type": "color",
      "default": "#000",
      "disableOnProperty": "showUnits",
      "disabled": true,
      "visible": true
    }
  ]
}]]></tb:metadata>
<rect x="1" y="1" width="398" height="398" fill="#fff" stroke="#1A1A1A" stroke-width="2" tb:tag="background"/><rect x="49" y="237" width="302" height="82" rx="3" fill="#DEDEDE" stroke="#1A1A1A" stroke-width="2" tb:tag="value-box-night"/><rect x="49" y="81" width="302" height="82" rx="3" fill="#DEDEDE" stroke="#1A1A1A" stroke-width="2" tb:tag="value-box-day"/><text x="199.70117" y="58.076347" fill="#000000" font-family="Roboto, sans-serif" font-size="34px" font-weight="400" text-anchor="middle" tb:tag="day-label" xml:space="default"><tspan dominant-baseline="start">T1</tspan></text><text x="199.70117" y="214.37646" fill="#000000" font-family="Roboto, sans-serif" font-size="34px" font-weight="400" text-anchor="middle" tb:tag="night-label"><tspan dominant-baseline="start">T2</tspan></text><text x="200.33984" y="139.47266" fill="#002878" font-family="Roboto, sans-serif" font-size="48px" font-weight="400" text-anchor="middle" tb:tag="day-rate" xml:space="default"><tspan dominant-baseline="start">000023</tspan></text><text x="200.33984" y="295.47266" fill="#002878" font-family="Roboto, sans-serif" font-size="48px" font-weight="400" text-anchor="middle" tb:tag="night-rate" xml:space="default"><tspan dominant-baseline="start">000023</tspan></text><text x="199.89453" y="371.67578" fill="#000000" font-family="Roboto, sans-serif" font-size="36px" font-weight="400" text-anchor="middle" tb:tag="units" xml:space="preserve"><tspan dominant-baseline="start">kWh</tspan></text><path d="m134.53-2e-4s-134.53 0-134.53 67v328.36c0 2.6512 3.5818 4.6404 8 4.6404h384c4.418 0 8-1.9892 8-4.6404v-328.36c0-67-132.14-67-132.14-67h-67.858zm134.14 81.2c-2.5774 0-4.6666 1.2536-4.6666 2.8v300.4c0 1.5464 2.0894 2.8 4.6666 2.8h29.332c2.5774 0 4.6666-1.2536 4.6666-2.8v-300.4c0-1.5464-2.0894-2.8-4.6666-2.8z" fill-opacity="0" tb:tag="clickArea"/><g fill="#d12730" style="display: none;" tb:tag="critical">
  <rect width="84" height="84" rx="4" fill="#fff" style=""/>
  <rect width="84" height="84" rx="4" style=""/>
  <rect x="2" y="2" width="80" height="80" rx="2" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 27.562-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g><g fill="#faa405" style="display: none;" tb:tag="warning">
  <path d="m35.955 2.2112c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" fill="#fff" style=""/>
  <path d="m35.955 2.2112c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" style=""/>
  <path d="m37.72 3.1061c0.7371-1.4741 2.8407-1.4741 3.5778-1e-5l35.528 71.056c0.6649 1.3298-0.3021 2.8944-1.7888 2.8944h-71.056c-1.4868 0-2.4538-1.5646-1.7889-2.8944z" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m42.092 32.618-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g>
</svg>