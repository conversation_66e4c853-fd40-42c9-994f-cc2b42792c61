{"fqn": "illuminance_card", "name": "Illuminance card", "deprecated": false, "image": "tb-image;/api/images/system/illuminance_card_system_widget_image.png", "description": "Displays the latest illuminance telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'illuminance', label: 'Illuminance', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Illuminance\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:lightbulb-on\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":1,\"to\":5,\"color\":\"#FFA600\"},{\"from\":5,\"to\":20,\"color\":\"#F36900\"},{\"from\":20,\"to\":50,\"color\":\"#F04022\"},{\"from\":50,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":52,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":1,\"to\":5,\"color\":\"#FFA600\"},{\"from\":5,\"to\":20,\"color\":\"#F36900\"},{\"from\":20,\"to\":50,\"color\":\"#F04022\"},{\"from\":50,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Illuminance card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"lx\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "brightness", "luminance", "luminosity", "light", "light level", "light intensity", "lux", "candela", "foot-candle"], "resources": [{"link": "/api/images/system/illuminance_card_system_widget_image.png", "title": "\"Illuminance card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "illuminance_card_system_widget_image.png", "publicResourceKey": "B2Uzi81X7hErrtlEvAPde2lOsHELDfuR", "mediaType": "image/png", "data": "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", "public": true}]}