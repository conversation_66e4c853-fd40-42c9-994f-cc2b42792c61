{"fqn": "leaf_wetness_chart_card", "name": "Leaf wetness chart card", "deprecated": false, "image": "tb-image;/api/images/system/leaf_wetness_chart_card_system_widget_image.png", "description": "Displays a leaf wetness data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'leaf', label: 'Leaf wetness', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: '%', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'leaf', '%', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Leaf wetness\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#4B70DD\"},{\"from\":10,\"to\":50,\"color\":\"#FFA600\"},{\"from\":50,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Leaf wetness\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:leaf\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "dew", "leaf moisture", "foliage dampness", "leaf humidity", "foliar moisture"], "resources": [{"link": "/api/images/system/leaf_wetness_chart_card_system_widget_image.png", "title": "\"Leaf wetness chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "leaf_wetness_chart_card_system_widget_image.png", "publicResourceKey": "1PXXIYTQgh0nH2EcSAQzFLvnbR6wONUw", "mediaType": "image/png", "data": "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", "public": true}]}