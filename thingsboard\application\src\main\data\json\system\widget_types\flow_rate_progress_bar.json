{"fqn": "flow_rate_progress_bar", "name": "Flow rate progress bar", "deprecated": false, "image": "tb-image;/api/images/system/flow_rate_progress_bar_(1).svg", "description": "Displays flow rate reading as a horizontal progress bar. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'flowRate', label: 'Flow Rate', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"flowRate\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#305AD7\"},{\"from\":10,\"to\":30,\"color\":\"#3FA71A\"},{\"from\":30,\"to\":50,\"color\":\"#F36900\"},{\"from\":50,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":100,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#305AD7\"},{\"from\":10,\"to\":30,\"color\":\"#3FA71A\"},{\"from\":30,\"to\":50,\"color\":\"#F36900\"},{\"from\":50,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Flow rate\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"m³/hr\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["liquid", "fluid", "flow rate", "fluid dynamics", "velocity", "mass flow", "volume flow"], "resources": [{"link": "/api/images/system/flow_rate_progress_bar_(1).svg", "title": "flow_rate_progress_bar.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "flow_rate_progress_bar_(1).svg", "publicResourceKey": "SziLIxUWBn05unZFZYFjV6EOZVw2LZp1", "mediaType": "image/svg+xml", "data": "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", "public": true}]}