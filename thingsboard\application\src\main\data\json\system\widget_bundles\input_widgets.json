{"widgetsBundle": {"alias": "input_widgets", "title": "Input widgets", "image": "data:image/png;base64,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", "description": "Various input forms to set the location, image, and other configuration parameters of the device, asset, or other entity.", "order": 14000, "externalId": null, "name": "Input widgets"}, "widgetTypeFqns": ["input_widgets.update_multiple_attributes", "input_widgets.markers_placement_openstreetmap", "input_widgets.markers_placement_image_map", "input_widgets.markers_placement_google_maps", "input_widgets.device_claiming_widget", "input_widgets.web_camera_input", "input_widgets.update_server_image_attribute", "input_widgets.update_shared_image_attribute", "input_widgets.update_server_location_attribute", "input_widgets.update_shared_location_attribute", "input_widgets.update_location_timeseries", "input_widgets.update_integer_timeseries", "input_widgets.update_double_timeseries", "input_widgets.update_boolean_timeseries", "input_widgets.update_string_timeseries", "input_widgets.update_server_date_attribute", "input_widgets.update_server_boolean_attribute", "input_widgets.update_server_double_attribute", "input_widgets.update_shared_date_attribute", "input_widgets.update_shared_boolean_attribute", "input_widgets.update_server_integer_attribute", "input_widgets.update_server_string_attribute", "input_widgets.update_shared_double_attribute", "input_widgets.update_shared_integer_attribute", "input_widgets.update_shared_string_attribute", "input_widgets.update_json_attribute"]}