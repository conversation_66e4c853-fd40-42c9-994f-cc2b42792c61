{"fqn": "snow_depth_chart_card", "name": "Snow depth chart card", "deprecated": false, "image": "tb-image;/api/images/system/snow_depth_chart_card_system_widget_image.png", "description": "Displays a snow depth data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'snow', label: 'Snow depth', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'cm', decimals: 1 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'snow', 'cm', 1);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Snow depth\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 120) {\\n\\tvalue = 120;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"cm\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":1,\"color\":\"#7191EF\"},{\"from\":1,\"to\":10,\"color\":\"#4B70DD\"},{\"from\":10,\"to\":30,\"color\":\"#305AD7\"},{\"from\":30,\"to\":60,\"color\":\"#234CC7\"},{\"from\":60,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 120) {\\n\\tvalue = 120;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"cm\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nif (value < -10) {\\n\\tvalue = -10;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"cm\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Snow depth\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"ac_unit\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "snow", "snowfall", "flurry", "blizzard", "snowstorm", "snowflake", "sleet", "whiteout", "snowdrift"], "resources": [{"link": "/api/images/system/snow_depth_chart_card_system_widget_image.png", "title": "\"Snow depth chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "snow_depth_chart_card_system_widget_image.png", "publicResourceKey": "7r1wFSoekN8tNc8ub8eFpvsb2gIXNeyq", "mediaType": "image/png", "data": "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", "public": true}]}