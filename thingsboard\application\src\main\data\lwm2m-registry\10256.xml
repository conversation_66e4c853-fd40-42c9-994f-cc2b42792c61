<?xml version="1.0" encoding="utf-8"?><!--BSD 3-Clause License

Copyright (c) 2017, Huawei
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>EC<PERSON>L, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON>
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>ECID-Signal Measurement Information</Name>
    <Description1><![CDATA[This LWM2M Object provides ECID signal measurements of a device.]]></Description1>
    <ObjectID>10256</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10256</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Multiple</MultipleInstances>
    <Mandatory>Optional</Mandatory>
    <Resources>
      <Item ID="0">
        <Name>physCellId</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[This field specifies the physical cell identity of the measured cell.]]></Description>
      </Item>
      <Item ID="1">
        <Name>ECGI</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[This field specifies cell global ID of the measured cell. The target device shall provide this field if it was able to determine the ECGI of the measured cell at the time of measurement.]]></Description>
      </Item>
      <Item ID="2">
        <Name>arfcnEUTRA</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[This field specifies the ARFCN of the measured E-UTRA carrier frequency.]]></Description>
      </Item>
      <Item ID="3">
        <Name>rsrp-Result </Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[This field specifies the reference signal received power (RSRP) measurement.]]></Description>
      </Item>
      <Item ID="4">
        <Name>rsrq-Result</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[This field specifies the reference signal received quality (RSRQ) measurement.]]></Description>
      </Item>
      <Item ID="5">
        <Name>ue-RxTxTimeDiff</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[This field specifies the UE Rx-Tx time difference measurement.]]></Description>
      </Item>
    </Resources>
    <Description2><![CDATA[]]></Description2>
  </Object>
</LWM2M>
