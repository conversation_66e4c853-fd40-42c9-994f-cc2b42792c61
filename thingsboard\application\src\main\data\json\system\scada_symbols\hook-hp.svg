<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="200" height="400" fill="none" version="1.1" viewBox="0 0 200 400"><tb:metadata xmlns=""><![CDATA[{
  "title": "HP Hook",
  "description": "Hook with various states.",
  "searchTags": [
    "hook",
    "drilling"
  ],
  "widgetSizeX": 1,
  "widgetSizeY": 2,
  "tags": [
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "critical",
      "stateRenderFunction": "element.attr({fill: ctx.properties.criticalColor});\nif (ctx.values.critical) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = ctx.values.critical && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'criticalClick');"
        }
      }
    },
    {
      "tag": "hook",
      "stateRenderFunction": "element.attr({fill: ctx.properties.hookColor});",
      "actions": null
    },
    {
      "tag": "warning",
      "stateRenderFunction": "element.attr({fill: ctx.properties.warningColor});\nvar warning = ctx.values.warning && !(ctx.values.warning && ctx.values.critical)\nif (warning) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = warning && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'warningClick');"
        }
      }
    }
  ],
  "behavior": [
    {
      "id": "warning",
      "name": "{i18n:scada.symbol.warning}",
      "hint": "{i18n:scada.symbol.warning-state-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.warning}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "MAJOR",
            "MINOR",
            "WARNING",
            "INDETERMINATE"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warningClick",
      "name": "{i18n:scada.symbol.warning-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "critical",
      "name": "{i18n:scada.symbol.critical}",
      "hint": "{i18n:scada.symbol.critical-state-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.critical}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "CRITICAL"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "criticalClick",
      "name": "{i18n:scada.symbol.critical-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "criticalAnimation",
      "name": "{i18n:scada.symbol.warning-critical-state-animation}",
      "hint": "{i18n:scada.symbol.warning-critical-state-animation-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.animation}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    }
  ],
  "properties": [
    {
      "id": "hookColor",
      "name": "{i18n:scada.symbol.hook-color}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": "",
      "divider": false,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "warningColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#FAA405",
      "required": null,
      "subLabel": "{i18n:scada.symbol.warning}",
      "divider": true,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "criticalColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#D12730",
      "required": null,
      "subLabel": "{i18n:scada.symbol.critical}",
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata>
<g fill="#fff" stroke="#1a1a1a" stroke-width="2" tb:tag="hook">
  <path d="m83.979 279.78c2.3649-3.644 3.8966-6.715 4.6115-9.864 0.64089-2.823 0.61129-5.644 0.017-8.921h30.209c0.58999 3.566 0.85699 11.114-3.162 23.222-1.333 4.015-4.2859 7.854-8.1039 11.674-3.19 3.19-6.9109 6.3-10.681 9.45-0.74019 0.619-1.4823 1.239-2.2226 1.862-4.4846 3.772-8.8978 7.63-12.264 11.697-3.3665 4.066-5.7648 8.433-6.0848 13.232-0.61659 9.244 1.9776 15.781 5.8941 20.39 3.8938 4.582 9.0245 7.176 13.366 8.691 3.3656 1.175 10.24 1.992 16.594-0.225 3.199-1.116 6.2889-3.011 8.7169-6.037 2.429-3.028 4.149-7.131 4.6969-12.59 0.34899-3.477 0.60699-6.524 0.82999-9.148 0.119-1.403 0.22699-2.684 0.33399-3.846 0.311-3.382 0.60399-5.72 1.064-7.249 0.46099-1.528 0.96899-1.88 1.476-1.959 0.33699-0.054 0.81799-0.013 1.519 0.199 0.69499 0.211 1.536 0.57 2.566 1.084 11.321 5.643 15.547 14.835 16.45 23.832 0.90798 9.053-1.564 17.881-3.637 22.532-8.6369 19.376-26.348 31.189-48.797 31.189-13.468 0-23.767-2.387-32.055-6.711-8.2843-4.322-14.63-10.617-20.134-18.55-4.4111-6.357-8.8287-16.852-9.925-28.56-1.0945-11.689 1.1254-24.52 9.889-35.661 8.2183-10.447 16.325-15.531 23.129-19.132 1.2292-0.65 2.4347-1.261 3.5979-1.851 2.0264-1.027 3.9244-1.989 5.5955-2.981 2.6649-1.581 4.9275-3.332 6.5092-5.769z"/>
  <path d="m58.08 261.38c-1.9988-1.28-4.4082-3.607-7.1067-6.92-2.688-3.299-5.6227-7.526-8.6813-12.552-6.1167-10.053-12.701-23.261-18.777-38.55-12.154-30.585-22.24-69.418-22.511-107.89-0.43146-61.407 29.982-85.126 51.186-90.56 10.719-2.7467 30.395-3.9859 49.703-3.9086 19.316 0.07727 38.034 1.4712 46.85 3.9039 17.425 4.8087 50.257 32.066 50.257 90.571 0 32.108-9.83 70.081-22.104 101.85-6.133 15.874-12.866 30.17-19.264 41.38-6.423 11.254-12.436 19.266-17.095 22.714-1.119 0.828-3.04 1.62-5.682 2.327-2.615 0.701-5.841 1.294-9.494 1.775-7.304 0.96-16.24 1.459-25.255 1.477-9.0138 0.019-18.079-0.442-25.642-1.393-3.7817-0.476-7.1717-1.072-9.9838-1.787-2.8296-0.72-5.0029-1.542-6.4004-2.437z"/>
 </g><mask id="path-5-inside-1_4471_52357" fill="#ffffff">
  <path d="m172.8 99c0 39.765-32.236 72-72 72-39.765 0-72-32.235-72-72 0-39.764 32.236-72 72-72 39.764 0 72 32.236 72 72zm-132.78 0c0 33.57 27.214 60.784 60.784 60.784 33.57 0 60.784-27.214 60.784-60.784 0-33.57-27.214-60.784-60.784-60.784-33.57 0-60.784 27.214-60.784 60.784z"/>
 </mask><path d="m172.8 99c0 39.765-32.236 72-72 72-39.765 0-72-32.235-72-72 0-39.764 32.236-72 72-72 39.764 0 72 32.236 72 72zm-132.78 0c0 33.57 27.214 60.784 60.784 60.784 33.57 0 60.784-27.214 60.784-60.784 0-33.57-27.214-60.784-60.784-60.784-33.57 0-60.784 27.214-60.784 60.784z" fill="#dedede" mask="url(#path-5-inside-1_4471_52357)" stroke="#1a1a1a" stroke-width="4"/><path d="m67.264 0s-67.264 0-67.264 67v328.36c0 2.6512 1.7909 4.6404 4 4.6404h192c2.209 0 4-1.9892 4-4.6404v-328.36c0-67-66.07-67-66.07-67h-33.929zm67.07 81.2c-1.2887 0-2.3333 1.2536-2.3333 2.8v300.4c0 1.5464 1.0447 2.8 2.3333 2.8h14.666c1.2887 0 2.3333-1.2536 2.3333-2.8v-300.4c0-1.5464-1.0447-2.8-2.3333-2.8z" fill="#000" fill-opacity="0" tb:tag="clickArea"/><g fill="#d12730" style="display: none;" tb:tag="critical">
  <rect width="84" height="84" rx="4" fill="#fff" style=""/>
  <rect width="84" height="84" rx="4" style=""/>
  <rect x="2" y="2" width="80" height="80" rx="2" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 27.562-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g><g fill="#faa405" style="display: none;" tb:tag="warning">
  <path d="m38.422 7.1554c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" fill="#fff" style=""/>
  <path d="m38.422 7.1554c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" style=""/>
  <path d="m40.211 8.0498c0.7371-1.4741 2.8407-1.4741 3.5778-1e-5l35.528 71.056c0.6649 1.3298-0.3021 2.8944-1.7888 2.8944h-71.056c-1.4868 0-2.4538-1.5646-1.7889-2.8944z" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 37.562-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g>
</svg>