<?xml version="1.0" encoding="utf-8"?>
<!--BSD 3-Clause License

Copyright (c) 2019, Cloudminds
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>EC<PERSON>L, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON>
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>Robot Service Info</Name>
    <Description1><![CDATA[This LWM2M Object includes robot service related information.]]></Description1>
    <ObjectID>10331</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10331</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Single</MultipleInstances>
    <Mandatory>Mandatory</Mandatory>
    <Resources>
      <Item ID="1">
        <Name>Current status</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>0..127</RangeEnumeration>
        <Units/>
        <Description><![CDATA[Current status, for example: working, pasued, dormant, holding, charging and so on.]]></Description>
      </Item>
      <Item ID="2">
        <Name>Services Providing</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>0..127</RangeEnumeration>
        <Units/>
        <Description><![CDATA[Providing services: for example: patrolling, advertisement playing, patrolling and so on,
			one robot can do several works at the same time, for example, patrolling with advertisement playing.]]></Description>
      </Item>
	  <Item ID="3">
		<Name>Advertising Contents</Name>
		<Operations>R</Operations>
		<MultipleInstances>Single</MultipleInstances>
		<Mandatory>Mandatory</Mandatory>
		<Type>String</Type>
		<RangeEnumeration/>
		<Units/>
		<Description><![CDATA[The Advertising Contents it is playing.]]></Description>
	  </Item>
      <Item ID="4">
        <Name>Current Language</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>0..127</RangeEnumeration>
        <Units/>
        <Description><![CDATA[Current language used by the robot.]]></Description>
      </Item>
      <Item ID="5">
        <Name>Volume</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>0..100</RangeEnumeration>
        <Units>/100</Units>
        <Description><![CDATA[The Volume of the speaker.]]></Description>
      </Item>	  
      <Item ID="6">
        <Name>Moving Status</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>0..2</RangeEnumeration>
        <Units/>
        <Description><![CDATA[0:Moving, 1:Stopped, 2:Suspended.]]></Description>
      </Item>
      <Item ID="7">
        <Name>Moving Speed</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Float</Type>
        <RangeEnumeration/>
        <Units>m/h</Units>
        <Description><![CDATA[The moving speed of the robot.]]></Description>
      </Item>
	  <Item ID="8">
		<Name>Location</Name>
		<Operations>R</Operations>
		<MultipleInstances>Single</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Type>Objlnk</Type>
		<RangeEnumeration/>
		<Units/>
		<Description>
		  <![CDATA[Contains the reference to the location object(6).]]>
		</Description>
	  </Item>
	  <Item ID="9">
        <Name>Map List</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[Assigned Map List and the Map currently within.]]></Description>
      </Item>
	  <Item ID="10">
        <Name>Planned Route list</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[Planned Route list, routes are separated by comma.]]></Description>
      </Item>
	  <Item ID="11">
        <Name>Current Route</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[Current Route.]]></Description>
      </Item>
	  <Item ID="12">
        <Name>Route to-do List</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[Routes to be done, routes are separated by comma.]]></Description>
      </Item>
	  <Item ID="13">
		<Name>Synchronous Whistle</Name>
		<Operations>R</Operations>
		<MultipleInstances>Single</MultipleInstances>
		<Mandatory>Mandatory</Mandatory>
		<Type>Boolean</Type>
		<RangeEnumeration/>
		<Units/>
		<Description><![CDATA[The status of the Synchronous Whistle, 1:On, 0:Off.]]></Description>
	  </Item>
	  <Item ID="14">
        <Name>Current Actions</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[Current Body Actions/Actions.]]></Description>
      </Item>
      <Item ID="15">
        <Name>ASR Type</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>0..2</RangeEnumeration>
        <Units/>
        <Description><![CDATA[ASR Type, 0:Cloud, 1:Local, 2:Offline.]]></Description>
      </Item>
	  
	  <Item ID="50">
        <Name>TTS Vendor</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[The TTS Vendor.]]></Description>
      </Item>
	  <Item ID="51">
        <Name>TTS Speaker</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[Which speaker of the vender is selected.]]></Description>
      </Item>
	</Resources>
    <Description2 />
  </Object>
</LWM2M>
