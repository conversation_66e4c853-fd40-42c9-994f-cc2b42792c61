<?xml version="1.0" encoding="UTF-8"?>

<!-- BSD-3 Clause License

Copyright 2019 Zumtobel Group. 

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, <PERSON><PERSON>IAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON><PERSON>NTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M-v1_1.xsd">
  <Object ObjectType="MODefinition">
		<Name>oA Status Report Structure</Name>
		<Description1><![CDATA[The 'oA Status_Report_Structure' describes the content of the status reports. Its ID is always the first byte, the following bytes are defined by Keys.]]></Description1>
		<ObjectID>3406</ObjectID>
		<ObjectURN>urn:oma:lwm2m:ext:3406</ObjectURN>
		<LWM2MVersion>1.1</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="4001">
				<Name>ObjectVersion</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[LWM2M Object versioning label.]]></Description>
			</Item>
			<Item ID="901">
				<Name>Documentary Description</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..256 bytes</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Resource to hold a documentary text description of the object.]]></Description>
			</Item>
			<Item ID="850">
				<Name>Status Report Structure ID</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Unsigned Integer</Type>
				<RangeEnumeration>0..255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[The ID of the status report structure. It is always the first byte of the transmitted status report.\nThis needs to be unique per receiving object. ID 255 is used to receive the 'go bootstrap' message.]]></Description>
			</Item>
			<Item ID="851">
				<Name>Keys</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Defines the list of keys that are part of the status report. Most keys are present in the form 'object ID/resource ID'.\nThere is a simplified form that is used with status reports for objects. This reporting has implicit instance identification (it's the instance that creates reports, so no explicit instance identification is needed). This is using straight forward a list of resource contents that are determined by the list elements in their order.\n\t\t\t\t\nExample of a simple form:\n- The key list for reporting ID 1 is: 4001/101; 4021/5850; 4021/100; 4021/102;\n- The values are: 4001/101:0.5; 4021/5850:false; 4021/100:0.3; 4021/102:7\n- The coded stream will be: 1; 0.5; false; 0.3; 7 with the first byte 1 the reporting ID.\n\t\t\t\t\nThe explicit form, that is used with device status reports, needs to explicitly name the instances the reported values are from. To allow for that two special keys are used:\n- *NoOfInstances:* This key is to allow several object instances to report their status in the same report, e.g. a Device reports the status of all its light point objects.\nThe block of keys following NoOfInstances, always starting with InstanceID, until the next NoOfInstances key is present in the status message for every instance, e.g. NoOfInstances=2 means the following block of keys is present twice.\n- *InstanceID:* This key initiates the report of an object, is the first key before all the resource values. It defines the object instance that reports its status.\n\t\t\t\t\nExample of an explicit form:\n- The key list for reporting ID 2 is: NoOfInstances; InstanceId; 4001/101; NoOfInstances; InstanceId; 4021/100\n- The coded stream will be 2;2;0;0.5;1;0.7;1;0;0.5 with the first byte 2 the ID of the structure definition, the second byte the number of instances (2), the third the instance ID of the first (0) instance, the reported value, the instance ID of the second (1) instance, the reported value, the number of instances (1) for the next object, the ID of the instance (0) and the value.]]></Description>
			</Item>
		</Resources>
		<Description2><![CDATA[]]></Description2>
	</Object>
</LWM2M>
