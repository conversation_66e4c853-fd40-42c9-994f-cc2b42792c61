<?xml version="1.0" encoding="utf-8" ?>

<!-- 
FILE INFORMATION
Public Reachable Information
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 502.xml
NORMATIVE INFORMATION
  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues
LEGAL DISCLAIMER
Copyright 2022 Open Mobile Alliance. 
Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
The above license is used as a license under copyright only.  Please
reference the OMA IPR Policy for patent licensing terms:
https://www.omaspecworks.org/about/intellectual-property-rights/
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
    <Object ObjectType="MODefinition">
    	<Name>CO Detector</Name>
  	<Description1><![CDATA[This is an alarm that should be raised if the sensor detects a higher than threshold CO level.]]></Description1>
        <ObjectID>502</ObjectID>
        <ObjectURN>urn:oma:lwm2m:oma:502</ObjectURN>
        <LWM2MVersion>1.0</LWM2MVersion>
        <ObjectVersion>1.0</ObjectVersion>
        <MultipleInstances>Multiple</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Resources>
          <Item ID="0">
            <Name>CO Sensor Location Tag</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>String</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Verbose identification of the location of the CO sensor.]]></Description>
          </Item>
          <Item ID="1">
            <Name>CO Detected</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Mandatory</Mandatory>
            <Type>Boolean</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[0 = false (CO NOT detected), 1 = true (CO Detected)]]></Description>
          </Item>
          <Item ID="2">
            <Name>Ambient CO Value</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0..12800</RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[CO value (in ppm) received for the area.]]></Description>
          </Item>
          <Item ID="3">
            <Name>CO Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Mandatory</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0..12800</RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[Last or Current Measured CO Value (in ppm) from the Sensor.]]></Description>
          </Item>
          <Item ID="4">
            <Name>Min CO Range Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0..12800</RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[The minimum value that can be measured by the sensor (in ppm).]]></Description>
          </Item>
          <Item ID="5">
            <Name>Max CO Range Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0..12800</RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[The maximum value that can be measured by the sensor.]]></Description>
          </Item>
          <Item ID="6">
            <Name>CO Detection Accuracy</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0..12800</RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[Indicate range of the accuracy for the temperature Sensor.]]></Description>
          </Item>
          <Item ID="7">
            <Name>Minimum Measured CO Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0..12800</RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[The minimum CO value (in ppm) measured by the sensor since power ON or reset.]]></Description>
          </Item>
          <Item ID="8">
            <Name>Maximum Measured CO Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0..12800</RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[The maximum CO value measured (in ppm) by the sensor since power ON or reset]]></Description>
          </Item>
          <Item ID="9">
            <Name>Upper CO Threshold</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0..12800</RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[Indicate the Upper threshold for the CO Value (in ppm).]]></Description>
          </Item>
          <Item ID="10">
            <Name>Reset</Name>
            <Operations>E</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type></Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Reset the "CO" object related parameters to default]]></Description>
          </Item>
          <Item ID="6048">
            <Name>Alarm loudness</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units>dB</Units>
            <Description><![CDATA[Indicate the loudness of the alarm.]]></Description>
          </Item>
          <Item ID="5514">
            <Name>Latitude</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>String</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[The decimal notation of latitude, e.g. -43.5723 (World Geodetic System 1984).]]></Description>
          </Item>
          <Item ID="5515">
            <Name>Longitude</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>String</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[The decimal notation of longitude, e.g. 153.21760 (World Geodetic System 1984).]]></Description>
          </Item>
          <Item ID="6039">
            <Name>Altitude</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units>m</Units>
            <Description><![CDATA[Altitude above sea level in meters.]]></Description>
          </Item>
          <Item ID="6044">
            <Name>Battery Percentage</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0.00..100.00</RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Current remaining battery level.]]></Description>
          </Item>
        </Resources>
	<Description2><![CDATA[]]></Description2>
    </Object>
</LWM2M>
