<?xml version="1.0" encoding="utf-8"?>
<!--BSD 3-Clause License

Copyright (c) 2019, Cloudminds
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>EC<PERSON>L, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON>
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>Robot Alarm</Name>
    <Description1><![CDATA[Here lists all items of an alarm reported by the robot.]]></Description1>
    <ObjectID>10334</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10334</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Multiple</MultipleInstances>
    <Mandatory>Optional</Mandatory>
    <Resources>
		<Item ID="1">
			<Name>Entity</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>String</Type>
			<RangeEnumeration>4..63</RangeEnumeration>
			<Units/>
			<Description><![CDATA[Contains the object ID and object instance ID/name, for example: </10320/2>.]]></Description>
		</Item>
		<Item ID="2">
			<Name>Probable Cause</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>Integer</Type>
			<RangeEnumeration>0..65535</RangeEnumeration>
			<Units></Units>
			<Description><![CDATA[Probable cause of this alarm.]]></Description>
		</Item>
		<Item ID="3">
			<Name>Specific Problem</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>String</Type>
			<RangeEnumeration/>
			<Units></Units>
			<Description><![CDATA[Specific Problem of of this alarm.]]></Description>
		</Item>
		<Item ID="4">
			<Name>Alarm Type</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>Integer</Type>
			<RangeEnumeration>2..6</RangeEnumeration>
			<Units></Units>
			<Description><![CDATA[The Alarm Type of this alarm,
				2:Communications, 3:Quality of Service (QoS), 4:Processing Error, 
				5:Equipment, 6:Environmental.]]></Description>
		</Item>
		<Item ID="5">
			<Name>Severity</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>Integer</Type>
			<RangeEnumeration>1..5</RangeEnumeration>
			<Units></Units>
			<Description><![CDATA[The severity of this alarm. Critical(1), Major(2), Minor(3), Warning(4), Cleared(5).]]></Description>
		</Item>
		<Item ID="6">
			<Name>Report Time</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>Time</Type>
			<RangeEnumeration></RangeEnumeration>
			<Units></Units>
			<Description><![CDATA[When was this alarm reported.]]></Description>
		</Item>
		<Item ID="7">
			<Name>Sequence No</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>Integer</Type>
			<RangeEnumeration>0..2^63-1</RangeEnumeration>
			<Units></Units>
			<Description><![CDATA[Sequence No of this alarm, used to detect alarm loss.]]></Description>
		</Item>
		<Item ID="8">
			<Name>Additional Info</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>String</Type>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[The information related to this alarm.]]></Description>
		</Item>
	</Resources>
    <Description2/>
  </Object>
</LWM2M>
