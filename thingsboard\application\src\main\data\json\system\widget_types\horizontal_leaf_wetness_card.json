{"fqn": "horizontal_leaf_wetness_card", "name": "Horizontal leaf wetness card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_leaf_wetness_card_system_widget_image.png", "description": "Displays the latest leaf wetness telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'leaf', label: 'Leaf wetness', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Leaf wetness\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:leaf\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#4B70DD\"},{\"from\":10,\"to\":50,\"color\":\"#FFA600\"},{\"from\":50,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#4B70DD\"},{\"from\":10,\"to\":50,\"color\":\"#FFA600\"},{\"from\":50,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal humidity card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"%\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "dew", "leaf moisture", "foliage dampness", "leaf humidity", "foliar moisture"], "resources": [{"link": "/api/images/system/horizontal_leaf_wetness_card_system_widget_image.png", "title": "\"Horizontal leaf wetness card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_leaf_wetness_card_system_widget_image.png", "publicResourceKey": "zQA3LjyUZkCaKTk9P3T3t8b0bA5qldvy", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAk1BMVEUAAADf39/f39/g4ODg4ODf39/////k5ORLcN3g4ODy8vLIyMhYWFjOzs5hguHo7fv5+fm7yfK7u7v09v2Opurb29utra3CwsK2trZmZmbV1dWlt+7n5+c9PT2QkJB0dHTd5PmaruzS2/d4lOWwwPBWed+Dned4k+WCgoJKSkrt7e1ti+MsLCzH0vSenp6dnZ2DnOgFpIuiAAAABnRSTlMAIEDfv1C6kOEmAAADEklEQVR42u3aaW+iQBzHcde2f8Y5GOSUm3rbc9//q9sB7Wq3DfqAbUbz+6QYHEIyXwZsEzsCAAAAAAAAAAAAAAAAAAAAAAAAAAAA+Em/rl6XMb5zrt3k7sF0OB5dPe6MR/ecboB3P5rQTZggxDIIsQ1CbIMQ2yDENheFxA4d+buMTjhkh4tCpDAvnJvNIS7jbp+3Ce1bwc2o2eXHbWDNtBNQJ1jm+tKQZvkYGvn8JETI9M3Z1VK8pET+Vki+c3ap9F/qeJeWtE2lU9WleKtN6MBy1tnPRrurDZtfFDJ12UF4EpJu30oeV2X3hsut9LdxGpeOdOKKSv4S129V7fP31Oc0sDXLp4bez45pWvwmcl/PhYSMfRtSCRHXzj6EpIxL8Z4KQR8hQjiZSONM1BUNbMH06fIQhRvK3eBMyCP7JmQnt5msK1Gmh5Bqy1+Il2nN07oNoUqmWWXutHcpfRqYy+g461c2D9xQsyn1h8zZ15ADTl/w4zD/2DgNja2eGVvo45V2dRjSmZA16ywOITYIGFvnLvt7LzUNTVf6XMiKGavD8/5IVgga8/LMlvRBu0u9zJveEGa4et9hTrVHfnJZQzPD5zWbn1uRJmd7c7JBM20+h2imlyyg57AvZMFY/rEgbs9jbgaf/h3g9F9M2aK7taZ04D5S7pqpbvo/tVxzZud45qyLMT9PEXU8j7yJagcm5BFRERfkRdH+gBkYNClwzcO+OF7WZhPQK3vVpqf398jSrEpnQychWaYiIZTZSyiJEt+bJSrLhPKflO8RqYL7fkRcdQeU8GlAet3ORlPn9EO4N4QCYp0w+BSiZlyoQu1Diiwy+2asiFQiupBJFJkQMSsEN7kJpyEFOvg6dPZvrWC9YqvNnI5iFSVqlomEtyui/CiZ8ZlQZuzJL4SiNsRrV6Tw4/ZAliT0w05DLhcVPQczEdElbAjxvL6DnC5hRYiNEGIbhNgGIbZBiG0mo/sb+JqdiN+Pxs4NfNHuOePR6OFu4ly7u/Et/VMNAAAAAAAAAAAAAAAAAAAAAAAAAAAAwA/5A5Ee9c9zue74AAAAAElFTkSuQmCC", "public": true}]}