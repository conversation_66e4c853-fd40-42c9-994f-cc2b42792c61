<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="200" height="200" fill="none" version="1.1" viewBox="0 0 200 200"><tb:metadata xmlns=""><![CDATA[{
  "title": "Vertical broken pipe",
  "description": "Vertical broken pipe.",
  "searchTags": [
    "pipe",
    "vertical pipe",
    "broken pipe"
  ],
  "widgetSizeX": 1,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "pipe-background",
      "stateRenderFunction": "var color = ctx.properties.pipeColor;\nelement.attr({fill: color});",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    }
  ],
  "properties": [
    {
      "id": "pipeColor",
      "name": "{i18n:scada.symbol.pipe-color}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata>
<g tb:tag="clickArea">
  <path d="m63.5 186.5s0.2293-53.5 0-60.5-6.5-17-6.5-17l21.5 14.5 6.5-14.5 9 10.5 11-14.5 6.5 18.5 6-10 11.5 2.5 14-7s-7.5 10-7.5 17v60.5h-72z" fill="#fff" tb:tag="pipe-background"/>
  <path d="m63.5 186.5s0.2293-53.5 0-60.5-6.5-17-6.5-17l21.5 14.5 6.5-14.5 9 10.5 11-14.5 6.5 18.5 6-10 11.5 2.5 14-7s-7.5 10-7.5 17v60.5h-72z" fill="url(#paint0_linear_2474_252378)"/>
  <path d="m61.52 114.23c-0.0889-0.188-0.1777-0.374-0.2661-0.556l16.407 11.066 1.4782 0.997 0.7293-1.627 5.5321-12.341 7.4602 8.703 1.2101 1.412 1.1238-1.481 9.314-12.278 5.576 15.868 1.058 3.013 1.643-2.738 5.447-9.078 10.448 2.272 0.517 0.112 0.473-0.236 9.023-4.512c-0.249 0.421-0.502 0.861-0.755 1.317-1.883 3.389-3.939 7.935-3.939 11.853v59h-68.994l0.0033-0.807c0.0058-1.476 0.0139-3.594 0.0228-6.157 0.0179-5.125 0.0394-12.032 0.0538-19.158 0.0286-14.23 0.0289-29.393-0.0868-32.927-0.1255-3.83-1.8722-8.32-3.4792-11.717z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
  <path d="m58 94.5s7.395-13.5 7.5-18.5 0-62 0-62h72s-0.086 54.5 0 62 4.5 18.5 4.5 18.5l-13.5-6-14 15-5-16.5-6 7.5-13-6-13 18-4.5-19.5-15 7.5z" fill="#fff" tb:tag="pipe-background"/>
  <path d="m58 94.5s7.395-13.5 7.5-18.5 0-62 0-62h72s-0.086 54.5 0 62 4.5 18.5 4.5 18.5l-13.5-6-14 15-5-16.5-6 7.5-13-6-13 18-4.5-19.5-15 7.5z" fill="url(#paint1_linear_2474_252378)"/>
  <path d="m67.003 15.5h68.995l-2e-3 0.8543c-2e-3 1.5049-5e-3 3.6632-8e-3 6.2766-7e-3 5.2267-0.015 12.274-0.02 19.555-0.011 14.556-0.011 30.069 0.032 33.831 0.046 3.9731 1.225 8.7675 2.346 12.479 0.357 1.1792 0.712 2.2636 1.031 3.1964l-10.268-4.5635-0.976-0.4341-0.73 0.7813-12.223 13.096-4.244-14.008-0.827-2.7275-1.78 2.2255-5.267 6.5829-13.055-6.0254-0.7231 1.0013-11.065 15.321-3.757-16.28-0.4284-1.8563-12.574 6.2868c0.5039-1.009 1.0566-2.1442 1.611-3.3352 0.9404-2.0204 1.8953-4.222 2.6248-6.2504 0.7123-1.9804 1.2726-3.9616 1.3044-5.4761 0.0529-2.5216 0.0528-18.048 0.0397-32.908-0.0066-7.4379-0.0164-14.72-0.0246-20.142-0.0041-2.711-0.0078-4.9572-0.0105-6.5257l-0.0017-0.9564z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
  <rect transform="rotate(-90 51.5 198.5)" x="51.5" y="198.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/>
  <rect transform="rotate(-90 51.5 12.5)" x="51.5" y="12.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/>
 </g><defs>
  <linearGradient id="paint0_linear_2474_252378" x1="63.5" x2="135.5" y1="167.52" y2="167.97" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint1_linear_2474_252378" x1="65.5" x2="137.5" y1="70.24" y2="70.67" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
 </defs>
</svg>