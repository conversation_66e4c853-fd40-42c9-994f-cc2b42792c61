{"fqn": "maps_v2.google_maps", "name": "Google Map", "deprecated": true, "image": "tb-image;/api/images/system/google_map_system_widget_image.png", "description": "Displays the location of the entities on Google Maps. Requires the Google map key to work properly. Highly customizable via custom markers, marker tooltips, and widget actions.", "descriptor": {"type": "latest", "sizeX": 8.5, "sizeY": 6, "resources": [], "templateHtml": "", "templateCss": ".error {\n    color: red;\n}\n.tb-labels {\n  color: #222;\n  font: 12px/1.5 \"Helvetica Neue\", Arial, Helvetica, sans-serif;\n  text-align: center;\n  width: 200px;\n  white-space: nowrap;\n}", "controllerScript": "self.onInit = function() {\n    self.ctx.map = new TbMapWidgetV2('google-map', false, self.ctx);\n}\n\nself.onDataUpdated = function() {\n    self.ctx.map.update();\n}\n\nself.onResize = function() {\n    self.ctx.map.resize();\n}\n\nself.actionSources = function() {\n    return TbMapWidgetV2.actionSources();\n}\n\nself.onDestroy = function() {\n    self.ctx.map.destroy();\n}\n\nself.typeParameters = function() {\n    return {\n        hasDataPageLink: true\n    };\n}", "settingsSchema": "", "dataKeySettingsSchema": "", "settingsDirective": "tb-map-widget-settings-legacy", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"First point\",\"entityAliasId\":null,\"filterId\":null,\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"latitude\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.05427416942713381,\"funcBody\":\"var value = prevValue || 15.833293;\\nif (time % 5000 < 500) {\\n    value += Math.random() * 0.05 - 0.025;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"longitude\",\"color\":\"#4caf50\",\"settings\":{},\"_hash\":0.680594833308841,\"funcBody\":\"var value = prevValue || -90.454350;\\nif (time % 5000 < 500) {\\n    value += Math.random() * 0.05 - 0.025;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"temperature\",\"color\":\"#9c27b0\",\"settings\":{},\"_hash\":0.9430343126300238,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Type\",\"color\":\"#8bc34a\",\"settings\":{},\"_hash\":0.1784452363910778,\"funcBody\":\"return \\\"colorpin\\\";\",\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]},{\"type\":\"function\",\"name\":\"Second point\",\"entityAliasId\":null,\"filterId\":null,\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"latitude\",\"color\":\"#f44336\",\"settings\":{},\"_hash\":0.05012157428742059,\"funcBody\":\"var value = prevValue || 14.450463;\\nif (time % 4000 < 500) {\\n    value += Math.random() * 0.05 - 0.025;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"longitude\",\"color\":\"#ffc107\",\"settings\":{},\"_hash\":0.6742359401617628,\"funcBody\":\"var value = prevValue || -84.845334;\\nif (time % 4000 < 500) {\\n    value += Math.random() * 0.05 - 0.025;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"temperature\",\"color\":\"#8bc34a\",\"settings\":{},\"_hash\":0.773875863339494,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Type\",\"color\":\"#3f51b5\",\"settings\":{},\"_hash\":0.405822538899673,\"funcBody\":\"return \\\"thermometer\\\";\",\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"provider\":\"google-map\",\"gmApiKey\":\"AIzaSyDoEx2kaGz3PxwbI9T7ccTSg5xjdw8Nw8Q\",\"gmDefaultMapType\":\"roadmap\",\"latKeyName\":\"latitude\",\"lngKeyName\":\"longitude\",\"xPosKeyName\":\"xPos\",\"yPosKeyName\":\"yPos\",\"defaultCenterPosition\":\"0,0\",\"disableScrollZooming\":false,\"disableDoubleClickZooming\":false,\"disableZoomControl\":false,\"fitMapBounds\":true,\"useDefaultCenterPosition\":false,\"mapPageSize\":16384,\"markerOffsetX\":0.5,\"markerOffsetY\":1,\"posFunction\":\"return {x: origXPos, y: origYPos};\",\"draggableMarker\":false,\"showLabel\":true,\"useLabelFunction\":false,\"label\":\"${entityName}\",\"showTooltip\":true,\"showTooltipAction\":\"click\",\"autocloseTooltip\":true,\"useTooltipFunction\":false,\"tooltipPattern\":\"<b>${entityName}</b><br/><br/><b>Latitude:</b> ${latitude:7}<br/><b>Longitude:</b> ${longitude:7}<br/><b>Temperature:</b> ${temperature} °C<br/><small>See advanced settings for details</small>\",\"tooltipOffsetX\":0,\"tooltipOffsetY\":-1,\"color\":\"#fe7568\",\"useColorFunction\":true,\"colorFunction\":\"var type = dsData[dsIndex]['Type'];\\nif (type == 'colorpin') {\\n\\tvar temperature = dsData[dsIndex]['temperature'];\\n\\tif (typeof temperature !== undefined) {\\n\\t    var percent = (temperature + 60)/120 * 100;\\n\\t    return tinycolor.mix('blue', 'red', percent).toHexString();\\n\\t}\\n\\treturn 'blue';\\n}\\n\",\"useMarkerImageFunction\":true,\"markerImageSize\":34,\"markerImageFunction\":\"var type = dsData[dsIndex]['Type'];\\nif (type == 'thermometer') {\\n\\tvar res = {\\n\\t    url: images[0],\\n\\t    size: 40\\n\\t}\\n\\tvar temperature = dsData[dsIndex]['temperature'];\\n\\tif (typeof temperature !== undefined) {\\n\\t    var percent = (temperature + 60)/120;\\n\\t    var index = Math.min(3, Math.floor(4 * percent));\\n\\t    res.url = images[index];\\n\\t}\\n\\treturn res;\\n}\",\"markerImages\":[\"tb-image;/api/images/system/map_marker_image_0.png\",\"tb-image;/api/images/system/map_marker_image_1.png\",\"tb-image;/api/images/system/map_marker_image_2.png\",\"tb-image;/api/images/system/map_marker_image_3.png\"],\"showPolygon\":false,\"polygonKeyName\":\"perimeter\",\"editablePolygon\":false,\"showPolygonLabel\":false,\"usePolygonLabelFunction\":false,\"polygonLabel\":\"${entityName}\",\"showPolygonTooltip\":false,\"showPolygonTooltipAction\":\"click\",\"autoClosePolygonTooltip\":true,\"usePolygonTooltipFunction\":false,\"polygonTooltipPattern\":\"<b>${entityName}</b><br/><br/><b>TimeStamp:</b> ${ts:7}\",\"polygonColor\":\"#3388ff\",\"polygonOpacity\":0.2,\"usePolygonColorFunction\":false,\"polygonStrokeColor\":\"#3388ff\",\"polygonStrokeOpacity\":1,\"polygonStrokeWeight\":3,\"usePolygonStrokeColorFunction\":false,\"showCircle\":false,\"circleKeyName\":\"perimeter\",\"editableCircle\":false,\"showCircleLabel\":false,\"useCircleLabelFunction\":false,\"circleLabel\":\"${entityName}\",\"showCircleTooltip\":false,\"showCircleTooltipAction\":\"click\",\"autoCloseCircleTooltip\":true,\"useCircleTooltipFunction\":false,\"circleTooltipPattern\":\"<b>${entityName}</b><br/><br/><b>TimeStamp:</b> ${ts:7}\",\"circleFillColor\":\"#3388ff\",\"circleFillColorOpacity\":0.2,\"useCircleFillColorFunction\":false,\"circleStrokeColor\":\"#3388ff\",\"circleStrokeOpacity\":1,\"circleStrokeWeight\":3,\"useCircleStrokeColorFunction\":false,\"useClusterMarkers\":false,\"zoomOnClick\":true,\"maxClusterRadius\":80,\"animate\":true,\"spiderfyOnMaxZoom\":false,\"showCoverageOnHover\":true,\"chunkedLoading\":false,\"removeOutsideVisibleBounds\":true,\"useIconCreateFunction\":false},\"title\":\"Google Map\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{}}"}, "tags": ["mapping", "gps", "navigation", "geolocation", "satellite", "directions"], "resources": [{"link": "/api/images/system/google_map_system_widget_image.png", "title": "\"Google Map\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "google_map_system_widget_image.png", "publicResourceKey": "phkffKRySNmqJA5QTLbqtJ1sHqDsbMgD", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/map_marker_image_0.png", "title": "Map marker image 0", "type": "IMAGE", "subType": "IMAGE", "fileName": "map_marker_image_0.png", "publicResourceKey": "CdCrVxsjA4EAiFaXK4a7K2MZFMeEuGeD", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/map_marker_image_1.png", "title": "Map marker image 1", "type": "IMAGE", "subType": "IMAGE", "fileName": "map_marker_image_1.png", "publicResourceKey": "DF3fuPXua9Vi3o3d9Nz2I1LXDTwEs2Tv", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/map_marker_image_2.png", "title": "Map marker image 2", "type": "IMAGE", "subType": "IMAGE", "fileName": "map_marker_image_2.png", "publicResourceKey": "rz5SFAw2Sg5T2EyXNdwLycoDwf4QbMiZ", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/map_marker_image_3.png", "title": "Map marker image 3", "type": "IMAGE", "subType": "IMAGE", "fileName": "map_marker_image_3.png", "publicResourceKey": "KfPfTuvKCeAnmTcKcrvZQHfdU0TPArWY", "mediaType": "image/png", "data": "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", "public": true}]}