{"fqn": "indoor_humidity_chart_card", "name": "Indoor humidity chart card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_humidity_chart_card_system_widget_image.png", "description": "Displays a indoor humidity data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'humidity', label: 'Humidity', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: '%', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'humidity', '%', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Humidity\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":30,\"color\":\"#FFA600\"},{\"from\":30,\"to\":60,\"color\":\"#3FA71A\"},{\"from\":60,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Humidity\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "humidity", "indoor", "moisture", "dampness", "wetness", "humidness", "moistness", "dew", "water vapor", "condensation", "dew point", "steaminess"], "resources": [{"link": "/api/images/system/indoor_humidity_chart_card_system_widget_image.png", "title": "\"Indoor humidity chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_humidity_chart_card_system_widget_image.png", "publicResourceKey": "glwOpjMHagY5gDqHRXhEkBJVj8RJqn2O", "mediaType": "image/png", "data": "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", "public": true}]}