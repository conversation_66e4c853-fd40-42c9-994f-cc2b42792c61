{"fqn": "indoor_horizontal_co2_card", "name": "Indoor horizontal CO2 card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_horizontal_co2_card_system_widget_image.png", "description": "Displays the latest indoor CO2 level telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'co2', label: 'CO2 level', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"CO2 level\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 160 - 80;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 400) {\\n\\tvalue = 400;\\n} else if (value > 1600) {\\n\\tvalue = 1600;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"co2\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":600,\"color\":\"#3FA71A\"},{\"from\":600,\"to\":800,\"color\":\"#F36900\"},{\"from\":800,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":600,\"color\":\"#3FA71A\"},{\"from\":600,\"to\":800,\"color\":\"#F36900\"},{\"from\":800,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal CO2 card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"ppm\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["environment", "co2", "indoor", "carbon dioxide", "carbon emission", "carbon footprint", "carbon output", "carbon pollution", "carbon capture", "carbon offset", "carbon reduction", "carbon neutral", "climate gas", "fossil fuel emission", "carbon cycle"], "resources": [{"link": "/api/images/system/indoor_horizontal_co2_card_system_widget_image.png", "title": "\"Indoor horizontal CO2 card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_horizontal_co2_card_system_widget_image.png", "publicResourceKey": "rpAVSdJz1Ll9WnkrEk3Yo3EhAgjqm2VQ", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAmVBMVEUAAADf39/f39/g4ODg4ODf39/////k5OQ/pxrg4OC33qnn9ONXsjbz8/OHyHBvvVPP6cbz+vHHx8fOzs6f04zV1dX5+flYWFhLrCjb29u2trbCwsK8vLzn5+fIyMiQkJCTzn5Xsjd0dHTD47jb79Sr2Jutra17wmHn9OKenp6CgoJmZmY5OTnD5Lhjt0Xt7e1KSkqTzn9juEU1uySMAAAABnRSTlMAIEDfv1C6kOEmAAADkElEQVR42u3aa2/aMBiGYdZ2b0wSx3HOISGcz7Rl+/8/bq+dCE3rhAobG6Dn+mCMjSbfDRlCbQ8AAAAAAAAAAAAAAAAAAAAAAAAAAADgX/py92zG07Nz7/rPX7nDyejuSeep9yLpAWQvvT49hD5CbgxCbg1Cbg1Cbg1Cbs3nQjajlCgaLc1Qm+cp/STd0v/3qZDtNjpEy120W0a7dC+JphFJSQ6RYx6jnC41cz0e566leRouktfrhRwkpU4eUbTjY7chdZ5vtoNo6hx2m8tDXCFcfgiExU2JYBN9rZA9sTylNCcajciE5KPNIZpuB9Ptcn9xyNpvQ5RYtFckFJNwvhLja4XkEY2i7YaWU9NhQ3ajKKJ9TlN+vDhkLHwb8iaoFZinWhRkaU/r11fPzki7ITGvm+mQN06FhC6/TPOP5+eXpXmeS2efH5xon+e1CUnz3YZGU97ilAtD1uKbsiF8ck1ki8xpfeGR4YpVIfgamcCV4Jln9gIzm/s8Lk6FiDdV0KxQfkI/k8fhSB4fL+VPPNVdAj7wymsTzBh2ISKZBaLQxEPivgll9iauyxXfzMbJEI88chUlCV1bwhE2ZC2KYMwn/BiyIjIv4ZAF9xZi3u4tTBIVwvtciBIioOtZm3/dhpBe22OGH0ICeyON23uHVjzaPdeG+KdD1PeC3EJNEtLeQtH1+CJx+e0SvB5v9OS3IQmPbUhwTsjM3uw8ejT21YKuRovOhELXsz958yaY2feMPobY9S7E7J4O+T0VuOGJ+1m+/7og6Ryh8SbGc1qIVXcdEvMRMuc2y+3WZ/Zq0VoI76KQsVJjsgZtjKT3kqwso6wfm4U+ZUQ0XA4pK8t2gxc+ndT+r+WLIFHm+Lrgmc9rXUi3ziF2PaCLQoxjSJrGZdPEPKuoKqs6G1RxmjZx/R7XGVE8lHVdkoztRtzU54TQWgkhlDmW+XwoFmTY8/LT754JGfNM6b8SMpBNPIzbkGFa8pzXhmVcNTakX5Yc0tTDRnJuJeks2tPU8o4zc4/w0+4T38w+ODdkGZdVPEibSporEtdlNZCDJua1dz54TCYkM1dkWC/NRlpV9OdsiGFDrvLFqhye2Eybksf7CMmyU5uS/pL58avJLJnjq+69QcitQcitQciteaCQlwf4NTuRfOk9OQ/wi/bMeer1vj73nXv3/PRIf1QDAAAAAAAAAAAAAAAAAAAAAAAAAAAA8I/8AKu9AITe3vWrAAAAAElFTkSuQmCC", "public": true}]}