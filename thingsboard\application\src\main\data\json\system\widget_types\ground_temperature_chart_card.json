{"fqn": "ground_temperature_chart_card", "name": "Ground temperature chart card", "deprecated": false, "image": "tb-image;/api/images/system/ground_temperature_chart_card_system_widget_image.png", "description": "Displays a ground temperature data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'temperature', label: 'Ground temperature', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: '°C', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'temperature', '°C', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Ground temperature\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#234CC7\"},{\"from\":-20,\"to\":0,\"color\":\"#305AD7\"},{\"from\":0,\"to\":10,\"color\":\"#7191EF\"},{\"from\":10,\"to\":20,\"color\":\"#FFA600\"},{\"from\":20,\"to\":30,\"color\":\"#F36900\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Ground temperature\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"device_thermostat\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "soil temperature", "terrestrial temperature", "subsurface temperature", "earth temperature", "below surface temperature", "surface temp", "soil warmth", "land temperature", "geothermal reading", "ground warmth"], "resources": [{"link": "/api/images/system/ground_temperature_chart_card_system_widget_image.png", "title": "\"Ground temperature chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "ground_temperature_chart_card_system_widget_image.png", "publicResourceKey": "JJTwL69spp7W964nxCI8oBPkq1OJOfsJ", "mediaType": "image/png", "data": "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", "public": true}]}