{"fqn": "simple_air_quality_chart_card_with_background", "name": "Simple air quality index chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_air_quality_index_chart_card_with_background_system_widget_image.png", "description": "Displays historical air quality index values as a simplified chart with background. Optionally may display the corresponding latest air quality index value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'air', label: 'Air Quality Index', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'air', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Air Quality Index\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 320) {\\n\\tvalue = 320;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 320) {\\n\\tvalue = 320;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#7CC322\"},{\"from\":50,\"to\":100,\"color\":\"#F89E0D\"},{\"from\":100,\"to\":150,\"color\":\"#F77410\"},{\"from\":150,\"to\":200,\"color\":\"#DE2343\"},{\"from\":200,\"to\":300,\"color\":\"#7B287A\"},{\"from\":300,\"to\":null,\"color\":\"#791541\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_air_quality_index_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Air Quality Index\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:weather-windy\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":1,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"AQI\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "air", "aqi", "pollution", "emission", "smog"], "resources": [{"link": "/api/images/system/simple_air_quality_index_chart_card_with_background_system_widget_background.png", "title": "\"Simple air quality index chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_air_quality_index_chart_card_with_background_system_widget_background.png", "publicResourceKey": "bDrmi8KWBlQHW80hdXvQHuOx6rUuvIbF", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_air_quality_index_chart_card_with_background_system_widget_image.png", "title": "\"Simple air quality index chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_air_quality_index_chart_card_with_background_system_widget_image.png", "publicResourceKey": "AlA8MijzSgSbb203JPDYNVMTEiANZ30P", "mediaType": "image/png", "data": "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", "public": true}]}