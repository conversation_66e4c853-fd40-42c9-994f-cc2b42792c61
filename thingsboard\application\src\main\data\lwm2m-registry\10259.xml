<?xml version="1.0" encoding="utf-8"?>

<!-- BSD-3 Clause License

Copyright 2019 Casa Systems. 

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>System Log</Name>
    <Description1><![CDATA[This object provides read access to log buffers as well as limited configuration of logging services.]]></Description1>
    <ObjectID>10259</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10259</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Multiple</MultipleInstances>
    <Mandatory>Optional</Mandatory>
    <Resources>
      <Item ID="0">
        <Name>Name</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[Short name describing the log source represented by this particular instance.
For syslog-compatible log sources this value should be "syslog". If multiple syslog-compatible log sources exist on a device then each should be given a distinct name that is then prefixed with "syslog-".]]></Description>
      </Item>
      <Item ID="1">
        <Name>Read All</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[Read the entire current contents of the log buffer.
Result is delivered as a display-formatted UTF-8 string, with each message entry on a new line.]]></Description>
      </Item>
      <Item ID="2">
        <Name>Read</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[Similar to Read All, but only reads log entries that have arrived since the last time this log buffer was read.
If the LwM2M client is connected to multiple servers then the last read position should be tracked separately for each server.
If this is the first attempt to read the log for a given connection then this resource behaves the same as Read All.]]></Description>
      </Item>
      <Item ID="3">
        <Name>Enabled</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Boolean</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[Enable or disable log capture for this source.
If this resource is not provided by an implementation, then the existence of a System Log instance is taken as implicit confirmation that the log source it represents is currently enabled.]]></Description>
      </Item>
      <Item ID="4">
        <Name>Capture Level</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[For log sources that support message priorities, this resource configures the minimum priority to capture.
For syslog-compatible log sources the following enumeration must be used: 1 = Emergency, 2 = Alert, 3 = Critical, 4 = Error, 5 = Warning, 6 = Notice, 7 = Info, 8 = Debug.
For other log sources this is left up to the implementer, with the requirement that consecutive values are used, starting at 1 and sorted so that message verbosity increases as the number ascends.]]></Description>
      </Item>
    </Resources>
    <Description2><![CDATA[]]></Description2>
  </Object>
</LWM2M>
