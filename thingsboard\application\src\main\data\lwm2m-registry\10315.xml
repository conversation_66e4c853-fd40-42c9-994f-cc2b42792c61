<?xml version="1.0" encoding="utf-8"?>
<!--BSD 3-Clause License

Copyright (c) 2019, Cloudminds
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>EC<PERSON>L, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON>
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>Robot</Name>
    <Description1><![CDATA[This LWM2M Object includes robot information and links to the location object.]]></Description1>
    <ObjectID>10315</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10315</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Single</MultipleInstances>
    <Mandatory>Mandatory</Mandatory>
    <Resources>
      <Item ID="1">
        <Name>Robot ID</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>0..255</RangeEnumeration>
        <Units/>
        <Description><![CDATA[The Robot identity.]]></Description>
      </Item>
      <Item ID="2">
        <Name>Robot Type</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>0..63</RangeEnumeration>
        <Units/>
        <Description><![CDATA[Robot Type, for example: Ginger, CloudCleanX, CloudPepper.]]></Description>
      </Item>
      <Item ID="3">
        <Name>Robot Serial Number</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>0..63</RangeEnumeration>
        <Units/>
        <Description><![CDATA[Robot Type, for example: Ginger, CloudCleanX, CloudPepper.]]></Description>
      </Item>
      <Item ID="51">
        <Name>Battery Level</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>0..100</RangeEnumeration>
        <Units>%</Units>
        <Description><![CDATA[Current Battery Level of the robot.]]></Description>
      </Item>
	  <Item ID="4">
        <Name>Charging</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Boolean</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[If the robot is charging or not,the setting of which is a Boolean value (1,0) where 1 is Yes and 0 is No.]]></Description>
      </Item>
	  <Item ID="5852">
        <Name>On time</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration/>
        <Units>s</Units>
        <Description><![CDATA[The time in seconds that the device has been on. Writing a value of 0 resets the counter.]]></Description>
      </Item>
	  <Item ID="5">
        <Name>Positioning</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Boolean</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[If the robot is needed to be positioned, the setting of which is a Boolean value (1,0) where 1 is Yes and 0 is No.]]></Description>
      </Item>
	  <Item ID="6">
		<Name>Location</Name>
		<Operations>R</Operations>
		<MultipleInstances>Single</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Type>Objlnk</Type>
		<RangeEnumeration/>
		<Units/>
		<Description>
		  <![CDATA[The location of the robot, contains the reference to the Location object(6).]]>
		</Description>
	  </Item>
    </Resources>
    <Description2 />
  </Object>
</LWM2M>
