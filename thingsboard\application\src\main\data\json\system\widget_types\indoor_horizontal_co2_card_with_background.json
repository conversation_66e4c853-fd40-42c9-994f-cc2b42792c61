{"fqn": "indoor_horizontal_co2_card_with_background", "name": "Indoor horizontal CO2 card with background", "deprecated": false, "image": "tb-image;/api/images/system/indoor_horizontal_co2_card_with_background_system_widget_image.png", "description": "Displays the latest indoor CO2 level telemetry in a scalable horizontal layout with the background image.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'co2', label: 'CO2 level', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"CO2 level\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 160 - 80;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 400) {\\n\\tvalue = 400;\\n} else if (value > 1600) {\\n\\tvalue = 1600;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"co2\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":600,\"color\":\"#3B911C\"},{\"from\":600,\"to\":800,\"color\":\"#F77410\"},{\"from\":800,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":600,\"color\":\"#3B911C\"},{\"from\":600,\"to\":800,\"color\":\"#F77410\"},{\"from\":800,\"to\":null,\"color\":\"#DE2343\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/indoor_horizontal_co2_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal CO2 card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"ppm\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["environment", "co2", "indoor", "carbon dioxide", "carbon emission", "carbon footprint", "carbon output", "carbon pollution", "carbon capture", "carbon offset", "carbon reduction", "carbon neutral", "climate gas", "fossil fuel emission", "carbon cycle"], "resources": [{"link": "/api/images/system/indoor_horizontal_co2_card_with_background_system_widget_background.png", "title": "\"Indoor horizontal CO2 card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_horizontal_co2_card_with_background_system_widget_background.png", "publicResourceKey": "bTRWa0KaU6Z4MAHIJvU9SSGhNVYLIBIk", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/indoor_horizontal_co2_card_with_background_system_widget_image.png", "title": "\"Indoor horizontal CO2 card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_horizontal_co2_card_with_background_system_widget_image.png", "publicResourceKey": "ilW9lFvfswZ3ySntbIqIBSb9mUBVmgnA", "mediaType": "image/png", "data": "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", "public": true}]}