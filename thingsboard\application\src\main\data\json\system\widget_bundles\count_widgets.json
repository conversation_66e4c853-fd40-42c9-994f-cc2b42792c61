{"widgetsBundle": {"alias": "count_widgets", "title": "Count widgets", "image": "data:image/png;base64,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", "description": "Cards to display the number of alarms or entities based on the selected filter.", "order": 5000, "externalId": null, "name": "Count widgets"}, "widgetTypeFqns": ["alarm_count", "entity_count"]}