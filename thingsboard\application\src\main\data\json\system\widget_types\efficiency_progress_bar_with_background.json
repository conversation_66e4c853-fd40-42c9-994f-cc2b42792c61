{"fqn": "efficiency_progress_bar_with_background", "name": "Efficiency progress bar with background", "deprecated": false, "image": "tb-image;/api/images/system/efficiency_progress_bar_with_background.svg", "description": "Displays efficiency reading as a horizontal progress bar with background. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'efficiency', label: 'Efficiency', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Efficiency\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 30;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":60,\"color\":\"#F89E0D\"},{\"from\":60,\"to\":80,\"color\":\"#3B911C\"},{\"from\":80,\"to\":null,\"color\":\"#2B54CE\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":100,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/efficiency_progress_bar_background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":60,\"color\":\"#F89E0D\"},{\"from\":60,\"to\":80,\"color\":\"#3B911C\"},{\"from\":80,\"to\":null,\"color\":\"#2B54CE\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Efficiency\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"%\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["productivity", "effectiveness", "performance", "capability"], "resources": [{"link": "/api/images/system/efficiency_progress_bar_background.png", "title": "efficiency_progress_bar_background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "efficiency_progress_bar_background.png", "publicResourceKey": "WPMH9Wc9t0Rymali5GAKdc6qpGQBBabB", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMUAAACACAMAAACMc10dAAADAFBMVEWlpqCgoZuXl4+qqqenqaOio52oqaWUlIygnph4eHO2uLGSkYqLioOampOenJZ2dnJzdHBxcm5DT1KHhn9XXl+ZmZHEysOIiIHCycFoamh+fXecm5Wclopub2xaYGCPj4h6enVlaGdHU1UnOD4YKzGzta6Zk4e5vLVwcW03R0s8Sk6NjYa7v7hdY2Koo5ZRWlsfMTaBf3k0REgcLjN9e3VsbWtOWFnCxb1LVVeFg30NGR8kNjurrKlqbGpgZWRUXF0IEhisrapiZmU/TVAWJy2pppmysKSCgXsxQUbEyMCfmYwQHyYOHCKvsqywsamEgnzV19AuP0QqO0DAw7ulopWcmY6NjIPAxr8hNDkKFR2trKE7R0jR08vGzMaqqJ3H0M+9moaWkIQ5REPT1c3AnYcrPUITJCrCysm0tKqurqain5Sgm4+9wbqdn5mQin7K1NPHzsm6u7CmpJjL0cy2urXEzcymoJKgnZK/x8aOh3usqp62t6ywrqHHysOjnY+xq52ykHuxtLCZlos1QUEvPT/P2deckIC+xL3AwLS1sqazt7Sijny8xMK5v76uqJmjloaTjYHCmH5+f3pYXFdCTEwVEhXNz8aSj4SGgXVBSkfO1NGYjHy+k3muoJDY2tMGDROpm4qVk4hIUE55dm20rqBcYFssOTq3vLqqkX9jZF6vpJVpamIWHyLR0cXMzL+xnYuIhXrW39+oingeIiSCfnNJRz9/em4nNDV1cmng6OqLhHhSWFM3PjrIyLuono6flIRNVFEeKixAR0I0My5QTEM/QTvDnoW6jnRgV0yrl4Wih3NSUktYUEbAu62le2VITUcqLSrFxLaYiHePgHCyl4O2kn6SiHiLiX5mX1Ssgmt5bWC6loCziG9xbmQfGBhwaFxxYlS8tqeKe2rDf1R2e3mEdWVAOjO4p5Vsc3GbaFBTQjJ9dWmafWo2JR65nYheSTlHKCAoHx2AhICpdVesaERWMyg0HBi6rJuEioeBY1FtWUxxSjaOVziHalh3Nyy4f146Y0+nAABGuklEQVR42myaCVyT9x3GQzhiDkxCAiGBkAQFognhRlMEJhoQFGiFgIsKy9wkQIic4TQok0sRKG2FiW5YtV6zWhXm2IR14rytV6kHzmo9263tqm3t2h3P/0042u4hCemnn/bzfnl+5/99aeELZxItXLZkccQGD8YH7Kysjbm5a/LKN5ZvVDcHBGSwbGlpprSyxnfeeecPL2Lj/RL4okieks9X7qgNUkNB6qBylinRYGCZs6uym4PqTGpzRnN2dra3t5eXlzel5OQ5QTZbjrEyxyUnJ6cm31SaWX/xXsm94WtNgYG+gb6+voGBgUYj3vinTouleqC/Z+TMsUcDB3pqWJoz/T39d44dd3XXu86bP6l585ydnfGGaH4xERGEInThzNdj597Z2c1gzK+qyF2DV+7K7LI5iTmBprS0tLJ3iF5ExQvj+CIBl8+XKneYgkAAjCBWgJ0Cl25m1ZiCzBlzsueAwhsUXg6KOUFBQZk5aSyWwZDY0tLaevEKtGvY6JsCBlBABMjo60soekYGz1zoeTTwyeGcAM2ZRz39Owc/qfRxp5GLn4AgBG4QKHgeBGLm6mULZwY/iT0+MC0/df36jUHqbK+VK5NZhoKaA8YAB8Uf3vkiNkQYrOSI5LCCDwpgEIrygNa6GkO5uTm72cyqq1VnZMyZkwwGiKCAIXkOpE6cU9a4de2Wl3/zm5dXvH0CFPdSCMMEBkyBG0ZgDI5U3xgBRU83KG48GuzvGRyorKwEBUSRwAa7DyBxpjFjX4EZ8GLx6kVvbThwnO3qHLB+/fqAtHwX18rSnDSf43p4kY+Igr4Qh4T4gYLJJxGVFmQXvMivq8kPMjc3m9W1oCBeeNsFM0ABDsis3tr4zm8cWrXqxIkrF23AmKCgfnUajZ2dndUj1XdHBk4eG2zIMWnOnAJUdQ8o3ObNm3DDeVJutPi4BcSMiNCXfvrSS6vFB+7wk1QymYzN2FHpkppWnpvaXZqWn59ftnUrLuGLKKEwTikQMJVKpRReTFCkGWoSg9TNzWoHRTPlxEq8obUOiozkrY2wAQj4fPnlgyeulNhSClOmUEBgsEDnq+/23z15rNo3n+RFdb+lp7OygFA42yEmjKCCihYc7zETCR4qnB3/+tJFuyO1YUlsT0+faa4u89dvrlizJnVHJigMJUFBObUBD6RCkt1a5AUoHF4guVPz82sSWYQiyFTPUsOKbFBQRkwxI8NrayOu3gGx6uDbJ0ABfQ/DaLRYOi2D56uP9yMfqvWE4szAn6p7jJWZmaCYBwAi5LWdgaJg8D0WLpwZsWxRRGjEQv+u41y+kkFzSd3I2pxXsaaRUBQaDIbKf3322cefffxneKEg2c2jKMrL7RCsVHgxSWEmFONJAU2EVPLWrS+vGvdiBSgoL76X3g0kLTqrd56v/mTk0YXqar2hXDNyd19/9WBDQYGrMxjsRszHz7gToJg2XRSDeFrmP3v2bD+ZoVsbKU/Ny9u8npbqVV7VVpHHMhYk1tT4fvk5fr48LxUKE3giAdNOwXJABKTmG3Lqyh0UyO5meEGJhJMdAhgZc8pWrFpFYcAKQnHRVliIkJrC0QAMcPzxALw4ery6usHA0vQPjJy3WPQFma4UwkRaz3NguLjRpju5x4dGRMTEx8f7KWkudHZ3N63o1pmPP/7szGf/OnPrbk53ik5Xk+KgUPr5KXgcEbzgS3fksyBAwApQJLKCzKBodXhhL7HwYa2dw0GxYsUqxNQUCggEMISisKMYB0/2WO6cPF9dXR1oqLWe6ul/rbpTX1BAIxQTwQQMt3EvGNOnC2ZFhMYs8I8OkbrS6Ax2941/EP2T+vy88APfHF2O7+dEX74n9QtW8FQiJg9m7DAEsCgFkIDKSSxRm81qVmJ9ydQShVZBPu0YUyiQ3G+DwlpoQ0jBDchOsGNHYGDnjRFL9fmTgxZkQ43JdqHn/B8txsKCTFBQVpD3uBOwAl7EKqP4s0JDY2aDIqqI1sBgZz365z//cZJQnPz44wu+h43WUl3DZxcufHxhZDcoxDy2KJIkRrchAAJEALFCBy8IRV19CZXd4LArebzawoypXsCKK/dshVZgQHp7VAFih9HYc3TAMvC347DijrEmzTbSs6/fskNfCYrxXgEYu4BAKPxmh/vHzAyNifcTxsf6uO5go8zSBbT168vLfdZvzKsoOGwsLc3RBXmZETCkRin4HIGcyaUoHEJE2SnU6gBQmGEFBeE1nhzU9+zsSS9+46Cw2jIzCyohfaE+RU8JXrz2yGI5+bvDlurBkYZ8eDHYc94S6IioeRAxBC4AAgjEDZpibjBHGBozQ6EIFiqmlzIQUU55Fbm5ZAJZs2ZNRYGls6BUl081vcauWFRapSidyWTypd2TEYWQyslnkWkkoL69hHQLR3miQMgnJgEvb4cXwCBeHCQUmbpScBSABKIoGnYcvryzc+flU7Bi36mGNJPtRnXn+cO+hQUFbvMpUTFFEIBiF40n8GSI/TxCEuKChX70yh2gYJRXVFRUrQFFbm5uQfV9Y2nOOIWYotDyuPAC2e1Ib1CgXzgo6oMy5lBXjwuH0PrAQ33zGqeg6uzBg29fuacp1ZWWllIY+CmgSALP/6XTcv58/2D1a6cGKk0BtrsW47H3GvQF09ym9AoqsQmFswu8kLAZSX7x/uEhQqGfkFG5g7P7NYlTVV5eVW5uFQwprhy8NZo5TvEgFjUKFFySF6i0YLBXWngxlcI7G9kArSQvxxsY9ogCBioUMEBhLciEFaR/G0cLAQEWve+pM5bB/juP7n/yp4HRyrQA661eY+A+1CgnUJAfwkAQSDQBAd9oEo5MGxv/yhJhfIhfnGdlYPC7v/siD7FECW1PfupPo7//VTAF0fhALAQFJwkQUmlDGiDQ+DDR2iutGhSm9voSFCMk81r0Ci+8HcKXZMoLcNitIJV2qNBmQ3oHGskLRRcchb6nbhiPnbzRf+byrcHRwjRW5sMmkvegoEoTGCgL7GkBkX6RLmKzOeIlS+JCooOjfEoD4156SVZVVdWG6bwCFI1dfzra++6bD+AFBAoqL8hkTroeZK9RNVR2owVSFGhxoPihkpMzisvKtqxYRTAcFDZfXHsKVorCgsmKe7j/WPXlN/75xsiZGzd801jWh0aygugzXZ2pnECfcDDYCy1EY4jYDLYgJjQhfm48z9XQsHzxT4s2b96zcePmPambN7dVPfnLe8N/eckDMdZWXv5Egf1CSSYQnlQ5QWFKrKvRgYL4kkYoMv4fBMwAxRYHxSqKomWoKcVWiEF2tCDTF0AwYvQa6donT77x2zcun9x5vgEUiDX8i5RJCudxCiqqSF44sdmoS/FLxcFxSlVqeeoLjyVuVXlVFRAVWHSBXvPar4RrGnPLsrOfJJBdT8UhTU9KDwhyqKQkwGRIZAGCldhel7F25RaMwGUOFU9o5ZyyrVuAAYpVJC1AUd9eV2eoqdFpNLrMgtJSH/n5/lMn33jj6NE3iH73l8u+plrrNQQaMqeA5uZMImq83eE9HlM0mjsw6Lxf8pkCdtF6ddtGtyfz8YevqMgFBTBoBbUlDfLgRqg4j6sgvVumZXJ5SiUoyIqED7KzGtIIRUCdpl6d7AUhnWHASiKkB9HKtXMABYqDoFhF+sWJlsTE1sTEOvRMXSlW2dKG93DtP3np6O+OPjp16uTRNy7fKkSl/bDXiE02pYDmPNmzCQbJbcoLUEyj0z3pnFm8JHaR28bm3IryL9ZXgQGivDDPSV47x+SDDMGA+yAKvZvLpiik7uVqMwDU5mZ8suqQFyxQ7G8vwX9CSpLDANSmZCLEVEYxHIIXL0NkjgJFHWTA/FJakFlaGXjn5NHLl4++AYb+/pGRC7dGRysDAmwfjvZ2GgP102kgwFTrgJiggGiurnQZfTojuEvAkbHnub548eDJ+ipYAS8q1kAZWD9bakuR7yi8D6KC48Rcht0Ld6ooqSEUW1MOvCAUmqFdia2trYnU565d9VB7Oz524bN25VZ7jULrtlNohob6Ojo6hpt8C32Hm5pGr926dev+1eEOouFeyMZi2b7pNXYGouvRqFxwBJSdgohQONHYYSJPuootkEQyaS/EYnHsRntE5VLdO9+QGfjwYWBZGTIjjxcVnBDFZIiYXK5SmpWTn4YfrFCGGl1mZhqV6fWaof3b97e37yfaPiGrhvzDvXEKWGEfQYY6OjYRXR3uw5dxdfQN9Q0PUySgsN4f9oX0BS4OAvyapKD8oPm4+LBFbHe6u0rC5Lq+4DK5keWgyHPkRS5C1pei2EoiShqnkDLdOaDgRSk/wDag16egtmAIsjoorEPb+7ZP0SFo+/ahvj783uWFxLBTwIwtpEYNd+zdu/fIkSO4dnxCkyiUbEEs61X0FPQLsq8SjMm2TVFQeUGnFQkkHHeVjN7FY7qyw8LC0kFRBYo8UGAC0WWm3L+fAojGNY2gEEvlnsQLfhS/2rEqEwwb8SKAFdC+fWho+/5xOTCgvo7Hn366a23xFkJBhkFQEC+u7j0LignZv4KIojjb12E1B2mG29s1Kb76SsoFigCi8hqyT+YMVxqHq6XL0hkigcq1CEtSdzm6Hija8nKRHqAovHbNVmz3IgpeyH0EoMBAb3FQoFMV2jQURapuSLMfSTCur7+u//rrrx8/3n9oOz7vJa8FxaopebF/06ZDfXuPbNo7riPU++zZvZv2dnT0gUJdbm3SURQYo5Dak91uimieRTQ6W8VmSNgcuYBOd8XGuLGNUFS0VZBpUK8ptTY1Wb2KtwLjgThOEcX01DKZ8CK9UodzvpoaAyVdGlk10nRD9SUtGXMyaoNayFqE2nClpeXiPegifrdc2UKVKGrX20IotndsJ39zBCGyyZ5O+I0/A15QuwYUHVaNFRROzuh5dh+gH1C406ZzmCJ3EUcml8hUDIz3G9vaSH5vJhRr0gsLDHWJdd7kKAdeJCiUch+KQqqvpY7UsOGRSpuTj3gKyNcNtZZkZCRntACBLEYtGS3QxV0Xie5dvLKWNO9JinZcfd/Zs3jhDU39Clc2bco0s4Y2DQ0N+YJiPLl/YAWNZDfD1UkilPgIkhhysYAjkMm6N+ZBFaAgk1RYoQ3l0JpMKIofxMYlSJlFWjkoYi2lhvy0tADIhDqlAwUGKs1QfWvtxRZSn3FACASIXD7e9xwUwCAbK6G4+Hj/Y5I413HJ6yZ1hNLNmzfPndM0szr+jsgiFG4TGOMQbg4MmhPDyVUl73KXyekCpUQlV8kY5UBATrQRily9rUBjHWpPLkZ5AQXJbnfsesiL7pq0VCCw7McHOsKTZkC7qC0hMQURipx8UEB2itoW4gUoxmvU48d2iuuO63+fMIxdunnp0s2bl6C/W5tZm55uGh4O9NW7UhV2olc4COyfNIY7ja4V0N0lMhlToooUpbP3gKIKFNSWhFWspq693ruYDESIKLFS4qmVo0aBIhUiHGSohRdkuMUKmpPjSisqcqezX7z4QqBVsenu7pUFmbr2ulaTq8scJDgSg2Q3RQFRFNdBgNf7t2+/v+7St0+/+felS88IhiaodtPTc5uaKAq7HAhuUyPKhY5aK1Oxp3NUDEmXSpskkAXtaVPnFWPbQ1UChbXOZKrJBgW8QF5IJUUiQhFLvEijOMiqVwovGIroaLK/h4REY+kSYv7lCnB2JeXzeEoejymXJIkESVpVsn0CmfACDO8TE8Ypbj59evPSs2dP7RSsc0/PnWtCRFHDoH2UBYIDg0Z+aDTn6QwnJ7ZK5c7g0BNmyFVyUZdMJMvy1OdXNJLejXWyBttDMqHw4kkTYvlhNBHJbvRuUKQhqoCRn0O8UHnMmrXAw2OBB07olof/aunSXy1/HXozOuQ1v2BUNyU2Xa5clDFBceXQunWOYAICIIhO33z6bGxs7Oalm5CmGRQ3zxEvXMb7xdRookH4dHZiuzsxVFmedBGjK0LI5griE548eRL/5mBpWS6UaS3Maddpsr0QUSt5UeIoXjqNw+QhosIqMYZiFHUxGHJKM4kXRdx9r/1lxinMcRfOYMO58eGHF146+s03//73N0QPr/WGLM9pLWnZQvYLB8U4gV23378NChCMjV16+uwZOHTm2kmKeeMj+dRgooS+x3Ciq1QMHxWDkSRnd4XJMZnvoR3vsTiRGaTUWlhTZ9A1eyEtvB4oY6WgQO/m8aOkWXqyKGMtgDJJdueX2lJ2ht7dNKk7Sy7//Rylv587cvZQ8KwrJ0iNmuIFYslx/eN6f+z02OnTpwnK2BGd2XTu2c0jw4GB7oikqVMgRDHYI4rGZhe5c1TsIjbbky1QcSIFbm1tqUUWo76A1NtSjS3HoCsowV0Zb68HuHZe0nxQcHnS2A/0hYDInKRAYFl5i1+PFeNAJdjPz08o9Jjxym5FQkKcIjbqwL4DCo9ZrifsW5LDC4wlt39G6ReUyLfbp7eNnd627fTN06e37c0xm47cvH4d54Y+WOwcy/Y4BW1C82gymZOPICvLh55FF3DD2ExRF6OoqGhaQVpaMSIK4VJjKM1Uo/5ne3P5iO2wVBETiSGVN2SWoncb8iFDTk4qodBlhbwSPms2ksPf338uUt0vOIGMyVFSSCVKkqebKC8AMU5BMYBiQq9ue3XbttugAAqh2Lvu00MpDQ3TXBxbKoTrn2IElOqkYrsWaTkcujuH3RUTwpCL/N70kycp/rg7q6wxtwKHdvrDDTUlJdkrvbLloOCmzxeAgifN0uWT1DY5DmpTSbuolAj9PXD5IfEh8UK/4OC4OIVYykeN4sqTGI15rgwZI5nMIICgatTXoKAYpmDc3vYq0baxbdtevV6jnqCgjY+BoADC9+TGyKLT3JkiERthpYqOkqWna4sY790ZtAzsbAjKq9IXBgZWB+rq1F7F3tlMHH3wwuYhohwUJjQLvLBnGCgKTlRIdDTKLDEhOEGRIFZERfG5TGakRMuhtzXO92HL8tfCC1Ac3PL2lZbHn+L6HZriBThuww9QGFbWEgp9A93JZSIpJiAmYOhsd7d5qgQVhzGdk0VPQmIwOU6MkQuWwG5LdaW6udCGM+wUXW2Gt1c2RcFNmseRk+YNChRadYDJhFUpLYgFCnemXwhk9yEhQSGORSzxuMzIMK2Aw3BubKPRZeyMLauotCAR9TVSwUGAj6kU206Tj+uG5nEKV2IFgcD7R2Z4orFP7xJzZO7uHJmn9gMOWyngqD552ImN3VhQ7o3TIt/hlBpWRkZ2hvcDKqJSBZGg4IMCq54J2ynaoiEf+3cQRxyCm7BCISji4gBB8oHPlUdGStJBMX3jHieYQUOlnaRw+DDuxV//+tefURTIDXxerzOD4md9ej2uE5rYUn8YUq6eKm26luE+jSOgs8VsGZ2vFbkPXAODcUcmqyolxTicYqsJylAnZ2dz7XkhGqfAQJ5IzjBwgkOsmC6dG+3HlcfGEQSFWByljJRx0iWRoAAGnc7hqDw5HMaJFS+vwHpBZTdBcIDYKcCBOjtBkQ8vToOiQU8DA9W3HZqKQBPJJSK2J+4oiZLYjPglP2fSE7p4kcdGjYE4d5AZvFNSArHKGXB/Pjm7GRR8rnY9RyJnMpVZpYZEE4yoq2+vTTM3Z2SYi+bO9hcqFCitpDApxFFyRtU8IBMKSVgSJBBp09lXVjjyAl44CCYpoNMEAB0DUbXXkO2gqERyE00MgJMMrq40OXYjTG4CeaQqK0m8O4ktUAji/ni8mlAYsypZ5NjRZkssMZtBgbzAudX6cS8MdbWtgKhv11mt5Eaxk/8CUOD6E2AF4kka6dnmJoAXwMAmjLrBEWi17BZC4YioSSsmKQBAUaBnrENerKMofGgTu/aPKJxoHB5XIGHKuTyxmCcXsenpCRJJwmvHBgN9QfFBfjYocJKaVmJWz8kwEy/k6akcQqHsLK2pa21tBwQoCnX4G8lAEa9QgAJvJIVSQrdTQGFJgNCKOAIBu4U64YQXb5O8sNfav05SvDo2BhvIa2zbXkNV7bptt2167KvjFD9KbVCwk9CImdy5IX6xWkFSVLBYwo9j7u6xe5EZ0OxLvCjATTv1HLUZZ2k8piCV8oKnr0tMLKk1ldTWmqwp1lrMKC6UFyAgxQkU/DD3zc4iuxeRMAOFSiTggOI3dg5QfPrpoe37DxGOSQrkA0hevbTt6WlQsNadBgWa3vd21UkjqDebI2FGRvIUfL4iShGXEMfs+uIF487goLHJ2NSUEmBGXhSCokWtTjaX8KWgECHU0fX4gfWJdbtaa0uAkZOpC8r2LnOlKEhKxKLERkn5Enq5CyiIkBdagYjDEYkIxct2ihNfY9XWaHBYRRkCBnCAgSLBCzVqkoIqtKTrfY/CTiJTCfC4gYSpTeLzmFGYof+8fMObuDHY1NTb1FRoUnf0DdsKCtJIjTKz+HxQaN1EckJhbK+r34WDVlNaYo3VZsJDIEXx/tFxPLkSIweFoeR2cVSCMIpBy2EUsWVsOlvGuHfQcWPv7bfv1bTrIAJy6FOCQfoFREiIDqWBYhuhcKK5OG5cUPPHVAZQqDgiLUI2LF2UrgXPwMAnd+7s/MBSPdwE2QIIReev/2goMQclezc/USoRUS6UF0pQ1NUlmmqhRI211ru4eHqsUBjlrpKS8Q9lirKDy5RIiBEihvN6HwYdx8IMA/X4Aaw4eJDs5DoNpNNYrdsPUZ0cYy0mRAfFxqogioLuiiZBCH7U8igKWZZKBKsFWsrv6mM9lh3d3WjbYOjtrVGbhzuGB34fm9iCGlVc/ISv5DIF8ziRSIsoYzuqLNZQE6HQ1GYXV4kShAl8ulYcR6RQUD0PExQVTgKGW5sbKFzojOk4VsPdJPuOcfDtXRSGHQXhBZwh61AfYXj1r4fK80BxvRJNj0A42yFo36Og8kKl4kBZgjAtKCzV+ko9Y0dDQ4PNatXlqIuLcRL83p8OWHclqpPLcnmotFyBM3o3F3mBiKrfBYrW1rr9GlN2XirfT6jgRnIVCXEotRQFH8MH1fWwqDo5FzHYnj4MNiOoccUWnK+V4Twk+crFe5rMTM0UZVqHOs6evU2FFSjKQVGoB/74QD71+iHygblcBQaVShTWJVJxsizdDQxyQ8MTtw0rdYb85Kbhprv9I5XkbnE+LZbUKFBQlfZwYabv1SN7Oz65fh0neSbvvKJo/2g/MRjighWxZBznSboEgki+eDcO2jkCURZulKcjv2WsxjJyZ2Blcoa6JLH+sVUDCqBAVg1BOvLsvx/ZdZ2i2IsBxNHrfhhM0CRFFpkUBKqsLEv14WM77xy/c/fatfv38Wq9OtyE24d1rXWtQQEBUYRCNF9Eerc00hjYiyXu6mfk0OUc8sLTY8HceGG0R7DQY8HPN2z4+et+jGPHbt0YOXXy3XffnE6nM7LC/KVagYBT/s5WSqvKtqy8cvFrigAf5GUlGnv27Pl3Xz4Hxft2Cl+9O406MKBBTj+kcHICRRYCSiXL0kq0WbKs7k7cNu/sRF70Xu3tHe5obeposlltyOJEPHxAVVrBfDQybEnHjIGWwdH29kNjdoo8d0Lh5x8vnB06M2Lh6nc3vEk/9vDhhyOPLh/93a/p6Z9cSEoSM5GAcIPBULFptcbDA/t2DvZZAUE4oEOfXsdB4JFnT7/98vPnzye8CNT7IJ5+mNYUBnmDAgaQiKISg5ih9yQUFuT21dHhoRqKwtpan5hoZgXxsPDIOeUCeMEjFJ3VnfWt26dSCIODg4XhMxb5h/h7eESz03sG746cvHz03Tdx2WxZUlK6FmNIF13GCdu5kz5468MLdy22KVmx/fr17X1D55599+VXX3373dNnZ/fkodJuCvScDi+I/k80UV5kgYJgaCUkpLq73Z2cnHAigGNkk8FgsqUMdWg0ia1I4mZTrZSPOZuzUUAqrbTaqrEVVrbe24+8OLK31tuLPXvWguh4IRY8kPj5JcQhANmiff0nf79hebxc0pUE+EgJV8kUrXfypOs7C3XDV5t6fTUaqmHoCIz1EE6e+0Dx+Veff/vtdw6KYaO+iDrkn1KdqFCyxxOh4FBCekfK05EgH+zbt3v3gYGBYwN37vR0khs6fUNNV3vRFJprE6NIv+BsRL/AeVQvRqi6RJxtt95r3VWPCeTF8oUbZvv7L1s2c8mSJasXL169ePXC1TMX4+uSmauXzpyxeEZoKBI/2v+LzfNcXXA0WlvToYNAMFGe+ohuPnv61Vf/+e75s2d95XkBY9uaAvVOuPwflCdokiILQwHJb6RsGEemkh02Zn3Q3RDYO3rtk/vXrl27ivTo6EipbW3NLjFFTUwgoMAEgrN5HNaT23ftJdnqBUsXxMfGBocvCl+8cPXCiNUxC9+K2BDx+5lvxUTMiPCf4TFTKIxO18qlsUltrADqeYtW2JBTOo6BEpWJTjF0/b8fPf/qqy+fP3/+3749eQFHxnpBMbmfAuDHFOgXaHgqFSjk6cgMLR5RasgsJHkBAowhw8OFtpQSlqlZzYqlKPZQTxZFje6vh3C7AXch27eXeG8OX7Qgns8Te3h44LnWhaGrQ1dvWPar0LdC31r2bsxCj5joUJwOCkRJEknXZrXaTB60bWnHLRAdERgg1CdbX9/1jz56/vnnVHYf2pNXfu7caIPedcqBhz2jKQC87RRsTxlyToX/u0iOcBHwBNjyUnzHKa42Dafg/202l3urWQqyf4rK0ymKXmoox82TXfX7+/pKvJrDZ/lj04vCIUgEnjGOWThj5qLly2csXRqzNOKViLmLopfF8rkijlYbpq3C41MrV4ICq0n7OISGgrANEYqP/nvz0tOPHBRXR/Xurj/uEgTDrmk0GcOdrU1Px8icxZDwIgVaqZxeWmpIy7HacJsTXqSkDNtsdWVeam+1CZMvKu1G6k5x1Oh2QOCGKjID94JaVmaHz5qrkEt4OMiJQAbg0kPDZy+fuSH8rUXLwpeFhIfEiLlMDAmCMFFFXp6XnULXDgpHTkyhAMbTpw4vjpyzNFROUExBcEAUwQsVxsykJAnGZg6Hq8Qmp4wTH9/Z2WMZ7W3q2F+PQQl3qnetXdGqJhFFUczXotAqxQdGjZi1/n7p5tkhxFVG8UqPBSG8pHTJ3Lkhy5YuC10UGh4Tvvz1V8JnbwiPmR1DUTDlaLGCJFnjmjJAgGJXe00NYgqpQVCsEBLjLLY8HKrh6H9s/54q1ukjnfrprnaE7/W6CYxpmGnxl5UwFXItM94/IZjPCeOFdRr1lt7RUaT26LX7g9cefvjh3TOdQWZEFNX11msxRknjdg/iofCmTUfO9dW3tJTgOZx4f7/IdFXS3LnRy5YuXTZ72aylODufu3zB0tn4iZ8dP0Mhl3MwhohkjY14CIFQtJqgWhOGezyBUF8DEX8fk6O1sZvPn1/KAcW6TUa9jyvNxQHgmJ6coMmIyhIwY5mo/1qRiJcQJ05HeuMRu+6eFKijo6nJNz/fbRq9oaEURSWIOiPTpoJbKRVnba6oaivHE+Ydml2mkpISlnCuH86z5P6g+OWM0OUR4TGv+78yK3zBhllvzQqNDscTM3ImWZNE7EaMH2XFXti7cOusGZtJUAkLtRxnERAq3uPH5x6ee/r0yDkD+sXeDuP0IrsXTlOK0xQM4sX/yDgTmDbLMI53la50vb+2K/26Xl8FeqyWdm1txbbYiq1gadpxDTKWmdQJCJuIbjA8QHSKoKRsGWoMTkRESESZA6fxNs4juqiRuThQ4z2MR9ToPP9vWx3qs4G4LBm//Z/jfZ/neRcheVOPdEurAjYeamAN6bhitRVxfUNL3rrOwguR5Do6N26MBdzo0/p64zy0zFWHBrddu2eyCbly1q9yY1pgD2pxSIIWTH2qtL6x3mLcb3d6L6eSlgSV9KS0RhXPlhYgvHWggBhbQUFGZ8DYWIy/hULsKBHrwYT5tUc/f/e3r15fAy3e23BQJPo7JvCxKjnlIEBRK3OJNSpTXClwYAHSjQro49ceYmOtoLrpkr1YzER6wn5jx/r1nRcFcNhGPyqCU5Q/ZOrFRHmotwm7UVfi9xWvw/JUxEdjKZQqTdbXm0HRmNrkaYyWRo3mEm9SbgzINXGlzBG3ggIYhOJ8WN2+zRjSwgqrwJDlePvhdz47MMOwJvdUPbJhGFqsCghCcBaiLUehl/hDKhqZ3CAPhnyof33bMYpvwfLV8Mfto1MT09Mm+vBi6+K6XkIhiV8YJyNWm3tyB1lq661qqG49WLF9QxpdzbRPI/dYGGNq07nRc6ObGjdZ3BbjQwj0Ek9UbvSaTXHcJ2V/U1yfCad9deeDohgQ8KgcxY3vvFz22ZmTrryhPZc8U3FIRKrDqrBehUCkQFyg3En9BoPYJ4v7g16PJJKmFXmFmMFvrLt2zyNXN/B1SgHuzuOz6sleg19jEitjGYrA6CBWdjDEnOwtbD2vtWvYZYND+qThKPwnmqi3lFpS4TAj3Y+SnmSSQac2SaX8Dl/cIcjGBQLjtqvJTLwOD0zgUIQii9HTc+eG58c++/LPD7qH9vSQNk4OIcOS9SJOFoFAEAqBIC5GQGjQMRLLKcru5pmUeWsOtSADVjU8212FqyImUTFBS2yoExS468V8iAt/aHjXLcS27RkcLLzkuuqOGpPBJpbFpeZEOBU1Gylj2Ex5GI3FG2WiTMLuDIIi6KYdNKIbDIRi89WAIEa0+EeMbsFE0fjc2NgbN73bPTTYgG3h3auSE+d/SojyWQKS+0xaeUCTFsj8OJWqIhpHR1PLbmz699x4XUescF0nrLjmysl9BZosRZxQ+MuvAQNZ89wztK68o7kpX+K3SZQ6pdzrTTjDKSpFWShP2LDf6/REmf4gKMLRoCbioOPWnYOx3q3X7LoeFNhZhRZXZcTYm4nu8qcumJ96a8vY2IlHH2kYGmwaxeCNfPP/dSgAICZgiH0Bl8sVxm1Bj4Gfz5eaowlGy2gdgtojHR3lDeT9VGcxXk0V1OGPubIZByFQILoJRff6rbsK9m2DV+0pbq3VCddi4iIR8K1yjydDkcCw1U6ZwkzUkwBFQp6yqySOuIP2RfJoSShdR+KiDoa4AAXRgkjRM7XFVF50fEvZzIt33QeK7qINIlL0MsUaNFkVMiLgU46Cy1l7zkVWrZfy0DpamzIaU3IMWtiHWs5DP619tKZosf0Qxnetz358tKMvZBJLxMreiBgUga7mvd3nFW8FRR1X/sUXlIHSanhKLtcGCjM8KmkxZyioqDeBuIgGU1oxL4LVJZ9J3LZ+6ydqVL2rCwpAQbQoJlqgd30Jv2yivLp9y/HLG/sb5ayhwfKuvvz8s1qA4KwvZRlAoRgY6L3oey8VtjB2e7TeWGr08PQyFqupB5uVTd2Y6h3sIUMvdPc5apVUAopOQqEKNd2DIMV7MCRK6ueN6773GkNu2qrmBjzehDlZ4qx3GhuT+z2WhDFcGjbak/KUlsdzwKOQ0dfec8+F1ksJxXpgbCZa5MKiZ2SmvPyGd747Pn/Bli3znKHBltN9mUSbq3Ocv6VYg3ggMSFSq+FRLI6QbwhbLE6zXGJLlaZSThva6DVY00W6xYrr9iMVrdlnNyxryCTBsfQiB6EINJG9G2RKrGxShoGNhoSZHLIU0AJx4UxSyXA4TDHyMNPvTVJGr9GegBaQgo7YqE8GNsrY1xOPAsS+nBZ7kZwaeo5fXl30zU9LS0sHyg68zJoc3L3Qp87PMmR8Csem1SYChZrl19N6cchihnlVUsoZ1Lp9aQF2N2pE6N7AzsNgHrvgFdsrztcRj0J0O3AYNISayeZvdoOcw9jlTMgrEdMCNddm96ScUWRacwI+FQqbjeF63C5SoMCJDRSO72NfeDQ/F6OTA4qcFshRV4Kiu7tsquL0d2BYWvr61zt6hwbzoEXuMkGkyAZEzpUgBCCEalZQK0XB8zgtGO66fZoADt06lCZlfHR21OU47MLdla5VCrj8mqYr+QYN8agBaOE2GNZdcw9+kF3TgoJCBfqJEl5aH2HroAXJtBajJRr2UhKGSjJGpt/jtKdsYrEDLhVh7Vz/fWfBrvXrryLRvW9f1qNAgTNhzZimaHnpwIGl73DzfmYAo/dxNijOHmFBADX+wQCEUMjyeAx6Jddt9gY0PLYugnakVCeL+2rRGc7aIdj2g+2Ls1OziwE0OFH1HLivGgwNlxZcej55XrGn4KrO5pYNXQt6NAi4ApvXm0KOCifgplrGRHlT3n4m4YmCggcKaMG6ZWszygU5gUAKLLSR2p0Ji4aOw2PTXScOLB3D+sWZleuahwYvUnFF2e/+bJXIUWSNUMg9WpOMHfEExDQmb0r0I6Uo1fG0DhQ6HZeNQVO+QgEP6xvtk9k0kEoWS4ulqHA1mwvwFqwYq8GdseaOotGTp/U+H7Z65KAwo1pEERdaSkIhzxoJRRBxoQEEHWHdsq2YLAwjuv+hwJW46vae8orxsemjJ5aWFmR33fXwu/dXgcLNVq+mIBxZMeBNxAiFyo3dRxFfauI5ZFyhFX9XSh/6CCpXOh5xudLQHy2kCKZZs4cdYjk8SuK7kMayvMFW21wIa2hoRsHr6zq9/On2NFxRhkxBpcxhUGA7x0ZJGXvCk/Q6vdACzx7oOK2Pc3Zu67yQ7J3flvGoDAWOgpggtOw+CC1Acaqr6Nl7H74/hriw80X5qxBW5VhCoBDig8WLWBVIUxGp2CHji/g+JVfEd0siWrscZg8G8VmbsfEJu9dOWjm+c2jcVwPy2ZZqGJ599A2Pnl5evveOW9O0z/q9PGgOJ51hFGrckexhE0WqhTfhSXhTdrmbpuFRa3builVhOQZanA8DBMICUdGAdZKWspGjx75bWl44ffLMh/fi2YGwUaDOKpBDyEUFYkKoIAxshYKFU40ay+a1YmhRy9ZhliGIS8QRbX1paakRHzAjrNQ4jv/zqtDL960BhT8gP4Lr4Htd7Ucx8Tix/N6rr95xq55H+8R23IyM0VQ96kWy0blf63QiR4WNDG4YjJkJuKBF265d69b9Q5HLUKBowkpbx8wLoPju1Gm0ij/tAgX38lr1vw5OZ6sdVICzw/AWho/ZAIfrcEV0wrgePf0I2rBxQ2XlZTmrzH5+a8tllQwosFmU1eLEyZMfnjlz7NixZUfe5gfvB4WY1keUfmTtpNlZGk6azRbvflUYRxGjJUUZPckwFXKgfxJR79zV20le+Fy9lzCAgjhUT08HepItx+ePIrpfOnby09/fvKNqcojbqFOvvk7kkpMaENAha6w0KEQY3UdomY40Q2gxDzOlOE8emphQmbD9QB7v+ANTE8+XXbCJUbmhBcsl1fht2lNdFc9eV411vkLcpB589NXXbpXoeTKuDF3zZDJVYq53psx2C1Juv2WTuT5cyhjtGpx59Xq9cOe1G6u2rgfF7Vkp4FCFyLJ4glFd8fTM6IkDc1ML8fZ3XrureXJIsV+HHLUagmAAYjUFTvtCUZuaHRe7dFw0neN6sUOAFSC6uml3Uet5HdU1o11HUcgXjpdtOZcJYfzrYOlBoQ2OntdQ1VnVgIsmHmIQinslPJcMZ1oPYzQmcDFyJqJeZ8iOA1WJuTScoEqDYnEkgtmHcOeurXVoHpx/9UeAAEZxMQp3Q1NLa3VL0Vtlh0/M3d3I2D8xLVaTuHiAL8p+/234rwgI5IMYie0cRdzK5eRz0c7m8WRoyGP1H5ucOgEtxnJjd8X9z9zLG3nu5bdGXnh+bm7mCsoAijRLj96B1vPAyfJYYdVBrhCbJLH7Xn31tbfdel7aCi28xpSz3lwadUYZJ8NQuK+ak1Q/VTqil/gwqxQLt5FaSSj2ZiBQLXAo7y5vxfuF4ZGx6WNzD0hc8eFPn61uHhpa8xy3jUAAAxTZsCZi4MdZCuQkjprN5sNfaQGXzRU4XDRfwVVax6878uE3P/747Rx5YLME++67F6mQCqP4NXqTW6VlNs3pY1UcPeZOQk7H/Q8//Nrbfuxp6gRauzdlCXpsQeyrqWzagMqmkodsqoCKCkJnmqYlbFAAAxSX4PQEAJw5m6p31ywelhqC/WPz83NP1dbU3Lry+B3lnQOiKbYon0R1Nj3ljoCwTKXAbAc/WUhRHPyiAhNHWolXMTqaFlvzOLVW/9SBsrGxshkcBn4i9jVe41IBld9Et+k1bkOQumxGUlXF4Ql1SHWtG+54fGXFLZXSoAh6tWm9TMcZmJycHBok78R33hPD7kTaLRbTtJ7mgQLzMLylvBonzr4iPOmuXZw9LNHIvR7GWTp2wdzchIyObH/v45WVX04OH+kT4ToEKbIQsAwCILIJikso8BKGkPLjLoeMLVJzZT6/j9Wr0MgtY8S2lB3IGcQI2wzQQsTT+A1a6gpeUyzGkXChXJd699srKysak4qnxBtGDy9uEOiueXKVTeJmrBSDQo+dLzaEyHpUaxEYACGDp0nccsbLOI3P40XShEOvrDm98sGff+B1cR+RIXc5VedkIKYgDsUlxhLouGpyemfj2CHjInysAoOY3csyMJ7LLigrA8ffFPgqrDWosKLC06hAca4CV4I8KZfPXjiqOPjxL7+vGKQqsQMUQfThrfwdqykGBKBAS5FG38GkuBYQWS2Kth+E1criDpfJL6e8VNT4QNcTz9+8/XD17uXfv/zgj2Pt7X25QpGLbMKQ04JNKPCT9GnVLPwrDmq0gWW6trVr2Tq/X3fO2rSBSTY2Ns6XlDpvnn9+ZmbuONwrbAuRPfOMFszFoFiXJ2YLTi3wFaMfn/l9JSBXBdIRrDbX6jAPjl27Y3DworY21iRepQxNdsaUpHunwQJY77U7MGIlFBWHQIFGnoyc3xBrYUvUePHcBXP7NePVN3668v4rfyy3D/dBCYCs8iWCAMtQwPhZCjzO5fB1sohA1NvJUhpCvjaWzqQxR0fs5osrk8GJ6amRt24GBaUlFG08t98gp85tw8pg3sj062+cOrV88vSZMyvjchuWsoN2LzqxOQ1igtqh3JeP6Xhit5ZqZCR1eGdDDvSb91ZgRQAUi3AonklloyygKLl7Zo7h6ZV9Xe+8/eYfJ9uH2YBAUGS1IGWCQBD7GyJLsQZPXLFA6HP52LHJtXqvTc9mqa1sXDosicT+oDyEGldSktXC79YreFIVKK5QoFLkvfDiZydMC67FEwvHPp6l6jfVJygPhV5N9ht/srNWMPBY1rbpXNLgxZWVwaqCXTt2kLjYvHcDYUBow6HGTdDCbE4Y+xfmkGn1ad3RDAU8CiJkEWA5gtVS5LTIX4unlfkKpcTBZw0NpBmDmI8HS5xgKRNAZpUa5FK/Zn4ToYAWoEBchILhy7iFscK8l148obQqBcv0wpkj3IDZmTKavZTPyrloCH0qThtbgMBLBxjbJzq+Va8KWii58qod2zJXxIIcxeLibISGFqBwOqPGm1eOzz2HP0Z39OOVjBbc1ZeiTK1DdjpLgRsEZkls3Phik5O9HD4unG2TgxyNySRbc46Ck6a8KjLro1GyVYTiAkqbeV0l8fsNwXClDu3bc14aJ1fB6SPWhQ8PsWkm7EyWJJ1YEtzx2D0Ft9xyDlrk6ZuvqMSo0qkXSFL1FnvIiqXW7Av8zVdmKAQkQ0GL6YDHDIr+34/PXT5adF75HR/8+cMfp9vbD+XSa06LHEI2Q+XiAl8IhWvw0HBwjY7n8ikGBgfSWIrn5Ss/oSrP9bhw2zBYPKHpiYmRqSmPPGCAFmIVtLBUWsmD6FM6mZU+tSywLn/I5au8YTPGSEZacO01nQOx2L5bzlFGLJf1f/F9uvGKRESaiFrktnSsAPkJFHVXQQtALMpmD7t445rpkN0STRjnjx+Ye252Q0f521/+8MMfR9qHFWBY5U85BMVZh+KzRLjNKUSxwR172rA7GOHGBge4OmWACnjx7YQhgipgN48cGR0dPdo17pGjYIjZPL/KQChwP2oJyByykWM+HSj4dBAU5BTPY93TvBZC5Q3t1KmumBdi+Vi0/2KDOIxTrsGtK9jxN0XfQUAszo7SrnGxaXrKbkmkjPUlM3Pa2b7d1e/98uWXoDgoPFutM/k1Q8H+FwWHlPC/yjj70NTKOI47r7smuunOtOVsvhwtPJrojtPOPKlNDQ/pGNqbInVJ0KJaQRTdCgLTikaj2BZduX9Ejm3I9kfQdh05SF2DYINFg7oE1Yg2oheioOgV+j5HW0G/O4+ubXQ+5/f8Xp7n+T0/gwzr3+dn1OZxk2xtTTKo41LukNGnNissUS4qZOpPP/3upUtX6lxPF0FLKMwkR55BlbzfrmnuXcWawMmxykLT8DEFrUJxYf7cNGlpI7kw4E/8hHiEI9hthxk+iHFZ9FP3it0QQHHja8Qq3r4EXWjG9IQibeU9sddtFeyeDQXLe388/egbOkNPGSIC7v6MgWCQibVEpjMhE+pHt4nphaHR8WEYRqnPEDIiJYqj1khNpxyOTKXa3O+sd5o0KIIKOShcbkdgBgvSD7XGVnY++Gx7efvKFwp/lGIKvNW1oHjhfGlaRporvDRbK0olg7OD/YN8WUEzDkyUQgv3AIJQPHjjo48SVVzCgNru6sIKXcQ2Nqo3ogHC5Y++++PSJ29IDaIqYNg6UIgYUvJFGEQKpeSWQRUwrpl+8sKcaQJps7L0CiqQR4cWcPRNaVINRQshi7+5vnzlyqV3l2ktwp5aCUfrQiR5FPtIT1WD+/tXr9aXx8fXQzTFsFYP71KYHytNl6YNyqlzF3RcUdkvu0ZqMHgdExx0wRiDLulNoi5uehkUbxNVbIJibL0ejgqiLj4PdK5Hv5NX3zv6ZnPYJD3zTjAKEeG/ECLF9CwOJEkNt1x88pW+kRk9+rOsTSsX7D4FNvKH4+agW3BbuNriyuL2Tr3FEQqFHMuboCi+fd9tdz63v7+4Urej7FeBtV7M6zI2b9Q9dsv0XKkkQ4Hc1LAlEZLI+iXn1cmQmuM49E7BQSXpPShbv/mml+9/9OmnRYorIoWWEgpWPtN+/Z2tR1G3dPno+HTzE8x//rVsUPwr8n8wcC7JCQqdBN0zSoNytd4XV8aHhrBwNDluxnawFssA2tUd3Oln25t2vzFkAYVGHzG6mezEfbc9d+MHW039mMY8OmoXHOhSw/lJ2yD95MUHniidmypdRLzOx77G0t6vjQrWf1ATqbcg8o/ccy0oHgEFIDCi/kORBgVJPSFYIdzd/EROKAiB6J6ghv9RvIFyeSVZdeor4Zhh6ZxSHRxTIEV3ovZLHTK6aSaNxbBAeQyetlkPc6AQR1REC13En8FJ3cMVlLCisM4cTDNRPGm3GyqxaHQXnpy7Yf7CLehFE8Scr5pnac1oCGesIkEUvI2pyMrmHaIuPulSbG+P6V3aGnRh5UEB+ZFcdjHp0Rl6GGeqkP8HA6c97ydHF5T41jCFITU3JxkLxWEQxPiV5jD2HxweeqGRbXeaOaq17OJE68aIAiDTNt+HI/JNnw/Vmhr1hL7AQBd+t5/G7n3E/tPs0V8GJake1gQt5Y3GmGLcDD9tsaA9ns838hIoXhIp3v7k7XHkH8vYtXUtQhfEMNrvbGx8/jky0N3DUawZd7M/8jqbUpxBSN+6/7XLknNOlQnfD8jmH7g4t1ZSTYpVg5iXSONayjJup8OGXLtSXyxQoeUI5Se6MGGp2eVnAxpSNeBGNjKGeiNQCKDAIRIaGAwuH36YL7tdRq2bjjYQAUIRjtOiCMwSwkl30xkFMJBFwUfZfcg5a2XiajMe9IJqx9qr5c7+xIhUHEu9gN27+d5LCkW8+NHRl5KSaURJ9NS/9sAr0zggAQQoEImwfNRPW4bNtOD0Vcp8LONYGIi7o+J+kd0SAkXWB4iHw4iL2GCCd6dTglVgGYiDYVOpdHLDy9DuUJhiHJmNNrYWBOwHwHi9S88//iwJevf8QxEnurAH11cW/Utl/AYw0Fms7a3kqi2FSqoDBYRcpGehDgSkS9SjqLf58i/JmsEkRzRHEoJN36npa5SI9wadFPFbgYqzCSx5K0boaIpnOPmFaTvlAoUTFEY3GzDjbNhTWj3SrmXFKIq8XWE0BeuuxpHnmQjEsKKTKpAbj6U8GVss5rGh7ZnNeNM9F5ENIl70rHscoXv7s3VQNCmkkxD8ecbbKINieQRBeaBLoTsL2EAghr356cmXHxGKV2Tk5zCf82tzMuWMehLLy4gTk6MKPU1FtArW6jKxdAgbA1+XZENuVwS6GLMQH2VT4Izrw67FZqu1uILFq9GZiQxvLQiCkErhK21LpLEl5uIcxGLpFAZ8ioUfcziCOAZPUvPrQPH0u0gKELkJRR0US+U0gbBmMnyjUa6CYpgkemS9m4S8M5MWKe6+evD7b18egeIBOMTzfYM6ua5/YIZjtMNS+cjkuGIsEuIcLrMfj9cRichnGcGaYWk+7Q6hI5kv4jL62eKlh7BIm9Nr3aGID2F/fNjotaYERhSWZTOxlIM2RvwYXmkvS8ELh90UDIdzR+mvv/rqa38qv8St1DffRrQABKFwN5dYUIgc6S4FdEEIIP94KPmZjJMzmr8fHx9LHphHw02Zznm3Uq4aHVKodAYlGnZpYZZRwe/UxGJaYw1m66C/Tvm/1lodIfS4w5EdUNyqx6bZWw6jFhAaBZaRR/wxL19gGQcgoBCrjWWQskAXVquXQ5GI24jtV4eV4gQeo6uYECW5eKlLsQ7jxojKV9IoDANKelWk0GBE6USBUfQolD2KT64efIBtjg+2JKgdmF+TOSedcjEtxORQGRYclDE+6mKoYSlFUUIiluFUs/OzAxfXtBQohn0hoovA4u33v7vNujEV1OB0BQriaT4RSKLkMYZekrjwngLrQBor4MGSQ8Z+lxE5ZMBBJ24lktzYgENtrF/aXN5eX2l1dlbb7bYHlgWCAiGBi6oeapy9lEM0il6g62Eot69+cXLy6cGW5MIDr8zPn8eZJ3hXbDyRs28hxjg2pDQptOq44WuO9noL0a9LF+bnzz355ALM2z4DCi0oqlc+/XSfj3Yd7SiCJeuNJYkUYzBzaxqDS8DIEtJChsLhNxQeGd3pmNdBFZPZWwPuX38aW9xptxEbAllAvQnBWyAQSBRJG0diGKuNStApPn7CIeL08j+l+H7l4PjLk+OtLcljqICYnx6Ae+0DggyHRQ0al2J4Qi6Na5SjugWWKxT8EfW5x+Z1C3O3BClXhOjCqOWEQHGj7UWxZibjhXdvw8XbIF4vHiccvqcgpFiGJQaSYgQOqghz2rCfj/FR2gMM3DiR7K3ZbAAKhOpAjlvHXjWRdDmfL+/t7Ozo40oVqrrjOCknMiAPJ/X9JhN5n/ngiy8/PTg4kKCOY35+Toa9P1n3oKjEMOPTDI9LB8zhYESpdGhDbuQM8dkBF1OvFQjFZLBLkUyQglnc/T+DHAPJRm4kAz+Z4VMpDCiBgXE7YNjY0qEAkil6GCqWxZMPFG3pnIBtWKwXwYwEEk4QudM88bP8aqWcB8PuzvqQUoMPaADUPmwtdsW1iE8rTQj2Bz7YOzgmulhbm5uSSdBrQ4ZqsGsMWKqNYy4sDVqZ8NCE/XxpSO/i8BwFoR3wcK6IBtbtCnOsNRyGh623ii2fPeLCp3UPtEEQPOILdwZVQFIwbRh3mMaegDfAM45YTmvNZmsRDLGVMBTEUdSSI7rkyOWESiW9umrlV7uyu7tbHzKttDey3fEWaHshuHgangZktfrF8d7WwYnkSXSrnJsWu0mBgKw7O+824To6Q9s4zfaw2fQTllcKrskRNRtIWv3QBewCt8TyIT2SxPrORn59TK+vw9uDwusFghdi8zDWNKpz4HpZ2q91a7HwaeQYWwBOzFagvbCMmt4N50FzNC54UQzUUq5AVvGvXN6B7O6tm00rO413ssQdFDdijdXVBn7ck0Zl/+D99z84kcAoUAgiQ53FoDj9u3tkJj6CQCh1JIpRbcXD4/x/WkgHZyUDlUCmgNzcPu5zIdtlMyEshayvQEFaHLpA3GthaMMsiUa8uPCZdKGAACgwNBIoWLYfAT8WwBiLpaI2YshNtZtD/hgOI/viALKEBgM52EO5ks93qtXDw62tg813lduHnbINo5cvw1jyeJELcCHlnS042y9xvnsQveOghwEpsnHUYJmx+GR2DvZHYLYY52laMew0hI06SR9dtLJuo8s3GnRBF6kErFE0Ck8mXVklFSQxQgE1xMgHL88j/7AWgOGgw35QIOqxxSTqIZNYAYaI2kCCaISE/Yt+YFBL1VyO3GiuWjvsbIHi6afeWj7s7HiKxdhqvloFHBGw5srlMoD3PkCrJgm8k6xUOtc/KHfOjKK/ptlsR3iakMtMXITyegTOhyX8W36KO2dH8jEmipJ+34Se6CKVRYNhIrEY2njiwldw96AgGKK3shKnL6BYKYrADUfLcXQ6WUyxhQBcqyjJqiUcRtobbnk8zSYZWtAHMPJ5KGL/fbTCOn60/63l/cM9T6zorUBBh0AAKZE8+cXO/qdHl1+UGPrQjHZuSmJQjvsimFSrTDNBt3sI5+8VigjDGjXj8tIDa1PK5npn1YatX60raBYpCtmuX+pGYFFIvOsKwKAiHhjwVL2UnXSn4BMe6CcAXcBWIdn8IkVhVFFlttVq1aglUReQzuH+Phnz7z7U//zyXqfTwGOqLAEBHIdLOZEDUsldPfn4xRfR+GAK7aanJCjx4rghbGEMyMcjoUmntC/us4eN5pG75VMPzBu03oa3gRgAK9UrMCkLU2n4eXwFcPfAwCWQTcYgBAK2LSoFnTFT6TRyEgrPWUAg55OeQoEHBZTRxagYyU9brSYgajVA5EXBQ95/H5Hg27v6n9/c2ttbheIruUNIDSw5mA/+QZrfHh19fBkQXQqDSk8bVVKTblBHquaHVP2moeH46N04KyOVzcbpGG7VgeIpUKgtyLMwOICAkEWCFt5vFUMAEWIVvK3YxfAgs2ORx8JHpBisCSVsfMFa7BkG5M1Aa2UpV2s2a7X/YMAwqvvAQE+OdcnzV/b2MA68oKgSCoKxlBPNJ1f79OPLH1++LEFzldIcFo90cUskLh+Uyw1S1ShaAqt1SunclFOFyhAsJGiCvC0RcKd4GlUQ6ggSEOji1rO8ATCEKUAY8EKdPCAgHuJ3eSHKEgrUroHCi7lHkYxA/DlBvzXQqtdyQIA2aku4w3xOpOgQu4DsQxeHnU7FY8tUutaNXyEMIkbt28vkYKEEzcshpfO6SfWMFBuVJilK/9G1T4WNsul+NeajqAVUzdAexpPV+lOOsBYURpFCzB0gGE+4rQ2SSJATMDbeKzopCIGwwlUJoi5AwSdiPG8jvx6AbkECCbTWa02ks02CsdS17jIoDgnE6d5db1zpdMqrHuhiZ6fc6XTy+Cp3pbKzf3KZUMyJEMS6UcB4jUSCOhy7XiUpDU6GOIs7GmWYoLTvGmfKxvBZdA0XOK1eE3FhpmAlENme3JokIlJ4eJLVirrA1I4kIwgaoGCwyMjwCSjDswHiADrBJ7pGnmzWSW7RqoGiCsMVowLudW9v73S3rVi/inyq0W57GztE8F9xFWMeie4HRy+++JEEo2l66hwyD3JOFi39DEpn2DipnB0SvDYMafyfjcq+PillY9PJsAtFT2G9OoKsgVBkMSZ6AiJIsmjLxMT0qmcfHkgGsV+ciiPF9SQTiDAiBd6Lye7fJptEG00MqFo1jxElRjdQ7Oy9f5qMNd4/3d1tfPjhh6en36CdNJE//iAtvfEdrr999OIR2QyTSLBXTIIeOuZMzUmGUpza5WcKFHHxFhQMD1/Tp6RQP+eNcpyQ5iIamAUq0ESrEG0DDDCMJChiouMFhQgBjAzsGztSLIvJImZOsUACfCKFh0cejydBrGoDtoFBVatiROXLXQwIHvxpMlt8/w/c9TddOQXRLoggeMfn02+Oj07E2kgw6ORKHBTDeu3FtQWW4litXquexN409kS1dtVwvWKN5j7krSzDCyE7qm+xoPD9L99//8svP34OeX0jCw3gC56si9GTmBj9UtYMVGHFOkKCODRRhYFGJePtUuBVXKwvNZvQBuwbxy5zwCApFSZOxJ1/uPshKh+6koQEINlAz0UmVz/9QoJlXDKFMt09gpoi5eD5ixf7zWNuOu4cMg87dbPyCTtlra3UF6304g+LLFY4vFTQhfU/R+5PUX5Gn+3vX29nE+j9jHSDrIBgtgZr8CD2ESmSbITQkI/FIlCJ2rJEKQncD6HA3SRa8LUO2DZSqUoadoHcNoP8JtG908B/BOQEoWeWoDi4+je417Owd4Fe9QAAAABJRU5ErkJggg==", "public": true}, {"link": "/api/images/system/efficiency_progress_bar_with_background.svg", "title": "efficiency_progress_bar_with_background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "efficiency_progress_bar_with_background.svg", "publicResourceKey": "1HlVleeuFFczWZ7FUCKLSY8WzhOSOsU2", "mediaType": "image/svg+xml", "data": "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", "public": true}]}