{"fqn": "pm10_chart_card", "name": "PM10 chart card", "deprecated": false, "image": "tb-image;/api/images/system/pm10_chart_card_system_widget_image.png", "description": "Displays a fine and coarse particulate matter (PM10) data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pm10', label: 'PM10', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'µg/m³', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'pm10', 'µg/m³', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM10\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#80C32C\"},{\"from\":20,\"to\":50,\"color\":\"#FFA600\"},{\"from\":50,\"to\":150,\"color\":\"#F36900\"},{\"from\":150,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nif (value < -10) {\\n\\tvalue = -10;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"PM10\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"bubble_chart\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "particulate", "matter", "air", "pm10", "coarse particulates", "coarse particles", "particulate matter 10", "inhalable particles", "larger particulates", "dust", "airborne coarse particles"], "resources": [{"link": "/api/images/system/pm10_chart_card_system_widget_image.png", "title": "\"PM10 chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "pm10_chart_card_system_widget_image.png", "publicResourceKey": "znsHJVx2FTaNSWlxjY6RxHhwRvCuDldL", "mediaType": "image/png", "data": "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", "public": true}]}