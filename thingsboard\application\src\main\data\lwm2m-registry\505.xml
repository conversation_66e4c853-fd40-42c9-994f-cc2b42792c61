<?xml version="1.0" encoding="utf-8"?>

<!-- 
FILE INFORMATION
Public Reachable Information
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 505.xml
NORMATIVE INFORMATION
  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues
LEGAL DISCLAIMER
Copyright 2022 Open Mobile Alliance. 
Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
The above license is used as a license under copyright only.  Please
reference the OMA IPR Policy for patent licensing terms:
https://www.omaspecworks.org/about/intellectual-property-rights/
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
<Object ObjectType="MODefinition">
		<Name>LwM2M SIM Provisioning</Name>
		<Description1><![CDATA[This is a device management object that should be used for Remote SIM Provisioning from a LwM2M server.]]></Description1>
		<ObjectID>505</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:505</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>Current SIM Type</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..3</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Provides the information about the currently being used SIM Type:
0: UICC (removable)
1: eUICC (removable)
2: eUICC (non-removable)
3: iUICC
]]></Description>
			</Item>
			<Item ID="1">
				<Name>Supported SIM Type</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..3</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Provides the information about the currently supported SIM Types: 
0: UICC (removable)
1: eUICC (removable)
2: eUICC (non-removable)
3: iUICC
]]></Description>
			</Item>
			<Item ID="2">
				<Name>Service Provider Name</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Provides the Service Provider Name using which LwM2M is currently communicating to the Server.]]></Description>
			</Item>
			<Item ID="3">
				<Name>Profile Package</Name>
				<Operations>W</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Profile Package is a personalised Profile using an interoperable description format that is transmitted to an eUICC to load and install a Profile.]]></Description>
			</Item>
			<Item ID="4">
				<Name>Profile URI</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[URI from where the device can download the profile package by an alternative mechanism. As soon the device has received the Profile package URI it performs the download at the next practical opportunity. The URI format is defined in RFC 3986. For example, coaps://example.org/profile is a syntactically valid URI. The URI scheme determines the protocol to be used. For CoAP this endpoint MAY be a LwM2M Server but does not necessarily need to be. A CoAP server implementing block-wise transfer is sufficient as a server hosting a firmware repository and the expectation is that this server merely serves as a separate file server making profile images available to LwM2M Clients. This server can be the future carrier server as well from which IoT devices would like to use the service.]]></Description>
			</Item>
			<Item ID="5">
				<Name>Update Profile</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Updates profile by using the profile package stored in Package, or, by using the profile downloaded from the Package URI. This Resource is only executable when the value of the State Resource is Downloaded.]]></Description>
			</Item>
			<Item ID="6">
				<Name>Update State</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..3</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates current state with respect to this profile update. This value is set by the LwM2M Client. 
0: Idle (before downloading or after successful updating) 
1: Downloading (The data sequence is on the way) 
2: Downloaded 
3: Updating If writing the profile package to Package Resource is done, or, if the device has downloaded the profile package from the Package URI the state changes to Downloaded. Writing an empty string to Package URI Resource or setting the Package Resource to NULL (â€˜\0â€™), resets the Profile Update State Machine: the State Resource value is set to Idle and the Update Result Resource value is set to 0. When in Downloaded state, and the executable Resource Update is triggered, the state changes to Updating. If the Update Resource failed, the state returns at Downloaded. If performing the Update Resource was successful, the state changes from Updating to Idle.]]></Description>
			</Item>
			<Item ID="7">
				<Name>Update Result</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..8</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Contains the result of downloading or updating the Profile 
0: Initial value. Once the updating process is initiated (Download /Update), this Resource MUST be reset to Initial value. 
1: Profile updated successfully, 
2: Not enough SIM memory for the new Profile package. 
3. Out of RAM during downloading process. 
4: Connection lost during downloading process. 5: Integrity check failure for new downloaded package. 
6: Unsupported package type. 
7: Invalid URI
8: Unsupported protocol. 

A LwM2M client indicates the failure to retrieve the Profile using the URI provided in the Package URI resource by writing the value 9 to the /x/0/7 (Update Result resource) when the URI contained a URI scheme unsupported by the client. Consequently, the LwM2M Client is unable to retrieve the Profile using the URI provided by the LwM2M Server in the Package URI when it refers to an unsupported protocol.]]></Description>
			</Item>
			<Item ID="8">
				<Name>Profile Name</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Name of the Profile Package.]]></Description>
			</Item>
			<Item ID="9">
				<Name>Profile Package Version</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Version of the Profile package.]]></Description>
			</Item>
			<Item ID="10">
				<Name>Profile Update Protocol Support</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..3</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource indicates what protocols the LwM2M Client implements to retrieve Profiles. The LwM2M server uses this information to decide what URI to include in the Package URI. A LwM2M Server MUST NOT include a URI in the Package URI object that uses a protocol that is unsupported by the LwM2M client. For example, if a LwM2M client indicates that it supports CoAP and CoAPS then a LwM2M Server must not provide an HTTP URI in the Packet URI. The following values are defined by this version of the specification: 
0: CoAP (as defined in RFC 7252) with the additional support for block-wise transfer. CoAP is the default setting. 
1: CoAPS (as defined in RFC 7252) with the additional support for block-wise transfer 
2: HTTP 1.1 (as defined in RFC 7230) 
3: HTTPS 1.1 (as defined in RFC 7230) Additional values MAY be defined in the future. Any value not understood by the LwM2M Server MUST be ignored.]]></Description>
			</Item>
			<Item ID="11">
				<Name>Profile Update Delivery Method</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..2</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[The LwM2M Client uses this resource to
indicate its support for transferring Profile
images to the client either via the Package
Resource (=push) or via the Package URI
Resource (=pull) mechanism.
0: Pull only
1: Push only
2: Both. In this case the LwM2M Server
MAY choose the preferred mechanism for
conveying the profile image to the
LwM2M Client.]]></Description>
			</Item>
			<Item ID="12">
				<Name>Free Memory on SIM</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>KiB</Units>
				<Description><![CDATA[Estimated current available amount of storage space on SIM which can store data and software in the LwM2M Device (expressed in kibibytes).]]></Description>
			</Item>
			<Item ID="13">
				<Name>Total Memory on SIM</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>KiB</Units>
				<Description><![CDATA[Total amount of storage space which can store data and software in the LwM2M Device.]]></Description>
			</Item>
			<Item ID="14">
				<Name>Integrated Circuit Card Identifier (ICCID)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This Elementary File (EF) provides a unique identification number for the UICC/Smart Cards. Please refer ETSI TS 102.22.1.]]></Description>
			</Item>
			<Item ID="15">
				<Name>eUICC ID</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[eUICC-ID (a.k.a. EID), see GSMA SGP.02 and GSMA SGP.29 for definitions.]]></Description>
			</Item>
			<Item ID="16">
				<Name>Profile Type</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Free optional text produced by the SM-DP+ server. This is equivalent to the "Profile Description ID" as described in Annex B of SGP.21.]]></Description>
			</Item>
		</Resources>
		<Description2 />
	</Object>
</LWM2M>
