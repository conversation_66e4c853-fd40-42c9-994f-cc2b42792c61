{"title": "Thermostats", "image": null, "mobileHide": false, "mobileOrder": null, "configuration": {"widgets": {"f33c746c-0dfc-c212-395b-b448c8a17209": {"type": "latest", "sizeX": 11, "sizeY": 11, "config": {"timewindow": {"displayValue": "", "selectedTab": 0, "realtime": {"realtimeType": 1, "interval": 1000, "timewindowMs": 86400000, "quickInterval": "CURRENT_DAY", "hideInterval": false, "hideLastInterval": false, "hideQuickInterval": false}, "history": {"historyType": 0, "interval": 1000, "timewindowMs": 60000, "fixedTimewindow": {"startTimeMs": 1694085177686, "endTimeMs": 1694171577686}, "quickInterval": "CURRENT_DAY", "hideInterval": false, "hideLastInterval": false, "hideFixedInterval": false, "hideQuickInterval": false}, "aggregation": {"type": "NONE", "limit": 200}}, "showTitle": true, "backgroundColor": "rgb(255, 255, 255)", "color": "rgba(0, 0, 0, 0.87)", "padding": "4px", "settings": {"enableSearch": true, "displayPagination": true, "defaultPageSize": 10, "defaultSortOrder": "entityName", "displayEntityName": true, "displayEntityType": false, "enableSelectColumnDisplay": false, "entitiesTitle": "Thermostats", "displayEntityLabel": false, "entityNameColumnTitle": "Thermostat name"}, "title": "Thermostats", "dropShadow": true, "enableFullscreen": false, "titleStyle": {"fontSize": "16px", "fontWeight": 400, "padding": "5px 10px 5px 10px"}, "useDashboardTimewindow": false, "showLegend": false, "datasources": [{"type": "entity", "name": null, "entityAliasId": "68a058e1-fdda-8482-715b-3ae4a488568e", "dataKeys": [{"name": "active", "type": "attribute", "label": "Active", "color": "#2196f3", "settings": {"columnWidth": "0px", "useCellStyleFunction": true, "useCellContentFunction": true, "cellContentFunction": "value = '&#11044;';\nreturn value;", "cellStyleFunction": "var color;\nif (value === \"true\") {\n    color = 'rgb(39, 134, 34)';\n} else {\n    color = 'rgb(255, 0, 0)';\n}\nreturn {\n    color: color,\n    fontSize: '18px'\n};"}, "_hash": 0.9264526512320641}, {"name": "temperature", "type": "timeseries", "label": "Temperature", "color": "#4caf50", "settings": {"columnWidth": "0px", "useCellStyleFunction": false, "useCellContentFunction": false}, "_hash": 0.9801965063904188, "units": "°C", "decimals": 1}, {"name": "humidity", "type": "timeseries", "label": "<PERSON><PERSON><PERSON><PERSON>", "color": "#f44336", "settings": {"columnWidth": "0px", "useCellStyleFunction": false, "useCellContentFunction": false}, "_hash": 0.5726727868178358, "units": "%", "decimals": 0}, {"name": "latitude", "type": "attribute", "label": "latitude", "color": "#ffc107", "settings": {"columnWidth": "0px", "useCellStyleFunction": false, "cellStyleFunction": "", "useCellContentFunction": false, "cellContentFunction": ""}, "_hash": 0.16055765877264894}, {"name": "longitude", "type": "attribute", "label": "longitude", "color": "#607d8b", "settings": {"columnWidth": "0px", "useCellStyleFunction": false, "cellStyleFunction": "", "useCellContentFunction": false, "cellContentFunction": ""}, "_hash": 0.10969512220289346}]}], "showTitleIcon": false, "titleIcon": null, "iconColor": "rgba(0, 0, 0, 0.87)", "iconSize": "24px", "titleTooltip": "", "widgetStyle": {}, "displayTimewindow": true, "actions": {"headerButton": [{"id": "85b803db-90f2-5c63-1388-a378e0eb10d6", "name": "Edit location", "icon": "map", "type": "openDashboardState", "targetDashboardStateId": "map", "setEntityId": false}, {"name": "Add", "icon": "add", "type": "customPretty", "customHtml": "<form #addEntityForm=\"ngForm\" [formGroup]=\"addEntityFormGroup\"\n      (ngSubmit)=\"save()\" class=\"add-entity-form\">\n  <mat-toolbar color=\"primary\">\n    <h2>Add thermostat</h2>\n    <span class=\"flex-1\"></span>\n    <button mat-icon-button (click)=\"cancel()\" type=\"button\">\n      <mat-icon class=\"material-icons\">close</mat-icon>\n    </button>\n  </mat-toolbar>\n  <mat-progress-bar color=\"warn\" mode=\"indeterminate\" *ngIf=\"isLoading$ | async\">\n  </mat-progress-bar>\n  <div style=\"height: 4px;\" *ngIf=\"!(isLoading$ | async)\"></div>\n  <div mat-dialog-content class=\"flex flex-col\">\n    <mat-form-field class=\"mat-block flex-1\">\n      <mat-label>Thermostat name</mat-label>\n      <input matInput formControlName=\"entityName\" required>\n      <mat-error *ngIf=\"addEntityFormGroup.get('entityName').hasError('required')\">\n        Thermostat name is required.\n      </mat-error>\n    </mat-form-field>\n    <div formGroupName=\"attributes\" class=\"flex flex-col\">\n      <mat-slide-toggle formControlName=\"temperatureAlarmFlag\">\n        High temperature alarm\n      </mat-slide-toggle>\n      <mat-form-field class=\"mat-block flex-1\">\n        <mat-label>High temperature threshold, °C</mat-label>\n        <input type=\"number\" step=\"any\" matInput\n               [required] = \"addEntityFormGroup.get('attributes').get('temperatureAlarmFlag').value\"\n               formControlName=\"temperatureAlarmThreshold\">\n        <mat-error *ngIf=\"addEntityFormGroup.get('attributes').get('temperatureAlarmThreshold').hasError('required')\">\n          High temperature threshold is required.\n        </mat-error>\n      </mat-form-field>\n\n      <mat-slide-toggle formControlName=\"humidityAlarmFlag\">\n        Low humidity alarm\n      </mat-slide-toggle>\n\n      <mat-form-field class=\"mat-block flex-1\">\n        <mat-label>Low humidity threshold, %</mat-label>\n        <input type=\"number\" step=\"any\" matInput\n               [required] = \"addEntityFormGroup.get('attributes').get('humidityAlarmFlag').value\"\n               formControlName=\"humidityAlarmThreshold\">\n        <mat-error *ngIf=\"addEntityFormGroup.get('attributes').get('humidityAlarmThreshold').hasError('required')\">\n          Low humidity threshold is required.\n        </mat-error>\n      </mat-form-field>\n    </div>\n  </div>\n  <div mat-dialog-actions class=\"flex flex-row items-center justify-end\">\n    <button mat-button mat-raised-button color=\"primary\"\n            type=\"submit\"\n            [disabled]=\"(isLoading$ | async) || addEntityForm.invalid || !addEntityForm.dirty\">\n      Create\n    </button>\n    <button mat-button color=\"primary\"\n            type=\"button\"\n            [disabled]=\"(isLoading$ | async)\"\n            (click)=\"cancel()\" cdkFocusInitial>\n      Cancel\n    </button>\n  </div>\n</form>", "customCss": ".add-entity-form{\n    width: 300px;\n}\n", "customFunction": "let $injector = widgetContext.$scope.$injector;\nlet customDialog = $injector.get(widgetContext.servicesMap.get('customDialog'));\nlet deviceService = $injector.get(widgetContext.servicesMap.get('deviceService'));\nlet attributeService = $injector.get(widgetContext.servicesMap.get('attributeService'));\n\nopenAddEntityDialog();\n\nfunction openAddEntityDialog() {\n    customDialog.customDialog(htmlTemplate, AddEntityDialogController).subscribe();\n}\n\nfunction AddEntityDialogController(instance) {\n    let vm = instance;\n    \n    vm.addEntityFormGroup = vm.fb.group({\n        entityName: ['', [vm.validators.required]],\n        attributes: vm.fb.group({\n            temperatureAlarmFlag: [false],\n            temperatureAlarmThreshold: [{value: null, disabled: true}],\n            humidityAlarmFlag: [false],\n            humidityAlarmThreshold: [{value: null, disabled: true}]\n        })\n    });\n    \n    vm.addEntityFormGroup.get('attributes').get('temperatureAlarmFlag').valueChanges\n    .subscribe(activate => {\n        if (activate) {\n            vm.addEntityFormGroup.get('attributes').get('temperatureAlarmThreshold').enable();\n        } else {\n            vm.addEntityFormGroup.get('attributes').get('temperatureAlarmThreshold').disable();\n        }\n    });\n    \n    vm.addEntityFormGroup.get('attributes').get('humidityAlarmFlag').valueChanges\n    .subscribe(activate => {\n        if (activate) {\n            vm.addEntityFormGroup.get('attributes').get('humidityAlarmThreshold').enable();\n        } else {\n            vm.addEntityFormGroup.get('attributes').get('humidityAlarmThreshold').disable();\n        }\n    });\n\n    vm.save = function() {\n        vm.addEntityFormGroup.markAsPristine();\n        saveEntityObservable().subscribe(\n            function (entity) {\n                saveAttributes(entity.id).subscribe(\n                    function () {\n                        widgetContext.updateAliases();\n                        vm.dialogRef.close(null);\n                    }\n                );\n            }\n        );\n    };\n    \n    vm.cancel = function() {\n        vm.dialogRef.close(null);\n    };\n    \n    function saveEntityObservable() {\n        const formValues = vm.addEntityFormGroup.value;\n        let entity = {\n            name: formValues.entityName,\n            type: \"thermostat\"\n        };\n        return deviceService.saveDevice(entity);\n    }\n    \n    function saveAttributes(entityId) {\n        let attributes = vm.addEntityFormGroup.get('attributes').value;\n        let attributesArray = [];\n        for (let key in attributes) {\n            if(attributes[key] !== null) {\n                attributesArray.push({key: key, value: attributes[key]});\n            }\n        }\n        if (attributesArray.length > 0) {\n            return attributeService.saveEntityAttributes(entityId, \"SERVER_SCOPE\", attributesArray);\n        } else {\n            return widgetContext.rxjs.of([]);\n        }\n    }\n}", "customResources": [], "id": "8ab5a518-67d2-b6a2-956d-81fd512294b2"}], "actionCellButton": [{"id": "ca241cd8-788d-5508-a9ce-74b03ef42a7f", "name": "Chart", "icon": "show_chart", "type": "openDashboardState", "targetDashboardStateId": "chart", "setEntityId": true}, {"name": "Edit", "icon": "edit", "type": "customPretty", "customHtml": "<form #editEntityForm=\"ngForm\" [formGroup]=\"editEntityFormGroup\"\n      (ngSubmit)=\"save()\" class=\"edit-entity-form\">\n  <mat-toolbar color=\"primary\">\n    <h2>Edit thermostat {{entityName}}</h2>\n    <span class=\"flex-1\"></span>\n    <button mat-icon-button (click)=\"cancel()\" type=\"button\">\n      <mat-icon class=\"material-icons\">close</mat-icon>\n    </button>\n  </mat-toolbar>\n  <mat-progress-bar color=\"warn\" mode=\"indeterminate\" *ngIf=\"isLoading$ | async\">\n  </mat-progress-bar>\n  <div style=\"height: 4px;\" *ngIf=\"!(isLoading$ | async)\"></div>\n  <div mat-dialog-content class=\"flex flex-col\">\n    <mat-form-field class=\"mat-block flex-1\">\n      <mat-label>Thermostat name</mat-label>\n      <input matInput formControlName=\"entityName\" readonly>\n    </mat-form-field>\n    <div formGroupName=\"attributes\" class=\"flex flex-col\">\n      <mat-slide-toggle formControlName=\"temperatureAlarmFlag\">\n        High temperature alarm\n      </mat-slide-toggle>\n      <mat-form-field class=\"mat-block flex-1\">\n        <mat-label>High temperature threshold, °C</mat-label>\n        <input type=\"number\" step=\"any\" matInput\n               [required] = \"editEntityFormGroup.get('attributes').get('temperatureAlarmFlag').value\"\n               formControlName=\"temperatureAlarmThreshold\">\n        <mat-error *ngIf=\"editEntityFormGroup.get('attributes').get('temperatureAlarmThreshold').hasError('required')\">\n          High temperature threshold is required.\n        </mat-error>\n      </mat-form-field>\n\n      <mat-slide-toggle formControlName=\"humidityAlarmFlag\">\n        Low humidity alarm\n      </mat-slide-toggle>\n\n      <mat-form-field class=\"mat-block flex-1\">\n        <mat-label>Low humidity threshold, %</mat-label>\n        <input type=\"number\" step=\"any\" matInput\n               [required] = \"editEntityFormGroup.get('attributes').get('humidityAlarmFlag').value\"\n               formControlName=\"humidityAlarmThreshold\">\n        <mat-error *ngIf=\"editEntityFormGroup.get('attributes').get('humidityAlarmThreshold').hasError('required')\">\n          Low humidity threshold is required.\n        </mat-error>\n      </mat-form-field>\n    </div>\n  </div>\n  <div mat-dialog-actions class=\"flex flex-row items-center justify-end\">\n    <button mat-raised-button color=\"primary\"\n            type=\"submit\"\n            [disabled]=\"(isLoading$ | async) || editEntityForm.invalid || !editEntityForm.dirty\">\n      Save\n    </button>\n    <button mat-button color=\"primary\"\n            type=\"button\"\n            [disabled]=\"(isLoading$ | async)\"\n            (click)=\"cancel()\" cdkFocusInitial>\n      Cancel\n    </button>\n  </div>\n</form>", "customCss": ".edit-entity-form{\n    width: 300px;\n}", "customFunction": "let $injector = widgetContext.$scope.$injector;\nlet customDialog = $injector.get(widgetContext.servicesMap.get('customDialog'));\nlet attributeService = $injector.get(widgetContext.servicesMap.get('attributeService'));\n\nopenEditEntityDialog();\n\nfunction openEditEntityDialog() {\n    customDialog.customDialog(htmlTemplate, EditEntityDialogController).subscribe();\n}\n\nfunction EditEntityDialogController(instance) {\n    let vm = instance;\n    \n    vm.entityId = entityId;\n    vm.entityName = entityName;\n    vm.attributes = {};\n    \n    vm.editEntityFormGroup = vm.fb.group({\n        entityName: [''],\n        attributes: vm.fb.group({\n            temperatureAlarmFlag: [false],\n            temperatureAlarmThreshold: [{value: null, disabled: true}],\n            humidityAlarmFlag: [false],\n            humidityAlarmThreshold: [{value: null, disabled: true}]\n        })\n    });\n    \n    vm.editEntityFormGroup.get('attributes').get('temperatureAlarmFlag').valueChanges\n    .subscribe(activate => {\n        if (activate) {\n            vm.editEntityFormGroup.get('attributes').get('temperatureAlarmThreshold').enable();\n        } else {\n            vm.editEntityFormGroup.get('attributes').get('temperatureAlarmThreshold').disable();\n        }\n    });\n    \n    vm.editEntityFormGroup.get('attributes').get('humidityAlarmFlag').valueChanges\n    .subscribe(activate => {\n        if (activate) {\n            vm.editEntityFormGroup.get('attributes').get('humidityAlarmThreshold').enable();\n        } else {\n            vm.editEntityFormGroup.get('attributes').get('humidityAlarmThreshold').disable();\n        }\n    });\n    \n    \n    getEntityInfo();\n    \n    \n    vm.save = function() {\n        vm.editEntityFormGroup.markAsPristine();\n        saveAttributes(entityId).subscribe(\n            function () {\n                vm.dialogRef.close(null);\n            }\n        );\n    };\n    \n    vm.cancel = function() {\n        vm.dialogRef.close(null);\n    };\n    \n    function getEntityAttributes(attributes) {\n        for (var i = 0; i < attributes.length; i++) {\n            vm.attributes[attributes[i].key] = attributes[i].value;\n        }\n    }\n    \n    function getEntityInfo() {\n        attributeService.getEntityAttributes(entityId, 'SERVER_SCOPE').subscribe(\n            function (attributes) {\n                getEntityAttributes(attributes);\n                vm.editEntityFormGroup.patchValue({\n                    entityName: vm.entityName,\n                    attributes: vm.attributes\n                });\n                // if(vm.attributes.temperatureAlarmFlag) {\n                //     vm.editEntityFormGroup.get('attributes').get('temperatureAlarmThreshold').enable();\n                // }\n                // if(vm.attributes.humidityAlarmFlag) {\n                //     vm.editEntityFormGroup.get('attributes').get('humidityAlarmThreshold').enable();\n                // }\n            }\n        );\n    }\n    \n    function saveAttributes(entityId) {\n        let attributes = vm.editEntityFormGroup.get('attributes').value;\n        let attributesArray = [];\n        for (let key in attributes) {\n            if (attributes[key] !== vm.attributes[key]) {\n                attributesArray.push({key: key, value: attributes[key]});\n            }\n        }\n        if (attributesArray.length > 0) {\n            return attributeService.saveEntityAttributes(entityId, \"SERVER_SCOPE\", attributesArray);\n        } else {\n            return widgetContext.rxjs.of([]);\n        }\n    }\n}", "customResources": [], "id": "7506576f-87ba-d3a0-88fb-e304d451776d"}, {"name": "Delete", "icon": "delete", "type": "custom", "customFunction": "let $injector = widgetContext.$scope.$injector;\nlet dialogs = $injector.get(widgetContext.servicesMap.get('dialogs'));\nlet deviceService = $injector.get(widgetContext.servicesMap.get('deviceService'));\n\nopenDeleteEntityDialog();\n\nfunction openDeleteEntityDialog() {\n    let title = 'Delete thermostat \"' +  entityName + '\"';\n    let content = 'Are you sure you want to delete the thermostat \"' +\n        entityName + '\"?';\n    dialogs.confirm(title, content, 'Cancel', 'Delete').subscribe(\n        function(result) {\n            if (result) {\n                deleteEntity();\n            }\n        }\n    );\n}\n\nfunction deleteEntity() {\n    deviceService.deleteDevice(entityId.id).subscribe(\n        function success() {\n            widgetContext.updateAliases();\n        },\n        function fail() {\n            showErrorDialog();\n        }\n    );\n}\n\nfunction showErrorDialog() {\n    let title = 'Error';\n    let content = 'An error occurred while deleting the thermostat. Please try again.';\n    dialogs.alert(title, content, 'CLOSE').subscribe(\n        function(result) {}\n    );\n}", "id": "3488848b-e47d-6af6-659f-5d78369ece5e"}], "rowClick": []}}, "id": "f33c746c-0dfc-c212-395b-b448c8a17209", "typeFullFqn": "system.cards.entities_table"}, "7943196b-eedb-d422-f9c3-b32d379ad172": {"type": "alarm", "sizeX": 13, "sizeY": 5, "config": {"timewindow": {"realtime": {"interval": 1000, "timewindowMs": 86400000}, "aggregation": {"type": "NONE", "limit": 200}}, "showTitle": true, "backgroundColor": "rgb(255, 255, 255)", "color": "rgba(0, 0, 0, 0.87)", "padding": "4px", "settings": {"enableSelection": true, "enableSearch": true, "displayDetails": true, "allowAcknowledgment": true, "allowClear": true, "allowAssign": true, "displayComments": true, "displayPagination": true, "defaultPageSize": 10, "defaultSortOrder": "-createdTime", "enableSelectColumnDisplay": false, "alarmsTitle": "Alarms", "enableFilter": true}, "title": "New Alarms table", "dropShadow": true, "enableFullscreen": false, "titleStyle": {"fontSize": "16px", "fontWeight": 400, "padding": "5px 10px 5px 2px"}, "useDashboardTimewindow": false, "showLegend": false, "alarmSource": {"type": "entity", "name": "alarms", "entityAliasId": "68a058e1-fdda-8482-715b-3ae4a488568e", "filterId": null, "dataKeys": [{"name": "createdTime", "type": "alarm", "label": "Created time", "color": "#2196f3", "settings": {}, "_hash": 0.7308410188824108}, {"name": "originator", "type": "alarm", "label": "Originator", "color": "#4caf50", "settings": {}, "_hash": 0.056085530105439485}, {"name": "type", "type": "alarm", "label": "Type", "color": "#f44336", "settings": {}, "_hash": 0.10212012352561795}, {"name": "severity", "type": "alarm", "label": "Severity", "color": "#ffc107", "settings": {}, "_hash": 0.1777349980531262}, {"name": "status", "type": "alarm", "label": "Status", "color": "#607d8b", "settings": {}, "_hash": 0.7977920750136249}, {"name": "assignee", "type": "alarm", "label": "Assignee", "color": "#9c27b0", "settings": {}, "_hash": 0.8678751039018493}]}, "alarmsPollingInterval": 5, "showTitleIcon": false, "titleIcon": null, "iconColor": "rgba(0, 0, 0, 0.87)", "iconSize": "24px", "titleTooltip": "", "widgetStyle": {}, "displayTimewindow": true, "actions": {}, "datasources": [], "alarmsMaxCountLoad": 0, "alarmsFetchSize": 100, "alarmFilterConfig": {"statusList": [], "severityList": [], "typeList": [], "searchPropagatedAlarms": true}, "widgetCss": "", "pageSize": 1024, "noDataDisplayMessage": ""}, "id": "7943196b-eedb-d422-f9c3-b32d379ad172", "typeFullFqn": "system.alarm_widgets.alarms_table"}, "c4631f94-2db3-523b-4d09-2a1a0a75d93f": {"type": "latest", "sizeX": 6, "sizeY": 6, "config": {"datasources": [{"type": "entity", "dataKeys": [{"name": "temperatureAlarmFlag", "type": "attribute", "label": "High temperature alarm", "color": "#4caf50", "settings": {"dataKeyType": "server", "dataKeyValueType": "booleanCheckbox", "required": false, "isEditable": "editable", "dataKeyHidden": false, "step": 1}, "_hash": 0.8725278440159361}, {"name": "temperatureAlarmThreshold", "type": "attribute", "label": "High temperature threshold, °C", "color": "#f44336", "settings": {"dataKeyType": "server", "dataKeyValueType": "double", "required": false, "isEditable": "editable", "dataKeyHidden": false, "step": 1, "disabledOnDataKey": "temperatureAlarmFlag"}, "_hash": 0.7316078472857874}, {"name": "humidityAlarmFlag", "type": "attribute", "label": "Low humidity alarm", "color": "#ffc107", "settings": {"dataKeyType": "server", "dataKeyValueType": "booleanCheckbox", "required": false, "isEditable": "editable", "dataKeyHidden": false, "step": 1}, "_hash": 0.5339673667431057}, {"name": "humidityAlarmThreshold", "type": "attribute", "label": "Low humidity threshold, %", "color": "#607d8b", "settings": {"dataKeyType": "server", "dataKeyValueType": "double", "required": false, "isEditable": "editable", "dataKeyHidden": false, "step": 1, "disabledOnDataKey": "humidityAlarmFlag"}, "_hash": 0.2687091190358901}], "entityAliasId": "12ae98c7-1ea2-52cf-64d5-763e9d993547"}], "timewindow": {"displayValue": "", "selectedTab": 0, "realtime": {"realtimeType": 1, "interval": 1000, "timewindowMs": 60000, "quickInterval": "CURRENT_DAY", "hideInterval": false, "hideLastInterval": false, "hideQuickInterval": false}, "history": {"historyType": 0, "interval": 1000, "timewindowMs": 60000, "fixedTimewindow": {"startTimeMs": 1694085177686, "endTimeMs": 1694171577686}, "quickInterval": "CURRENT_DAY", "hideInterval": false, "hideLastInterval": false, "hideFixedInterval": false, "hideQuickInterval": false}, "aggregation": {"type": "AVG", "limit": 25000}}, "showTitle": true, "backgroundColor": "#fff", "color": "rgba(0, 0, 0, 0.87)", "padding": "8px", "settings": {"showActionButtons": false, "showResultMessage": true, "fieldsAlignment": "column", "fieldsInRow": 2, "groupTitle": "${entityName}", "widgetTitle": "Termostat settings", "columnGap": 10, "rowGap": 5}, "title": "New Update Multiple Attributes", "dropShadow": true, "enableFullscreen": false, "enableDataExport": false, "widgetStyle": {}, "titleStyle": {"fontSize": "16px", "fontWeight": 400}, "useDashboardTimewindow": true, "showLegend": false, "actions": {}, "showTitleIcon": false, "titleIcon": null, "iconColor": "rgba(0, 0, 0, 0.87)", "iconSize": "24px", "titleTooltip": "", "displayTimewindow": true}, "id": "c4631f94-2db3-523b-4d09-2a1a0a75d93f", "typeFullFqn": "system.input_widgets.update_multiple_attributes"}, "eda8a397-0959-690c-405c-11e2c9b2bc7e": {"typeFullFqn": "system.time_series_chart", "type": "timeseries", "sizeX": 8, "sizeY": 5, "config": {"datasources": [{"type": "entity", "name": "", "entityAliasId": "12ae98c7-1ea2-52cf-64d5-763e9d993547", "dataKeys": [{"name": "temperature", "type": "timeseries", "label": "Temperature", "color": "#EF5350", "settings": {"yAxisId": "default", "showInLegend": true, "dataHiddenByDefault": false, "type": "line", "lineSettings": {"showLine": true, "step": false, "stepType": "start", "smooth": true, "lineType": "solid", "lineWidth": 2.5, "showPoints": false, "showPointLabel": false, "pointLabelPosition": "top", "pointLabelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "pointLabelColor": "rgba(0, 0, 0, 0.76)", "pointShape": "circle", "pointSize": 14, "fillAreaSettings": {"type": "gradient", "opacity": 0.4, "gradient": {"start": 60, "end": 10}}}, "barSettings": {"showBorder": false, "borderWidth": 2, "borderRadius": 0, "showLabel": false, "labelPosition": "top", "labelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.76)", "backgroundSettings": {"type": "none", "opacity": 0.4, "gradient": {"start": 100, "end": 0}}}}, "_hash": 0.5973804076994531, "units": "°C", "decimals": 1, "aggregationType": null, "funcBody": null, "usePostProcessing": null, "postFuncBody": null}], "alarmFilterConfig": {"statusList": ["ACTIVE"]}, "latestDataKeys": [{"name": "temperatureAlarmThreshold", "type": "attribute", "label": "temperatureAlarmThreshold", "color": "#4caf50", "settings": {"__thresholdKey": true}, "_hash": 0.7120450032526351}]}], "timewindow": {"hideAggregation": false, "hideAggInterval": false, "hideTimezone": false, "selectedTab": 0, "realtime": {"realtimeType": 0, "timewindowMs": 3600000, "quickInterval": "CURRENT_DAY", "interval": 30000}, "aggregation": {"type": "AVG", "limit": 25000}, "timezone": null}, "showTitle": true, "backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(0, 0, 0, 0.87)", "padding": "0px", "settings": {"showLegend": true, "legendConfig": {"direction": "column", "position": "bottom", "sortDataKeys": false, "showMin": true, "showMax": true, "showAvg": true, "showTotal": false, "showLatest": false}, "thresholds": [{"type": "latestKey", "yAxisId": "default", "units": "°C", "decimals": 0, "lineColor": "rgb(233, 30, 99)", "lineType": "solid", "lineWidth": 2, "startSymbol": "none", "startSymbolSize": 5, "endSymbol": "arrow", "endSymbolSize": 8, "showLabel": true, "labelPosition": "end", "labelFont": {"size": 14, "family": "Roboto", "weight": "500", "style": "normal"}, "labelColor": "rgb(233, 30, 99)", "latestKey": "temperatureAlarmThreshold", "latestKeyType": "attribute"}], "dataZoom": true, "stack": false, "yAxis": {"show": true, "label": "", "labelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "600", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.54)", "position": "left", "showTickLabels": true, "tickLabelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "tickLabelColor": "rgba(0, 0, 0, 0.54)", "showTicks": true, "ticksColor": "rgba(0, 0, 0, 0.54)", "showLine": true, "lineColor": "rgba(0, 0, 0, 0.54)", "showSplitLines": true, "splitLinesColor": "rgba(0, 0, 0, 0.12)"}, "xAxis": {"show": true, "label": "", "labelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "600", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.54)", "position": "bottom", "showTickLabels": true, "tickLabelFont": {"family": "Roboto", "size": 10, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "tickLabelColor": "rgba(0, 0, 0, 0.54)", "showTicks": true, "ticksColor": "rgba(0, 0, 0, 0.54)", "showLine": true, "lineColor": "rgba(0, 0, 0, 0.54)", "showSplitLines": true, "splitLinesColor": "rgba(0, 0, 0, 0.12)", "ticksFormat": {}}, "legendLabelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "16px"}, "legendLabelColor": "rgba(0, 0, 0, 0.76)", "showTooltip": true, "tooltipTrigger": "axis", "tooltipValueFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "500", "lineHeight": "16px"}, "tooltipValueColor": "rgba(0, 0, 0, 0.76)", "tooltipShowDate": true, "tooltipDateFormat": {"format": "MMM dd yyyy HH:mm", "lastUpdateAgo": false, "custom": false, "auto": true, "autoDateFormatSettings": {}}, "tooltipDateFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "16px"}, "tooltipDateColor": "rgba(0, 0, 0, 0.76)", "tooltipDateInterval": false, "tooltipBackgroundColor": "rgba(255, 255, 255, 0.76)", "tooltipBackgroundBlur": 4, "background": {"type": "color", "color": "#fff", "overlay": {"enabled": false, "color": "rgba(255,255,255,0.72)", "blur": 3}}, "yAxes": {"default": {"units": "°C", "decimals": 0, "show": true, "label": "", "labelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "600", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.54)", "position": "left", "showTickLabels": true, "tickLabelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "tickLabelColor": "rgba(0, 0, 0, 0.54)", "ticksFormatter": null, "showTicks": true, "ticksColor": "rgba(0, 0, 0, 0.54)", "showLine": true, "lineColor": "rgba(0, 0, 0, 0.54)", "showSplitLines": true, "splitLinesColor": "rgba(0, 0, 0, 0.12)", "id": "default", "order": 0, "min": 0}}, "noAggregationBarWidthSettings": {"strategy": "group", "groupWidth": {"relative": true, "relativeWidth": 2, "absoluteWidth": 1000}, "barWidth": {"relative": true, "relativeWidth": 2, "absoluteWidth": 1000}}, "animation": {"animation": true, "animationThreshold": 2000, "animationDuration": 500, "animationEasing": "cubicOut", "animationDelay": 0, "animationDurationUpdate": 300, "animationEasingUpdate": "cubicOut", "animationDelayUpdate": 0}, "padding": "12px"}, "title": "Temperature", "dropShadow": true, "enableFullscreen": true, "titleStyle": null, "configMode": "basic", "actions": {}, "showTitleIcon": false, "titleIcon": "thermostat", "iconColor": "#1F6BDD", "useDashboardTimewindow": false, "displayTimewindow": true, "titleFont": {"size": 16, "sizeUnit": "px", "family": "Roboto", "weight": "500", "style": "normal", "lineHeight": "24px"}, "titleColor": "rgba(0, 0, 0, 0.87)", "titleTooltip": "", "widgetStyle": {}, "widgetCss": "", "pageSize": 1024, "units": "", "decimals": null, "noDataDisplayMessage": "", "timewindowStyle": {"showIcon": false, "iconSize": "24px", "icon": null, "iconPosition": "left", "font": {"size": 12, "sizeUnit": "px", "family": "Roboto", "weight": "400", "style": "normal", "lineHeight": "16px"}, "color": "rgba(0, 0, 0, 0.38)", "displayTypePrefix": true}, "margin": "0px", "borderRadius": "0px", "iconSize": "0px"}, "row": 0, "col": 0, "id": "eda8a397-0959-690c-405c-11e2c9b2bc7e"}, "ac90f089-197f-b767-82c3-2668844265a2": {"typeFullFqn": "system.time_series_chart", "type": "timeseries", "sizeX": 8, "sizeY": 5, "config": {"datasources": [{"type": "entity", "name": "", "entityAliasId": "12ae98c7-1ea2-52cf-64d5-763e9d993547", "dataKeys": [{"name": "humidity", "type": "timeseries", "label": "<PERSON><PERSON><PERSON><PERSON>", "color": "#2196F3", "settings": {"yAxisId": "default", "showInLegend": true, "dataHiddenByDefault": false, "type": "line", "lineSettings": {"showLine": true, "step": false, "stepType": "start", "smooth": true, "lineType": "solid", "lineWidth": 2.5, "showPoints": false, "showPointLabel": false, "pointLabelPosition": "top", "pointLabelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "pointLabelColor": "rgba(0, 0, 0, 0.76)", "pointShape": "circle", "pointSize": 14, "fillAreaSettings": {"type": "gradient", "opacity": 0.4, "gradient": {"start": 60, "end": 10}}}, "barSettings": {"showBorder": false, "borderWidth": 2, "borderRadius": 0, "showLabel": false, "labelPosition": "top", "labelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.76)", "backgroundSettings": {"type": "none", "opacity": 0.4, "gradient": {"start": 100, "end": 0}}}}, "_hash": 0.4481337125330429, "units": "%", "decimals": 0, "aggregationType": null, "funcBody": null, "usePostProcessing": null, "postFuncBody": null}], "alarmFilterConfig": {"statusList": ["ACTIVE"]}, "latestDataKeys": [{"name": "humidityAlarmThreshold", "type": "attribute", "label": "humidityAlarmThreshold", "color": "#4caf50", "settings": {"__thresholdKey": true}, "_hash": 0.134733085341747}]}], "timewindow": {"hideAggregation": false, "hideAggInterval": false, "hideTimezone": false, "selectedTab": 0, "realtime": {"realtimeType": 0, "timewindowMs": 3600000, "quickInterval": "CURRENT_DAY", "interval": 30000}, "aggregation": {"type": "AVG", "limit": 25000}, "timezone": null}, "showTitle": true, "backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(0, 0, 0, 0.87)", "padding": "0px", "settings": {"showLegend": true, "legendConfig": {"direction": "column", "position": "bottom", "sortDataKeys": false, "showMin": true, "showMax": true, "showAvg": true, "showTotal": false, "showLatest": false}, "thresholds": [{"type": "latestKey", "yAxisId": "default", "units": "%", "decimals": 0, "lineColor": "rgb(4, 138, 211)", "lineType": "solid", "lineWidth": 2, "startSymbol": "none", "startSymbolSize": 5, "endSymbol": "arrow", "endSymbolSize": 8, "showLabel": true, "labelPosition": "end", "labelFont": {"size": 14, "family": "Roboto", "weight": "500", "style": "normal"}, "labelColor": "rgb(4, 138, 211)", "latestKey": "humidityAlarmThreshold", "latestKeyType": "attribute"}], "dataZoom": true, "stack": false, "yAxis": {"show": true, "label": "", "labelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "600", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.54)", "position": "left", "showTickLabels": true, "tickLabelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "tickLabelColor": "rgba(0, 0, 0, 0.54)", "showTicks": true, "ticksColor": "rgba(0, 0, 0, 0.54)", "showLine": true, "lineColor": "rgba(0, 0, 0, 0.54)", "showSplitLines": true, "splitLinesColor": "rgba(0, 0, 0, 0.12)"}, "xAxis": {"show": true, "label": "", "labelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "600", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.54)", "position": "bottom", "showTickLabels": true, "tickLabelFont": {"family": "Roboto", "size": 10, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "tickLabelColor": "rgba(0, 0, 0, 0.54)", "showTicks": true, "ticksColor": "rgba(0, 0, 0, 0.54)", "showLine": true, "lineColor": "rgba(0, 0, 0, 0.54)", "showSplitLines": true, "splitLinesColor": "rgba(0, 0, 0, 0.12)", "ticksFormat": {}}, "legendLabelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "16px"}, "legendLabelColor": "rgba(0, 0, 0, 0.76)", "showTooltip": true, "tooltipTrigger": "axis", "tooltipValueFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "500", "lineHeight": "16px"}, "tooltipValueColor": "rgba(0, 0, 0, 0.76)", "tooltipShowDate": true, "tooltipDateFormat": {"format": null, "lastUpdateAgo": false, "custom": false, "auto": true, "autoDateFormatSettings": {}}, "tooltipDateFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "16px"}, "tooltipDateColor": "rgba(0, 0, 0, 0.76)", "tooltipDateInterval": false, "tooltipBackgroundColor": "rgba(255, 255, 255, 0.76)", "tooltipBackgroundBlur": 4, "background": {"type": "color", "color": "#fff", "overlay": {"enabled": false, "color": "rgba(255,255,255,0.72)", "blur": 3}}, "yAxes": {"default": {"units": "°C", "decimals": 0, "show": true, "label": "", "labelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "600", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.54)", "position": "left", "showTickLabels": true, "tickLabelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "tickLabelColor": "rgba(0, 0, 0, 0.54)", "ticksFormatter": null, "showTicks": true, "ticksColor": "rgba(0, 0, 0, 0.54)", "showLine": true, "lineColor": "rgba(0, 0, 0, 0.54)", "showSplitLines": true, "splitLinesColor": "rgba(0, 0, 0, 0.12)", "id": "default", "order": 0, "min": 0}}, "noAggregationBarWidthSettings": {"strategy": "group", "groupWidth": {"relative": true, "relativeWidth": 2, "absoluteWidth": 1000}, "barWidth": {"relative": true, "relativeWidth": 2, "absoluteWidth": 1000}}, "animation": {"animation": true, "animationThreshold": 2000, "animationDuration": 500, "animationEasing": "cubicOut", "animationDelay": 0, "animationDurationUpdate": 300, "animationEasingUpdate": "cubicOut", "animationDelayUpdate": 0}, "padding": "12px"}, "title": "<PERSON><PERSON><PERSON><PERSON>", "dropShadow": true, "enableFullscreen": true, "titleStyle": null, "configMode": "basic", "actions": {}, "showTitleIcon": false, "titleIcon": "thermostat", "iconColor": "#1F6BDD", "useDashboardTimewindow": false, "displayTimewindow": true, "titleFont": {"size": 16, "sizeUnit": "px", "family": "Roboto", "weight": "500", "style": "normal", "lineHeight": "24px"}, "titleColor": "rgba(0, 0, 0, 0.87)", "titleTooltip": "", "widgetStyle": {}, "widgetCss": "", "pageSize": 1024, "units": "", "decimals": null, "noDataDisplayMessage": "", "timewindowStyle": {"showIcon": false, "iconSize": "24px", "icon": null, "iconPosition": "left", "font": {"size": 12, "sizeUnit": "px", "family": "Roboto", "weight": "400", "style": "normal", "lineHeight": "16px"}, "color": "rgba(0, 0, 0, 0.38)", "displayTypePrefix": true}, "margin": "0px", "borderRadius": "0px", "iconSize": "0px"}, "row": 0, "col": 0, "id": "ac90f089-197f-b767-82c3-2668844265a2"}, "5186d1e3-f076-e062-5129-e4dd8e4adfb0": {"typeFullFqn": "system.map", "type": "latest", "sizeX": 8.5, "sizeY": 6, "config": {"datasources": [], "timewindow": {"displayValue": "", "selectedTab": 0, "realtime": {"realtimeType": 1, "interval": 1000, "timewindowMs": 60000, "quickInterval": "CURRENT_DAY", "hideInterval": false, "hideLastInterval": false, "hideQuickInterval": false}, "history": {"historyType": 0, "interval": 1000, "timewindowMs": 60000, "fixedTimewindow": {"startTimeMs": 1741884755143, "endTimeMs": 1741971155143}, "quickInterval": "CURRENT_DAY", "hideInterval": false, "hideLastInterval": false, "hideFixedInterval": false, "hideQuickInterval": false}, "aggregation": {"type": "AVG", "limit": 25000}}, "showTitle": false, "backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(0, 0, 0, 0.87)", "padding": "0px", "settings": {"mapType": "geoMap", "layers": [{"label": "{i18n:widgets.maps.layer.roadmap}", "provider": "openstreet", "layerType": "OpenStreetMap.Mapnik"}, {"label": "{i18n:widgets.maps.layer.satellite}", "provider": "openstreet", "layerType": "Esri.WorldImagery"}, {"label": "{i18n:widgets.maps.layer.hybrid}", "provider": "openstreet", "layerType": "Esri.WorldImagery", "referenceLayer": "openstreetmap_hybrid"}], "imageSource": null, "markers": [{"dsType": "entity", "dsLabel": "", "dsDeviceId": null, "dsEntityAliasId": "68a058e1-fdda-8482-715b-3ae4a488568e", "dsFilterId": null, "additionalDataKeys": [{"name": "temperature", "type": "timeseries", "label": "temperature", "color": "#2196f3", "settings": {}, "_hash": 0.570889787682481, "aggregationType": "NONE", "units": null, "decimals": null, "funcBody": null, "usePostProcessing": true, "postFuncBody": "return value || \"\";"}, {"name": "humidity", "type": "timeseries", "label": "humidity", "color": "#2196f3", "settings": {}, "_hash": 0.13597394595782442, "aggregationType": "NONE", "units": null, "decimals": null, "funcBody": null, "usePostProcessing": true, "postFuncBody": "return value || \"\";"}, {"name": "active", "type": "attribute", "label": "active", "color": "#2196f3", "settings": {}, "_hash": 0.21080919932756603}], "label": {"show": true, "type": "function", "pattern": "${entityName}", "patternFunction": "var color;\nif(data.active !== \"true\"){\n    color = 'rgb(255, 0, 0)';\n} else {\n    color = 'rgb(39, 134, 34)';\n}\nreturn '<span style=\"border: solid ' + color + '; border-radius: 10px; color: ' + color + '; background-color: #fff; padding: 3px 5px; font-size: 14px; position: relative; bottom: 6px;\">' + \n           '${entityLabel}' + \n        '</span>'"}, "tooltip": {"show": true, "trigger": "click", "autoclose": true, "type": "pattern", "pattern": "<b>${entityName}</b><br/><br/><b>Temperature:</b> ${temperature:1} °C<br/><b>Humidity:</b> ${humidity:0} %<br/><br/><link-act name=\"navigate_to_details\">Thermostat details</link-act><br/>", "offsetX": 0, "offsetY": -1, "patternFunction": null, "tagActions": [{"name": "navigate_to_details", "type": "openDashboardState", "targetDashboardStateId": "chart", "setEntityId": true, "stateEntityParamName": null, "openRightLayout": false, "openInSeparateDialog": false, "openInPopover": false}]}, "click": {"type": "doNothing"}, "groups": null, "edit": {"enabledActions": [], "attributeScope": "SERVER_SCOPE", "snappable": false}, "xKey": {"name": "latitude", "label": "latitude", "type": "attribute", "settings": {}, "color": "#2196f3"}, "yKey": {"name": "longitude", "label": "longitude", "type": "attribute", "settings": {}, "color": "#2196f3"}, "markerType": "icon", "markerShape": {"shape": "markerShape1", "size": 34, "color": {"type": "constant", "color": "#307FE5"}}, "markerIcon": {"size": 48, "color": {"type": "function", "color": "rgb(255, 0, 0)", "range": null, "colorFunction": "if (data.active !== \"true\"){\n   return 'rgb(255, 0, 0)';\n} else {\n   return 'rgb(39, 134, 34)';\n}"}, "iconContainer": "iconContainer1", "icon": "thermostat"}, "markerImage": {"type": "function", "imageFunction": "return {};", "images": []}, "markerOffsetX": 0.5, "markerOffsetY": 1, "markerClustering": {"enable": false, "zoomOnClick": true, "maxZoom": null, "maxClusterRadius": 80, "zoomAnimation": true, "showCoverageOnHover": true, "spiderfyOnMaxZoom": false, "chunkedLoad": false, "lazyLoad": true, "useClusterMarkerColorFunction": false, "clusterMarkerColorFunction": null}}], "polygons": [], "circles": [], "additionalDataSources": [], "controlsPosition": "topleft", "zoomActions": ["scroll", "doubleClick", "controlButtons"], "scales": [], "dragModeButton": false, "fitMapBounds": true, "useDefaultCenterPosition": false, "defaultCenterPosition": "0,0", "defaultZoomLevel": 14, "mapPageSize": 16384, "mapActionButtons": [], "background": {"type": "color", "color": "#fff", "overlay": {"enabled": false, "color": "rgba(255,255,255,0.72)", "blur": 3}}, "padding": "8px"}, "title": "Map", "useDashboardTimewindow": true, "displayTimewindow": true, "showTitleIcon": false, "titleTooltip": "", "dropShadow": true, "enableFullscreen": false, "widgetStyle": {}, "widgetCss": "", "titleStyle": {"fontSize": "16px", "fontWeight": 400}, "pageSize": 1024, "noDataDisplayMessage": "", "configMode": "advanced", "titleFont": null, "titleColor": null, "margin": "0px", "borderRadius": "0px", "iconSize": "24px", "titleIcon": "map", "iconColor": "#1F6BDD", "actions": {}}, "row": 0, "col": 0, "id": "5186d1e3-f076-e062-5129-e4dd8e4adfb0"}, "1bbd4b8d-db42-ed7c-70c2-3d32c771843d": {"typeFullFqn": "system.map", "type": "latest", "sizeX": 8.5, "sizeY": 6, "config": {"datasources": [], "timewindow": {"displayValue": "", "selectedTab": 0, "realtime": {"realtimeType": 1, "interval": 1000, "timewindowMs": 60000, "quickInterval": "CURRENT_DAY", "hideInterval": false, "hideLastInterval": false, "hideQuickInterval": false}, "history": {"historyType": 0, "interval": 1000, "timewindowMs": 60000, "fixedTimewindow": {"startTimeMs": 1741884755143, "endTimeMs": 1741971155143}, "quickInterval": "CURRENT_DAY", "hideInterval": false, "hideLastInterval": false, "hideFixedInterval": false, "hideQuickInterval": false}, "aggregation": {"type": "AVG", "limit": 25000}}, "showTitle": false, "backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(0, 0, 0, 0.87)", "padding": "0px", "settings": {"mapType": "geoMap", "layers": [{"label": "{i18n:widgets.maps.layer.roadmap}", "provider": "openstreet", "layerType": "OpenStreetMap.Mapnik"}, {"label": "{i18n:widgets.maps.layer.satellite}", "provider": "openstreet", "layerType": "Esri.WorldImagery"}, {"label": "{i18n:widgets.maps.layer.hybrid}", "provider": "openstreet", "layerType": "Esri.WorldImagery", "referenceLayer": "openstreetmap_hybrid"}], "imageSource": null, "markers": [{"dsType": "entity", "dsLabel": "", "dsDeviceId": null, "dsEntityAliasId": "68a058e1-fdda-8482-715b-3ae4a488568e", "dsFilterId": null, "additionalDataKeys": [{"name": "temperature", "type": "timeseries", "label": "temperature", "color": "#2196f3", "settings": {}, "_hash": 0.570889787682481, "aggregationType": "NONE", "units": null, "decimals": null, "funcBody": null, "usePostProcessing": true, "postFuncBody": "return value || \"\";"}, {"name": "humidity", "type": "timeseries", "label": "humidity", "color": "#2196f3", "settings": {}, "_hash": 0.13597394595782442, "aggregationType": "NONE", "units": null, "decimals": null, "funcBody": null, "usePostProcessing": true, "postFuncBody": "return value || \"\";"}, {"name": "active", "type": "attribute", "label": "active", "color": "#2196f3", "settings": {}, "_hash": 0.21080919932756603}], "label": {"show": true, "type": "function", "pattern": "${entityName}", "patternFunction": "var color;\nif(data.active !== \"true\"){\n    color = 'rgb(255, 0, 0)';\n} else {\n    color = 'rgb(39, 134, 34)';\n}\nreturn '<span style=\"border: solid ' + color + '; border-radius: 10px; color: ' + color + '; background-color: #fff; padding: 3px 5px; font-size: 14px; position: relative; bottom: 6px;\">' + \n           '${entityLabel}' + \n        '</span>'"}, "tooltip": {"show": true, "trigger": "click", "autoclose": true, "type": "pattern", "pattern": "<b>${entityName}</b><br/><br/><b>Temperature:</b> ${temperature:1} °C<br/><b>Humidity:</b> ${humidity:0} %<br/><br/><link-act name='delete'>Delete</link-act>", "offsetX": 0, "offsetY": -1, "patternFunction": null, "tagActions": [{"name": "delete", "type": "custom", "customFunction": "widgetContext.mapInstance.saveMarkerLocation(additionalParams, null, null).subscribe(function success() {\n        widgetContext.updateAliases();\n});", "openInSeparateDialog": false, "openInPopover": false}]}, "click": {"type": "doNothing"}, "groups": null, "edit": {"enabledActions": ["add", "move", "remove"], "attributeScope": "SERVER_SCOPE", "snappable": false}, "xKey": {"name": "latitude", "label": "latitude", "type": "attribute", "settings": {}, "color": "#2196f3"}, "yKey": {"name": "longitude", "label": "longitude", "type": "attribute", "settings": {}, "color": "#2196f3"}, "markerType": "icon", "markerShape": {"shape": "markerShape1", "size": 34, "color": {"type": "constant", "color": "#307FE5"}}, "markerIcon": {"size": 48, "color": {"type": "function", "color": "rgb(255, 0, 0)", "range": null, "colorFunction": "if (data.active !== \"true\"){\n   return 'rgb(255, 0, 0)';\n} else {\n   return 'rgb(39, 134, 34)';\n}"}, "iconContainer": "iconContainer1", "icon": "thermostat"}, "markerImage": {"type": "function", "imageFunction": "return {};", "images": []}, "markerOffsetX": 0.5, "markerOffsetY": 1, "markerClustering": {"enable": false, "zoomOnClick": true, "maxZoom": null, "maxClusterRadius": 80, "zoomAnimation": true, "showCoverageOnHover": true, "spiderfyOnMaxZoom": false, "chunkedLoad": false, "lazyLoad": true, "useClusterMarkerColorFunction": false, "clusterMarkerColorFunction": null}}], "polygons": [], "circles": [], "additionalDataSources": [], "controlsPosition": "topleft", "zoomActions": ["scroll", "doubleClick", "controlButtons"], "scales": [], "dragModeButton": false, "fitMapBounds": true, "useDefaultCenterPosition": false, "defaultCenterPosition": "0,0", "defaultZoomLevel": null, "mapPageSize": 16384, "mapActionButtons": [], "background": {"type": "color", "color": "#fff", "overlay": {"enabled": false, "color": "rgba(255,255,255,0.72)", "blur": 3}}, "padding": "8px"}, "title": "Map", "useDashboardTimewindow": true, "displayTimewindow": true, "showTitleIcon": false, "titleTooltip": "", "dropShadow": true, "enableFullscreen": false, "widgetStyle": {}, "widgetCss": "", "titleStyle": {"fontSize": "16px", "fontWeight": 400}, "pageSize": 1024, "noDataDisplayMessage": "", "configMode": "advanced", "titleFont": null, "titleColor": null, "margin": "0px", "borderRadius": "0px", "iconSize": "24px", "titleIcon": "map", "iconColor": "#1F6BDD", "actions": {}}, "row": 0, "col": 0, "id": "1bbd4b8d-db42-ed7c-70c2-3d32c771843d"}, "c9da2d6b-4ba9-1ac8-63b8-68ab49b13c81": {"typeFullFqn": "system.map", "type": "latest", "sizeX": 8.5, "sizeY": 6, "config": {"datasources": [], "timewindow": {"displayValue": "", "selectedTab": 0, "realtime": {"realtimeType": 1, "interval": 1000, "timewindowMs": 60000, "quickInterval": "CURRENT_DAY", "hideInterval": false, "hideLastInterval": false, "hideQuickInterval": false}, "history": {"historyType": 0, "interval": 1000, "timewindowMs": 60000, "fixedTimewindow": {"startTimeMs": 1741884755143, "endTimeMs": 1741971155143}, "quickInterval": "CURRENT_DAY", "hideInterval": false, "hideLastInterval": false, "hideFixedInterval": false, "hideQuickInterval": false}, "aggregation": {"type": "AVG", "limit": 25000}}, "showTitle": false, "backgroundColor": "rgba(0, 0, 0, 0)", "color": "rgba(0, 0, 0, 0.87)", "padding": "0px", "settings": {"mapType": "geoMap", "layers": [{"label": "{i18n:widgets.maps.layer.roadmap}", "provider": "openstreet", "layerType": "OpenStreetMap.Mapnik"}, {"label": "{i18n:widgets.maps.layer.satellite}", "provider": "openstreet", "layerType": "Esri.WorldImagery"}, {"label": "{i18n:widgets.maps.layer.hybrid}", "provider": "openstreet", "layerType": "Esri.WorldImagery", "referenceLayer": "openstreetmap_hybrid"}], "imageSource": null, "markers": [{"dsType": "entity", "dsLabel": "", "dsDeviceId": null, "dsEntityAliasId": "12ae98c7-1ea2-52cf-64d5-763e9d993547", "dsFilterId": null, "additionalDataKeys": [{"name": "temperature", "type": "timeseries", "label": "temperature", "color": "#2196f3", "settings": {}, "_hash": 0.570889787682481, "aggregationType": "NONE", "units": null, "decimals": null, "funcBody": null, "usePostProcessing": true, "postFuncBody": "return value || \"\";"}, {"name": "humidity", "type": "timeseries", "label": "humidity", "color": "#2196f3", "settings": {}, "_hash": 0.13597394595782442, "aggregationType": "NONE", "units": null, "decimals": null, "funcBody": null, "usePostProcessing": true, "postFuncBody": "return value || \"\";"}, {"name": "active", "type": "attribute", "label": "active", "color": "#2196f3", "settings": {}, "_hash": 0.21080919932756603}], "label": {"show": true, "type": "function", "pattern": "${entityName}", "patternFunction": "var color;\nif(data.active !== \"true\"){\n    color = 'rgb(255, 0, 0)';\n} else {\n    color = 'rgb(39, 134, 34)';\n}\nreturn '<span style=\"border: solid ' + color + '; border-radius: 10px; color: ' + color + '; background-color: #fff; padding: 3px 5px; font-size: 14px; position: relative; bottom: 6px;\">' + \n           '${entityLabel}' + \n        '</span>'"}, "tooltip": {"show": true, "trigger": "click", "autoclose": true, "type": "pattern", "pattern": "<b>${entityName}</b><br/><br/><b>Temperature:</b> ${temperature:1} °C<br/><b>Humidity:</b> ${humidity:0} %<br/><br/><link-act name='delete'>Delete</link-act>", "offsetX": 0, "offsetY": -1, "patternFunction": null, "tagActions": [{"name": "delete", "type": "custom", "customFunction": "widgetContext.mapInstance.saveMarkerLocation(additionalParams, null, null).subscribe(function success() {\n        widgetContext.updateAliases();\n});", "openInSeparateDialog": false, "openInPopover": false}]}, "click": {"type": "doNothing"}, "groups": null, "edit": {"enabledActions": ["add", "move", "remove"], "attributeScope": "SERVER_SCOPE", "snappable": false}, "xKey": {"name": "latitude", "label": "latitude", "type": "attribute", "settings": {}, "color": "#2196f3"}, "yKey": {"name": "longitude", "label": "longitude", "type": "attribute", "settings": {}, "color": "#2196f3"}, "markerType": "icon", "markerShape": {"shape": "markerShape1", "size": 34, "color": {"type": "constant", "color": "#307FE5"}}, "markerIcon": {"size": 48, "color": {"type": "function", "color": "rgb(255, 0, 0)", "range": null, "colorFunction": "if (data.active !== \"true\"){\n   return 'rgb(255, 0, 0)';\n} else {\n   return 'rgb(39, 134, 34)';\n}"}, "iconContainer": "iconContainer1", "icon": "thermostat"}, "markerImage": {"type": "function", "imageFunction": "return {};", "images": []}, "markerOffsetX": 0.5, "markerOffsetY": 1, "markerClustering": {"enable": false, "zoomOnClick": true, "maxZoom": null, "maxClusterRadius": 80, "zoomAnimation": true, "showCoverageOnHover": true, "spiderfyOnMaxZoom": false, "chunkedLoad": false, "lazyLoad": true, "useClusterMarkerColorFunction": false, "clusterMarkerColorFunction": null}}], "polygons": [], "circles": [], "additionalDataSources": [], "controlsPosition": "topleft", "zoomActions": ["scroll", "doubleClick", "controlButtons"], "scales": [], "dragModeButton": false, "fitMapBounds": true, "useDefaultCenterPosition": false, "defaultCenterPosition": "0,0", "defaultZoomLevel": 5, "mapPageSize": 16384, "mapActionButtons": [], "background": {"type": "color", "color": "#fff", "overlay": {"enabled": false, "color": "rgba(255,255,255,0.72)", "blur": 3}}, "padding": "8px"}, "title": "Map", "useDashboardTimewindow": true, "displayTimewindow": true, "showTitleIcon": false, "titleTooltip": "", "dropShadow": true, "enableFullscreen": false, "widgetStyle": {}, "widgetCss": "", "titleStyle": {"fontSize": "16px", "fontWeight": 400}, "pageSize": 1024, "noDataDisplayMessage": "", "configMode": "advanced", "titleFont": null, "titleColor": null, "margin": "0px", "borderRadius": "0px", "iconSize": "24px", "titleIcon": "map", "iconColor": "#1F6BDD", "actions": {}}, "row": 0, "col": 0, "id": "c9da2d6b-4ba9-1ac8-63b8-68ab49b13c81"}}, "states": {"default": {"name": "Thermostats", "root": true, "layouts": {"main": {"widgets": {"f33c746c-0dfc-c212-395b-b448c8a17209": {"sizeX": 11, "sizeY": 11, "row": 0, "col": 0, "mobileOrder": 1, "mobileHeight": 5}, "7943196b-eedb-d422-f9c3-b32d379ad172": {"sizeX": 13, "sizeY": 5, "row": 0, "col": 11, "mobileOrder": 2, "mobileHeight": 5}, "5186d1e3-f076-e062-5129-e4dd8e4adfb0": {"sizeX": 13, "sizeY": 6, "row": 5, "col": 11, "resizable": true, "mobileOrder": 3, "mobileHeight": 5}}, "gridSettings": {"backgroundColor": "#eeeeee", "color": "rgba(0,0,0,0.870588)", "columns": 24, "backgroundSizeMode": "100%", "autoFillHeight": true, "mobileAutoFillHeight": false, "mobileRowHeight": 70, "margin": 10, "outerMargin": true, "layoutType": "default"}}}}, "map": {"name": "Edit location", "root": false, "layouts": {"main": {"widgets": {"1bbd4b8d-db42-ed7c-70c2-3d32c771843d": {"sizeX": 24, "sizeY": 11, "resizable": true, "row": 0, "col": 0}}, "gridSettings": {"backgroundColor": "#eeeeee", "color": "rgba(0,0,0,0.870588)", "columns": 24, "backgroundSizeMode": "100%", "autoFillHeight": true, "mobileAutoFillHeight": false, "mobileRowHeight": 70, "margin": 10, "outerMargin": true, "layoutType": "default"}}}}, "chart": {"name": "${entityName}", "root": false, "layouts": {"main": {"widgets": {"c4631f94-2db3-523b-4d09-2a1a0a75d93f": {"sizeX": 6, "sizeY": 6, "row": 0, "col": 0, "mobileOrder": 3, "mobileHeight": 5}, "eda8a397-0959-690c-405c-11e2c9b2bc7e": {"sizeX": 18, "sizeY": 6, "row": 0, "col": 6, "mobileOrder": 1, "mobileHeight": 6}, "ac90f089-197f-b767-82c3-2668844265a2": {"sizeX": 18, "sizeY": 6, "row": 6, "col": 6, "mobileOrder": 2, "mobileHeight": 6}, "c9da2d6b-4ba9-1ac8-63b8-68ab49b13c81": {"sizeX": 6, "sizeY": 6, "resizable": true, "row": 6, "col": 0}}, "gridSettings": {"backgroundColor": "#eeeeee", "color": "rgba(0,0,0,0.870588)", "columns": 24, "backgroundSizeMode": "100%", "autoFillHeight": true, "mobileAutoFillHeight": false, "mobileRowHeight": 70, "margin": 10, "outerMargin": true, "layoutType": "default"}}}}}, "entityAliases": {"68a058e1-fdda-8482-715b-3ae4a488568e": {"id": "68a058e1-fdda-8482-715b-3ae4a488568e", "alias": "Thermostats", "filter": {"type": "deviceType", "resolveMultiple": true, "deviceNameFilter": "", "deviceTypes": ["thermostat"]}}, "12ae98c7-1ea2-52cf-64d5-763e9d993547": {"id": "12ae98c7-1ea2-52cf-64d5-763e9d993547", "alias": "Thermostat", "filter": {"type": "stateEntity", "resolveMultiple": false, "stateEntityParamName": null, "defaultStateEntity": null}}}, "timewindow": {"displayValue": "", "selectedTab": 0, "hideAggregation": false, "hideAggInterval": false, "realtime": {"interval": 1000, "timewindowMs": 60000}, "history": {"historyType": 0, "interval": 1000, "timewindowMs": 60000, "fixedTimewindow": {"startTimeMs": 1587473857304, "endTimeMs": 1587560257304}}, "aggregation": {"type": "AVG", "limit": 25000}}, "settings": {"stateControllerId": "entity", "showTitle": false, "showDashboardsSelect": true, "showEntitiesSelect": true, "showDashboardTimewindow": true, "showDashboardExport": true, "toolbarAlwaysOpen": true, "titleColor": "rgba(0,0,0,0.870588)", "showDashboardLogo": false, "dashboardLogoUrl": null, "hideToolbar": false, "showFilters": true, "showUpdateDashboardImage": true, "dashboardCss": ""}, "filters": {}}, "name": "Thermostats", "resources": []}