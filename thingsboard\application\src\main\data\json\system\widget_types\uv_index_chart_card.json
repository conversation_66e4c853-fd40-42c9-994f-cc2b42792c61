{"fqn": "uv_index_chart_card", "name": "UV Index chart card", "deprecated": false, "image": "tb-image;/api/images/system/uv_index_chart_card_system_widget_image.png", "description": "Displays a UV index data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'uv', label: 'UV Index', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: '', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'uv', '', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"UV Index\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.ceil(Math.random() * 4 - 2);\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 14) {\\n\\tvalue = 14;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#80C32C\"},{\"from\":2,\"to\":5,\"color\":\"#FFA600\"},{\"from\":5,\"to\":7,\"color\":\"#F36900\"},{\"from\":7,\"to\":10,\"color\":\"#F04022\"},{\"from\":10,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.ceil(Math.random() * 4 - 2);\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 14) {\\n\\tvalue = 14;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = Math.ceil(prevValue + Math.random() * 2 - 1);\\nif (value < -2) {\\n\\tvalue = -2;\\n} else if (value > 2) {\\n\\tvalue = 2;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"UV Index\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"light_mode\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "uv", "ultraviolet", "sunburn"], "resources": [{"link": "/api/images/system/uv_index_chart_card_system_widget_image.png", "title": "\"UV Index chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "uv_index_chart_card_system_widget_image.png", "publicResourceKey": "azbmuhTyaXQatybSBFWJ3HljMujDhCh2", "mediaType": "image/png", "data": "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", "public": true}]}