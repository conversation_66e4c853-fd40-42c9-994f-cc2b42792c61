#
# Copyright © 2016-2025 The Thingsboard Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

name: Check configuration files
on:
  push:
    branches:
      - master
  pull_request:
    paths:
      - 'application/src/main/resources/thingsboard.yml'
      - 'transport/http/src/main/resources/tb-http-transport.yml'
      - 'transport/http/src/main/resources/tb-mqtt-transport.yml'
      - 'transport/http/src/main/resources/tb-coap-transport.yml'
      - 'transport/http/src/main/resources/tb-lwm2m-transport.yml'
      - 'transport/http/src/main/resources/tb-snmp-transport.yml'
      - 'msa/vc-executor/src/main/resources/tb-vc-executor.yml'

jobs:
  build:
    name: Check thingsboard.yml file
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Set up Python 3.13
        uses: actions/setup-python@v3
        with:
          python-version: "3.13.2"
          architecture: "x64"
        env:
          AGENT_TOOLSDIRECTORY: /opt/hostedtoolcache
      - name: Run Verification Script
        run: python3 tools/src/main/python/check_yml_file.py
