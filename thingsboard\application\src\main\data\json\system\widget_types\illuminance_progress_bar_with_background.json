{"fqn": "illuminance_progress_bar_with_background", "name": "Illuminance progress bar with background", "deprecated": false, "image": "tb-image;/api/images/system/illuminance_progress_bar_with_background_system_widget_image.png", "description": "Displays illuminance reading as a horizontal progress bar with background. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'illuminance', label: 'Illuminance', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Illuminance\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":1,\"to\":5,\"color\":\"#F89E0D\"},{\"from\":5,\"to\":20,\"color\":\"#F77410\"},{\"from\":20,\"to\":50,\"color\":\"#F04022\"},{\"from\":50,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":100,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/illuminance_progress_bar_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":1,\"to\":5,\"color\":\"#F89E0D\"},{\"from\":5,\"to\":20,\"color\":\"#F77410\"},{\"from\":20,\"to\":50,\"color\":\"#F04022\"},{\"from\":50,\"to\":null,\"color\":\"#DE2343\"}]},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Illuminance\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"lx\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"mdi:lightbulb-on\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["progress", "weather", "environment", "brightness", "luminance", "luminosity", "light", "light level", "light intensity", "lux", "candela", "foot-candle"], "resources": [{"link": "/api/images/system/illuminance_progress_bar_with_background_system_widget_background.png", "title": "\"Illuminance progress bar with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "illuminance_progress_bar_with_background_system_widget_background.png", "publicResourceKey": "7iNis7M6PRCxonI4m9zoCyr3xIZ7Bm37", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/illuminance_progress_bar_with_background_system_widget_image.png", "title": "\"Illuminance progress bar with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "illuminance_progress_bar_with_background_system_widget_image.png", "publicResourceKey": "KXJlLMIxcjqb33zixEkELpUZ4pz4ii0p", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAC/VBMVEUqJScbHiAAAACPXjSMXDCaZTlHPDVQRDuPXzTLysrIycnd08wYHB/Ny8reI0POzMvQzcvk2dDm2tDb0szZ0s3b087i2M/l2c/f1Mzn2tDUz8vSzsvd1M7f19Le19H//v0XGx7o3NEhIiLV0Mzg1s7h2dLY0czTzsva0csbHh/Ny8kjIyPY0s7i1s0fISIeHyElJSTX0Mzf1c3q3dHk2tLX0c7Uz8woIiDZ088tJSHk2M/V0c7QzswbGhrs3tHNxLzSz83Xz8r//fvj2tT+/PklIR8qJCHk180wJyJySCkjIB44KiMzKCL++vZWQDNSPzNsVURZQzUrIyBxWEZPPzV1SipuRylfQCrs39Pn2c1pU0RWQzdPPTN7TituVkRmRSx5TCoZHB5TQjdiQSmOXzVmUEBbRzqLXTNiQyw8LCNqRSr8+PNcPymQYDVlQylYRjk2KSP/8+coJybu4dbh2dOSYTb58ehqUkD47uXz59yVYzeNXjTv49iIWzNrSC19TyxHR0b79e/27OFwTC93SypPQjg+LST68+z06d9LPDJQNycdHh8+MyxALyUeHBvy5dnm29KBZ1GcaDlDLyRzcnF6ZFGhazteQzEhHx7+9eu0tLOZZThdRjZ9VTJLPzY6Mi1URDppSTNaPCcvJSH/+fL97+F2UzljRTE1MzDq287Pxr1+Y03/8ePx4NGfnpxiTD1xTjVZQTNFOTF5X0p1Ti+BUSxWOydMNSb87N1fSTouLCr359f049TSycKkbj0+ODJ4UTHb1dBjT0FDNi756dqKiIeDWDI3LypHMyb/+O91XEjcOFNlSzliRzVPPDBhX111X01vWklrTjlJOjCGWTLEu7TTjJjZTGR8VzmHgX1rRCfdLUsyMC/QoKhHMCXMtbjOxb2Tk5N+e3h2cW2ZkozAv7/JwrxtaWeBWjrLv8HOra6ncD7XX3PaQlyupp/UdobXbX7TgY60rKaso5qtdUJBPjxVUU2qqajRlqDTubbYeYfdOFTYSmCbb0jZY3acmYUNAAAACXRSTlMgtwDvIL+/sK8iyrfPAAAnxElEQVR42lSVv4qjUBjFs/9gi3mIYbqwRbaT+wCKRquAppI1JJAxJOE2aQwZi5VbGGwUQkCm0VeYaCdbDT7AVCnyAmnnAfZ818nCfrnXSEbmfr97zrn2Pvc+/by76/fv+pM+DdxjYN5P7nCz3z8cFUVhGuOWblmmZencYrqmKpqmMPwikraN4wqjGC8cIzyHXuh5RnjyjMgxnPUYf8qSzDTNkpWMKWw0YpbFuS/8JLlQ1XWMqxAm56U+YlhtqCjqcKiiFBWPM2aZWR0vfz09beer/HB4vx4OTZM2eZ7m+XazmX//+rnX+3E/uf9oHq3T7Ooevw0mg/3gh6ISChuVaJzrJme6rmmaqjIU95Osbau4qpbVeuHYjhGF3smI7OgU2o7jFFUV15c28y3LQptUjPhN3/R9kV3oU18uWeb7vslBChBNAYgsRS5cljxJYgL5s1vladNcD831vUkPaZ4G6WqTb2dfvvY+3Rrv374nN1FA1R8MhkdVY4oGTUpeggQDy2lUIDKxRla3dRUXy7VzjiLvbIeGe7KjyAXIdD0FSJu16FLXoYaqAKUsQWKavhBZhm0AxgUc0KzEE0QBNTpBgALyEttVS5DXeQpB3t8PTZ4C5BDkm9kmCFbzbz1sPfolK91/8Mhvqc1kD2vBWUwHCPlBR+fYNRJkqDAFmnMh2gQgdTV+mp7XkRNGrucahocrfLWYFnEctyLzO3wFjaFoQ/wbCD4iEyDtFBn9Azmq3eNwlkjq5eOvl9fXXQpTNe+HvDnAXkEOQeZzGO5LDxT/aXLTgyYE2feHmqJqqgZJdB3+gC/IWcSi4dYUIslgrWUxXq+ntuuCwHE940RAxnm9hiBx6ycC4UKbnUtHcBmHIiiRJCJLxIWMZVlAJUUAgYyARZEkeDbJ4mr8+/czdr+5wlnkKlzS2TafBavdJt/1KA2dGJRvzBsRLAY99oPjwxC5HsFMpU4oujQWFtAgjWkmCRapq2U8nS7WZ9txT+jfcW3DC92zM10WcXupsd/ocqRRj7I3pnMuUQQqE8IXpgkSRtYaqUPUEVORMYEinbNenl93eY54HFCgQNJzJH213a52W4DcjNSF4naRPFBkPzwqlAjYQmdwOS8tDgaNQol2fAESGAsJoUMrghoRnVo2hu2cpwh73V4SgOjS/tL3jNzFzY/E41+AByCWjAiKMKQiSBQtw/02JpA/z7sAnroSBMyFiMBXwXwzD2abXidGR9HJcss5iQKQPXZG6eKuc6ZDB3mOajABBDItP4uhSPX4NF0bjgFBPNvFjDzXRtaLChnJsoSb1CQ5RiExS2ahOCJPW4EPog7vdeYDAkAIRT5LEWmRwcffr7tVkB+uiAeiHuDwRcxns/l2i5j0YKuubgGRlwlo7if0Xhnuj9gZDf9TR7oRETQEHImmcdPn0loFQjJdLGy4avf2Fp1OkR26jmNMi3FV4zUCRRiTgnTtMcQd0nKTREkQew6l4dwPQQhElSQoxrlo4+X46WU3mwdNmgIkuAKiybf5BiSbzdss71HrnRA3kD4N3OOn/b7/8PADXlChAKMXACadLABTyVoW2mjlG7EoFgt07p3D0DbsMDy5rnM+F1CkBokwcXSrFJEuwGykW3R0AETIyHPyHlkLKBDk2GkiORiy3lbF49Pz23xFh1Z6bQJw5Kt0lgarzXa+w0uxh6h/5OHjCxCDf0ATGRLyFkAwAMJ1eEyDSBodxn85LXvepqEoDHclURST0Dqxu6WyU0sZ7LFbBtRUYibqkAlLV0RcxHIXXwUGJBY2IqFKEQsMbEwV2Tr3N1RdGFjgFzBU4j3HOSjhJqDkdRTZyeA8ec6H8W9eT2ntPhsOxzfj0WjEkxdLBHaeA+TjHNOXjWBpL7YcyhJKeC8iaCDAvMDQGsgaIScyt9CI1OvD15fUIe+vSAisvKMVosFRWJtbo8upxTIYRIBwQu9vsdkvMM8ZhJxQRcEJLvHRI5pab+bT6zmG4xiL/Qntw9Grs8fAOEPH3AzZyNMpSgfTgn8jkZDcTzgGqE08GqC+sEUGA1kixHHBGA95ZmGvf/388vKDNRDy82rGI8sUGmOrMBqvfKb2VoTcfvlecXNvbWqUuueFcRwE/TSKus1ms/onuOh2u1HaD4IgjkNOLAk9pC7hsxrHvQ3dIww60f5hdtQ78RuNdrvdoPy4y4tCgcKomS4sGxGYW2BsBVIXkKCT4mcTxypJ1AcISCR04ZIISJK4t6F7xEH6YP/waBXkvHqnbZHbYpZPco0ekVZHvlXWg/yFUv5viYCIEQJxjTCJpF8GZw4KCxGQyuJASLqAHDBIyeH7/i9ToENwaKPJyDGBcEdUkB2NpClAVkoLWQYBK8MSHXsKGYVxvMTbZCShyoo7AMlKEJAwB9LSSpnJROXWaozfpWwCqfwXpJO6pQUOJqGIMfkm6osUilNaIgRZtEizKiBlfIpRWmmrrJ5otSfjit52N9KBEKe0uLaiNIULpmi1sh6StVpVRhEp/+qRmoBQrx/c94WDY3NtDUDUxGBqgWQHI4iAhALiNjuTiIws651yiKW6LIXmFmGsMZLUZWitATG5MirnNqGnXyE53saICxKtBUFKQlD455LTXgYpSyQCIhju0GIQ31/mOMFCzFFc6BNjYYQ3ItNUtlEipeVtNCIkmAEoqt+kmb9LG2EcxrOmIbSpJVE6lYY2FQJWt8BVnPwX2lE4uClDToTq9SDo0umyZIhCtgopaCaHYhviYA46ZbkMgSzG2GwOlaRrn+/73jd3evmh7QO5wzjIh+d5vt/3xcTCwsrKO9LXr0BZnp9Lvb1DEg06IrrOIOyIxMhkTG3LMXYczbA1R+yRGR2ZnKyp0YKYYxkb4MXKu/fHUkCBKXdIwBENOiIrMpq+CeYQJDj5ampWNUzNBAgwWPdxJMrvRYB4jvD0DZDgS+JYAMbmD1eE4idhEM4WO+Lv+jxAYm60GASZyhqqllWzWbnZN9zr+r858nqyIxBiJTgI47sroIwj8ToyYWjBkcTID3IEx0XURDNMwwhtoCFg+XcQdNEDGcuRFBzAOHcFlONjl4Qbj46AJOp3hLsukiXWCCzxKbOFuwiWoshWCBDQmGhdNK4eHSj5R4/yykGAgk8oza5IFkdrPEf8KTgKwPjmCiTSk2WewhMcWbzd9YzHQMlKqGbWdEw6zAPEZQg6kleqE0HYkT/KAGWfDIIv5pPpxAviAMahFEjOJUla7hO2xBtbvEU4We7QikkKV4msTXVHUTRMLQ4W6cEgj6u/XEdevXo2BiQiCwKO7XPCOCL5SChcTCJXicQIVISMZUeYA9HCqdFQtwzHtLWQ9GIWyEURMNXiH/pcNPv4Ao/o46ti48nzYvtXsdkgR1rtZrcFina71m72iAPvy9WV958KqXa/lyYMJuGauIuRW+I5wiCvAcIVAUkslsFbCotdzdKt3TQdGS3YApopIE2lGg4XlQY+naUlZalJj4PH+P2T54re6ShKP5Wq6vrAsqqRSEWv6JbVjERaFatiWY3NwvVpqV6qrx/lcjkmCYQr6Aif4bkihAAQVhz1oL5ju9voiKSgfN0PZHiFnzpX0b7SdUGUxsuWrqdSA6sVubbyALHacy3dikSaVju9qp+mP9Tr84c3pS44mIQt4XAB5G5HuCJvMXwliGtHPB7PxPGytZ0djF9TNR2xR7gjG/cCKYbDDTyieDAI/lZHoelba7WsCoGgH4Rl6WhIr79+XeqjH6d1YEiUW5aIbUKW8NhijboukoVoAYQwWMRhUNOzpig7681DQMJBkFbeggQI2p63QJWXI6tdkmIQvyUcLgKBJcGKcLKwRe6AqCJYGkhMW4K8kTT/CVKxrmsRP0jNGqwD5FOhV2rekHKuPBAMLrJEtERukuDtEDPLrTpzsCOGStIMx7FDvA9ngVyEw/1x0XpOIGsChBhqLsgcgcxX9NrTlfZwrVUaHh7lCpKCs8VbkUi4JXBkbLJ4rWdiDJGOp9PmlqkZdNRS7XJo9F+RqWXvKp1GcWlGR3Sr2xtIEExeAmlbg17Xqm/vDkuXN706yu63xANB32EJg3gzCyAyWWgIL8MRSTptQ1vY7qZNIBtu2fGaDAJLlCW/I9FFduT1COR3BTO3YtXIEQmy3MUYHta2dzcv0ZBhbF9KWMIlkS2hcHHbvXUokyUNSQjR/JV+QI6jOqppl8t2+Yw6whxwZPKp8epgynVkzT0ztmoRV7BE3NETqxGs9d3Do0J8mxgYZQTClrylbN1xxLvlxuXlkEgECDiSySQYbNsExl55L0QEniMPOP8u8ik+cB0hCpeDuk4gH3O5/f2fLCLxZ8sdXMgWO3J7ZsEQ724IEFIScsrQWfns5ORsLyTuIrNApl5HwHH7FI8lchvkM0AExxchgZJjELZEgPgcifLJF4ZIjgUmYRCbIMrlk5O9vRNR9tnRCqIsTr1XzRGHH+Qv6ebP4kQURXFrY/yTiEl0QRfD+gdSmIBFCkERZArLTHaLIZE0IRsCsrAoSIbdDOxio62t2NgbKwtbP4GgiLUiqN/Ac++7L/fFN04cvaviWrj745xz731vZsEBDFsgub8LkicsCZFw3LkBpzrrFDBQTGJCQiCzt7NX09lsit8zgOBDTu15rOUrAh20LEgJY+Th4YPdZ3vguCtFKM8Odh/sP3zyZtM2LoD4irCzWJCaKUtSp5rO3r6dTWevZqPeVNb4/7PWzXQQcOBsCJB9gBDHbSlisSQ7m63FWRFpt4ro4ZAFIY71dUOiIBCjN50Np1089nNXlDzWKlJlKSLOMiC7B3uG4xYVo7zY3hOSG62tLQNyTtqWRoScVW+cqgCDS0hckGmvN5lMR7I00kS8kqdrWQ45V4HDB2lQRFqICIPcFQwiuQWUbSY5JJJ13ualbbnT8PpFXF6zIICwKCwJgzDFsDsYTocjKCKT5EJ+awFEFNlIUYSuSLco64jIwTuAAOE8F7FAFCF5tNOq3Wn8BlJ0nVVhEKkFSLMOjHF3OBwMJmQtHiRXVmREzaW3Wv7Nr6dIu2azbkDO22J/CQliQpKYtLvOOmmcVTeCtFAiiVirOe0Nh/TwcjIZ9/iC7h8G4lXMQ//C1I+IZB3OAggzKAqTcExaFQFRRczlNfcsAmlJsSSiSLMHCryiMh4OxhPefleGXQVRSfyHCuU0EJrrUERBPBKYa7N2B2mX/us7qwY9bLG3BGQ4Hoy7z/GEf9DrLk6IFJRcWRdFLEjZ5XDGoWRdnaXFJAe7+wBZbzfRthREnSVRN/ffTKIg3fEYL/M8H3Qno9ERoeCk5FHEGeyrQZ4JiEfCkiAl66eaBQui01BBwMHlKNIECOSAKDBWd2JAcmbEH+zpIGdKDOJnXd21zZJQSCwIkVxdXJ9wzxJBqIwiHPbm2hiSAIWaFiuClOTLiN7FS9O6dvl0+jzkpiXd9/Z5D4QlYW+VXJAiC+JEpOWCWEUAgjdE8Col3q+yIMey5wgqax6qIv6mZRcUzbrnLXRggNQBYo5W0MNxVr3NEbmhINR+GwzyePwaikCS0Sjke617EpN/BAHHRvbKuLcNQdRaXkhcRVBFdRZFhHvWMgg7aw1iYIrAV4NRgMmuIVnBcTQDRAVREmMtBTmfBVKvGpAicyw5KxWkSSDPEfcp6RGGuqJAlLwZcUEu/wFkJxPklgV52jAgZCsDclbHuoK0FKS6ttZlVw3CsN8JZCDmbL96rBKQ054i5sRek51xtSIMIs8WzBCxzlpWBO23IiCF5+PuqDeCIEHQgSIa95UZ0XKX+D+DtEu0ovwdSEEUsVEXZyEiriIMgqyTswoYIyEaVtAJggQgQuGBZJHozuiD2JL+S23rAN5KA9H2q12ryCCXrLMqdmF0x0hdQCbACMMgSmAtAOScI/483EgF4ZDUzERkECXxQFoVgJhdq6g9q2qdReVlvVAYICFBGNDboAl3LRv3bEX8VcuLiO+tWoa3wCGTnVeUjbPmrlGj3lwC4Y/1RdYLa+XJYBDCV1Gn04k17HkyUqSippUJotuW8VbGrvX0TLV8jUFkiFgQdhZIhMXNOhTBsoh4QJIkNgcrHyTbW0U9VklElMOTZOcR9S31lr/9StMiEBFEpmEDgrgkyyBlNN8kIEn68wQgWtnWAkt+EJXEI9HzCHZGyfoJHSJl6ywuF6TdEEUmYQhr9Tv9KJL26ymSLUnxN5DTCuLHHZJQSpREz+3gMII8PdMsm4ig9N0/Oa0rCWfdAQEJ/XxHFEd9br/5j7rEsQCRBSVbEjSubeRdy1yk0JldBJGsG0G4ZzUYRMo5HzYWGUHM+0k0j2ICQdxXT/aMjDhZr/rX2DzdZZZw6xIM4TAHXRaEncVTXZ1VqaWA8O5LX2oUJhF8Ne/HcSw3jbQCX8jTtbT7qiLKAZKyrMB8JYS8CwlQuO4SBjhgLPReCMI9y1185R4IILaAISCiSIBCPvpJv5/IQMzZtXT3vXRds15VEJWEU2IusiknYEERhrlpPMStVkmuUE5etVE/u3G6QD1LneWDVKFIEAVRNI8Rkci8eCY9OI8iujIqiGMt/rCS0FQUkj1iIQpgmNs5MpYIIo695PQsVxHmcEHCD1FMNe8HgWTkgpeRTJbU7ltFqSb60IrMRSTICVAOCAZ/AmPf3DK2MUNYEAaBIG7PoqJvX+8Z7fkQX+wD/VjaPJnPSRH4ahGTPHPEBSl71sK/OClhElxwHe4TC9WDB4Tx5NEmc9inPL8NkTZHpGZKBeGrhyaRBDRBomQeh0nH7lpceeaIghhB1FoadkNCkmy1QPIIKGDhOiQM3JWWwGEeIOp6oiA1BalZELkwJZIOlpM59OiEAUA460yyQpGVIOqssinJO5O8bN3YAcqTh1ygAEZrHX43HGaGeM5iW0k5d/ENAcHa24npZx/5PMJBzwui8xDnXB+EUXSWCMnmDlhM7QCD5GjyM3YYS44i6izqWa4i8ndYi0CYpB9z7407nYSOuvzBNCvD7iui3dfPiJJQTBjlxqYp7OOEYd+ouaQcuvie0oRowVoE0qg3CCRByufgCMKQuxZVKsjXT58+Lz75gs+yQVCqCEoal5AQysuXZkC/rG217xg5bhKHHNZl8eUhAmfpN19aBiFJ8Ksfz0kTjMNwIg9DU4+6Pz8eR70XlO/02cfvv4F43VdIbFlJGEXrDj9cJznwxgMFhECcIxVv8PoAFOWAVNpEgv9yju2kg50xGE3ME6v0yf79uKmPTPJNPvuxtKG4ivggriSMYqsBDLhKXzHV+yw/6iUpBTnVhrlQcRTM+0kchrhJMV0r7dHbL2LuH6aJMIzjeNcaPYOKgxKH4p+YKmCCxEQTdTK6C+NpulyIYXC3DrixuOHCwEITp5rASNLJpZsxQNIQmIwQCbDhnxi/79P35Xnv3qMniz7Uao0xfPw9z/tee3du8F2vn9jaIxNerUTRzkZ5o83vIekGgmQsC4GiEG05KFqigJFyAHEfXeuoayCm3CtZt0yh+Pxm/A2rb33KzkjO0e9BFH0zk9GJOt1ANhCsSyTeiBzVWukV2KPYPwXjoTC8y8llRLKjbu/fcRhJxEyJJPL6GYPOPXbjtJaOemZDJIMV8/NqFPHciXZkzjtRO/WplgdRSbhwaYlC0uhOh5lzIFR31lNvqWwCFBJX0ltIBPLmMXlMjk/W7bGWfD1PQcw/vvm5HbXJhQYTyE4UqYPOckcoAxkIv+VmhFKEY9xycdg0KHdm3V0ioBCpFKSvC1kyiZgRmarJsOdei4KgcyAjv8vAAJGNZNVC9rdPe63Vn5eIPCidH71hyW8rcaR3QwLJQigfQiSPbn5cYiPhJu1JTiL6H2KTiF9msjsdmZQQ0l7RY0YgAyHEbu5ayiAOO+VuzO1pYgOR97j2rSGQwKGQR69fdzsrlkRwOEkaskUYVGfFQsoBxB/2gQASFgzrYDw0Ds4bnirre0PtLIXkRnJ+Se6kjccn+ey0pLd/ZxPZIYyVfWLZz4ccbF8c217RGVFJsYMb3uxqJXlQepZKr3UogvDWcHG8PjlZn6x1d/bnBsJzdtXaNbkwKlu5kP3O8rX2noMgCRzBzWNH9ZVAXGfJPbl+ICFEJECWFh+Px9OcDI3j0nNRGMZ1hdhlV7b0fbMMb8mMcOC447XW6t6Pn8umtfoNJJwRHj7F3pc46M05BUMcMiLaWeLID4TCYSCLi0/rT6e4OmgirpWeY7huClAWsiXJmN4qsy3KWyvZTyzkeydal+W3v38ghFDhpPuBaB4CsZ11zvtcTtesQCKQZ63xFmlMxWbVun4Sw0kgcFIQ9vJ1+/NBuSyvZGf/Jn0gw97uLAMxiRwFOZuBXLUbCBANJD0iQHTUBdK19OHIQBZbi/FkfSrhBHWtxP8igIUavj6TnZF291irvXXCCNrb5fU2/XYI+fFzde9w+VVIryHhhld367QGoiNiT+RmIMLo4ykrMZdlsvJS5loUFOIYnklB9HiXQOgqQFK7ZQf5Hq18p7cEQiLqyJfAMBBGxIOc8q82cgeMOiLKyIW8nuPazFpcT8ikJOMhkhlaK51JO6JW7bupXfOqs18+hLR/Xxxj2oEUz4gm4s+6BqJX/unia/OA4cqTyFHKx8VpIqlNJFNxAoSaIRAiCT7v3V7f3iq74tWGdrR/rKWrVpGDReuCv2jp6e7UVTQKcY4QgoSrfqfjOJ6YSCbiEoph82UqheArrDzIDU0ESIFEF630zbl21t2IKEQdVLq3gLTkqsYEik0ExZCDGIOHwBT8wj/6vX3OQCiBDPSChNuIg3TfiwzeykCovh6Q1hxX/dZR1BIgtBWNVRkafjcjURydhDEGEFb+Y8wIkSjEn3V7xZyMyBk/kTREHQilteQCzVoSl4ZlOoYqw5XKcEDo0VmayI1+CylKhLqagjDtLhDvijmBwFBILoUX3G0xTW8lNYakJA4UlWqlUu5dBOLPyD2biJsRKOoohuCwECo76ySCowAiibD48mBGcFAVqhjiOQTyQBJxkGKJQAZ1Z09B9HJll0jooPxE5rjdwsx6EtNaQ9ZBIu+KIDwUYlvrtk1EIuG5EKKJeMMuI6KzbiFUb8jc4jSSiRdYSkNDQxUeVSRPysebkXv20yAHwUEVQ/RdlR+IQAY8yNviROa434J9ncuxYyASh/lRmAjNpRA3IwQCxAXyV62lG4m/i+isK6SvJ+Rri0jk8ng2xEq1WpGvkeoxZkQh7Ic2EerYECTerN8QiCSCAwiBhGUdowIhEdNbL5ISSQxXTVVGqgUQKCnIPW8f0SqAXFWI3r9zSiE4BOICCR0iEcir1lpLhoTrZg2kymNkZKQ6UsTQSE7LsLNqyaJ1DIhOOw4qfYCiEBz5EGr0zBkeJhGO4+uJWXxfCMQwKkgykFO9li1JRCBX8yBnqaMgF1IQHZExu2j1vZXGco7zIQQGkNG+tdZaszkfc0tPMluSnpJAgOg2wVNOIC4St2ppIupQSQCxQ+IgSHIXrTNvTeGwECiBZBQIM9Jstqbn54mEGSESgbxkRvwwQoh8OapNJICEnzMWt5YuWpeA4PAgOJj/FAMIZSBrrebC9HwjoUooTBovXyI5/C5RbKxG/7x2tr98ub/53tbm/bCWLwvFQta+0lpNhr0xMVuSpgJh6tDBYyf6D/XLg2xu5kq8TIDgmE7mG7MvGiUUZBLOSPRfKgUJGVQKsraw0GREGrMNINRIBmI/Uvz3tfM3iVAgujOy1gTSaCSzEyUAEKS5FEIiG/+ht37pjKgjnBFKAqG1Fhbm51l/E5MIq5WT9NgLXWX3kbusWnbZ0lWrn88e+UFlV19Zfgf9ZcsdSbP+9Q+wH/I9ypol3ykrVqb8xYtEFpoLTEhjdhaIZbzsAaHyILKP3GX5zUBAnHUQKCFkUDcSD3KD1ddBRgWSL1GIm5HEQOzqayw9IZbiDlFsIgK5EyQiBUeeA4jd2tUh+/rdFOSIQK7wcAwSwdFsMiON2Q8CQSKDUuigLER29gcPgOTsiNJY0l5ef/Eb53Jbi79LOktOgl4xhKMh4nB1Za250PzEkNBaZh9x61Zha4WQMYH8oeSOeZsGgzCOZ2ViCVmQO1AbIRXBVBSJDCD2SP4CDHwGPgpSJOSFhTFSxg4eGNhiIUWqRMbGabNlYGHlf/cenO3XwerJ8UAl5F+fe+y2qUoiInGHQWC4gy0DMm5BTGKQKYlMMnMopIfBOEQTUQcQ/VJLdovjXh1xiJbEHRbIeIxDRgim00SQBAgTHNZ1EgHijgFIln39GTaLQx+IoSSc71v2WQPSLchYBlBzsI0F0nqnB45V5NtZmlki6shihkE4ZYx0fQNCV4s8/Mk+DGn8/jJlJ5ImhLNCjGESDrexW+cOaQQSup7RdRgGyXohpmB4jkgi1QIJZVdI3JHlp92NK6KOaCJe9iAB4lcdJDGE1XplEEYD6amIOLJnxz3XX275g12TyXG3XWat+crzULu+WgWI5NFdrdvD7YPdZ5f0dMSeiFb2ILFLNkckMYglYg6BEAiQBIiNOup9DWR/nPzaTq7rZ+WhbEF4jmxwEMmiEogoOF20IL93Dx7c3AjiBORhG4LDIe4AwmETJWIQuqabBcQdMpMSSFqXk2eHyXGfZftfbQgVAQJDOmJ5dCHL70OQWYDYExEIqQxB1KEQJI1Azvl0WNcdwpR1mqY/5JUtlxFEN6uqqi+6WvodomZyAqIMnxbkjScCI3Yw9pS3f0MikFdAkFgg8r9M25uVdSFpL2Sz+VKtZLOAMFCIpB/igrjtbxRyOTVJzHCJl/1SV8sgFki4+Z6C1GWaHtLjMokg3LSqNZEs8hWJAAmBtMv+g7L/trWKE3EIkXQhToEBRE4OuWwmwnmmN182KwHyrgVJyjpJ0+11+nmbXm+TpL5rMJJMvkJZr4WxKEYXL3CA6CRCHIdPOxj/g8xOQLpjOlsthyDBAYTNEkgGpOUIkLt6W9+lyX63W2JtSFgtyaNYFCsSCT/5hWIQz+RWzgOQ1wqZ9kJ8HIJDy+5/EFvenSCQR2cGkePvlTIpk5SJzNu3cvaPCYREiqpYVcXIun7R/2Q/2REks38Qi8T3Kh7vuq6WlYR5jCPcs+Qa3+n4tTIAm6Mf4Eg4cFRrGPl8noe7FokwT2KFS6KyG4QZhjAOea2rZRBZrL+blcQOxh3RaCDrfFXkeU4izEWYGAJjEHLpuzUM8URmwcFmnRuEQMK4o0vJWpANkCoXRl5IIrzNI5RuIiA4hiBWkmm3IM9Prta5QZBYIHrPAvKxAUmGB0hFIGFGyuDoTeQeEJNMjfHcju48Z7WsIzN6bs/UaYC8s+k6zijKWT/kan2FYp7PRy+fBIZwehSchiHTMDCmQaHzVI6uowmRkapLIN8kEIc0ED4R5L1U5KrAkWvZX/ISSlT2Pkbzye4Qk/ByCD+wRgKnJbGO0HZ1WCAtyEcgMcMkTSKO/KqYF9Tkw8jeC6UhHUiEiBN5w9hq2XggHgsrFq0WECSPxfFvs/50dj8rTkNRHMdHF/oeCm4UXSmCG32BJiVZ5YaAF5JtQIQ8QRfBblx0V9IsBcHQdpXZCOlCCFpaizs3LnwBn8DvOUmM//+d3txpOzrmM79zmoEp+PiPEGpkCOTtkppaEjFnN68jAUJd+UMeo2OEaCJDJFTPuM3iGGpg6Kfu3QOiEoXoqI8QHI8fA1HFT+pr4etl31nGALmCQ+v6zX9MRCXfQm5ogVDKYBkTGSA4RkjXWZx/56B+7SAITaODZK+XdW2MdWyiv2fnGIZ9ZGgkv5wRJCNkzGOAjNVBbvw8kTviuI3jryFKGYo46jSxqUlTgVy5RSjXZeA//uYCMtavEsFxd3R8T0GhjmsKQaB56IiMnSUOLXH8uarXy4pAjLUkInEA0dZ60Z2lMtj/vbX6+fjWMUh65HeQcUQ6yT84PtVVkiYpEL9MgYiCiae7nn0UhdRfJYLjR8ioGClaI+SqQO73E8KI/CfkwaGqpK+sSa0paS2qu5Ag+SCn2afB9k+J4Lj7G0jvGCBIOL6C4Ph7CIx3h6quU/IgEFP60lqyKDTMiwbExpO3urqpb7GTd3BST4Qtv4U4Pz9fzeeb3akwJkmX07qup5TDj9QOtd9zZ793eLBe8/PperKeLifsDs/mm/l8Nd/N57uT45hllUXt9vlTqedPn2+p1osi18viKMuyuMpkd6Ms8rix2tbz5DN1VSeWAfH90pZ9awmAdwMy9wJSxM1BAgPALVkPxXXlC2S1ernJfSPFFXaJg7OfTvYKUcRkvZ5orSk04ICcMGhtTrljqirbtpz98+cCYd962zbK3EgB3LKKw40z13MhgEAZxTGNJQMSimRWnvGmTJ12zp+dxaEyVl84sEiJBi4QHO+f4dgUvjUJtSQSA8MRByvcUxOHi+56v6Te4BAVxJ1CVitJxHfqquLk2q0QyINE2NxIypUkdHl4vCh2QSjFCwRSp8yHL4xZeoZDShTXh3CuszSVIRG6SuqhJCOhkQiQ89XLl0BmvIondZLWiTETLE7ohCy5yQOJg6TWEpj68t1uN38lDCCpTas4duksDYMDBr2TZdpacay5uJJHEHkkIQptOwKRRGwhDgtEFCohEijyEdzgeNJRKNkh4ehb69kzWiv36VCTaHFdspa2IpQwDylnMsEx5cbOJmnlp5ME8qrvLD9NmjhqCQSEYoiEBtp6XuZmZCCdVWEJgpiECKT10MRBU1UpZa0kMtPWonTr3ickHp16OKwukEHCIRAcQHCQyKxg1PjWJDWboWUdnyg6CAFMGZQpJQ6EoSMMgWhn5UCqxnUP6mDjkOZyPb7rFOeficXVbDwILRCcbtDUNV1QWlsUMysQBKrQjVKOxsJNjNwgDBSGvYcwIzhkRtIyFQkKvciGoe87eciSSBwzmZrlEkdowpza0FirV6/IZMcrhU2q4Bi1C20qtgN9RT5RC4NTj5s4CBriwBG5gfC8oA0iINpZqZU8ilkpiQwOHRKNREoZHUUdsguD+tJaSAq+Cu2BRA5jfeuEvvHzPBSHUChjHCo8Oaf8pA4gUHjJKwQSHNvD4ikWtu1isT1sdRYYCoIImqCJ44DHgecdeZZbK7ImqXCUM59IJJFHI2RIBARr7C2FdAVEpJLIe2YEyKYoGDbyoF0JxAjEhqRCe3E43IRhHBv6p83mhGOuDhqTPG1aN8HhsFjgeMqO5XBoj5FMdMP3vYlRxBpIw5NUjBIHIyKdNSuKIi8KErnAuepwY2Fx7lA4voxOHwYWBfFHCaQfdl60NhJsyniQMxBGL/QFwW59GHywPmEQ0OaEZMhj9UouQvzVJjgeDwsoFBY0h+Nx6x6DmNN1gyAKYMg9nqLEwlONtBYOIDOpy2dnjzjdHqCWsasA9hPCGkrMMiTddYRIcJSMSc2XpbWQaCDaXBwhrpznuH/KYeCYw5BAVhtaqywFogqN5XA4ynKPR7cJICBoYtcFwOZyz/P4AKkqkzJFUOgyZ/Lfmkvnsyi9wotEbRSbZNEXIm2uHvJeryNI0jKhfGNZeYgl9082zy1NJghBaRjdBQTF+TkQfc0rk6YhEUF0GnnA1nKuDAeracC49BmzBCYSVeA2TZqW5YwJ4d83l84ufgaHzUd8K5YZvgAAAABJRU5ErkJggg==", "public": true}]}