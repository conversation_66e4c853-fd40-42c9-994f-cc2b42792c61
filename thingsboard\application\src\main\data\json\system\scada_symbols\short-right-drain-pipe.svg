<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="200" height="200" fill="none" version="1.1" viewBox="0 0 200 200">
 <tb:metadata><![CDATA[{
  "title": "Short right drain pipe",
  "description": "Short right drain pipe with configurable fluid and leak visualizations.",
  "searchTags": [
    "pipe",
    "drain"
  ],
  "widgetSizeX": 1,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "fluid-background",
      "stateRenderFunction": "var color = ctx.properties.fluidColor;\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "leak",
      "stateRenderFunction": "var leak = ctx.values.leak;\nif (leak) {\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "pipe-background",
      "stateRenderFunction": "var color = ctx.properties.pipeColor;\nelement.attr({fill: color});",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "openInSeparateDialog": false,
        "openInPopover": false
      }
    },
    {
      "id": "leak",
      "name": "{i18n:scada.symbol.leak}",
      "hint": "{i18n:scada.symbol.leak-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.leak-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    }
  ],
  "properties": [
    {
      "id": "fluidColor",
      "name": "{i18n:scada.symbol.fluid-color}",
      "type": "color",
      "default": "#518DA0",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "pipeColor",
      "name": "{i18n:scada.symbol.pipe-color}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata>
 <g clip-path="url(#clip0_1924_305417)" tb:tag="clickArea">
  <rect x="14" y="64" width="86" height="72" fill="#fff" tb:tag="pipe-background"/>
  <rect x="14" y="64" width="86" height="72" fill="url(#paint0_linear_1924_305417)"/>
  <rect x="15.5" y="65.5" width="83" height="69" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
  <rect x="1.5" y="51.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/>
  <ellipse transform="rotate(-90 100 100)" cx="100" cy="100" rx="36" ry="4" fill="#5D5C5C"/>
  <path d="m123 94.5c-4-9.2-20-22.333-24-24.5 0 0-1.4995 18.101-1.4995 30.5 0 12.399 1.4995 33 1.4995 33 0 6.5-1.5 9-3.9998 14.5-5.0887 11.196-3.4999 10.5-12.5 13.5s-24 2.5-21.5 11.5 32-8 34-4-16 17-10.5 21.5 17.5-10.5 21.5-10.5 4.5 10.5 10 10.5 7-11.5 16-10.5 12 13 18 17 14-2 14.5-11-20.5-7-20.5-13 39 1.5 45.5-4 0.5-6.5-6.5-10.5-38 5.5-39 0 16.5 0 15-5.5-16.5-2-23-10.5-8-36.5-13-48z" fill="#518DA0" tb:tag="fluid-background"/>
  <g style="display:none" tb:tag="leak">
   <path d="m89.054 127.83c-0.1006-0.428-0.1587-0.97-0.3378-1.362-0.3378-1.362-1.0252-2.66-2.0757-3.507-0.5286-0.327-1.1357-0.619-1.707-0.832-1.521-0.632-2.9637-1.3-4.4064-1.969-1.2499-0.661-2.6926-1.329-4.0191-0.913-0.0068 0.193-0.0494 0.307-0.0562 0.5-2.3344-0.546-4.8887-0.327-7.228 0.362-1.5977 0.446-3.2089 1.276-4.8646 1.179-0.5781-0.02-1.1135-0.155-1.6131-0.211-0.885-0.07-1.7341-0.061-2.5901 0.14-0.6206 0.094-1.2481 0.381-1.8329 0.553-1.6761 0.481-3.4238 0.806-5.2364 0.78l1.5396-0.987s0.1278-0.343 0.0852-0.229c0.1501 0.121 0.3854 0.014 0.5064-0.136-0.5644-0.406-1.1289-0.812-1.6576-1.139-2.7506-1.872-5.5798-3.708-8.5725-5.28 3.0131 0.994 5.9342 2.409 8.7702 4.052 0.6429 0.37 1.2073 0.776 1.9713 0.996 1.7922 0.603 3.7461-0.1 5.55-0.924l0.0785-0.035c0.1568-0.072 0.3921-0.18 0.3989-0.372 0.0852-0.229-0.1365-0.507-0.3224-0.706-1.0079-0.962-1.9373-1.959-2.8668-2.956-1.1511-1.276-2.067-2.658-3.2966-3.898-1.0796-1.118-2.2801-2.087-3.4449-2.976-0.5644-0.406-1.0505-0.848-1.7002-1.025-0.6855-0.256-1.3778-0.319-2.1127-0.267-0.9276 0.044-1.7767 0.053-2.6685 0.176-5.1153 0.63-13.66-5.451-18.688-4.008-1.2766 0.273-2.7759 0-1.2764-1.727 2.0004-1.4995 1.9997-1.9996 1.9997-4.9996-0.7572-0.4125 2-3.5 2-5 0.5-1-0.3767-0.9415-1.069-1.0045-1.3845-0.1259-2.8118-0.1376-4.0308 0.5139 0.7126-0.5151 1.497-0.8733 2.2387-1.1172-0.1859-0.1995-0.4077-0.4774-0.5935-0.6769-1.5588-1.7526-3.0392-3.5411-4.5621-5.2153 0.8714 0.4552 1.6576 1.1388 2.3653 1.8583 0.9295 0.9973 1.581 2.2164 2.7526 2.9136 1.3216 0.8183 2.9841 0.7225 4.5546 1.048 2.4912 0.4736-3.4917 6.84-1.2203 8.0776 2.2714 1.2375 3.9327 2.7911 7.8089 4.5241 2.7935 1.249 7.3531 3.179 9.6789 2.875 0.7349-0.051 1.5124-0.216 2.2541-0.46 0.5064-0.137 0.977-0.352 1.526-0.603 0.6275-0.286 1.1765-0.537 1.8398-0.745 0.3921-0.179 0.8201-0.28 1.2055-0.266 0.4638-0.022 0.9566 0.226 1.3351 0.433 1.3643 0.704 2.7285 1.408 4.0927 2.112-0.2285-0.085-0.5712-0.213-0.7997-0.299-1.8281-0.681-3.6203-1.285-5.4754-1.196-0.3854-0.013-0.8134 0.087-1.0913 0.309-0.0784 0.036-0.1568 0.072-0.121 0.15-0.3205 0.336-0.2693 1.071 0.1519 1.163 0.1723 0.585 0.73 1.183 1.1018 1.582 3.0595 2.963 6.1974 5.891 9.2995 8.739 0.336 0.321 0.6719 0.641 1.1648 0.89 0.4928 0.249 0.914 0.341 1.4136 0.397 3.6047 0.629 7.4448 1.15 11.025 0.273 0.0785-0.036 0.1569-0.072 0.2353-0.107 0 0-0.0358-0.079-0.0716-0.157 0.2711-0.029 0.4996 0.056 0.7707 0.027 1.8194-0.167 3.5943-1.262 5.098-2.328 1.2661-1.138 1.7877-1.768 1.8202-2.377 0.0368-0.691 0.1656-3.04-1.0539-4.04-0.5644-0.406-1.7596-0.38-1.9726-0.617-0.0752-0.084-0.2984-1.622 0.2454-0.372-0.6088-0.033-6.4278-2.328-10.667-2.478-0.3853-0.013-0.8491 9e-3 -1.2771 0.11-0.2711 0.029-0.5064 0.136-0.7059 0.322-0.0784 0.036-0.121 0.15-0.1636 0.264-0.0426 0.115 0.0716 0.157 0.1074 0.236 1.9819 2.886 4.5059 5.715 4.9191 9.318-0.15-0.122-0.1859-0.2-0.3359-0.321-0.0358-0.078-0.1501-0.121-0.2217-0.278-1.2587-1.51-2.1542-3.471-3.5764-4.717-0.6362-0.563-1.3507-1.09-1.7873-1.838-0.5441-0.984-0.6109-2.375-1.3544-3.173-0.3718-0.399-0.9004-0.727-1.3575-0.897-1.2567-0.469-2.6771-0.673-3.8913-1.256-0.6071-0.292-1.2073-0.776-1.8144-1.067-0.1143-0.043-0.1501-0.121-0.2643-0.164-1.3642-0.704-5.8232-4.0432-7.3647-4.0975 1.3488 0.0475 5.5435 3.2235 6.899 3.0775-1.2944-1.5885-2.5531-3.099-4.0044-4.6164 1.4291 1.0536 2.7371 2.2572 3.9241 3.6109 0.5935 0.6765 1.187 1.3535 1.9015 1.8805 1.3933 0.975 3.477 0.971 4.4521-0.422 0.121-0.15 0.2421-0.3 0.2489-0.493 0.4193-0.9496 0.3816-2.0698 0.3797-3.1117-0.1625-3.0539-0.446-5.9577-0.8011-9.0184-0.2681-2.2473-0.6146-4.4588-1.6243-6.4622-0.0717-0.1568-0.2218-0.2779-0.3786-0.2062 0.4057-0.5645 0.1976-1.2278-0.1674-1.8194-0.2866-0.6274-0.73-1.1832-1.095-1.7749-0.3292-0.5132-0.6583-1.0264-0.9875-1.5396-1.5742-2.4091-3.2269-4.7824-5.079-6.9698 1.1647 0.8899 2.2443 2.0083 3.0459 3.3485 0.6225 0.9479 1.0882 1.9675 1.7533 2.8012 0.701 0.9122 1.644 1.5242 2.345 2.4363 0.2933 0.4348 0.6225 0.948 0.9943 1.3469 0.155-1.1135 0.2315-2.1911 0.3865-3.3046 0.0871 0.8133 0.1742 1.6267 0.2613 2.44 0.0803 1.006 0.239 1.9762 0.3978 2.9464 0.5323 2.411 1.8781 4.7349 2.0609 7.2107l-0.0204 0.5781c-0.15-0.1211-0.2217-0.2779-0.3717-0.399-0.0427 0.1143 0.029 0.2711-0.0136 0.3854-0.1531 2.1553 0.1149 4.4026 0.504 6.4999 0.2817 1.862 0.635 3.8808 1.9004 5.1984 0.3718 0.399 0.822 0.762 1.1938 1.161 0.3717 0.399 0.5799 1.062 0.4452 1.598 2.5475-0.026 5.3825-0.521 7.3692 1.131 1.5366 1.288 3.6117 0.984 3.8349 2.523-0.5763-0.642 0.962-0.865 2.1472-0.191 1.1851 0.674 1.6866 6 0.7832 7.55-0.8609 1.436-2.2504 2.545-3.7338 3.033-0.0785 0.035-0.0785 0.035-0.1569 0.071 2.2356 1.159 4.5855 2.361 6.6933 3.863 0.6003 0.484 1.279 0.933 1.6798 1.603 0.4163 1.326 0.3978 2.946 0.3434 4.487z" fill="#5C5A5A"/>
   <path d="m83.058 107.1c-0.8133 0.088-1.2484 0.285-2.0481-0.013-1.5995-0.597-1.9619-1.241-3.4762-2.066-1.707-0.832-2.611-0.911-4.531-1.171-0.9992-0.113-1.9627-0.147-2.8902-0.102 0.2198-0.764-1.2619-1.965-1.8196-2.564-0.5576-0.598-1.2722-1.125-1.5161-1.8665-0.2798-0.8201-0.0958-1.6625-0.1044-2.5116-0.0445-0.9276-0.3601-1.8262-0.5973-2.7606-0.3959-1.9045-0.1353-3.8246 0.2395-5.702 0.0359 0.0784 0.0359 0.0784 0.0717 0.1569-0.1531 2.1553 0.1149 4.4026 0.504 6.4999 0.2817 1.8619 0.635 3.8808 1.9004 5.1989 0.3718 0.399 0.822 0.762 1.1938 1.161 0.3717 0.399 1.5107 1.472 1.3761 2.007 2.5474-0.026 4.3568-0.455 6.3435 1.197 1.5366 1.289 2.0832 1.186 3.9471 1.947 0.3001 0.242 0.8714 0.455 1.4068 0.589z" fill="#8B8B8B"/>
   <path d="m73.673 115.32c-0.1501-0.121-0.1859-0.2-0.336-0.321-0.0358-0.078-0.15-0.121-0.2217-0.278l-0.0358-0.078c-0.1723-0.585-0.3446-1.17-0.7096-1.762-0.2149-0.47-0.5441-0.983-0.8375-1.418-1.245-1.896-2.9607-3.577-3.7709-5.766 0.549-0.251 1.098-0.502 1.6402-0.56-0.2711 0.029-0.5064 0.137-0.7059 0.323-0.0784 0.035-0.121 0.15-0.1636 0.264s0.0716 0.157 0.1074 0.235c2.1746 2.893 4.6202 5.758 5.0334 9.361z" fill="#8B8B8B"/>
   <path d="m63.723 102.05c-0.6274 0.287-1.4407 0.374-2.1689 0.232-0.7281-0.141-1.4852-0.554-2.2492-0.773-0.1142-0.043-0.15-0.122-0.2643-0.164-1.3642-0.704-5.2902-3.6776-6.8317-3.7319 1.3488 0.0475 5.0105 2.8569 6.366 2.7119-1.2944-1.5887-2.7417-3.1474-4.193-4.6647 1.429 1.0535 2.9258 2.3052 4.1127 3.6589 0.5935 0.6769 1.187 1.3538 1.9016 1.8808 1.3932 0.975 3.4769 0.971 4.452-0.422 0.1211-0.15 0.2421-0.3 0.2489-0.493-0.092 0.421-0.2198 0.764-0.4619 1.064-0.1637 0.264-0.52 0.522-0.9122 0.701z" fill="#8B8B8B"/>
   <path d="m66.97 88.789c-0.6496-0.1773-1.1647-0.8899-1.3012-1.3963-0.2372-0.9344-0.3959-1.9046-0.5904-2.9533-0.2014-0.8559-0.3243-1.7477-0.6041-2.5678-0.7386-2.0324-2.1047-3.7782-3.6344-5.2598 0 0-0.0359-0.0784-0.1143-0.0426-1.5742-2.4091-3.2269-4.7824-5.079-6.9698 1.1647 0.8899 2.2443 2.0083 3.0459 3.3484 0.6225 0.948 1.0882 1.9676 1.7533 2.8013 0.701 0.9121 1.644 1.5241 2.345 2.4363 0.2933 0.4347 0.6225 0.9479 0.9943 1.3469 0.155-1.1135 0.2315-2.1912 0.3865-3.3047 0.0871 0.8134 0.1742 1.6267 0.2613 2.4401 0.0803 1.006 0.239 1.9762 0.3978 2.9464 0.725 2.4177 1.9565 4.699 2.1393 7.1749z" fill="#8B8B8B"/>
   <path d="m89.054 127.83c-0.1007-0.427-0.1587-0.97-0.3378-1.362-0.0871-0.813-0.2168-1.512-0.6535-2.261-0.5082-0.905-1.2518-1.703-2.0022-2.308-1.3506-1.09-2.9075-1.8-4.4644-2.511-0.7213-0.334-1.4001-0.783-1.7224-1.488 0.1142 0.042 0.1142 0.042 0.2285 0.085-0.0785 0.036-0.0785 0.036-0.1569 0.071 2.2356 1.16 4.5855 2.361 6.6933 3.863 0.6003 0.484 1.279 0.933 1.6798 1.603 0.8084 1.147 0.7899 2.767 0.7356 4.308z" fill="#8B8B8B"/>
   <path d="m74.616 118.21c-0.6991 0.13-1.4272-0.011-2.1263 0.118-0.7349 0.052-1.3982 0.26-2.0188 0.354-1.1987 0.073-2.3054-0.274-3.4257-0.237-0.6565 0.016-1.3914 0.067-2.0837 4e-3 -1.2277-0.198-2.2356-1.159-3.0934-2-3.2454-3.162-6.4549-6.246-9.7003-9.409-0.5219-0.52-1.1512-1.275-0.8171-1.996 0.1637-0.265 0.4416-0.486 0.6769-0.594-0.0785 0.036-0.1569 0.072-0.1211 0.15-0.3205 0.336-0.2692 1.071 0.1519 1.163 0.1724 0.585 0.73 1.183 1.1018 1.582 3.0595 2.963 6.1974 5.891 9.2995 8.739 0.336 0.321 0.672 0.641 1.1648 0.89s0.914 0.341 1.4136 0.397c3.6048 0.629 7.4448 1.15 11.026 0.273 0.0784-0.036 0.1568-0.072 0.2353-0.107 0 0-0.0359-0.079-0.0717-0.157 0.2711-0.029 0.4996 0.056 0.7707 0.027-0.7843 0.358-1.5686 0.716-2.3819 0.803z" fill="#8B8B8B"/>
   <path d="m57.315 120.8c-1.2055 0.266-2.4623-0.202-3.6832-0.593-0.1143-0.042-0.1143-0.042-0.2285-0.085-2.7507-1.872-5.5798-3.708-8.5726-5.279 3.0131 0.993 5.9343 2.408 8.7702 4.051 0.6429 0.37 1.2073 0.776 1.9713 0.996 1.7922 0.603 3.7461-0.1 5.55-0.923-0.2779 0.221-0.7484 0.436-0.9053 0.508-0.4706 0.215-0.8627 0.394-1.3333 0.609-0.4774 0.407-0.948 0.622-1.5686 0.716z" fill="#8B8B8B"/>
   <path d="m58.918 104.92c-0.2285-0.085-0.5712-0.213-0.7997-0.298-0.0359-0.079-0.1501-0.121-0.1501-0.121-0.6429-0.37-1.2858-0.74-1.8929-1.032-1.2567-0.468-2.2286-0.639-3.4767-0.258-0.6633 0.208-1.7027 0.99-2.3302 1.277-1.8039 0.824-3.8166 0.533-5.7144 0.736-1.7051 0.21-5.1345-2.488-6.9285-3.195-1.794-0.706-2.0821-1.027-3.2985-1.715-1.9491-1.1037-1.9713-0.9957-2.9569-1.4934-1.3284-0.6256-1.9891-0.7126-2.4891-2.2126 0-2 2.5003-4.493 2.5003-6.5-0.8664-1.1428-1.4939-0.5121-2.9501-0.795-0.7639-0.2198-1.5279-0.4397-2.1708-0.8096-1.5587-1.7526-3.0391-3.5411-4.562-5.2153 0.8714 0.4552 1.6575 1.1388 2.3653 1.8583 0.9295 0.9973 1.581 2.2164 2.7526 2.9136 1.3216 0.8183 2.9841 0.7225 4.5545 1.0479 2.4913 0.4737-3.3159 7.4351-1.0444 8.6727 2.2714 1.2376 3.8077 2.4304 6.3551 2.4044 2.4333-0.068 7.736 4.533 10.062 4.229 0.7349-0.052 1.5125-0.217 2.2542-0.461 0.5064-0.137 0.9769-0.351 1.526-0.602 0.6274-0.287 1.1764-0.538 1.8397-0.746 0.3921-0.179 0.8201-0.279 1.2055-0.266 0.4638-0.022 0.9566 0.227 1.3352 0.433 1.3642 0.704 2.7284 1.408 4.0142 2.148z" fill="#8B8B8B"/>
  </g>
 </g>
 <defs>
  <linearGradient id="paint0_linear_1924_305417" x1="36.36" x2="35.98" y1="64" y2="136" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <clipPath id="clip0_1924_305417">
   <rect width="200" height="200" fill="#fff"/>
  </clipPath>
 </defs>
</svg>