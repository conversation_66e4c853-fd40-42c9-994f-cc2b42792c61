{"fqn": "air_quality_card", "name": "Air quality index card", "deprecated": false, "image": "tb-image;/api/images/system/air_quality_index_card_system_widget_image.png", "description": "Displays the latest air quality index telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'air', label: 'Air Quality Index', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Air Quality Index\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 320) {\\n\\tvalue = 320;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:weather-windy\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":100,\"color\":\"#FFA600\"},{\"from\":100,\"to\":150,\"color\":\"#F36900\"},{\"from\":150,\"to\":200,\"color\":\"#D81838\"},{\"from\":200,\"to\":300,\"color\":\"#8D268C\"},{\"from\":300,\"to\":null,\"color\":\"#6F113A\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":26,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":100,\"color\":\"#FFA600\"},{\"from\":100,\"to\":150,\"color\":\"#F36900\"},{\"from\":150,\"to\":200,\"color\":\"#D81838\"},{\"from\":200,\"to\":300,\"color\":\"#8D268C\"},{\"from\":300,\"to\":null,\"color\":\"#6F113A\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Air quality card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"AQI\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "air", "aqi", "pollution", "emission", "smog"], "resources": [{"link": "/api/images/system/air_quality_index_card_system_widget_image.png", "title": "\"Air quality index card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "air_quality_index_card_system_widget_image.png", "publicResourceKey": "4ababWeeQrpkxszp0iF64fpZmlT4SsxO", "mediaType": "image/png", "data": "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", "public": true}]}