{"fqn": "gateway_widgets.gateway_connectors", "name": "Gateway connectors", "deprecated": false, "image": "tb-image;/api/images/system/gateway_connectors_system_widget_image.png", "description": "Allows to create and manage gateway connectors.", "descriptor": {"type": "latest", "sizeX": 11, "sizeY": 8, "resources": [{"url": "tb-resource;/api/resource/js_module/system/gateway-management-extension.js", "isModule": true}], "templateHtml": "<tb-gateway-connector [device]=\"entityId\" *ngIf=\"entityId\" [ctx]=\"ctx\"></tb-gateway-connector>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    if (self.ctx.datasources && self.ctx.datasources.length) {\n        self.ctx.$scope.entityId = self.ctx.datasources[0].entity.id;\n    }\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.gatewayConnectors?.onDataUpdated();\n};\n\nself.typeParameters  = function() {\n    return {\n        dataKeysOptional: true,\n        singleEntity: true\n    };\n}", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{},\"title\":\"Gateway connectors\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"dropShadow\":true,\"enableFullscreen\":false,\"enableDataExport\":false,\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":500},\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showLegend\":false}"}, "tags": ["router", "bridge", "hub", "access point", "relay", "opc ua", "opc-ua", "modbus", "bacnet", "odbc", "ftp", "snmp", "mqtt", "xmpp", "ocpp", "ble", "bluetooth"]}