{"fqn": "pump_vibration_chart_card", "name": "Vibration chart card", "deprecated": false, "image": "tb-image;/api/images/system/vibration_chart_card.svg", "description": "Displays vibration data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'vibration', label: 'Vibration', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'mm/s', decimals: 1 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'vibration', 'mm/s', 1);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Vibration\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 3.3 - 1.7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"mm/s\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2.8,\"color\":\"#3FA71A\"},{\"from\":2.8,\"to\":4.5,\"color\":\"#FFA600\"},{\"from\":4.5,\"to\":7.1,\"color\":\"#F36900\"},{\"from\":7.1,\"to\":null,\"color\":\"#F04022\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 3.3 - 1.7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"mm/s\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"#000000DE\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"mm/s\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Vibration\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"waves\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"margin\":\"0px\"}"}, "tags": ["vibration", "pump vibration", "motion", "resonance", "dynamic balancing", "mechanical integrity", "pulsation", "frequency"], "resources": [{"link": "/api/images/system/vibration_chart_card.svg", "title": "vibration_chart_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "vibration_chart_card.svg", "publicResourceKey": "7QFJuyvHRGTDNWB6Ne3f1MaVI9f6yNQE", "mediaType": "image/svg+xml", "data": "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", "public": true}]}