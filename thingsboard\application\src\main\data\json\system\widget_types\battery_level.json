{"fqn": "battery_level", "name": "Battery level", "deprecated": false, "image": "tb-image;/api/images/system/battery_level_system_widget_image.png", "description": "Displays the current battery level of the device.", "descriptor": {"type": "latest", "sizeX": 2.5, "sizeY": 2.5, "resources": [], "templateHtml": "<tb-battery-level-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-battery-level-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.batteryLevelWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.batteryLevelWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '200px',\n        previewHeight: '200px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'batteryLevel', label: 'batteryLevel', type: 'timeseries' }];\n        }\n    };\n};\n\nself.actionSources = function() {\n    return {\n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false\n        }\n    };\n}\n\nself.onDestroy = function() {\n};\n", "settingsDirective": "tb-battery-level-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-battery-level-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"batteryLevel\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"layout\":\"vertical_solid\",\"showValue\":true,\"autoScaleValueSize\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":20,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"24px\"},\"valueColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"batteryLevelColor\":{\"type\":\"range\",\"color\":\"rgb(224, 224, 224)\",\"rangeList\":[{\"from\":null,\"to\":25,\"color\":\"rgba(227, 71, 71, 1)\"},{\"from\":25,\"to\":50,\"color\":\"rgba(246, 206, 67, 1)\"},{\"from\":50,\"to\":null,\"color\":\"rgba(92, 223, 144, 1)\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"batteryShapeColor\":{\"type\":\"range\",\"color\":\"rgba(224, 224, 224, 0.32)\",\"rangeList\":[{\"from\":null,\"to\":25,\"color\":\"rgba(227, 71, 71, 0.32)\"},{\"from\":25,\"to\":50,\"color\":\"rgba(246, 206, 67, 0.32)\"},{\"from\":50,\"to\":null,\"color\":\"rgba(92, 223, 144, 0.32)\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"sectionsCount\":4},\"title\":\"Battery level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"%\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"mdi:battery-high\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["accumulator", "capacity", "lithium", "lithium-ion", "power cell", "energy cell", "cell"], "resources": [{"link": "/api/images/system/battery_level_system_widget_image.png", "title": "\"Battery level\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "battery_level_system_widget_image.png", "publicResourceKey": "daWuhdvZvW5wmeuaY6K8COw847r9dK8B", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAilBMVEXg4ODf39/g4OAAAADg4ODf39////9c35Dg4ODL9dz5/vzX9+QhISHl+u3f+enB89XC89XHx8c9PT2QkJCu8MiF56x0dHRw453j4+Px8fFYWFisrKz0/fcvLy/S9uDq+/GCgoK6uro8PDzV1dVKSkpw457O9t6enp7i+etmZmarq6tm4ZeZ67p65aQon2vGAAAABnRSTlPvIL8Ar7DvmsykAAAEcklEQVR42u3dbVebMBjG8Tq3Sym3zARlSSA8ax+2ff+vtyCds666oOsSuvxf5CCnL/o7gUIRThfnZx8Wl3Pv49n54mwhMfukYXw8AYeRmM0KJ9HiVCCXAeJZAeJbAeJbAeJbAeJbAeJb/wOkzky5wDx6BcJJKU1cPqHxDmh4Aw97DcLNkFEO06jJiAH5uAaPQD++Yf4JsqKVGTRRJqA06bo2gwJYb1ZJMF5z6ngNs5TCQXYQIRjXAoz6JqUNGkUpYxllDQS1eU01GJHKoLVETQwOsoLQUAp0uRhYTzetjMwqpQ0kA5Ca9byFg+xnZEMNwLKe0x6kJ6WUJsEGqOHUwogcZL+PCNogp75h+zPCKRuSIwRKr9xsWfYQZiAZdc83rc2wChI7SEq6hYMsITpNa05bpFQ3NRlITTVDQ4oZAG8apX5CJDnasux3drUF5IZI9SQhWsog28G0bYn6boSYFHVwl/25lhQYE3I3mFHgV6rFm/LspLGraYuJeQlRpJyfp/wViOjgvP/h+8i8ChDfChDfChDfChDfChDfChDfChDfejekiKOnxUvY5RtkGT3r3lLiGySJnreGVZ5BzIR4MiXvgxRl9HtlAYs8gBTruBy7jw53X47Fycsm55BlYt5obJeBJhYbmhPIsoziwn7ykqi0kLiAlNEVplRZSFxArgZH/O3WsmsjsZA7gJQlcHNh341hH/48dgspogrVxZTM66MKB3ILqaIC8STINZaHj/VuIVdvgiQ4kGvIMkCeFSABEiABEiABEiAm95BqEiT2F4JvExy38BiCm+9fLLspvIZMKUD+BSS5sa3yGnI95eKDx5Di7mJChb+QkzkgBsh+ARIgARIgM4KINMdQrnifwiRTzlU+M4hcKaL0wUE6aykzSxnpTUv5vCAp6X6EcBKQLTEwamEGjbEmFXOAMIb0AdKRMmNNq+HvnWuo7jMuwbyHmMY3zh4gOdW7508VMQz1DJyJVhwZkkyCxBaQ7BlEpVJ3fIsjQ3A7wXGHN0C6Vqd5hqNDim/WjttqKmSX6MUsvrM/7uy75ZpW+5AsTTXfzgUiSZtxQ83DrACaJMbyVpJo+LEhxWfLoupVCBSlXUOthNSUi9podnEhNNixIcnXC9u+Xr8KES0R6cYsbbVZevzAlQxos74+MuTO2mEkxUGIYAIPNatcjmuaFdt/SdPM4IA4dBJnv6YACZAACZAACZAAMQVIgLzv4kPkL2TaxQeP/xmK6vuEiw8+Qwwltqvy/IaBk7nzAUlsmd+Q6s5+X098hky6+HAydz54fBw5mSN7gOwXIAESIAESIAEyZ8iJPBlaRdXkiw+FxXPgDp6eXk87jb8rkJhZPJBTyPhw+tpe8nWNZVniUI4hRRQvDeezXcNcJBaPszuA4GqQ2LeMLZ5mdwIxkigpiqVV1fr+xY8s5xBUZWRfWeGF3EOAorqyqyrwYj5AvCxAfCtAfCtAfCtAfCtAfOtyscBJdLn44PyHKv5G8tPp/Ij2+dnHy7m3+HB2/gO3i0/vBj05fgAAAABJRU5ErkJggg==", "public": true}]}