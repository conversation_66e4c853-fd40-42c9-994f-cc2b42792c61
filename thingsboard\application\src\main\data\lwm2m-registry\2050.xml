<?xml version="1.0" encoding="utf-8"?>

<!-- BSD-3 Clause License

Copyright 2019 oneM2M Partners. 

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON><PERSON>NTIA<PERSON> DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>CmdhDefaults</Name>
		<Description1><![CDATA[Defines which CMDH related parameters will be used by default. ]]></Description1>
		<ObjectID>2050</ObjectID>
		<ObjectURN>urn:oma:lwm2m:ext:2050</ObjectURN><LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion><MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0"><Name>DefaultEcRules</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Objlnk</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[1 or more references to  CmdhDefEcValue Object Instances containing default values for the ec (event category) parameter of an incoming request or response when this parameter is not indicated in the message itself ]]></Description>
			</Item>
			
			<Item ID="1"><Name>DefaultEcParamRules</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Objlnk</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[1 or more references to CmdhEcDefParamValues Object Instances ]]></Description>
			</Item></Resources>
		<Description2 />
	</Object>
</LWM2M>
