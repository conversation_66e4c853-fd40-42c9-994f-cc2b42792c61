<?xml version="1.0" encoding="UTF-8"?>

<!--
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-XML_LWM2M_Portfolio-V1_0_1-20200616-A
   Type: xml

Public Reachable Information
   Path: http://www.openmobilealliance.org/tech/profiles
   Name: LWM2M_Portfolio-v1_0_1.xml

NORMATIVE INFORMATION

  Information about this file can be found in the latest revision of

    OMA-TS-LWM2M_PortfolioObj-V1_0_1

  This is available at http://www.openmobilealliance.org/release/

  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

<PERSON><PERSON><PERSON> HISTORY

14112017 Status changed to Approved by TP, TP Ref # OMA-TP-2017-0047-INP_LWM2M_Portfolio-V1_0_RRP_for_Final_Approval
16062020 Status changed to Approved by DMSE, Doc Ref # OMA-DM&SE-2020-0019-INP_LWM2M_Portfolio_V1_0_1_RRP_for_Final_Approval 

LEGAL DISCLAIMER

  Copyright 2020 Open Mobile Alliance.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

  1. Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
  3. Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived
  from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.

  The above license is used as a license under copyright only.  Please
  reference the OMA IPR Policy for patent licensing terms:
  https://www.omaspecworks.org/about/intellectual-property-rights/

-->
  
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>Portfolio</Name>
		<Description1><![CDATA[The Portfolio Object allows to extend the data storage capability of other Object Instances in the LwM2M system, as well as the services which may be used to authenticate and to protect privacy of data contained in those extensions. In addition, a service of data encryption is also defined]]></Description1>
		<ObjectID>16</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:16</ObjectURN><LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion><MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0"><Name>Identity</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Data Storage extension for other Object Instances. 
 e.g  for [GSMA]  : 
0 : Host Device ID, 
1:  Host Device Manufacturer
2:  Host Device Model
3:  Host Device Software Version,

This Resource contains data that the GetAuthData executable Resource can work with.
]]></Description>
			</Item>
			<Item ID="1"><Name>GetAuthData</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Executable resource to trigger Services described in Section 5.2.2 
Arguments definitions are described in Section 5.2.1 as well as in table 2 of this document
]]></Description>
			</Item>
			<Item ID="2"><Name>AuthData</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Buffer which contains the data generated by the  process triggered by a GetAuthData request]]></Description>
			</Item>
			<Item ID="3"><Name>AuthStatus</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..2</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This Resource contains the state related to the process triggered by GetAuthData  request.
0 :  IDLE_STATE :  AuthData doesn’t contain any valid data
1 :  DATA_AVAIL_STATE : AuthData  contains a valid data 
2 :  ERROR_STATE :  an error occurred  
This state is reset to IDLE_STATE, when the executable resource "GetAuthData" is triggered or when the AuthData resource has been returned to the LwM2M Server (READ / NOTIFY) .
]]></Description>
			</Item></Resources>
		<Description2 />
	</Object>
</LWM2M>
