{"fqn": "indoor_horizontal_humidity_card_with_background", "name": "Indoor horizontal humidity card with background", "deprecated": false, "image": "tb-image;/api/images/system/indoor_horizontal_humidity_card_with_background_system_widget_image.png", "description": "Displays the latest indoor humidity telemetry in a scalable horizontal layout with the background image.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'humidity', label: 'Humidity', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Humidity\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:water-percent\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":30,\"color\":\"#F89E0D\"},{\"from\":30,\"to\":60,\"color\":\"#3B911C\"},{\"from\":60,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":30,\"color\":\"#F89E0D\"},{\"from\":30,\"to\":60,\"color\":\"#3B911C\"},{\"from\":60,\"to\":null,\"color\":\"#DE2343\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/indoor_horizontal_humidity_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal humidity card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"%\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["environment", "humidity", "indoor", "moisture", "dampness", "wetness", "humidness", "moistness", "dew", "water vapor", "condensation", "dew point", "steaminess"], "resources": [{"link": "/api/images/system/indoor_horizontal_humidity_card_with_background_system_widget_background.png", "title": "\"Indoor horizontal humidity card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_horizontal_humidity_card_with_background_system_widget_background.png", "publicResourceKey": "o7nLog9yNrkfCxhhi1Bcf3fT0isyDs6g", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/indoor_horizontal_humidity_card_with_background_system_widget_image.png", "title": "\"Indoor horizontal humidity card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_horizontal_humidity_card_with_background_system_widget_image.png", "publicResourceKey": "8pXtbWOZpZtFtik4NP97w02A1tWu9ZlJ", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAC/VBMVEUAAAAPDw+qn5W/v8GKcGTi5eien6Kgk4R2c3NxZV2OiIetrrC+o5zS0tO4t7nWxsOIi4yyqZ9XUEu7vLzMzM17d3Pm5ubBwcSCfnv29/m5vr34+Pp+eXT1+fzy7+v6+vr29vb19fb7+/zz8O39/f3x7enx8fPw8PH29/j4+fn09PQ/pxrz8/Ps7Oz+/v/3+Pnv7+/r6urs7e749/fz9Pbr7O3a19Xq6eni4eHv6+jk5OTs7e/k4+Lx8vTu7e7p6Ojf2dXg2tbn5+jn5eTp6uzo6evZ1dTu7/Hq5OHe3NvOwLP29fTg396mpab18/Li4+Xo39nk3Nfv6OPc2djs5uPCw8XNwrfNv7C9rJzy8fH08u/k5ejMva6NbF3z8O+7vb+trK3R0tX08vHq4drV1dfm5ubt49zIuKnp5+bk393m3Neys7VkYV/s6Obf4eO5urxyXlLu5uDY2Nng29ji2tbW1NOQi4jY2d2/wMCGfHXc3d/c19XPwrSvsLHKu6upqarBsaGNg3zd2NXKy83m4d/NxLyLiouBd3G+wMOfn6GLgHmjoqOampnFxca/rp6VlpiKhoXGtabEs6STk5N3YVXNzs+2t7qalJGVkIzw6eWEgHxVsDTNz9PJycrEx8q1tbay2aWPhoCVcF9+ZlpKqyfNx8Lc6tnm3tnPy8iXmp+Fg4F4dHBWVVSrrrGpo6CXi4R9e3twcG+DxGyBa2GFaVzi7t/Hw8K7npuOyHpraGVsulBWsTbcysSkqKyanaKu1aGemJQ6OjrS0NDU5M6/urm7rKWwp6SioZ+cnJ2gnpykkYubzonL48S5uLi8tbKgm5medmLc0c3Fvryipaqnnpqzm5avlo+Pj4+RdmpgW1huW09NSEVgtUK7nI2go6idoKWcn6SGb2Xjxr7Vxbuk0ZWVyYOdfXB3b2x4aWKei4fs8+vp7+fE3Ly62a+foqjWsJm/p5ipiX94vmCzraqXhoLewLPSt6yeoabHqplWUk8tLS0gICDh19DcuajjuZ4H1qy9AAAAHnRSTlMABCcmICAgJiYgISYgJiAgJiYhSMBDQeDg39/AwFAoM0PiAAATsElEQVR42uzBgQAAAACAoP2pF6kCAAAAAAAAAJj9OQZNIwoDOO5wOTgJJ9gSaKFDlqSdOmWSLKXS6XASAnJ4mTp41GxpA8UcoVVjIbZaB0kVLnTQSxolUhGumWoaOojYGr0eGE2gxuWakqatQ6Hfe0aTELJmer/h8XjvfTz+BEEQBEEQBEEQV4mmWQtLmymKocw0CzsGWK1WhjFjdA9Ls8N9lhPsMIyhtzBqxjdwcAl2CIMhqoc5g6IGX8FyHotYLEOXwxnXbgpCYCHgSytKwV8LeZK+TEEUs5re1QpKOhx+/sLHBYMhjhOOjorFqanDw78//h3vIer+w2pHenLwTddFRersVPP5fPXn93q91WptgBbfJ8tyvfJmwuG4Z5t9XeJlPpdbWYlGozMgOgObA38cvqotpVKpJZDa3P2z22w2N5vGhzVPzO1MLE/Pzj1yuFwuu91+f8JmezAwPjl645bJNLLsTAruRICLQ0imxiVjvRCx210Vs5l0OMJ51kJBlLK1VcQlvRBVVfeflarr0qJf1HWtvdj5fCZkA8nlTjvKcuU9DpmDEJ6Hjn4I6IdEIASBDsNoNBqG8QU63IEE7nC4UAJuGBsfmxwYvX33zojp+vSCV/A4H3+KK+1sJiJ4vTikIGqrGoSEa9u+2FMuuA0l73DJ/Pyrl7+P91TwtlL6ui5J8baGQn7hkB0UUr8QUi7LH/+zXee/LcZxHMD7f0jW5yn6WJ+WnlpdtXSO0rVzrKSIdtNiVhqroyLLQpnSCbGoe5NYEOZMKqOuMjcRd9wS95UgQsIv3t/PU9Xi3bDIuvq+vD+f53ncHFZdPbliTi1ByEGQB0wCCBVy8uRJ5jj5HY5bt26dgCOabE9QH3AAoVQOJoTRyPNGHr/3g0PeSxYKrQ80B5OBVkD2bm5rCDAIJJH79yORvXta1qT2dezzpAC5kNq/H5J1M2ZMmfj8+PEbN3YDsv/AkrUL9hztuv960dfLdwF5/PnVKwZ5swMhCfWBnOuZPJkgPXAQhBh4IW9WrEIhgGCagHkGBRh5R/sfhwABwiP9KGDIy+VyhWxrbQIQX7QNkINrWxuSgebNRyFhwY60XfQE9zWn0Agk+5lk3cQJy5c/hwOQIz3nNyxoa21Dh29WfH37G/IKEDAIAgYcLHd65jirJ5vm1N6kFUEfzCEFEDTCVoPl2dPvT06cAGSftB8JabBYH3AUEShyimxboj0a7PA1XPRcv+QJRhO1tb7m1tYNbffubWw7faG5IRr3xePRa9euRaM9PTdv3ly++8bSWCxWj4wa3zh/W8+1eDIQvLRhw+3z52ZMGT3627pPnz7duXtZyl2Wx1LW7bbqh1UIw+bMp2+/ffv2AOUrXgc23N7fHMSpfyfJ4mtnhhBt+fCKkUog6PTlLPKiKBQKWSCejKaT22ptFovF6tTZtVqT2WqpGsVSZbGFHWadzuxwOMz4qtHY7Vq/X+3mOIPBwHEqtV/QazQ6R9hWVVW1zFUzbtOmcXOnT582ZldsKEssNhaZNWvXrjFjprkG+blyefkAfupsV31s6JClS5cupgwcuHj8qFEWmzXsGEYZLn0xmfSYJUGpNvIc9oAO3Kck/fv36UMMQJo6ogFfYlQYEJvVrNEKgl7nhIQgoJl1gCCMobfDKdQBYiAIrwLERBBLVf0/EESCIASpM+A05QZv2exlYwsQOAhSZQs7h+PwJhAoGsmBHiQIOUoYFEUe4gk2JBO1YasFkLDOLiiVqIRJEFYIzq/RsUgM7VR/AcKzRuyA4P02SylkrASZN5YFDkBq+noN+EvlBq6ub019jCDkQMZL7Q/TV1To9bAwBhyAKAGBo195MQQGYkgSCXIxtc9XG7LSZDn06DEvsTGZ1Ynz45M1GhMxEAEQFUcOQJRavTRbVtuy/0KYJF/Ipt5uA8YDEG9vVEIQMIogw03sHlEBjAkv9CEVwrNCAFEUQchSAmldGfRt3WqTCtEq1Wp2OA0kVpu1Eg46vh0MySFBUAlXKAQQsyNcWekqhQwZMoQkBGGOMi9nwJIaDG5v70GuXUPAIAdBaIxxKUADgkAY1PFnsEpWhBD/NgJIYrWFVt0kqI0SROdwImadic2a0i9AIDEEvwQhh5LeimCLZs6srCmBkAQQSACZPq6szs1+zO12eyf1ZrNFfRRDHCZcmBCysAh/NqS4ERiKWumjgJDtSHMTrnY0Rw6ToIQD/8p0nWIMrUAdKYU8hRyAMAcrRIJgntHK7CLILDTCIkFQyNy+k9zeukmTJnnxC5DZywAZWAIJmysEutsRhVK06iWNEIJe+ErXX7ks2NSQbmigyXJo7PhRzBEcTodZo9cqwcAnQ8Io5PCrGcSNBaFGgIaDTZ6OIOPykHmAFCQoZFNvL4pAyNG77yBX/RBARgwcMaIA0Y1U4tA8X0zBARiEJAT5N3K6JcoaotF0cxAObLZZ2mzdlUdVTocODnyKSqXyNKoTTX6lUCjkIO/m4554SmllbJOpQsA3NQXItGLIUIKwQup6l5VBQikbVLMsthiKEYxCEJvThEI4BBQaMIKgkiLI/yTlmDtA0slk2tPMJssaRg1mDNVLUdxyltWDQlQqnu+K855DYasQ0oasVnUjZG73eE9LIh54WB3SWixatkcSBA6CjAVECoPsQiHYjDKEGBD1xU1xKAwUguCaCQjOTJ0QRfh4aicgZ7dT3kmVqE9dyZRA+vHsbi9Lt7cHTgRZIb8DB5MQBBKe62qNHzwUP6TsCuUO5iKRLj7nPhjJtaRaWnJNP6oPppQsdt0/kKV5yKxpNWVet3dqCYRVsrQg+QNhEqKoBytPieIpQA6LlAxtSSabfSTuZABFPkZ6/pKlEwlfU5ONIDZ2Fe4WKY9s2HQBEDRyqCWShzSmWlQ/+Rz3w+0BJNSlXduWa6Trmt1c4wJEWhGMFoOQZN7YMS7cCjFZpRBUElv8B2KxmnEXYw9TJGGUnVsAwSmPiS+oEdr3K53qPtnDfTLZqwq5lJEmDPdIWce2hK8jbaWAUfVBzKfbPlJLw4XR4jwEaZQgXM6c4/KQaC4CB1ZENxP3EThKIJAMHbvLNciLmwcmqwiSlwz8D6RA6Ra3AKI2PhJREz0qgnIlK1ccO6zozsrxR4yUYYCzuhoPlbJg3OfriEoQGx5Mtoi/84E1wiQECT+M/ChAuJbIw5bUWtvDoLYrhaVkq175GzKtGDI0Vu8aNMlQbsBkUchBEElSDMFVi+ZdouwUu48RROzk3jEJWV6ImUy2OyNm6G0cMmfhwoWNjbKVTel0RyDMAojlhSh+udIpdn7BmpCDnj15FS66YTXCtp89oJjpiVGwhR5Wow67xoxC/oYg5KgzyGmyJAheBCmRMMhwBuHYgSVLNrsTEKPxvdjZiVNlqKoB7x6JYjaTPSy1plIZVY1bkfWytpX4n1OU3cghsVhQiMX2pfOq5hEqgQJREURCIDzbx/wjo9+/pklyVFYuK4LMAmQoY8TqK5mj3KBCIcUhCEnGDyxABEBoggZgaK6I27lj4nasith5uHuLmOXyOXuWe7FlJwzsVo2caV+9enX7L3bt/belMIwD+HFJSERCEH4QbV2mW1e7metstMpcaphiJrSUlWqX1GUsq43NvTUsRUyNZEpchiYWcQnpxCxziVtEgkgQiUsEIX4Q8X2ec9aLgz9A+p2dnrbTnY/nfc/7vucQHlff3GHcS2ddHWaK76lzXH16S/uadsZgHOESMIT6vQrP2KHmKYo0NUvT6bOqYiAzMEUhRpUjG1PeSEFQkTCEJbOqluVjQHThrAUIX0cQc14TTFEDgt/5LqAa+q5e8z6J/v2HIoH6q02trQFu06mph4opwuOaas+2Ol5z5OhGfdAg9bfSyaEJ4tDp4MWJFYmwg4YFB0PwGjBYvyzBamQZQWYxZDpBVhFDn7ZgCk6ZicppCwCQl4Qo+qpJ+fn5gKCzU0EUYi5pWg8ffqoJfhDPYDgHt2JoSU6gBIOB+otPNbcwGiNb3RShBleRPHYtkoaZYpPk+PyqHhWBQolQp2oTcbuKQMgxfBRWYStFyCyGHFt+bMYM0HJQDjjQsBYN+AsEElvuMiziABmdoJLWTwrFW40YtKhbhwOQXNC0tk1brmoCF+pTs2svpGfSUReXGxGhxlNd47FnYqLB03Ec/2HUY/BgjEIfqDsgWvQ9JUda85MkbVTEkT8sf+WyYRsZkkuQY8fgcmgXKBP700pKhYL8AcIUbBw2fdaILJpqqeDoL+YW5anmQpP6leYzalKreY+GhClx6vna19nB2vT0i0Etz7vrmltaWjxCtcdTffN2OiUTb9D0BENi64daTW0Snb4RO5psZkqKVr3EimeoSEFBRoHLm5STMMKSPgqOxseNk/IbG3Nzq6pmHZsxffq96WCMXDAlo/9AONTK5EV/gyD0AIsNy+xkZYrkID9WxegjKeq3lzTB1ouaWjqrjMZhfgDiVX1TU/1VKDBL/3Jt3759+wni2bGQF09UqfORceSqShqF7IkKn89vNZn8IC1UL1y9sG6wu9hb6V9rcrtuG1dPWG1uDDUYG3PHXb+xrOHLnTt3bhADrQrziES1clryPAkilwwiCI8q2uyEJHWiHJKScv4ijuZigCfmFHQDvIRXwLDZbDf271+z5ojg8bQ0l6/l6Tv9WHagTfIK3RoOgigUoUp7usk/wq8giGWqL+Qt9fpN5Vbv7XJzaDUg9++jIivvNITujL3e4KjTSwyceJVJ0xYtAEReEg6VhEeXAQsWJf1eEYSagPJd4N0YKghDtJS0pqbh5NDr9Q1rjmzYsE5oad5hdB8S1xu0nE14d/UpOnywCQ1LdCjMlda1lfaQaW26HRB/naXYnYMJwW1TeX75cbN7NUGGGVGR0I3mlQ13rjfevk4MdqhxzSgMwbcMQgFlACDzpk3BZRaJwQ6GpEinekCYweHeAQfy8MiRDdvXCUZjubmukq45QJJKs0ReMyWJjqgrYOJOIkZCS6m4OtTm6Eego69GcPqs0ufOcjgcjddDqAYU7KBxEwtDhkRb5BK8M48kGbTc40gMGsKxQPmjQ4JsIIgZS8Rin7SeLWCHisZvmSMCSc1mB4bzrKmTGIKRGYOGzeEY2W/kcGIMwVJaciSHIUyJgcgoi6bQBQpWsIMhXBBq+xEHwg0LyQJkOyB1uBq68CAOHRTRoQQFjowoRyyEfrYgVZtmyxoRBcnSO9KyqYszgxzcJlCQaIi8IrENjIuSISGiHAkyBxcEjiypIsXFuPZgSaLwLITC0xA4ZJD+bRCsP7Q5KIgEodmrPmdkKo1/YEiODHagIAyJJCKBRVYWUDArJQKHF4zJ0Y40yfEbZOtaxMcAbLge4gQ3FiLthSE8weKKTCAIltw2bcGURGYAArHoSI51cGQdXk4hC/oG/lA9pA4SZsQ4sq7BAYjJhKvt/gSVUgocDBEvJCliQs1eLUG0DBErQpNXmxazXGZwPSTHNG5YBJFRZEXh0ZEiUTgqOKgegERaVYxjGLoIQyiVTpU4i1IrI12dKyGDpIiXSjFz1y0RIVa0rKl6OPozgyEKqR7/dsghSFjCDLFdDY5xIOwQITe4IOsELxxenzVvsBIMdRiS8UeIQoSokgsYMkqqCCbhOalTqB6xEO4gDPm7RAYJSxjCBeGhkB1hiE0qSOjhdmJs2SJ4EX/I6sItIYOh0FnqslosVldpnm48lvQcesw06HDZaeLozMK8Utx2sW6tMxu3eWo2nzi34tSpJ+u+KCUDEcaM1jldlpDv+PHjR49u2rRptpjJZZM5c+bMmS9lPVJUVLSbsmdPRUXFTsrcuTsrKnavn/zyR+4SV+WhQ6aFWMO63WZjQ0Pzw4d3kWs8uzpzBiMhFMTYchIV8Xp9FhcguPGrK3SWEES8gwoKWfgbty5wKXb0eF1hHu4RW7faw5AVnz4uHRLjMDhLLL7fHWCUsYMSDSFKRMIQUHZWFOFG5Y+ZOf7ivXa33W0uNza3EIEN+1mBNsUIcrwRqCA+FKQENxuRvFIXQXBZQmfQwSLWBYDxuDAMiMGANwpLttpPX9lxE5CT135+7NfGCNcDDjDgQNocZWUEKZsjk3AAYQk7mFKxe37Zy3uLB1ndZnO5xIABWYNEFYMhJ4WeJq//ILUspzMPR05HcZAqwvfmcdyZlPEGHe5EomnRXiEgpr1u84Ob1Y9rLv/8lhhdj6GjDaXWkK+SHREGG+iLaxKNiSoKICyRUlFRNBk3XXPvYeHUDAcVgxXsgAQ3xpF1nL5Cj7WoiMVV4qS2jyPPK7HAhX1Q8BQ2QAyFBrHD0C7iAuT0lW3Vz5d//BpmsGMiPuAgHMSI7h8UsiARS3RRWMJFCUt27imaUzZ7/r09+J8XVI9wQTi7KCdOnDhwApqzvYR2PXr7K3H3t7SEWxfaFnUSLhD1/jyW6Ar5AYHUmee0ommdvvLiuerj98j9FnIMNjhLLXCAEXFQNSjYMkLcRCSIrHVJzWvP+qJHk5/tNG5rudmCFeC+mghk82ZgQCFLn17thHbt2nXp0qVDhw7ilndo95/p0r4bpX3XTt27xoTf5LRv3717e2zwzVvxyV/Tib460bc8HTvyr+vcrfPfAoTQ7j+JIAj/h0X4g0SQ/sgi4MexxUO4lvxEfJnfFz8y5rOkF/ldJLzDEcLb8AfJD4Yfxa/Yvy+0vRVPPPHEE0888cQTTzzx/GoPDgQAAAAABPlbLzBCBQAAAAAAAACcAEoMP11KvHM+AAAAAElFTkSuQmCC", "public": true}]}