<?xml version="1.0" encoding="utf-8"?>
<!-- 
Copyright 2017 World-Direct eBusiness solutions GmbH
Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), 
to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, 
and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, 
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER 
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS 
IN THE SOFTWARE.
 -->
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>Current Loop Output</Name>
    <Description1><![CDATA[This LWM2M Object provides a representation of a current loop source, which may be used to carry control signals.]]></Description1>
    <ObjectID>10258</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10258</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Multiple</MultipleInstances>
    <Mandatory>Optional</Mandatory>
    <Resources>
      <Item ID="0">
        <Name>Current Loop Output Current Value</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Float</Type>
        <RangeEnumeration>3.8..20.5</RangeEnumeration>
        <Units>mA</Units>
        <Description><![CDATA[The current value of the current loop output.]]></Description>
      </Item>
      <Item ID="5603">
        <Name>Min Range Value</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Float</Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[The minimum value that can be measured by the sensor.]]></Description>
      </Item>
      <Item ID="5604">
        <Name>Max Range Value</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Float</Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[The maximum value that can be measured by the sensor.]]></Description>
      </Item>
      <Item ID="5701">
        <Name>Sensor Units</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[Measurement Units Definition.]]></Description>
      </Item>
      <Item ID="5750">
        <Name>Application Type</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[The application type of the sensor or actuator as a string depending on the use case.]]></Description>
      </Item>
      <Item ID="5821">
        <Name>Current Calibration</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Float</Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[Read or Write the current calibration coefficient.]]></Description>
      </Item>
    </Resources>
    <Description2><![CDATA[]]></Description2>
  </Object>
</LWM2M>
