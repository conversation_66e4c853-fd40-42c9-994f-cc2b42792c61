{"fqn": "uv_index_chart_card_with_background", "name": "UV Index chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/uv_index_chart_card_with_background_system_widget_image.png", "description": "Displays a UV index data by combining the latest and aggregated values with the background image and optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'uv', label: 'UV Index', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: '', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'uv', '', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"UV Index\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.ceil(Math.random() * 4 - 2);\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 14) {\\n\\tvalue = 14;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#7CC322\"},{\"from\":2,\"to\":5,\"color\":\"#F89E0D\"},{\"from\":5,\"to\":7,\"color\":\"#F77410\"},{\"from\":7,\"to\":10,\"color\":\"#F04022\"},{\"from\":10,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.ceil(Math.random() * 4 - 2);\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 14) {\\n\\tvalue = 14;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = Math.ceil(prevValue + Math.random() * 2 - 1);\\nif (value < -2) {\\n\\tvalue = -2;\\n} else if (value > 2) {\\n\\tvalue = 2;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/uv_index_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"UV Index\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"light_mode\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "uv", "ultraviolet", "sunburn"], "resources": [{"link": "/api/images/system/uv_index_chart_card_with_background_system_widget_background.png", "title": "\"UV Index chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "uv_index_chart_card_with_background_system_widget_background.png", "publicResourceKey": "5ubYVucLQB6OwpBFzxdJt1tfiLhj9j1x", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/uv_index_chart_card_with_background_system_widget_image.png", "title": "\"UV Index chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "uv_index_chart_card_with_background_system_widget_image.png", "publicResourceKey": "hwS5xWmxCzHe9LJIcD9acMXKuIfblRYB", "mediaType": "image/png", "data": "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********************************/Wl262lDpj25qtF3MAikEPzrcwQk1LkBYBJWHKQ7YP+aWmFGEKkhBJDEt4ik8wIZRBIBW+1jB/jm0xDByYiAqLYLUyieMd2rVVzXbebujo5IZPNSYItBko+SFR2TJi4+DF3wApNJDX+YUwajQ/G0iv0hznhiizehTwSIrKh9ouICXWHaQOXnqiIuBet2sVFav/q6mhjf2MTFrgleUckVOZvZ1YMEhpMnBzKjIaBFIiMj9B6SGShQVEt1lAjC0KesauQyJo0FYR4FvNbZy2367bbqaODo4319fXf/QJigXtIaPG/rfNpcRoIw3gRL/73JILoF/ADeBDEi6AX0YMHYS8iy1JQIWkSaaEgGPAflUE2IA1VaQgUgpBCpyyk0CRNCJKAtIfQg6fu0bqnQj36vGlsKvXJ8s5sWsr8+rzv0DadKQr3v0J2bHEUGLAJBUIVAg4S5dbq5gwjSyziyAdfqACBJdkMs+Yoeq8iJBaz2qWKUlFsx1le3QSghqwgN3IQarcRcm2RFBSkvELWIHCEtvcDR1ZqSCxw/Dv+decRQGBJPqfnGEXnuQIK1wKIDYyZ4zjfHxdm/OsD9Lfdwij0PzuKysHUe2UD5ARAYAiSiuzY5sBslflDvU2QQjnHB5vFlmUxxS4pkMM5ofz48Z1EDeldJjqR6zMF7PuPc7SPNn6Kk+Jn7HP+bUP3aEf9v2ee3rnz7enbW291U9Jfn76y//GtoO+/Pv3k487O7s6dhw8eCJogivV6NWShbX9fzmYj27adYOTGh3FsjV0WO+nvBQ+CWTBrd7ttjqf9Cw9CPptxZ6nECvIqstqOQ6lVAQt8sRUFPXocRWm1FJwvk1qZqhRsu1WtVsudTqMhdzpluSGKVVK53Kw3G5KmCYKqqthSqdkUVUnSTVPWddk09YMDvf/0yZWLT3b3pSNzd6c/NIempGoS1qeatb3+e8/zfN+wXNcdu902C2lG9YzpdDKZzOc/f83HSRIZ0/Fi4TJkUoJcMhgLQzAwKwZzyEuVTAoJLXFkWKRWBoLYw7GpRhVqkIDSbNbrNHJRgFRBgwRV1EVR1VRdkvAnm7pq1jTz/vk3F16au2/2nw1rw37/4MDUgWHKHd/zJtOJP/ExcMiIMD7G4micTHw/ms8Xi3Fk+NE0SpLEMjx/EmGiigyGeyWuC24lZHAEBCv1eusGAVbkWgPBC7JjZQICOUUikKZaF5t1GCIQiXb9zk369VjtjibpNYx7iJGfuXgKbw2fDI6gwQCu1IbmYDDA15QG/qHnAWNKAlOMvjfxI7iCf+d4FZKmBjHE8CN1AUCFEbJ2EIy6nM+WS6fkZFo5UqiXC04UbmRDBwN6a2W+VckUQfv0Ce4Aoom1/gASgKM2dFHSZenby3MvT7y4DF16s9s/OpK+3pDgyRALvBCwhPC2j2GDxYtjg55sXBW0mJsmcCeajufuiAe861rRYcxCXDS0UtYmcRwhx2eNs1IJuYRsIhY4xDlHrfxDU1HWppBJGQ1IkFOQiKbaIktQJVXYIoICnevYF1lTRfWGJiC/sLBOunHybLb5/ZVrdwcHL/f7lFcyIGtHvu8fIgkslIU/8VAbUYTIgi+4ttm1rFFAhQMlqP00DRlLU56OgqDNeUCaBaNRUCodK6EicjvWvlBs4aisAHqtcm9vLzMCMHl9N3KJYkNGENV6UwQKcX1HtmG6eqbL6vVn0g3hmSrLw8EQjhDLFW1H7/clTaAlkB157/17ZNfe7UPf81HzFA3W7rowxIqNJMHcNU5S5qLe09QaWyGKHHYEuHI44u0RfU765cvxY38AWnH/4YRo2A4AAAAASUVORK5CYII=", "public": true}]}