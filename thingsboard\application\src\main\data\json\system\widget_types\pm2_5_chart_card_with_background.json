{"fqn": "pm2_5_chart_card_with_background", "name": "PM2.5 chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/pm2_5_chart_card_with_background_system_widget_image.png", "description": "Displays a fine particulate matter (PM2.5) data by combining the latest and aggregated values with the background image and optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pm2.5', label: 'PM2.5', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'µg/m³', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'pm2.5', 'µg/m³', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM2.5\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 120 - 60;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#7CC322\"},{\"from\":10,\"to\":35,\"color\":\"#F89E0D\"},{\"from\":35,\"to\":75,\"color\":\"#F77410\"},{\"from\":75,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 120 - 60;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nif (value < -10) {\\n\\tvalue = -10;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/pm2_5_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"PM2.5\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"bubble_chart\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "pm2.5", "particulate", "matter", "air", "fine particulates", "fine particles", "particulate matter 2.5", "airborne particles", "microscopic particles", "respirable particles", "aerosol particles", "dust particles"], "resources": [{"link": "/api/images/system/pm2_5_chart_card_with_background_system_widget_background.png", "title": "\"PM2.5 chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "pm2_5_chart_card_with_background_system_widget_background.png", "publicResourceKey": "ZSDbQtWmJxv18XHxvyLufDg0Nf4Dx918", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/pm2_5_chart_card_with_background_system_widget_image.png", "title": "\"PM2.5 chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "pm2_5_chart_card_with_background_system_widget_image.png", "publicResourceKey": "Se844CXm1KPK958ngk2l4spgBatkdCk6", "mediaType": "image/png", "data": "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", "public": true}]}