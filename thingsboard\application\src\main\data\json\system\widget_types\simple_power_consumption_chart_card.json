{"fqn": "simple_power_consumption_chart_card", "name": "Simple power consumption chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_power_consumption_chart_card_(1).svg", "description": "Displays historical power consumption values as a simplified chart. Optionally may display the corresponding latest power consumption value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'powerConsumption', label: 'Power consumption', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'powerConsumption', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Power consumption\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":5,\"color\":\"#3FA71A\"},{\"from\":5,\"to\":15,\"color\":\"#F36900\"},{\"from\":15,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Power consumption\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"bolt\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"units\":\"kW\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true},\"margin\":\"0px\"}"}, "tags": ["power", "energy", "energy usage", "electric load", "electricity", "power efficiency", "load profile"], "resources": [{"link": "/api/images/system/simple_power_consumption_chart_card_(1).svg", "title": "simple_power_consumption_chart_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_power_consumption_chart_card_(1).svg", "publicResourceKey": "XcffuX8nJYOUC1TqCF9DEHAcarY5T9Im", "mediaType": "image/svg+xml", "data": "PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMTYwIiBmaWxsPSJub25lIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjExMiIgeT0iMjQiIGZpbGw9IiNmZmYiIHJ4PSI0Ii8+PHJlY3Qgd2lkdGg9IjE5OSIgaGVpZ2h0PSIxMTEiIHg9Ii41IiB5PSIyNC41IiBzdHJva2U9IiMwMDAiIHN0cm9rZS1vcGFjaXR5PSIuMSIgcng9IjMuNSIvPjxwYXRoIGZpbGw9IiMwMDAiIGZpbGwtb3BhY2l0eT0iLjkiIGQ9Ik0yMC44IDQ1LjNoLTIuNnYtMS40aDIuNmMuNCAwIC44IDAgMS0uMi40LS4xLjYtLjMuNy0uNmwuMi0uOWMwLS4zIDAtLjYtLjItLjktLjEtLjItLjMtLjUtLjYtLjZhMiAyIDAgMCAwLTEuMS0uM2gtMlY0OUgxN1YzOWgzLjhjLjggMCAxLjQuMiAyIC41LjUuMiAxIC42IDEuMiAxIC4zLjYuNCAxIC40IDEuNyAwIC42LS4xIDEuMi0uNCAxLjctLjMuNC0uNy44LTEuMiAxLS42LjMtMS4yLjQtMiAuNFptNC44IDBjMC0uNi4xLTEuMS4zLTEuNmwuNi0xLjJhMyAzIDAgMCAxIDEuMS0uN2MuNC0uMiAxLS4zIDEuNS0uM3MxIDAgMS40LjNjLjQuMS44LjQgMS4xLjdsLjcgMS4yLjIgMS41di4yYzAgLjUgMCAxLS4yIDEuNWwtLjcgMS4yYTMuMSAzLjEgMCAwIDEtMi41IDFjLS42IDAtMSAwLTEuNS0uMmEzLjYgMy42IDAgMCAxLTEuOC0ybC0uMi0xLjVabTEuNyAwdjFsLjQuOGMuMS4yLjMuNC42LjUuMi4yLjUuMi44LjJhMS42IDEuNiAwIDAgMCAxLjMtLjdjLjItLjIuMy0uNS4zLS44bC4yLTFjMC0uNCAwLS43LS4yLTEgMC0uMy0uMS0uNi0uMy0uOGExLjYgMS42IDAgMCAwLTEuMy0uN2MtLjMgMC0uNiAwLS44LjItLjMuMS0uNS4zLS42LjUtLjEuMi0uMy41LS4zLjhsLS4xIDFabTguOCAyIDEuOC01LjdoMWwtLjMgMS43TDM3IDQ5aC0xbC4yLTEuNlptLTEtNS43IDEuNCA1LjhWNDloLTFsLTItNy40aDEuNlptNS40IDUuNyAxLjMtNS43aDEuNmwtMiA3LjRoLTFsLjEtMS43Wm0tMS40LTUuNyAxLjcgNS43LjIgMS43aC0xbC0xLjctNS43LS4zLTEuN2gxWm05IDcuNWMtLjYgMC0xLjEgMC0xLjUtLjJhMy4zIDMuMyAwIDAgMS0xLjktMiA0IDQgMCAwIDEtLjItMS4zdi0uM2MwLS42IDAtMS4xLjItMS42cy40LS45LjctMS4yYTMgMyAwIDAgMSAxLjEtLjhsMS4zLS4yYy42IDAgMSAwIDEuNC4yYTMuMSAzLjEgMCAwIDEgMS42IDEuOWwuMiAxLjV2LjdoLTUuN3YtMS4yaDR2LS4xbC0uMS0uOS0uNS0uNi0uOS0uMmMtLjIgMC0uNSAwLS43LjItLjIgMC0uNC4yLS41LjVsLS40Ljd2Mi4zbC41LjcuNi41LjguMUEyLjMgMi4zIDAgMCAwIDUwIDQ3bC45LjktLjcuNmMtLjIuMi0uNS40LS45LjUtLjQuMi0uOC4yLTEuMy4yWm02LTZWNDloLTEuNnYtNy40aDEuNlY0M1ptMi40LTEuNVY0M2EzLjIgMy4yIDAgMCAwLTEuNSAwIDEuNCAxLjQgMCAwIDAtLjggMWwtLjIuNmgtLjRjMC0uNCAwLS44LjItMS4yIDAtLjQuMi0uOC40LTEgLjItLjQuNC0uNi43LS44YTEuOSAxLjkgMCAwIDEgMS4zLS4yaC4zWm04IDYuMi43LS4xLjUtLjUuMy0uNmgxLjVjMCAuNC0uMS45LS40IDEuM2EzIDMgMCAwIDEtMSAuOWMtLjUuMi0xIC4zLTEuNi4zLS42IDAtMSAwLTEuNS0uM2EzIDMgMCAwIDEtMS0uOGMtLjMtLjMtLjUtLjctLjYtMS4xLS4yLS41LS4yLTEtLjItMS41di0uMmMwLS41IDAtMSAuMi0xLjUuMS0uNC4zLS44LjYtMS4xLjMtLjQuNi0uNiAxLS44bDEuNS0uM2MuNiAwIDEuMSAwIDEuNi4zLjQuMy44LjYgMSAxIC4zLjQuNC45LjQgMS40SDY2bC0uMi0uNy0uNS0uNWMtLjMtLjItLjUtLjItLjgtLjItLjMgMC0uNiAwLS44LjItLjMuMS0uNC4zLS42LjVsLS4yLjgtLjEuOXYxLjJsLjMuN2MuMi4yLjMuNC42LjUuMi4yLjUuMi44LjJabTQuMi0yLjR2LS4ybC4zLTEuNS42LTEuMmEzIDMgMCAwIDEgMS4xLS43Yy40LS4yIDEtLjMgMS41LS4zczEgMCAxLjQuM2MuNC4xLjguNCAxIC43LjQuNC42LjguOCAxLjJsLjIgMS41di4yYzAgLjUgMCAxLS4yIDEuNWwtLjcgMS4yYTMuMSAzLjEgMCAwIDEtMi41IDFjLS42IDAtMSAwLTEuNS0uMmEzLjUgMy41IDAgMCAxLTEuOC0ybC0uMi0xLjVabTEuNy0uMnYxLjJsLjQuN2MuMS4yLjMuNC42LjUuMi4yLjQuMi44LjJhMS42IDEuNiAwIDAgMCAxLjMtLjdjLjItLjIuMy0uNS4zLS44bC4xLTF2LTFsLS40LS44YTEuNiAxLjYgMCAwIDAtMS4zLS43Yy0uNCAwLS42IDAtLjkuMmwtLjUuNWMtLjIuMi0uMy41LS4zLjhsLS4xIDFabTguNS0yVjQ5aC0xLjd2LTcuNGgxLjZ2MS42Wm0tLjMgMS44SDc4YzAtLjUuMS0xIC4zLTEuNC4xLS41LjMtLjguNi0xLjFhMi43IDIuNyAwIDAgMSAyLTFsMSAuMS44LjUuNS44LjEgMS4zVjQ5aC0xLjZ2LTQuOGMwLS4zIDAtLjYtLjItLjhhLjkuOSAwIDAgMC0uNC0uNWwtLjctLjFhMS42IDEuNiAwIDAgMC0xLjQuN2wtLjMuNy0uMS44Wm0xMC44IDItLjEtLjRhMSAxIDAgMCAwLS41LS40bC0xLS4zLTEtLjNjLS40LS4xLS43LS4zLS45LS41LS4yLS4xLS40LS4zLS41LS42LS4yLS4yLS4yLS41LS4yLS44IDAtLjMgMC0uNi4yLS45bC41LS43IDEtLjUgMS4xLS4xYy42IDAgMS4yIDAgMS42LjMuNS4yLjguNSAxIC44LjMuNC40LjguNCAxLjJoLTEuN2wtLjEtLjVhMSAxIDAgMCAwLS40LS41bC0uOC0uMS0uNy4xYTEgMSAwIDAgMC0uNC40LjkuOSAwIDAgMCAwIC44bC4yLjIuNS4yLjcuMiAxLjUuNWMuNC4yLjcuNC45LjcuMi4zLjMuNy4zIDEuMWEyIDIgMCAwIDEtLjggMS42IDMgMyAwIDAgMS0xIC41TDg4IDQ5Yy0uNiAwLTEuMiAwLTEuNy0uMy0uNC0uMy0uOC0uNi0xLTEtLjMtLjMtLjQtLjctLjQtMS4xaDEuNmMwIC4zLjEuNS4zLjdsLjUuNC44LjEuNy0uMWMuMiAwIC40LS4yLjUtLjNsLjEtLjVabTcuOC4zdi01LjdoMS42VjQ5aC0xLjVsLS4xLTEuN1ptLjItMS42aC42YzAgLjUgMCAxLS4yIDEuNGwtLjUgMWMtLjIuNC0uNS42LS45LjhhMyAzIDAgMCAxLTEuMy4yIDMgMyAwIDAgMS0xLS4xIDIgMiAwIDAgMS0uNy0uNWMtLjItLjItLjQtLjUtLjUtLjlhNCA0IDAgMCAxLS4yLTEuMnYtNC44aDEuNnY0LjhsLjEuNy4zLjQuNC4yLjQuMWMuNSAwIC45IDAgMS4xLS4zLjMtLjIuNS0uNC42LS43bC4yLTFabTUtMi42VjQ5aC0xLjZ2LTcuNGgxLjVsLjEgMS41Wm0tLjMgMmgtLjVjMC0uNiAwLTEgLjItMS41bC41LTEuMSAxLS44YTMgMyAwIDAgMSAxLjItLjJsMSAuMS43LjUuNS44LjEgMS4yVjQ5aC0xLjZ2LTQuOGMwLS4zIDAtLjYtLjItLjhhLjkuOSAwIDAgMC0uNC0uNWwtLjctLjFjLS4zIDAtLjYgMC0uOC4yLS4yIDAtLjQuMy0uNi41bC0uMy43di44Wm00LjYtLjUtLjcuMi4xLTEuMy42LTFhMi42IDIuNiAwIDAgMSAyLjEtMWwxIC4xYy40LjEuNi4zLjguNS4yLjIuNC41LjUuOWwuMiAxLjJWNDloLTEuN3YtNC44YzAtLjQgMC0uNy0uMi0uOWEuOS45IDAgMCAwLS40LS40bC0uNy0uMS0uNy4xLS41LjQtLjMuNnYuN1pNMTE1IDQzdjguOGgtMS43VjQxLjZoMS41bC4yIDEuNFptNC44IDIuMnYuMmMwIC41IDAgMS0uMiAxLjVsLS42IDEuMmMtLjIuMy0uNS42LS45LjhsLTEuMi4yYy0uNCAwLS44IDAtMS4yLS4ybC0uOS0uN2E0IDQgMCAwIDEtLjUtMS4yYy0uMi0uNC0uMy0uOS0uMy0xLjR2LS41YzAtLjYuMS0xIC4zLTEuNWwuNS0xLjFhMi41IDIuNSAwIDAgMSAyLjEtMWMuNSAwIC45IDAgMS4yLjJsMSAuOC41IDEuMi4yIDEuNVptLTEuNy4ydi0xLjJsLS40LS43YzAtLjItLjMtLjQtLjUtLjVsLS43LS4yLS44LjEtLjUuNGMtLjIuMi0uMy40LS4zLjdsLS4yLjdWNDZjMCAuNC4xLjcuMyAxbC41LjYgMSAuMmMuMyAwIC41IDAgLjctLjIuMiAwIC40LS4zLjUtLjUuMi0uMi4zLS41LjMtLjhsLjEtMVptNi43LTMuOHYxLjJoLTQuMXYtMS4yaDQuMVptLTMtMS44aDEuN1Y0N2wuMS41LjMuMmguNGEyLjUgMi41IDAgMCAwIC42IDBWNDlhNC4yIDQuMiAwIDAgMS0xLjEuMWMtLjQgMC0uNyAwLTEtLjItLjMtLjEtLjUtLjMtLjctLjYtLjItLjMtLjItLjctLjItMS4ydi03LjNabTYuMyAxLjhWNDloLTEuNnYtNy40aDEuNlptLTEuOC0yIC4zLS42LjctLjJjLjMgMCAuNSAwIC43LjIuMi4yLjIuNC4yLjcgMCAuMiAwIC40LS4yLjZhMSAxIDAgMCAxLS43LjIgMSAxIDAgMCAxLS43LS4yLjguOCAwIDAgMS0uMy0uNlptMy42IDUuOHYtLjJjMC0uNSAwLTEgLjItMS41LjEtLjQuNC0uOC43LTEuMmEzIDMgMCAwIDEgMS0uN2wxLjUtLjNjLjUgMCAxIDAgMS40LjMuNS4xLjguNCAxLjEuN2wuNyAxLjJjLjIuNS4yIDEgLjIgMS41di4yYzAgLjUgMCAxLS4yIDEuNWwtLjcgMS4yYTMuMiAzLjIgMCAwIDEtMi41IDFjLS41IDAtMSAwLTEuNS0uMmwtMS0uOGMtLjMtLjQtLjYtLjgtLjctMS4yLS4yLS41LS4yLTEtLjItMS41Wm0xLjYtLjJ2LjJsLjEgMSAuMy43LjYuNWMuMi4yLjUuMi44LjJhMS42IDEuNiAwIDAgMCAxLjQtLjdsLjMtLjh2LTJjMC0uMy0uMi0uNi0uNC0uOGExLjYgMS42IDAgMCAwLTEuMy0uN2MtLjMgMC0uNiAwLS44LjJsLS42LjUtLjMuOC0uMSAxWm04LjUtMlY0OWgtMS43di03LjRoMS42bC4xIDEuNlptLS4zIDEuOGgtLjVjMC0uNSAwLTEgLjItMS40LjEtLjUuMy0uOC42LTEuMWEyLjYgMi42IDAgMCAxIDIuMS0xbDEgLjEuNy41LjUuOC4yIDEuM1Y0OWgtMS43di00LjhsLS4xLS44YS45LjkgMCAwIDAtLjUtLjVsLS43LS4xYTEuNiAxLjYgMCAwIDAtMS4zLjdsLS40LjctLjEuOFoiLz48cGF0aCBmaWxsPSIjM0ZBNzFBIiBkPSJNMjUuNSA5NnYyMGgtMy4zdi0xNmwtNC45IDEuNnYtMi43bDcuOC0yLjloLjRaTTM2LjMgOTh2MThoLTIuOFY5OGgyLjhabTcuOSA1LjMtNS41IDYuMS0zIDMuMS0uOC0yLjQgMi4zLTIuOCAzLjYtNGgzLjRaTTQxLjQgMTE2bC00LjEtNiAxLjctMiA1LjYgOGgtMy4yWm05LjEtMy44TDU0IDk4LjloMS44bC0uMiAzLjRMNTIgMTE2SDUwbC41LTMuOFpNNDggOTguOWwyLjcgMTMuMi4zIDMuOWgtMmwtNC0xN2gzWk01OS42IDExMmwyLjctMTNoM2wtNCAxN2gtMmwuMy00Wm0tMy4yLTEzIDMuNCAxMy4yLjUgMy44aC0xLjlsLTMuNi0xMy43LS4yLTMuNGgxLjhaIi8+PHBhdGggc3Ryb2tlPSIjMDAwIiBzdHJva2Utb3BhY2l0eT0iLjkiIHN0cm9rZS13aWR0aD0iMS41IiBkPSJNMTg3IDU0Yy0xNiAwLTE0IDU2LjEtMzAuNiA1Ni4xLTE0IDAtMTYuMy0yMy44LTI3LjEtMjMuOFMxMTMuNyAxMTkgOTkuNyAxMTljLTExLjUgMC0xNy45LTYuNi0yOC43LTYuNiIvPjwvc3ZnPg==", "public": true}]}