<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="200" height="200" fill="none" version="1.1" viewBox="0 0 200 200">
 <tb:metadata><![CDATA[{
  "title": "Short left drain pipe",
  "description": "Short left drain pipe with configurable fluid and leak visualizations.",
  "searchTags": [
    "pipe",
    "drain"
  ],
  "widgetSizeX": 1,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "fluid-background",
      "stateRenderFunction": "var color = ctx.properties.fluidColor;\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "leak",
      "stateRenderFunction": "var leak = ctx.values.leak;\nif (leak) {\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "pipe-background",
      "stateRenderFunction": "var color = ctx.properties.pipeColor;\nelement.attr({fill: color});",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "openInSeparateDialog": false,
        "openInPopover": false
      }
    },
    {
      "id": "leak",
      "name": "{i18n:scada.symbol.leak}",
      "hint": "{i18n:scada.symbol.leak-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.leak-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    }
  ],
  "properties": [
    {
      "id": "fluidColor",
      "name": "{i18n:scada.symbol.fluid-color}",
      "type": "color",
      "default": "#518DA0",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "pipeColor",
      "name": "{i18n:scada.symbol.pipe-color}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata>
 <g clip-path="url(#clip0_1924_305497)" tb:tag="clickArea">
  <rect x="101" y="64" width="85" height="72" fill="#fff" tb:tag="pipe-background"/>
  <rect x="101" y="64" width="85" height="72" fill="url(#paint0_linear_1924_305497)"/>
  <rect x="102.5" y="65.5" width="82" height="69" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
  <rect x="187.5" y="51.5" width="11.07" height="97" rx="5.5352" fill="#D9D9D9" stroke="#727171" stroke-width="3"/>
  <ellipse transform="rotate(-90 100 100)" cx="100" cy="100" rx="36" ry="4" fill="#5D5C5C"/>
  <path d="m76.285 94.5c4-9.2 20-22.333 24.001-24.5 0 0 1.499 18.101 1.499 30.5 0 12.399-1.499 33-1.499 33 0 6.5 1.5 9 3.999 14.5 5.089 11.196 3.5 10.5 12.5 13.5s24 2.5 21.5 11.5-32-8-34-4 16 17 10.5 21.5-17.5-10.5-21.5-10.5-4.5 10.5-10 10.5-7-11.5-16-10.5-12 13-18 17-14-2-14.5-11 20.5-7 20.5-13-39 1.5-45.5-4c-6.5-5.5-0.50001-6.5 6.5-10.5 7-4 38 5.5 39 0s-16.5 0-15-5.5 16.5-2 23-10.5 8-36.5 13-48z" fill="#518DA0" tb:tag="fluid-background"/>
  <g style="display:none" tb:tag="leak">
   <path d="m110.95 127.83c0.101-0.428 0.159-0.97 0.338-1.362 0.338-1.362 1.025-2.66 2.076-3.507 0.528-0.327 1.136-0.619 1.707-0.832 1.521-0.632 2.964-1.3 4.406-1.969 1.25-0.661 2.693-1.329 4.019-0.913 7e-3 0.193 0.05 0.307 0.057 0.5 2.334-0.546 4.888-0.327 7.228 0.362 1.597 0.446 3.208 1.276 4.864 1.179 0.578-0.02 1.114-0.155 1.613-0.211 0.885-0.07 1.734-0.061 2.59 0.14 0.621 0.094 1.248 0.381 1.833 0.553 1.676 0.481 3.424 0.806 5.237 0.78-0.514-0.329-1.027-0.658-1.54-0.987 0 0-0.128-0.343-0.085-0.229-0.15 0.121-0.386 0.014-0.507-0.136 0.565-0.406 1.129-0.812 1.658-1.139 2.751-1.872 5.58-3.708 8.573-5.28-3.014 0.994-5.935 2.409-8.771 4.052-0.643 0.37-1.207 0.776-1.971 0.996-1.792 0.603-3.746-0.1-5.55-0.924l-0.078-0.035c-0.157-0.072-0.393-0.18-0.399-0.372-0.086-0.229 0.136-0.507 0.322-0.706 1.008-0.962 1.937-1.959 2.867-2.956 1.151-1.276 2.067-2.658 3.296-3.898 1.08-1.118 2.281-2.087 3.445-2.976 0.565-0.406 1.051-0.848 1.7-1.025 0.686-0.256 1.378-0.319 2.113-0.267 0.928 0.044 1.777 0.053 2.669 0.176 5.115 0.63 13.659-5.451 18.687-4.008 1.277 0.273 2.776 0 1.277-1.727-2.001-1.4995-2-1.9996-2-4.9996 0.757-0.4125-2-3.5-2-5-0.5-1 0.377-0.9415 1.069-1.0045 1.385-0.1259 2.812-0.1376 4.031 0.5139-0.713-0.5151-1.497-0.8733-2.239-1.1172 0.186-0.1995 0.408-0.4774 0.594-0.6769 1.559-1.7526 3.039-3.5411 4.562-5.2153-0.872 0.4552-1.658 1.1388-2.366 1.8583-0.929 0.9973-1.581 2.2164-2.752 2.9136-1.322 0.8183-2.984 0.7225-4.555 1.048-2.491 0.4736 3.492 6.84 1.221 8.0776-2.272 1.2375-3.933 2.7911-7.809 4.5241-2.794 1.249-7.353 3.179-9.679 2.875-0.735-0.051-1.513-0.216-2.254-0.46-0.507-0.137-0.977-0.352-1.526-0.603-0.628-0.286-1.177-0.537-1.84-0.745-0.392-0.179-0.82-0.28-1.206-0.266-0.463-0.022-0.956 0.226-1.335 0.433l-4.092 2.112c0.228-0.085 0.571-0.213 0.799-0.299 1.828-0.681 3.621-1.285 5.476-1.196 0.385-0.013 0.813 0.087 1.091 0.309 0.078 0.036 0.157 0.072 0.121 0.15 0.321 0.336 0.269 1.071-0.152 1.163-0.172 0.585-0.73 1.183-1.102 1.582-3.059 2.963-6.197 5.891-9.299 8.739-0.336 0.321-0.672 0.641-1.165 0.89s-0.914 0.341-1.414 0.397c-3.604 0.629-7.444 1.15-11.025 0.273-0.078-0.036-0.157-0.072-0.235-0.107 0 0 0.035-0.079 0.071-0.157-0.271-0.029-0.499 0.056-0.77 0.027-1.82-0.167-3.595-1.262-5.098-2.328-1.267-1.138-1.788-1.768-1.821-2.377-0.036-0.691-0.165-3.04 1.054-4.04 0.565-0.406 1.76-0.38 1.973-0.617 0.075-0.084 0.298-1.622-0.246-0.372 0.609-0.033 6.428-2.328 10.667-2.478 0.386-0.013 0.849 9e-3 1.277 0.11 0.271 0.029 0.507 0.136 0.706 0.322 0.079 0.036 0.121 0.15 0.164 0.264 0.043 0.115-0.072 0.157-0.108 0.236-1.981 2.886-4.505 5.715-4.919 9.318 0.15-0.122 0.186-0.2 0.336-0.321 0.036-0.078 0.15-0.121 0.222-0.278 1.259-1.51 2.154-3.471 3.576-4.717 0.637-0.563 1.351-1.09 1.788-1.838 0.544-0.984 0.611-2.375 1.354-3.173 0.372-0.399 0.901-0.727 1.358-0.897 1.256-0.469 2.677-0.673 3.891-1.256 0.607-0.292 1.207-0.776 1.814-1.067 0.115-0.043 0.15-0.121 0.265-0.164 1.364-0.704 5.823-4.0432 7.364-4.0975-1.348 0.0475-5.543 3.2235-6.899 3.0775 1.295-1.5885 2.553-3.099 4.005-4.6164-1.429 1.0536-2.737 2.2572-3.924 3.6109-0.594 0.6765-1.187 1.3535-1.902 1.8805-1.393 0.975-3.477 0.971-4.452-0.422-0.121-0.15-0.242-0.3-0.249-0.493-0.419-0.9496-0.381-2.0698-0.38-3.1117 0.163-3.0539 0.446-5.9577 0.802-9.0184 0.268-2.2473 0.614-4.4588 1.624-6.4622 0.071-0.1568 0.222-0.2779 0.378-0.2062-0.405-0.5645-0.197-1.2278 0.168-1.8194 0.286-0.6274 0.73-1.1832 1.095-1.7749 0.329-0.5132 0.658-1.0264 0.987-1.5396 1.575-2.4091 3.227-4.7824 5.079-6.9698-1.164 0.8899-2.244 2.0083-3.046 3.3485-0.622 0.9479-1.088 1.9675-1.753 2.8012-0.701 0.9122-1.644 1.5242-2.345 2.4363-0.293 0.4348-0.622 0.948-0.994 1.3469-0.155-1.1135-0.232-2.1911-0.387-3.3046-0.087 0.8133-0.174 1.6267-0.261 2.44-0.08 1.006-0.239 1.9762-0.398 2.9464-0.532 2.411-1.878 4.7349-2.061 7.2107l0.021 0.5781c0.15-0.1211 0.221-0.2779 0.371-0.399 0.043 0.1143-0.029 0.2711 0.014 0.3854 0.153 2.1553-0.115 4.4026-0.504 6.4999-0.282 1.862-0.635 3.8808-1.9 5.1984-0.372 0.399-0.822 0.762-1.194 1.161s-0.58 1.062-0.445 1.598c-2.548-0.026-5.383-0.521-7.37 1.131-1.536 1.288-3.611 0.984-3.835 2.523 0.577-0.642-0.961-0.865-2.147-0.191-1.185 0.674-1.686 6-0.783 7.55 0.861 1.436 2.251 2.545 3.734 3.033 0.078 0.035 0.078 0.035 0.157 0.071-2.236 1.159-4.586 2.361-6.693 3.863-0.601 0.484-1.28 0.933-1.68 1.603-0.417 1.326-0.398 2.946-0.344 4.487z" fill="#5C5A5A"/>
   <path d="m116.94 107.1c0.813 0.088 1.248 0.285 2.048-0.013 1.6-0.597 1.962-1.241 3.476-2.066 1.707-0.832 2.611-0.911 4.531-1.171 1-0.113 1.963-0.147 2.891-0.102-0.22-0.764 1.261-1.965 1.819-2.564 0.558-0.598 1.272-1.125 1.516-1.8665 0.28-0.8201 0.096-1.6625 0.105-2.5116 0.044-0.9276 0.36-1.8262 0.597-2.7606 0.396-1.9045 0.135-3.8246-0.24-5.702-0.035 0.0784-0.035 0.0784-0.071 0.1569 0.153 2.1553-0.115 4.4026-0.504 6.4999-0.282 1.8619-0.635 3.8808-1.901 5.1989-0.371 0.399-0.822 0.762-1.193 1.161-0.372 0.399-1.511 1.472-1.376 2.007-2.548-0.026-4.357-0.455-6.344 1.197-1.537 1.289-2.083 1.186-3.947 1.947-0.3 0.242-0.871 0.455-1.407 0.589z" fill="#8B8B8B"/>
   <path d="m126.33 115.32c0.15-0.121 0.186-0.2 0.336-0.321 0.036-0.078 0.15-0.121 0.222-0.278 0.036-0.078 0.036-0.078 0.036-0.078 0.172-0.585 0.344-1.17 0.709-1.762 0.215-0.47 0.544-0.983 0.838-1.418 1.245-1.896 2.96-3.577 3.771-5.766-0.549-0.251-1.098-0.502-1.641-0.56 0.272 0.029 0.507 0.137 0.706 0.323 0.079 0.035 0.121 0.15 0.164 0.264s-0.072 0.157-0.108 0.235c-2.174 2.893-4.62 5.758-5.033 9.361z" fill="#8B8B8B"/>
   <path d="m136.28 102.05c0.627 0.287 1.44 0.374 2.168 0.232 0.729-0.141 1.486-0.554 2.25-0.773 0.114-0.043 0.15-0.122 0.264-0.164 1.364-0.704 5.29-3.6776 6.832-3.7319-1.349 0.0475-5.011 2.8569-6.366 2.7119 1.294-1.5887 2.741-3.1474 4.193-4.6647-1.429 1.0535-2.926 2.3052-4.113 3.6589-0.594 0.6769-1.187 1.3538-1.902 1.8808-1.393 0.975-3.477 0.971-4.452-0.422-0.121-0.15-0.242-0.3-0.248-0.493 0.092 0.421 0.219 0.764 0.461 1.064 0.164 0.264 0.52 0.522 0.913 0.701z" fill="#8B8B8B"/>
   <path d="m133.03 88.789c0.65-0.1773 1.165-0.8899 1.302-1.3963 0.237-0.9344 0.396-1.9046 0.59-2.9533 0.201-0.8559 0.324-1.7477 0.604-2.5678 0.739-2.0324 2.105-3.7782 3.635-5.2598 0 0 0.035-0.0784 0.114-0.0426 1.574-2.4091 3.227-4.7824 5.079-6.9698-1.165 0.8899-2.244 2.0083-3.046 3.3484-0.623 0.948-1.088 1.9676-1.753 2.8013-0.701 0.9121-1.644 1.5241-2.345 2.4363-0.294 0.4347-0.623 0.9479-0.995 1.3469-0.155-1.1135-0.231-2.1912-0.386-3.3047-0.087 0.8134-0.174 1.6267-0.261 2.4401-0.081 1.006-0.239 1.9762-0.398 2.9464-0.725 2.4177-1.957 4.699-2.14 7.1749z" fill="#8B8B8B"/>
   <path d="m110.95 127.83c0.101-0.427 0.159-0.97 0.338-1.362 0.087-0.813 0.217-1.512 0.653-2.261 0.509-0.905 1.252-1.703 2.002-2.308 1.351-1.09 2.908-1.8 4.465-2.511 0.721-0.334 1.4-0.783 1.722-1.488-0.114 0.042-0.114 0.042-0.228 0.085 0.078 0.036 0.078 0.036 0.157 0.071-2.236 1.16-4.586 2.361-6.694 3.863-0.6 0.484-1.279 0.933-1.679 1.603-0.809 1.147-0.79 2.767-0.736 4.308z" fill="#8B8B8B"/>
   <path d="m125.38 118.21c0.699 0.13 1.427-0.011 2.126 0.118 0.735 0.052 1.399 0.26 2.019 0.354 1.199 0.073 2.306-0.274 3.426-0.237 0.656 0.016 1.391 0.067 2.084 4e-3 1.227-0.198 2.235-1.159 3.093-2 3.245-3.162 6.455-6.246 9.7-9.409 0.522-0.52 1.152-1.275 0.817-1.996-0.163-0.265-0.441-0.486-0.676-0.594 0.078 0.036 0.157 0.072 0.121 0.15 0.32 0.336 0.269 1.071-0.152 1.163-0.173 0.585-0.73 1.183-1.102 1.582-3.06 2.963-6.197 5.891-9.3 8.739-0.335 0.321-0.671 0.641-1.164 0.89s-0.914 0.341-1.414 0.397c-3.605 0.629-7.445 1.15-11.025 0.273-0.079-0.036-0.157-0.072-0.236-0.107 0 0 0.036-0.079 0.072-0.157-0.271-0.029-0.5 0.056-0.771 0.027 0.785 0.358 1.569 0.716 2.382 0.803z" fill="#8B8B8B"/>
   <path d="m142.68 120.8c1.206 0.266 2.462-0.202 3.683-0.593 0.114-0.042 0.114-0.042 0.229-0.085 2.75-1.872 5.58-3.708 8.572-5.279-3.013 0.993-5.934 2.408-8.77 4.051-0.643 0.37-1.207 0.776-1.971 0.996-1.792 0.603-3.746-0.1-5.55-0.923 0.278 0.221 0.748 0.436 0.905 0.508 0.471 0.215 0.863 0.394 1.333 0.609 0.478 0.407 0.948 0.622 1.569 0.716z" fill="#8B8B8B"/>
   <path d="m141.08 104.92c0.229-0.085 0.572-0.213 0.8-0.298 0.036-0.079 0.15-0.121 0.15-0.121 0.643-0.37 1.286-0.74 1.893-1.032 1.257-0.468 2.229-0.639 3.477-0.258 0.663 0.208 1.703 0.99 2.33 1.277 1.804 0.824 3.817 0.533 5.715 0.736 1.705 0.21 5.134-2.488 6.928-3.195 1.794-0.706 2.082-1.027 3.298-1.715 1.95-1.1037 1.972-0.9957 2.957-1.4934 1.329-0.6256 1.99-0.7126 2.49-2.2126 0-2-2.501-4.493-2.501-6.5 0.867-1.1428 1.494-0.5121 2.95-0.795 0.764-0.2198 1.528-0.4397 2.171-0.8096 1.559-1.7526 3.039-3.5411 4.562-5.2153-0.871 0.4552-1.657 1.1388-2.365 1.8583-0.93 0.9973-1.581 2.2164-2.753 2.9136-1.321 0.8183-2.984 0.7225-4.554 1.0479-2.492 0.4737 3.316 7.4351 1.044 8.6727-2.271 1.2376-3.808 2.4304-6.355 2.4044-2.433-0.068-7.736 4.533-10.062 4.229-0.735-0.052-1.512-0.217-2.254-0.461-0.506-0.137-0.977-0.351-1.526-0.602-0.627-0.287-1.176-0.538-1.84-0.746-0.392-0.179-0.82-0.279-1.205-0.266-0.464-0.022-0.957 0.227-1.335 0.433-1.365 0.704-2.729 1.408-4.015 2.148z" fill="#8B8B8B"/>
  </g>
 </g>
 <defs>
  <linearGradient id="paint0_linear_1924_305497" x1="123.1" x2="122.72" y1="64" y2="136" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <clipPath id="clip0_1924_305497">
   <rect width="200" height="200" fill="#fff"/>
  </clipPath>
 </defs>
</svg>