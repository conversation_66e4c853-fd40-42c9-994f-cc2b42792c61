{"fqn": "visibility_card", "name": "Visibility card", "deprecated": false, "image": "tb-image;/api/images/system/visibility_card_system_widget_image.png", "description": "Displays the latest visibility telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'visibility', label: 'Visibility', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Visibility\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"visibility\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":1,\"color\":\"#D81838\"},{\"from\":1,\"to\":4,\"color\":\"#FFA600\"},{\"from\":4,\"to\":null,\"color\":\"#80C32C\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":1,\"color\":\"#D81838\"},{\"from\":1,\"to\":4,\"color\":\"#FFA600\"},{\"from\":4,\"to\":null,\"color\":\"#80C32C\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Air quality card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"km\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "visibility", "sight", "view", "clarity", "transparency", "perceptibility", "discernibility", "range of view", "clearness"], "resources": [{"link": "/api/images/system/visibility_card_system_widget_image.png", "title": "\"Visibility card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "visibility_card_system_widget_image.png", "publicResourceKey": "Pzwt5fufVhEkCtIM2inUyMVhnHRWbsIe", "mediaType": "image/png", "data": "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", "public": true}]}