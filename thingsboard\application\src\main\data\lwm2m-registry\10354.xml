<?xml version="1.0" encoding="utf-8"?>
<!--BSD 3-Clause License

Copyright (c) 2019, Cloudminds
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>EC<PERSON>L, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON>
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>APP</Name>
    <Description1><![CDATA[This LWM2M Object includes the APP information.]]></Description1>
    <ObjectID>10354</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10354</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Multiple</MultipleInstances>
    <Mandatory>Mandatory</Mandatory>
    <Resources>
		<Item ID="1">
			<Name>APP Name</Name>
			<Operations>RW</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>String</Type>
			<RangeEnumeration/>
			<Units/>
			<Description>The name of the APP, human readable string.</Description>
		</Item>
		<Item ID="2">
			<Name>APP Version</Name>
			<Operations>RW</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>String</Type>
			<RangeEnumeration/>
			<Units/>
			<Description>The version of the APP, human readable string.</Description>
		</Item>
		<Item ID="3">
			<Name>APP Build No</Name>
			<Operations>RW</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>String</Type>
			<RangeEnumeration/>
			<Units/>
			<Description>The Build No of the APP, human readable string.</Description>
		</Item>
		<Item ID="4">
			<Name>APP Patch No</Name>
			<Operations>RW</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>String</Type>
			<RangeEnumeration/>
			<Units/>
			<Description>The Patch No of the APP, human readable string.</Description>
		</Item>
		<Item ID="5">
			<Name>Package URI</Name>
			<Operations>W</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>String</Type>
			<RangeEnumeration>0..255 bytes</RangeEnumeration>
			<Units></Units>
			<Description><![CDATA[URI from where the device can download the software package by an alternative mechanism.]]></Description>
		</Item>
		<Item ID="6">
			<Name>Vendor Name</Name>
			<Operations>RW</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>String</Type>
			<RangeEnumeration/>
			<Units/>
			<Description>The vendor of the package.</Description>
		</Item>
		<Item ID="7">
			<Name>Installation Target</Name>
			<Operations>RW</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>Objlnk</Type>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Specify where this APP should be installed, RCU or which CCU.]]></Description>
		</Item>		
		<Item ID="8">
			<Name>APP Status</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>Integer</Type>
			<RangeEnumeration>0..5</RangeEnumeration>
			<Units/>
			<Description>The Status of the APP, 0:Downloading, 1:Downloaded, 2:Installed, 3:Verified, 4:Activated, 5:Stopped.</Description>
		</Item>
		<Item ID="100">
			<Name>APP Restart</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Restart the APP.]]></Description>
		</Item>
		<Item ID="101">
			<Name>APP Start</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Start the APP.]]></Description>
		</Item>
		<Item ID="102">
			<Name>APP Stop</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Stop the APP.]]></Description>
		</Item>
		<Item ID="103">
			<Name>APP Download</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Download the APP.]]></Description>
		</Item>
		<Item ID="104">
			<Name>APP Install</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Install the APP.]]></Description>
		</Item>
		<Item ID="105">
			<Name>APP Uninstall</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Uninstall the APP.]]></Description>
		</Item>
		<Item ID="106">
			<Name>APP Activate</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Activate the APP.]]></Description>
		</Item>
		<Item ID="107">
			<Name>APP Deactivate</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Deactivate the APP.]]></Description>
		</Item>
		<Item ID="108">
			<Name>APP Verify</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Verify the installed APP after APP is activated.]]></Description>
		</Item>
    </Resources>
    <Description2 />
  </Object>
</LWM2M>
