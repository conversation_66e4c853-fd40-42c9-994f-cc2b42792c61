{"fqn": "power_consumption_chart_card_with_background", "name": "Power consumption chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/power_consumption_chart_card_with_background.svg", "description": "Displays power consumption data by combining the latest and aggregated values with the background image and optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'powerConsumption', label: 'Power consumption', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'kW', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'powerConsumption', 'kW', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Power consumption\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"kW\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":5,\"color\":\"#3B911C\"},{\"from\":5,\"to\":15,\"color\":\"#F77410\"},{\"from\":15,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"kW\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0,0,0,0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"kW\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/power_consumption_chart_card_background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":4}},\"autoScale\":true},\"title\":\"Power consumption\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"bolt\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"margin\":\"0px\"}"}, "tags": ["power", "energy", "energy usage", "electric load", "electricity", "power efficiency", "load profile"], "resources": [{"link": "/api/images/system/power_consumption_chart_card_background.png", "title": "power_consumption_chart_card_background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "power_consumption_chart_card_background.png", "publicResourceKey": "Xo78V9JPP6573qXQJA7ayuU1hiOVLsNn", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/power_consumption_chart_card_with_background.svg", "title": "power_consumption_chart_card_with_background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "power_consumption_chart_card_with_background.svg", "publicResourceKey": "CN1F9MsJA6c9hpOT5TTIiMGuvv9hwa7K", "mediaType": "image/svg+xml", "data": "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", "public": true}]}