{"fqn": "solar_radiation_card", "name": "Solar radiation card", "deprecated": false, "image": "tb-image;/api/images/system/solar_radiation_card_system_widget_image.png", "description": "Displays the latest solar radiation telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'radiation', label: 'Solar Radiation', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Solar Radiation\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 1100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:radioactive\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#5B7EE6\"},{\"from\":0,\"to\":250,\"color\":\"#80C32C\"},{\"from\":250,\"to\":500,\"color\":\"#FFA600\"},{\"from\":500,\"to\":1000,\"color\":\"#F36900\"},{\"from\":1000,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":28,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#5B7EE6\"},{\"from\":0,\"to\":250,\"color\":\"#80C32C\"},{\"from\":250,\"to\":500,\"color\":\"#FFA600\"},{\"from\":500,\"to\":1000,\"color\":\"#F36900\"},{\"from\":1000,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Solar radiation card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"W/m²\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "solar", "radiation", "sunlight"], "resources": [{"link": "/api/images/system/solar_radiation_card_system_widget_image.png", "title": "\"Solar radiation card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "solar_radiation_card_system_widget_image.png", "publicResourceKey": "51sSUkBqUFlaoM2JG6wdfo2Ld1mJxm7x", "mediaType": "image/png", "data": "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", "public": true}]}