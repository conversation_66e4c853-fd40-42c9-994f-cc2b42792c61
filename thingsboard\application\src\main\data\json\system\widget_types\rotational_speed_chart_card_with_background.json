{"fqn": "rotational_speed_chart_card_with_background", "name": "Rotational speed chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/rotational_speed_chart_card_with_background.svg", "description": "Displays rotational speed data by combining the latest and aggregated values with the background image and optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'rotationalSpeed', label: 'Rotational speed', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'RPM', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'rotationalSpeed', 'RPM', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Rotational speed\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 4000 - 2000;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 4000) {\\n\\tvalue = 4000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"RPM\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":500,\"color\":\"#2B54CE\"},{\"from\":500,\"to\":1500,\"color\":\"#3B911C\"},{\"from\":1500,\"to\":3000,\"color\":\"#F89E0D\"},{\"from\":3000,\"to\":null,\"color\":\"#F04022\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 4000 - 2000;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 4000) {\\n\\tvalue = 4000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"RPM\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0,0,0,0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"RPM\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/rotational_speed_chart_card_background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Rotational speed\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"360\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"margin\":\"0px\"}"}, "tags": ["angular speed", "spin rate", "revolutions", "rotational frequency", "spin motion"], "resources": [{"link": "/api/images/system/rotational_speed_chart_card_background.png", "title": "rotational_speed_chart_card_background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "rotational_speed_chart_card_background.png", "publicResourceKey": "SJSjzcdnw2HhqXwDZzXL0VLiNU41RZpY", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/rotational_speed_chart_card_with_background.svg", "title": "rotational_speed_chart_card_with_background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "rotational_speed_chart_card_with_background.svg", "publicResourceKey": "GLIg8oJWHzUOeNhXCeEnprXcGmgaAKEB", "mediaType": "image/svg+xml", "data": "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", "public": true}]}