{"fqn": "leaf_wetness_card", "name": "Leaf wetness card", "deprecated": false, "image": "tb-image;/api/images/system/leaf_wetness_card_system_widget_image.png", "description": "Displays the latest leaf wetness telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'leaf', label: 'Leaf wetness', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Leaf wetness\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:leaf\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#4B70DD\"},{\"from\":10,\"to\":50,\"color\":\"#FFA600\"},{\"from\":50,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":52,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#4B70DD\"},{\"from\":10,\"to\":50,\"color\":\"#FFA600\"},{\"from\":50,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Humidity card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"%\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "dew", "leaf moisture", "foliage dampness", "leaf humidity", "foliar moisture"], "resources": [{"link": "/api/images/system/leaf_wetness_card_system_widget_image.png", "title": "\"Leaf wetness card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "leaf_wetness_card_system_widget_image.png", "publicResourceKey": "TuVhvZrVLFH2GO8PwndNjvQXI5eGuv9E", "mediaType": "image/png", "data": "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", "public": true}]}