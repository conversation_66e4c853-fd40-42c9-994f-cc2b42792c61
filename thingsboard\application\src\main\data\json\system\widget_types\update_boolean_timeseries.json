{"fqn": "input_widgets.update_boolean_timeseries", "name": "Update boolean timeseries", "deprecated": true, "image": "tb-image;/api/images/system/update_shared_boolean_attribute_system_widget_image.png", "description": "Simple form to input new boolean value for pre-defined timeseries key.\nThe widget is deprecated. Use \"Update Multiple Attributes\" widget. Timeseries key type and boolean value type can be selected in widgets data key configuration.", "descriptor": {"type": "latest", "sizeX": 7.5, "sizeY": 3, "resources": [], "templateHtml": "<div tb-toast toastTarget=\"{{ toastTargetId }}\" style=\"width: 100%; height: 100%;\">\n    <form *ngIf=\"attributeUpdateFormGroup\"\n          class=\"attribute-update-form\"\n          [formGroup]=\"attributeUpdateFormGroup\"\n          (ngSubmit)=\"updateAttribute()\">\n        <div style=\"padding: 0 8px; margin: auto 0;\">\n            <div class=\"attribute-update-form__grid\" [class.!hidden]=\"!entityDetected || !isValidParameter || !dataKeyDetected\">\n                <div class=\"grid__element\">\n                    <mat-checkbox formControlName=\"checkboxValue\"\n                                  (change)=\"changed()\"\n                                  aria-label=\"{{'widgets.input-widgets.switch-timeseries-value' | translate}}\">\n                        {{currentValue}}\n                    </mat-checkbox>\n                </div>\n            </div>\n\n            <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\" [class.!hidden]=\"entityDetected\">\n                {{ 'widgets.input-widgets.no-entity-selected' | translate }}\n            </div>\n            <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\"\n                 [class.!hidden]=\"!entityDetected || dataKeyDetected\">\n                {{ 'widgets.input-widgets.no-timeseries-selected' | translate }}\n            </div>\n            <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\"\n                 [class.!hidden]=\"!entityDetected || isValidParameter\">\n                {{ 'widgets.input-widgets.attribute-not-allowed' | translate }}\n            </div>\n        </div>\n    </form>\n</div>", "templateCss": ".attribute-update-form {\r\n    overflow: hidden;\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.attribute-update-form__grid {\r\n    display: flex;\r\n}\r\n.grid__element:first-child {\r\n    flex: 1;\r\n}\r\n\r\n.grid__element {\r\n    display: flex;\r\n}\r\n\r\n.attribute-update-form .mat-button.mat-icon-button {\r\n    width: 32px;\r\n    min-width: 32px;\r\n    height: 32px;\r\n    min-height: 32px;\r\n    padding: 0 !important;\r\n    margin: 0 !important;\r\n    line-height: 20px;\r\n}\r\n\r\n.attribute-update-form .mat-icon-button mat-icon {\r\n    width: 20px;\r\n    min-width: 20px;\r\n    height: 20px;\r\n    min-height: 20px;\r\n    font-size: 20px;\r\n}\r\n\r\n.tb-toast {\r\n    font-size: 14px!important;\r\n}", "controllerScript": "let settings;\nlet utils;\nlet translate;\nlet http;\nlet $scope;\nlet map;\n\nself.onInit = function() {\n    self.ctx.ngZone.run(function() {\n        init();\n        self.ctx.detectChanges(true);\n    });\n};\n\n\nfunction init() {\n    $scope = self.ctx.$scope;\n    utils = $scope.$injector.get(self.ctx.servicesMap.get('utils'));\n    translate = $scope.$injector.get(self.ctx.servicesMap.get('translate'));\n    http = $scope.$injector.get(self.ctx.servicesMap.get('http'));\n    $scope.toastTargetId = 'input-widget' + utils.guid();\n    settings = utils.deepClone(self.ctx.settings) || {};\n    settings.showResultMessage = utils.defaultValue(settings.showResultMessage, true);\n\n    $scope.isValidParameter = true;\n    $scope.dataKeyDetected = false;\n\n    settings.trueValue = utils.defaultValue(utils.customTranslation(settings.trueValue, settings.trueValue), true);\n    settings.falseValue = utils.defaultValue(utils.customTranslation(settings.falseValue, settings.falseValue), false);\n\n    map = {\n        true: settings.trueValue,\n        false: settings.falseValue\n    };\n    \n    $scope.checkboxValue = false;\n    $scope.currentValue = map[$scope.checkboxValue];\n\n    $scope.attributeUpdateFormGroup = $scope.fb.group({checkboxValue: [$scope.checkboxValue]});\n\n    $scope.changed = function() {\n        $scope.checkboxValue = $scope.attributeUpdateFormGroup.get('checkboxValue').value;\n        $scope.currentValue = map[$scope.checkboxValue];\n        $scope.updateAttribute();\n    };\n\n    if (self.ctx.datasources && self.ctx.datasources.length) {\n        var datasource = self.ctx.datasources[0];\n        if (datasource.type === 'entity') {\n            if (datasource.entityType && datasource.entityId) {\n                $scope.entityName = datasource.entityName;\n                if (settings.widgetTitle && settings.widgetTitle.length) {\n                    $scope.titleTemplate = utils.customTranslation(settings.widgetTitle, settings.widgetTitle);\n                } else {\n                    $scope.titleTemplate = self.ctx.widgetConfig.title;\n                }\n\n                $scope.entityDetected = true;\n            }\n        }\n        if (datasource.dataKeys.length) {\n            if (datasource.dataKeys[0].type !== \"timeseries\") {\n                $scope.isValidParameter = false;\n            } else {\n                $scope.currentKey = datasource.dataKeys[0].name;\n                $scope.dataKeyType = datasource.dataKeys[0].type;\n                $scope.dataKeyDetected = true;\n            }\n        }\n    }\n\n    self.ctx.widgetTitle = utils.createLabelFromDatasource(self.ctx.datasources[0], $scope.titleTemplate);\n\n    $scope.updateAttribute = function() {\n        if ($scope.entityDetected) {\n            var datasource = self.ctx.datasources[0];\n\n            let observable = saveEntityTimeseries(\n                datasource.entityType,\n                datasource.entityId,\n                [{\n                    key: $scope.currentKey,\n                    value: $scope.checkboxValue\n                }]\n            );\n\n            if (observable) {\n                observable.subscribe(\n                    function success() {\n                       $scope.originalValue = $scope.attributeUpdateFormGroup.get('checkboxValue').value;\n                        if (settings.showResultMessage) {\n                            $scope.showSuccessToast(translate.instant('widgets.input-widgets.update-successful'), 1000, 'bottom', 'left', $scope.toastTargetId);\n                        }\n                    },\n                    function fail() {\n                        if (settings.showResultMessage) {\n                            $scope.showErrorToast(translate.instant('widgets.input-widgets.update-failed'), 'bottom', 'left', $scope.toastTargetId);\n                        }\n                    }\n                );\n            }\n        }\n    };\n\n    function saveEntityTimeseries(entityType, entityId, telemetries) {\n        var telemetriesData = {};\n        for (var a = 0; a < telemetries.length; a++) {\n            if (typeof telemetries[a].value !== 'undefined' && telemetries[a].value !== null) {\n                telemetriesData[telemetries[a].key] = telemetries[a].value;\n            }\n        }\n        if (Object.keys(telemetriesData).length) {\n            var url = '/api/plugins/telemetry/' + entityType + '/' + entityId + '/timeseries/scope';\n            return http.post(url, telemetriesData);\n        }\n        return null;\n    }\n}\n\nself.onDataUpdated = function() {\n    try {\n        if ($scope.dataKeyDetected) {\n            $scope.checkboxValue = self.ctx.data[0].data[0][1] === 'true';\n            $scope.currentValue = map[$scope.checkboxValue];\n            $scope.attributeUpdateFormGroup.get('checkboxValue').patchValue($scope.checkboxValue);\n            self.ctx.detectChanges();\n        }\n    } catch (e) {\n        console.log(e);\n    }\n}\n\nself.onResize = function() {}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true\n    }\n}\n\nself.onDestroy = function() {}", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-update-boolean-attribute-widget-settings", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{},\"title\":\"Update boolean timeseries\",\"dropShadow\":true,\"enableFullscreen\":false,\"widgetStyle\":{},\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{}}"}, "resources": [{"link": "/api/images/system/update_shared_boolean_attribute_system_widget_image.png", "title": "\"Update shared boolean attribute\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "update_shared_boolean_attribute_system_widget_image.png", "publicResourceKey": "zAupsZVMEFIohSErelHC0Umw22uaj8aQ", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAAAAABslHx1AAAAAmJLR0QA/4ePzL8AAAIQSURBVHja7d3PSxRhHMfx+VcWgmg3ltKD7BJshyBIQusQ3vIYCILCRMyhH6xShlGWMGq1p1gIibxFULRR1F4ixcNqdNhmUXFipWnTYvfZ/TrjJqFQ13ge3p/L8MztBQ/P8/0eZr6WNHyvrHk8vy5WoxIo0TwqqDQsPxADEviWp0yAKM8qixEpAwECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAEC5H9DSiP2noyUNIXsc9j2qKYQ2/732lzI7LyukN4jUV7uLrtndIWcuP4lzKYBELf9rD66/bzZhqw9vFcM37ReTzz5pR3kc8fAWPriDmT58NDl5F0R51j2zOm6PpB+13VfyNNbIm8O/Iwg430iz87Lu/iaNDKz+kB6Hcd5HH60PDedjfkR5FXi2oeWyI3j+Xy+56puW2vh6KXcnTZEFq90pQripAbC5HSDDDoin9qQjxVRkwk1dUrLU2v4bLAxGFuNIEPnanK/s7WSeNDcct7rBvFOxg7djBUjSLX/YDJdEHmbiccv1PQrUb7++T/B9/Xfd8sPikbK+L81VqP7HEu0ukCAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAADEUYsyAYDNGNn/zrboZQ7SVZcZYcyXbTFGWg8aBYQQAAAAASUVORK5CYII=", "public": true}]}