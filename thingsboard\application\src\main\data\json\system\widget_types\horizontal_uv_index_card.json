{"fqn": "horizontal_uv_index_card", "name": "Horizontal UV Index card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_uv_index_card_system_widget_image.png", "description": "Displays the latest UV index telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'uv', label: 'UV Index', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"UV Index\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.ceil(Math.random() * 4 - 2);\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 14) {\\n\\tvalue = 14;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"light_mode\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#80C32C\"},{\"from\":2,\"to\":5,\"color\":\"#FFA600\"},{\"from\":5,\"to\":7,\"color\":\"#F36900\"},{\"from\":7,\"to\":10,\"color\":\"#F04022\"},{\"from\":10,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#80C32C\"},{\"from\":2,\"to\":5,\"color\":\"#FFA600\"},{\"from\":5,\"to\":7,\"color\":\"#F36900\"},{\"from\":7,\"to\":10,\"color\":\"#F04022\"},{\"from\":10,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal UV Index card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":null,\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "uv", "ultraviolet", "sunburn"], "resources": [{"link": "/api/images/system/horizontal_uv_index_card_system_widget_image.png", "title": "\"Horizontal UV Index card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_uv_index_card_system_widget_image.png", "publicResourceKey": "jYs2omvZH08s0kxjep4IF5i35zHN3atD", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAArlBMVEUAAADg4ODf39/g4ODg4ODg4ODf39/g4ODg4ODf39/////39/eAwyzg4ODCwsKQkJD6+vrn5+fz8/O2trbt7e3b29vV1dXOzs50dHSQy0bx8fHP6K88PDzv9+XHx8e7u7v3+/Lj4+PU1NTH5aPA4Zatra1YWFjY7L2enp7IyMiqqqqCgoKYzlMhISHf8Mqv2Xun1m5mZmZKSkqIxzmo1m6g0mDn9Ne43YgvLy/H5aK3Tah+AAAACnRSTlMAvxDv36eQgG8gyT+wCQAAAqtJREFUeNrt2OtuokAAhmFqtYevODDAgFIExPOhtrXHvf8b20HcqNntYv9QNN9jgs44MXkRBTWIiIiIiIiIiIiIiIiIiIiIiIiIiIiI6Nsa162LU3fTMIzbpufcnTqv2TBaIc6Ad2Vc2jgHl0YbZ6HNkJphSN0wpG7OPiRdDnBSvgrpmY84EIZAP+xDb4vRVhyjFo4O6VjoP4QPIeIoH42wZVmo3uppUh7S6w4BpB+T/PFycBCC8RSLzibEt6xFiGlHzyc6a+r3F6jG6uPFNO/LQ9LublXXHB6GJGNE8SYkjpLONIn8Z8uP/HESrztTVGJimseFoPc2QToZDnrA0y8choSfcYQiZAxLyzfROLLQ+UQ17t8GaWnI7vAytSH2LBbwI30XWXsh0+diE6K/XseoTHnIriM3wE5f7/c5ED/090LC/M0In8dRv2PFa5SoPmRoFl5S7AnxD2GxKVF9SLrsdld4M7fe8dTt1vDMeGzIq7m1OtmQvw6tejo2JN1+2IeoqW98/da648iQ3usE0CfEd+B116Js/OEdvEB7f0rmo8xDiUpCegeXKANsCQeQgWNnypl6gIR02vkwUE4mnblnZx7gBAJ2JuRmKbJMzyoUqg9Jl0Noj5NN1H6I7WezQPnO3AZcuFK0hfJnKpOurW9eYgOOgJ6VgK/0c0Eg5EyiRMWX8TrEcX3hBa49RxESwBWOUjN/roejIChCEjuQsPXSQMoggVIoUXVIIjxXCF/ona8ANxhJVyiZJGqWzO2k7QuBTYgvRnnITMi5G+jlDkr88E9dGzLAfzlqplDu5/98sL2y15b4Wo1C6oUhdcOQumFI3bSNS5wD+9K48nAGvJbRaHp3p87xmreG0bi5OHWt64ZBRERERERERERERERERERERERERERERN/1G1Juby0y7njrAAAAAElFTkSuQmCC", "public": true}]}