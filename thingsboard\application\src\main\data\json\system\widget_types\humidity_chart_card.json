{"fqn": "humidity_chart_card", "name": "Humidity chart card", "deprecated": false, "image": "tb-image;/api/images/system/humidity_chart_card_system_widget_image.png", "description": "Displays a humidity data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'humidity', label: 'Humidity', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: '%', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'humidity', '%', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Humidity\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#D81838\"},{\"from\":20,\"to\":40,\"color\":\"#FFA600\"},{\"from\":40,\"to\":60,\"color\":\"#5B7EE6\"},{\"from\":60,\"to\":80,\"color\":\"#305AD7\"},{\"from\":80,\"to\":100,\"color\":\"#234CC7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Humidity\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "moisture", "dampness", "wetness", "humidness", "moistness", "dew", "water vapor", "condensation", "dew point", "steaminess"], "resources": [{"link": "/api/images/system/humidity_chart_card_system_widget_image.png", "title": "\"Humidity chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "humidity_chart_card_system_widget_image.png", "publicResourceKey": "AT7x2pHqGMG9QTmM7JgcDm6FetctLEem", "mediaType": "image/png", "data": "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", "public": true}]}