{"ruleChain": {"additionalInfo": {"description": ""}, "name": "Device Profile Rule Chain Template", "firstRuleNodeId": null, "root": false, "debugMode": false, "configuration": null}, "metadata": {"firstNodeIndex": 6, "nodes": [{"additionalInfo": {"layoutX": 822, "layoutY": 294}, "type": "org.thingsboard.rule.engine.telemetry.TbMsgTimeseriesNode", "name": "Save Timeseries", "configurationVersion": 1, "configuration": {"defaultTTL": 0, "useServerTs": false, "processingSettings": {"type": "ON_EVERY_MESSAGE"}}}, {"additionalInfo": {"layoutX": 824, "layoutY": 221}, "type": "org.thingsboard.rule.engine.telemetry.TbMsgAttributesNode", "name": "Save Client Attributes", "configurationVersion": 3, "configuration": {"processingSettings": {"type": "ON_EVERY_MESSAGE"}, "scope": "CLIENT_SCOPE", "notifyDevice": false, "sendAttributesUpdatedNotification": false, "updateAttributesOnlyOnValueChange": true}}, {"additionalInfo": {"layoutX": 494, "layoutY": 309}, "type": "org.thingsboard.rule.engine.filter.TbMsgTypeSwitchNode", "name": "Message Type Switch", "configuration": {"version": 0}}, {"additionalInfo": {"layoutX": 824, "layoutY": 383}, "type": "org.thingsboard.rule.engine.action.TbLogNode", "name": "Log RPC from Device", "configuration": {"scriptLang": "TBEL", "jsScript": "return '\\nIncoming message:\\n' + JSON.stringify(msg) + '\\nIncoming metadata:\\n' + JSON.stringify(metadata);", "tbelScript": "return '\\nIncoming message:\\n' + JSON.stringify(msg) + '\\nIncoming metadata:\\n' + JSON.stringify(metadata);"}}, {"additionalInfo": {"layoutX": 823, "layoutY": 444}, "type": "org.thingsboard.rule.engine.action.TbLogNode", "name": "Log Other", "configuration": {"scriptLang": "TBEL", "jsScript": "return '\\nIncoming message:\\n' + JSON.stringify(msg) + '\\nIncoming metadata:\\n' + JSON.stringify(metadata);", "tbelScript": "return '\\nIncoming message:\\n' + JSON.stringify(msg) + '\\nIncoming metadata:\\n' + JSON.stringify(metadata);"}}, {"additionalInfo": {"layoutX": 822, "layoutY": 507}, "type": "org.thingsboard.rule.engine.rpc.TbSendRPCRequestNode", "name": "RPC Call Request", "configuration": {"timeoutInSeconds": 60}}, {"additionalInfo": {"description": "", "layoutX": 209, "layoutY": 307}, "type": "org.thingsboard.rule.engine.profile.TbDeviceProfileNode", "name": "Device Profile Node", "configuration": {"persistAlarmRulesState": false, "fetchAlarmRulesStateOnStart": false}}], "connections": [{"fromIndex": 2, "toIndex": 4, "type": "Other"}, {"fromIndex": 2, "toIndex": 1, "type": "Post attributes"}, {"fromIndex": 2, "toIndex": 0, "type": "Post telemetry"}, {"fromIndex": 2, "toIndex": 3, "type": "RPC Request from Device"}, {"fromIndex": 2, "toIndex": 5, "type": "RPC Request to <PERSON><PERSON>"}, {"fromIndex": 6, "toIndex": 2, "type": "Success"}], "ruleChainConnections": null}}