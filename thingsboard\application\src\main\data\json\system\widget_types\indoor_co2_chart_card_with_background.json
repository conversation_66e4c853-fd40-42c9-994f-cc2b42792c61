{"fqn": "indoor_co2_chart_card_with_background", "name": "Indoor CO2 chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/indoor_co2_chart_card_with_background_system_widget_image.png", "description": "Displays a indoor CO2 level data by combining the latest and aggregated values with the background image and optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'co2', label: 'CO2 level', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'ppm', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'co2', 'ppm', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"CO2 level\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 160 - 80;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 400) {\\n\\tvalue = 400;\\n} else if (value > 1600) {\\n\\tvalue = 1600;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"ppm\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":600,\"color\":\"#3B911C\"},{\"from\":600,\"to\":800,\"color\":\"#F77410\"},{\"from\":800,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 160 - 80;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 400) {\\n\\tvalue = 400;\\n} else if (value > 1600) {\\n\\tvalue = 1600;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"ppm\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"ppm\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/indoor_co2_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"CO2 level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"co2\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "co2", "indoor", "carbon dioxide", "carbon emission", "carbon footprint", "carbon output", "carbon pollution", "carbon capture", "carbon offset", "carbon reduction", "carbon neutral", "climate gas", "fossil fuel emission", "carbon cycle"], "resources": [{"link": "/api/images/system/indoor_co2_chart_card_with_background_system_widget_background.png", "title": "\"Indoor CO2 chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_co2_chart_card_with_background_system_widget_background.png", "publicResourceKey": "EWhlvEZgiTS1uEmiIjTWOTRZ2ZN003rg", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/indoor_co2_chart_card_with_background_system_widget_image.png", "title": "\"Indoor CO2 chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_co2_chart_card_with_background_system_widget_image.png", "publicResourceKey": "bBsQkcrEiJsi0BzouD8ZTD0oHPlAbN6q", "mediaType": "image/png", "data": "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", "public": true}]}