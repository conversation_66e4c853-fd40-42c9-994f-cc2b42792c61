{"fqn": "simple_wind_speed_chart_card", "name": "Simple wind speed chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_wind_speed_chart_card_system_widget_image.png", "description": "Displays historical wind speed values as a simplified chart. Optionally may display the corresponding latest wind speed value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'speed', label: 'Wind Speed', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'speed', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Wind Speed\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 16 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 26) {\\n\\tvalue = 26;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 16 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 26) {\\n\\tvalue = 26;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0.2,\"color\":\"#7191EF\"},{\"from\":0.2,\"to\":3.4,\"color\":\"#5B7EE6\"},{\"from\":3.4,\"to\":8,\"color\":\"#4B70DD\"},{\"from\":8,\"to\":10.8,\"color\":\"#305AD7\"},{\"from\":10.8,\"to\":17.2,\"color\":\"#234CC7\"},{\"from\":17.2,\"to\":24.5,\"color\":\"#F04022\"},{\"from\":24.5,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Wind Speed\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:windsock\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":1,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"m/s\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "wind", "speed", "airspeed", "flow", "gust"], "resources": [{"link": "/api/images/system/simple_wind_speed_chart_card_system_widget_image.png", "title": "\"Simple wind speed chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_wind_speed_chart_card_system_widget_image.png", "publicResourceKey": "sStnTtOeiqMhjXqcDnNLpucU6jqQxUwh", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAgVBMVEUAAADg4ODf39/g4ODg4OD////h4eEjTMchISHHx8c8PDx0dHQvLy9YWFjx8fHI0vGQkJDk6fisrKxaeNWCgoJ1j9w+Ys7y9PtKSkqenp6tvOrV1dVaedW6urqRpeOfsecwV8tmZmbW3fSDmt+rq6tohNhMbtK6x+0xV8rw8PDW3vVvo8R5AAAABXRSTlMA7yC/r1EOHTEAAATVSURBVHja7M9JAcAgDAAwyvXEB/4NbjKgJA5SAAAAAAAAnlNbzNv1+j9ij+utqKWvkcBuZY4UIktkihxG5DQfu3Ww4kgIRWF4dS7n6lVEXCjiIpD3f8ZxqphM6GSgpxeJAX9EQd18FEJtyGptyGptyGptyGptyGo9hUjGl2LqQB6ANrSGu1w6ll58EvwzZjz0Aog6DI24zwpgFQgB5QmkMyTn4nIQdlpw9VKa4MwHKBlhaRqhqlkBaJYT4g2IbDFLHpidxzFnOW+974vQw3MkJpw1Qws2OhVhHlkNVGRWd95IbJjJ4QeKBSaIc8E6Emt9JyReWC43SGcPyZfBeEAq4DyqR7TzRqHzAuGAUueY8ugdEDysQN4FsU5Xfcr976Fl01xLxQEJx8J2e+yQqzMRKsCcGUKlBAtzCPWdjz3iS6EaImu5h9gN0gYQmW6QoaoIYc5dON4JeSjxAjiOe0ioIpYOputQXoUFjTJJkIZkgmuHXaALQZRXoFDuIeLo3AGJnuTcYCATcHXH4knrGI7V1oE8T/CnKBEQaoznQTw3b/NjL4BIwc8SKv63Jf+1YhJ8s7Uh329DNuRT2pDV2pDV2pDV2pDV2pAXpC0l/XxID/ydyx8OGUYrqTgyfTREjCVilkj9ZMgv9sx1V1EYCMC/hhluBVZoFREQ1kv2/R9wh1bkHAIH2E0IJH4JtIDE+dJpB9QlFwwh+XsW8aiNS3hk71fEpiO0xBTvV8Sl+IuUtzcRy/wNwTh0hjcO2fsSiYmJhR4EBzpCcnclEpPnXqkJWhy/hS7IE1sWsV3neL10kRBZun44Pm/fovLptGGRkDRhr3JYDhH534M6kf/PIkFdBsBkaEjhjSqyA/yIfM5ZYCk+n09Epy8Dojnb/ZCE9/a1rCUiKm/i1yKyLxIkJeLPIg9MYBLfxHYhrxuQUX4TuRbLXz0iulqzRVATmG4aNSgw5MhMiCR86xSCSHRrqxmQiTz0GgvH0SvbTJEyCQIjokzTkR9ylb1FFAtG6SPiXpAG7UmUuq3SdFzo0qZ9qCe2dZxYYi3XY5VQcC9kncvM1AJ4iVSIqn8JOpEEbzUyxR/JexlBQ6ozK8qQkdVo6T6B4Uh05I11JrBE23GIXDFHBIyIafK7zFJ40xPBMrk1AZfJATF7Xa705JJ1LvGpoEe/WouYGN9auOLZy0QKNNSjIpH+rFT6gPcQoTT3V00fCxjCIg867LNY/DADC0fkIIMouPPBsIjpIeY6pTDSTaFzUjd6lRjgF13h/1goYlAlJrNF7rw3BUjWDxjhSr/XF2FueJ8rUr0uqlrP/2L0vWldEU6NhSLJl9pZsEswnFlHWFdEYg26uc0VkSazHnlhcjIf+lKHfq8sUuOz0uU85VPJY1okeCkX+GShqBxatWyPYlhZRElEydtdz+LntIjJLHNjdit1kexxIl3F1xWBKEHEMld6dJJpkVIC096Ih2iomIXArCDSI2qDUTBJ2srCWBGJqfeWtM0fsavgZ1vhk3fRvY2LTOGQcwbDrkWE1z3k7loEhICWfYu8+Yh8RPbCR2RrfES2xkdka3xEtsbfdu6gAEAYBmBg26174gP/BrEB5c5BDETI2wwKyR7hxBqwDuq+dlQOmAfdWRG1z9flqgAAAAAAAPibBywlq1F+6U0fAAAAAElFTkSuQmCC", "public": true}]}