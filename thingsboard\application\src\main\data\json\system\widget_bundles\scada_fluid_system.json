{"widgetsBundle": {"alias": "scada_fluid_system", "title": "Traditional SCADA fluid system", "image": "tb-image:c2NhZGFfZmx1aWRfc3lzdGVtX2J1bmRsZV9pbWFnZV8oMSkucG5n:IlNDQURBIGZsdWlkIHN5c3RlbSIgc3lzdGVtIGJ1bmRsZSBpbWFnZQ==:SU1BR0U=;data:image/png;base64,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", "scada": true, "description": "Bundle with SCADA symbols for fluid system", "order": 9300, "name": "SCADA fluid system"}, "widgetTypeFqns": ["horizontal_pipe", "long_horizontal_pipe", "extra_long_horizontal_pipe", "vertical_pipe", "long_vertical_pipe", "extra_long_vertical_pipe", "left_bottom_elbow_pipe", "bottom_right_elbow_pipe", "top_right_elbow_pipe", "left_top_elbow_pipe", "cross_pipe", "left_tee_pipe", "bottom_tee_pipe", "right_tee_pipe", "top_tee_pipe", "right_elbow_drain_pipe", "left_elbow_drain_pipe", "left_drain_pipe", "right_drain_pipe", "short_left_drain_pipe", "short_right_drain_pipe", "horizontal_broken_pipe", "vertical_broken_pipe", "long_horizontal_broken_pipe", "long_vertical_broken_pipe", "top_flow_meter", "right_flow_meter", "bottom_flow_meter", "left_flow_meter", "horizontal_inline_flow_meter", "vertical_inline_flow_meter", "left_analog_water_level_meter", "right_analog_water_level_meter", "meter", "small_meter", "small_right_meter", "small_left_meter", "leak_sensor", "centrifugal_pump", "small_right_motor_pump", "small_left_motor_pump", "right_motor_pump", "left_motor_pump", "right_heat_pump", "left_heat_pump", "short_bottom_filter", "long_bottom_filter", "short_top_filter", "long_top_filter", "sand_filter", "horizontal_wheel_valve", "vertical_wheel_valve", "horizontal_ball_valve", "vertical_ball_valve", "water_stop", "vertical_tank", "stand_vertical_tank", "cylindrical_tank", "stand_cylindrical_tank", "small_cylindrical_tank", "vertical_short_tank", "stand_vertical_short_tank", "large_cylindrical_tank", "large_stand_cylindrical_tank", "large_vertical_tank", "large_stand_vertical_tank", "horizontal_tank", "stand_horizontal_tank", "spherical_tank", "small_spherical_tank", "conical_tank", "large_conical_tank", "elevated_tank", "pool"]}