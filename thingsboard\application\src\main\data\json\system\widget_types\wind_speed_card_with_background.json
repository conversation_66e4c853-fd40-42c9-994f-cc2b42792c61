{"fqn": "wind_speed_card_with_background", "name": "Wind speed card with background", "deprecated": false, "image": "tb-image;/api/images/system/wind_speed_card_with_background_system_widget_image.png", "description": "Displays the latest wind speed telemetry in a scalable rectangle card with the background image.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'speed', label: 'Wind Speed', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Wind Speed\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 16 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 26) {\\n\\tvalue = 26;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:windsock\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0.2,\"color\":\"#6083EC\"},{\"from\":0.2,\"to\":3.4,\"color\":\"#5579E5\"},{\"from\":3.4,\"to\":8,\"color\":\"#4369DD\"},{\"from\":8,\"to\":10.8,\"color\":\"#2B54CE\"},{\"from\":10.8,\"to\":17.2,\"color\":\"#224AC2\"},{\"from\":17.2,\"to\":24.5,\"color\":\"#F04022\"},{\"from\":24.5,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":0.2,\"color\":\"#6083EC\"},{\"from\":0.2,\"to\":3.4,\"color\":\"#5579E5\"},{\"from\":3.4,\"to\":8,\"color\":\"#4369DD\"},{\"from\":8,\"to\":10.8,\"color\":\"#2B54CE\"},{\"from\":10.8,\"to\":17.2,\"color\":\"#224AC2\"},{\"from\":17.2,\"to\":24.5,\"color\":\"#F04022\"},{\"from\":24.5,\"to\":null,\"color\":\"#DE2343\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/wind_speed_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Wind speed card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"m/s\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "wind", "speed", "airspeed", "flow", "gust"], "resources": [{"link": "/api/images/system/wind_speed_card_with_background_system_widget_background.png", "title": "\"Wind speed card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "wind_speed_card_with_background_system_widget_background.png", "publicResourceKey": "4mQGTRZXK41qpnvyXevK6ghyDGmqitpH", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/wind_speed_card_with_background_system_widget_image.png", "title": "\"Wind speed card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "wind_speed_card_with_background_system_widget_image.png", "publicResourceKey": "IaLNXJhFpynUAormSlG38rtrHWe7FXks", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAANYAAACuCAMAAAB9TAOwAAAC9FBMVEUAAAAHBwcQEBCWcVGwjGiOaUvI0tycdVGyvsmiflexvsipg1zg5+/f6O+wvcn4+vv3+fr7/P309/j2+Pr8/f75+/36+/v9/v/y9ffw8vXz9fjx9PYiSsHv8fT5+vr3+Pj3+/3W3+Xb4urZ4ejT3OO7xc/y9vq9x9HJ09zCzdbe5e3k6/G5xM7H0drL1d729vbp8Pb29/jR2uLN1+Di6fC2wcvz+PzG0NnEz9j5+Pjo7vTz8O09YMn29fTs6urP2eDw9fnBy9WkfVjt7Ovw6+aed1Th5uni6OsgICDt5d/o3tWddVH29PPw7++zv8rc4+f49/b08/Py8fDr6Ojz8vLd5Oy4wsz09PW/ydP1+vzu8/fY3+ebc1Dg5+/v7eygd1GdcUzu8faie1Xs8PPe5emYcU3b4ebq4dmpueZyjNeZb0o6OjuedE+sh2Pm7fOgeVTZ292YbEfAwsOieFXl5+nOtaGngFygelnn3NPf5fXs49ykflzKsp718u/q39bk6u7t6eWmmJGkeVHDzu3y7emNot+cb0np7vLv6OLBytOYmZqng2KadVWVbUuTakfc3t+7vL6rhF7HyMovVcWrgFqhc0+lpqikpabs8fhVVVbm7PDp6+zr4trY4eaPZ0bf4ePRy8mkmZegcEiVYjvr7e/P2eKzs7SwsLGnfFWbbEefdEycakPOz9BXds/QuKZYdtChnp+acU2WaUNzV0KMYj1/l9q2t7ihkouQXTXMzc+jpaZwcHHR2vG2xOrWwrK8yOjUvawuLS3JsJuKi4yjtOK7j2JKOS/U1tirq61oTz5JasyefWKrf1VWQzbWz8yspKK0o5e0imK3iFamdUqDXDTS1NSpqauNbVSbruNwidRkgNN9fn+9vb+loKKgmpeDZU6ib0BAMivX3u/o4drax7pjYmOXeF9ISEmjs+Gse0l/X0V2Vim9yenb2djGv7p8fH1eSTxVdM6EfHewvuaWqd+Jn9vd08ytkn9Yds+ViobAmmy4lHaSxKBOAAAAD3RSTlMABwb+/iQgtu/vwMC/r69sDufmAAAfp0lEQVR42uzUvW4aQRTF8ciFFUX5sOwGg1IEsHASNlIwVugQTdLFVVbIaAvkaF/DpeVXiJSS98w5c3Z12bnM4hTu+M9dqH+6O/vi0KFDz9vxq5c/q66v+eR5jh80zsfsIjRiy9FyuVgsPqIZ+8Yy/c04Gbus+6Ku2HxzNVeT+SQ0nAzVh7rVCsMGA8xW/e1+hT6Fqr83x05FlLlQzoEph4gjFkDLRz5CkQVGVKZkwkilapZMJJkqciVEMvVxqMJYg9j1NkIpkBqrwsAF1COXpbgsTfjJUquSi6KNXLYpJk8YgGIUjxUtyypeR6yahFF5jmUFkjY10gELKO7KWPTYkgxlq4JJwYQAs1VBNEwsaiVTeKJVeRJVRd+zaphUdqvIokuFF5BVKEbRLNtyVSipjAUVXQCBZa6GqjbZuvo+mOjyrMKzuCihpCIo+lgstayAWhCltCq7UhlNFMnEkSrsaUPVkKaJv1RMW9qJ4p74sAaITacJFlH6XARVTpJUYgUVtiWUqSiSiiRlrCpuSrtyb6Ch/JfCuTj6VJiJD/Msu1bjgMJAZSiMPhfRG4gv4c5b1UBpUeFObUjSoOha2bvX1098p7QvYxU81tSx7LOOxgooW5XeQrHkCqbMPuh2qexWCUUX0rJU4lqZiqKBW1V8rQptCyKqItYRUGJdlmX5F4/rprxhv+u+V/2Iut/dH9dDs7t9re/Waz6+24cCGcuqVXn5vqXzqF6jTq/TrNvFo05xuqfpzvZ3cnbieofwczstePy2ZMLLV/63ylgplWCtqq84CYyCybtACrYeQBjktiWVsbyp3dVxKrpMlV7W/jXplyoHw4AllGfpQ9HO8vXSMIDw86RNtbqIoSvC2LoQWOzesY5gwkmzzj2rh0mZWhdlHp4we2A+I2lbcrltjcduW/uWFakaMDMFEh6Peuo7mDCBw8e2xT5HrGBqv1utFwvHo1TCZNtKcfTDs3NVHGNJFbMAIuvCsdKb8q+goTre5WUUJVEipT4UOsYS6h9pdqziNhCEARjyBEEisBFOGVKkdRVcpNwub5FmwQihN0gVNS4CNhv3qty6cBQIQq78BrFTGYxSBMxxd33+3XG8ccYjYfIfOuHrPv7ZwewJLPet4r1g6tsWcleJcKbo6Z68K2udNKQKLIfibT0hFRtCGXZdhe0nNcVxnU2J8SZKYMHkVEJbL9EWW39C5CUYSKEtVhe9maZP5ZviLGcShhAoxmIyYV+Iy4KbgAkRmuqAkYqxIEKGjAUUZxGqh3WBUh0jyCu7jXQ+USxvhyfX+J+z5UScNbzbDLtZeQpNnuNJVV4mdLIIdJxFPvlqJexBn6yaX7rkBBM/WwRjbRHqlWOFsr4NBj9FFLF0oZQ1SpV61eiUUOQylQcUtdYmZSyEXFM9uSiryyTAwPImzgKJLpYYC7/3MkuZSuVa5+pYK7W6OFLEynQTpa0VRpCxOlRPZdZbHKswhIz1klhkQog1GAw2d8uH+w9DrlKVUT90/UXZg8psktuZNccoSq2pas+a6Qy2JpoeKtOitIU1No3iDC9wCtMeAgumbhciscS2EGrrObGorcF3sP7k0932cf1tf0Yhhc6PtjoqDOMXnaS6LXeAGFMW2rPmxjQp3o3eZcZEab2DKE7xqbVoCn90LEKJbRGonzX+zFlU2LvQlWct9441+rV+3Abe5vH8/XalM1sUBq/Es77gma30jIYQSSuj0U9jomiq00Jnk6le7OoJXpODCUMIUecORPpZbAj91RK1RX1dDOEIH/bOc/+w3LgKw2avG71KdaETamvmnkxnxKJMjfUs/HmnXbKKXtYGFlS3jh8fQs56Sax3MHnSVdYdMGtiQeVjaywLU9u/WWloa9f6CfSsUi8KPXdrcFf7rV5RWxD17sBeFoWxTnkP1U2sQh+wOPQxSYgVucca9HLahFVWmhayosTZmtSHrGjnmT5kjY2xJksDlkPJXTHVTSy6jkZbyAuBNXr8vvnkWOrMyswsUaUpk6Q0SWrKyD+2todd5FLa2lQ4W3VVuxW4wMcijstW2zKOm9o0ZiIvQOLQq581ZiyIKO8IdGZt7x3r08NyOzrvw833Nb+OoURSGiN9bWcisavbV4britrCEAYWDMuw4Efb7w/rr6+Fm4t+FlfRmYqluphIYo0dCb8Y66QC67KtNYYQ9SzX93vpnhMaFUxCFmUcSIwlhbFubMub8MNYOFvD513XnIl39bKcKeKozjCTzBr7vBFYV9qCo/s6MAxgp0to6r9Nz/wQnlhvrrEQzoJJRhGJcltVtNFjQXTLBBILJoSz0NVH/Peqh6WupK+peS5fLf0m3e5ZnYbCOIB/hMZEi63UuwkOUi+C+IJVqVQEBVsLVYeCKM6XklHRQRC7NGRIxAx1KGbp0Clgv0C76XILgi7O6mfw/5yTmJqnT9LqkxevIhd+/J9zctKkY4mFvTgo7MfoVNUdyFkUFVhvOEv+MFBlFbY0K67Hh5zlOvouxB6nonFkU1yTocjaxkUqOqqXLpGKs4CC6c3960/Wb6t+gCVkVY1jmo/oHIyINKqZo2GAn2o12oPaiH4euw5ItlUbRBWrYk9INXVmYwN/GWuWbdNpUqnQWVotyaxSVZE2sCBSjxlFFgWFI02KwkpY7jyaP26tolXLGQ50PtiH0WplHswi37GWkT8dL/2DiRctdUt6aD8vXCqWF3lTw/Wi2WLihd6URtZ2JNWF6MVrAKELz/K03mjVaYEFClQsK2KZ1WorGM1GH1etURAMzZRlmjM7/GgOnNp44voWUhtEE5zRfsQa+7oJkaAbGjPbWC4WkWHPKtsGRSIlO6U7kKeFrAAT0xKuVDotMFbubGQehvMsyx+Hh+DU/IGjWM7ScdyEdRhq1sQbfA2N4cT4sHC/lkrDyVYqlROVYglNSKJME574w9q0ADQT1mEQPJ4fBEjroxl9DIY1sD5GpgMWkMFgYDmOPbOmiuWG1mRCLAMsezZRY2vsGdMloqp4i/FysvAKQbQDo2yolHX2bHZsZVkqpO9fvvzgrLVnVlUznM/nB4erMDwIwjAMTGdOU/pqHg7MoeNPrWDlh5D5ztKyl9OK44c2qYwQY8v1v/rUhF/9r0vDXi69Rcn1PtjFScUu7AkLImJl00qGVjK20qUFbkEYSpnis3wFHlq85PuqhW17Yz4DyvNfWmkTZtPSJlRrLazExQcWaeiUW6FlMpXssj9E09JfZaCYiA7OoqDOYt+elR1UOMxtipny4+ICw9qzrD327/E0wVk4OAuq65zF+4/O+Rz5gRxAoopfgS1rH0X/PY0Km9o5i1ScBRKp9NgSFxYcxUm65If22y7ZjT2g6vX6/t6ewdJCMRZQnEUqnZb8BL8gKys9IyyxA2UTc0EF1z46kYlYE6I463TydlYrvbtiK1v2xEp+HiewRNQmFrl0UStCJbNiFGehSAVW7us+hSI5KyNHBZbs6vUA04mJrEvbsPgFK5kDqxIqTUkBGQmH6EpQ3IVZg1gaJriK07oAVu7j+/+aLYpQYmC9Ith5mUUqkVXVLBn1N20XlUIVw9qQadh/sviSPfdCBYsUVcGsbuSvaw2LXG1UkpjIeruBpd8mbkmfLxWnRX8QbicVNsmjVxPxVN9bh6WgI5m0JNZNPlskcQlJYVuHMVMOKzcpmOJgqBMpsG5XwSiw1EVlxWG9fZtladSFmMXCKlwzaY6507IiF7VeOjDk1YVMD7HEpI7zioQezLKUCq4zLaEDiSXFtW5jLhmWf++xGdZNYZqlZBZQj2h7xFlAgXVzY1hgiWHJ84Whtp2zgogXDTHt6miYAVj5GEwYXtbbRw/1O9YZFkzqjXawYOLr27ysNGrXtxzlnKSLLgXW68EFGAWmXVTWI10sLagIBdZx4d0ssfksDZNNvHKndLmSwDoJrFQqK9YDHRUS42lBRayCx3EcJ7CKFoFyVGKV48DaHSrA4NJpQaRhLK34CxWKxZ+IiFOFLLJklRyVhDpC+5GygtXhuoPqtOEqlymth7o4K/mG0k1+N6wq7znIPnfJLHlZofpMMCVTeblcOonAunf6zaZynQRMZlFSUGVY2mTKWcljaueoJBOKVMqGQmIn9+rtO81Go9EnF/0+S2aRK2GlKtoyKrZU3/UmmIuKWZqUuPZ7nWbj3r0mXD36UMDUptdP3cz7hFqFepGYqnqnYijKSpjVFShPZXBVcpZVfxXi0iy40Ii36/XjIB24H/xvn7JpJV8nIxaf2BlM/rgiZxXISGwKFJLCvs4q7dW7/cZLVINg3a7tfvj06dNnVAELQQksK41MaD8pK/kBDzOJUWmXUW9f7t94GcP6d95/pnr26/3PDCtGaRZAxJJQOKjk/pNLiqpoULEuvNK+/AquGPb+2ftR8BB1jbFAonpR1S51MsES0xKuVbvcWhUPKpZWOhl2LsN1S7Ga13RxFkqzyJMsbqvStE4bZ+VHJc4VuWltrGNw3b39XOfV6N+pKRTCeiimRWFxVMbHRHJMxffApWISz6t88e7tzqvGy3uNfr9TUahNrOSLp2DFLhnFYUZRTDgJ978SCUdewVXv0kWZLl7H4xZkl2Mi6bTiBpSjAktYUuyelRhTrumonjf2sdZokqpuXkuKsaBCWOf0lCGjpPdh/uGzpbxFbXGB1SPWnU5v3zwls1DEki9WrPm2yUpaKrHFkth+MqutWF2wrhWwrr6jsMSZYmcWb8DiDtxCdVRdk7OsU4z1m5T7eXEaiOIADv4DVq1RMbZdV8XbdhUKq6kKgqdd6i+QIgiyRZKbiJecPIQinhR7KYqoIHhR1ksVIT0r3pUi6p/gWW9+37yZjs3LNKm+TW3Uuu5nvzNv0qTb6bsJ3FnkJ2mFSKKca5UjK/oolxaOoOwgPHIEKJHWNo06fuwR5pX00CZNJdp6ZcHnVsUke2DYBosa4ZWVnVCxTKYFFepR+avBJxIDaCbTiuUz+2Y6K2KTswcmSVpqatXXL14j1gY6IURcGdZp88Y3j8qOvyQavDaAsT+tfrZXJKNBILJyqVCJ74fFM4s7BlinMLkoLScLKGzlWEl/AMKU1RcsrWr2A/xZkOmDcw9sI98flggLHQOsU4p1pYCFkiypGr9WAssaYb/PlXDzU65UPy5gUMFqpSdW4EdFYVWrZmqBRT1jr4vFqkO5LNHXKZxB8BfrKwZOTk8P6XGggWUHH7ucxxYYz2M3iG9VNQbV1OKDJxdrG3KCCptkyeOK/mCUVKK/WPjSE0uiHWYNfiXN0LJ0WHEcD3fvTsJxTJrhmHf0Khz5gTbQX6T5WSGtyuoFw9qQLFNAoQQr/6cMmlivZlhILmXKzDoVN8k2kxaFtYVsJwMfFQ1399XOyCzDQ1+PwXSLx2+Y69qB5/2KhbqGyeVkmfeHuVuUFVioWRb+/0o8HifbZVkWTGqCESvwubYivTPSYYXIne5jaLn6syRmeTVMrZPX4FI9Y7+LxSrJypgquayY2gd/b/NZhLETi3IIJuMJbHZHd78tPQbxR5+SOMWk9ePM9Kry1GpfvrhhJpeTxShmuVVsEqzkr/7uSItN7KK0YppjpIoRU+KbPhEjN75nzhChTSyJTGZqrSsWXJhc+wBq5bIgkiznq5YkKwoT9V1PctNC2dYOVqR28Pi+6oEDswSH0BiWahdJGKbZQcin1MDScYHFKtkJNWqJWe6GQQo5t5rjMQloQrx2sQBzsDDkDOuT/0ndEzQIIZwl8SDUp57oCp5eufYRqtWSaR0SableOSdZ9sRmH+tU9vCPWBpUyIr5HjXmPhglMx3QjEJvbbW9fgGwjbMqrgMtmBBYy8FaWrorVuDiuTVlpDQlMlcLClj4ei1rZBtEvOWzLJ6uw6aQVuX8OpVZuojFMskCyrLcUUmWdcXEyjyj4kHoTsuw+H7LphOHEU3WwXAmKd3eV9rr7Xb7CkahZaEEi11gibBsb5csRo2iKFWWxLKggmwea4dISx68hwN6iPFY1ipM9Nq1y2ChNhysbWwiVu4Vbmp/cAkWT6tPaMtKEoGQOU53sNQxRZYVcTSoURDw30T02Kp1makF1Xl1+Y6unLhZjGKWRNmSLLioU1Bc6QBfBO6HoyhFVg7WDmIBJVn24H2CzxjT4KPvGCDWxEdO58Gqr+EAysniWsLEYpYYfm7WLz2tmjQHvvYjqIKYQxsMAZCsHbS5WOh+pvENA/yrfjihvhEbz8zUWlldq61p1iVidXNZ07Qcl4AdLN0qyIWi0FB0yJPHooGHK9g8CCXrqx/waotbGvi6JpCIMbiCIVijJ8gXmdXtFrHKni/D8P/KKMor/ESoUaymVRoM+tOAxkGwpVUoD7coCEZ8ABgEE7Mz3hFrLR/NxhF/xrSaLW97hVhrfLH1GrFetLrdRViLnK2Nm+7rivB4rHIXYk15fTINPR4yRIxBNEGEhUMozXrcnc968uRuydPQMJU9qWk8Hn14blafWrksOQbrKix8QsN66mY9WXqCWt4sCkv86MfcU0oeNmhoY5KzuI0Xw4A5j9cF1fBAnKa5uEGsm60uw7qCBRI2sGxW/39Z0QNFY4hWwGKUgIkxiBf8Iyyw1LUgsD6cAaoBlWAhLKjAsgNQepxjj1miGOUJkguGUr4C1vl6BWGBVdOsz3cbDYpLpAUTVMQqPLteVrXDozJJecUiDopvbhWmFoeF39SwcBHr1e0GXJDJtIA6fRUs9/uHzisZlGUVl4EUJMVhrdXrazWvqlirvB6/us8qwaKZtXz0UKejWdgWSUmquHhKeSVcEuRmVWoIa4b1uUElWNtItXzj3o3OZl2/FCErqrh7n+dAaVWZ0mlRYIUsZIWw9uypUodn1ntmNbJpLS93ljude0tg1XfVsyMw3+QxS6ksCJtZoPBLGRCrVF4l2rtXq3l4/J6qYq2rhevZt4YqwboBV+fg0uHOJkT1QhOzlEeYuLgFli3JcbtQeDilVUVazHrb5bCyrA7XrRubdTGl1E2izAsQJAklknKDTMMooO3RLJRSoZBWm1kvj0Al09rGqt6Hh5tl3+iLoiKYqlmTHYblUyqZFz+QWbweXzsL1pfrDa78tHoPwCo3BEmlNi2SrhK1qEmTgKLCeqxZ3x/NY/V67+/X89KqSRWbPKkyHm9+myipgYAcZo8wdGdZG4r1NJe1TaFQN27WVe2iX5wH65kZJVh6y/WYWjQl8pgyLLNwPXvzwZ1Wj+qFZlEZmUTRzCKQDMskRbvurBYWcUqGJFnvXSwy3frQ21xVpXGaVZFRCZJh7XYPQLtAldPQxgr2ZIoXLsX6ccvFguvD758/L62sQLVzlUu7RBPEh6NKNPSFYjLziIiCVdXr8bPnb7v5c6tH9erhu2dQ4fwny8CSLk9MKOEqyKrsGqUw7FK/FQVWm1kfW7lp9Tq9c6jem8+kMSVQvA7/K2rRPm7bno5Ksip/KLuD1iaCKA7gH8GkrmIobj5Cz0FyUrK0tJBKIFSkF8VCT7H0IKLFgAdPteRQD5WmKyEBCbKWkJKrl96Mh8ZTN5eec/Xsm3mTvGT2zXT233aSNG3hx9t5M9kNtAgdHlhfnrHVUv/qohZOqBFCpMrXSCyLVmGbCUplhWgmNdDBmJhchRVkxS0DqyTSH8O6JTA46rW6nx5ESbPwUn3Iw7LkwgWsqMkehKjqDsMmQCg+ZHFemWnWBRhAjiL0KAxOL3OyfnkTWTWWJVBBszMM95UHPu4QiUw8K0ssyysPdxbNKxgtUQvXWTgeGViQRr8ftaYoH3ZMOuuWbmGdVzA4o8gDd2zx/PL6xjawwoaR1Wx/7sQVycLoKjuJZ9GrQxcWUtRdh6iFC1hHbMsQrL1B6TiuilqRyd1lrhR8ugaro+6mYrW3DNWqHZVK8bCq6gSqKYymlGFWeZZ9rSMGgh3CvVq0Hj8G1t8lA+u6GbyKOks+lks/aUE7QNOWnTNhqbLu21lIOlahWJGs+AXHCkrdyyCoRf2qbIBC5c9MRs5sNO79xI0rSg2yvbiysvJ89Vm9HpX4anWDIBhF11WtBUoT1Cr1nMIyMSquKap2DgM+D3GbXNkCLFzbv+sX4YRlBSKDcWNZWohl3SpZXYqQQMBTjAuDKvy7nhNLXJGUrAHHCmQa426O6X73cNBJ9vObOohYnItqhecC8aSZC6u8Lln1PTPraDxaFi3QZ2wp9+3Tk7SsCn7BcNYFiwWtGFguh6Hn5xWrnTGyYKeby0Bw6YIbNuCe9Ucmht0gseQPMZNLooglXE4dvrIBrAvo8CZWZ9zM5U0pl2GAL0qmkCmoaHKLUx1irAs+IPMs73bWimDVL+KHRtZxtL9R5LJOgTcb6YFLuYsBJ0WD+r74HmI9NsTyXBYueNOuqFYkr3FxrJfxcL/1VORRmrzXovyKr0vlo7yUQdjmCXbqGWTzeFYZWWFtK7knrE0ml5NR/c+/mydsPswFHulPf1xMEg1KyiaIocDJcmJbBZRQUzGnaxrHEq0QWQNx/Vir1vfet/PeFeTd6jRv4YsezGWVyadkdDMQKUpK1SQioPCC433uRBXDKlYk6/rkpJXTWOevTw8Pd3/uPv9xI7PjlK+zIBhuGbvuVFLyzQHVcQq1TB6lJFxg5ZFVby/dfdDSWFCp3lWv1zv9tSbzRmTNmgNDdg6UmcJBdSDy8H2C/9uzctepgSjsiYidreC/IP4Lg2MzlWA5OGEgzZgQQjARY2OKNJsEAklhEzBaJFkRsgpaiQqKsuu5wnYeeICKBxaK2vjNeqyuN4KVX+Z4O2/mzXzz3sv+2J9uwQvENL7zj9hvaOGL6+Im/BC9lFua1uHDpw4Pz+39ORZ0/4D0R5oLt37Db0Fujp1Iv7nrNgOa2Zfc5viC1pYPtK4e2QhaX+POnbqOh8M79bv95z48wH6UPXv279+zf88xCBDnsh7+IKHgk9ZgBCIAcT4ZBYpje46hOYZu3mAYk2BdWwS08swcx/B8BhRnzpz+BvdRPuLVq7339m3btXMHfsrcuvPVszmt7Rs3LtMa3pnWd+5Mp9O6rlurd422NQxXua3oXcswrNLq4qgsY41R3LYKrZtV7igeuUjJqjqRZa6BJW42at1RPWphIW7rUVx6yrMs4ZZG3HheUXox5g2x7vDh0agd1WVrlCUmw15ZlvWwrMu6HQ6HdVNP66GuGocRSqdOHUbRFRgO27ZuW4+OB6+vXTsAXD165em9JVqx0elDu0q5buyxnhjMIIwzanJuKNX3WZpmWaYybnLT4+hUT02TppxTRlMtMuYxqpjnMKdnpsqUvg5DMEmFYsrw8BEDEGgQBAPmc5961HMo8bwwsBlhjhcyzyCksIuozJu8KYs1+fnreTFFLSbNtAQ/0PqEwxq6vXbo5BvQejaeJUu0OOMZDrM65Sx1e+X2HOdnYAU2VQVCZtWbppkw5qAQJm3pi474QkiHEM4dTxiMWZCltAiOyzxSdnCVdrFnGJ7rcs6tLGO95YQDPx8MbGDNGtvOcxsfiHYp96wSgRLXZVR6RdPkmHK9KNbYa9CfP38d9fwNjUePprrcOXXnzuHh4deHDu6e03o4XqLlCGkG1EyShPMkoEnAk4RCMCkPwsSUArfqWEIQ9HEkmslkOolVLKK666K4iKSALu5c14qEEQkErtVbAnQaz3csg8BRPfccx2McN2g1LCR27vug54fhIBwMiNUbvcoMlbkWYt91h/GwrqNJU+RgrmmBkCYFLjce3RxFUTQprq8R18+/Pf/61ssHt0ArTR8u0SK4Od+xfQIjeidH9lZXRoV0lE9CQoQsiGyKopnYRRFFxWQyqZsi6qK2c42ua8XI7QqlhNUaCg9u3pKWOuEarhEpSXwiLSmJxDYhFYzkwi8K34fCd6Q/CMMwqR5XNEQ4UkR1rzxVGaMSmEYFti3yQnvr/HVNbTJ5hIAajYYnTkhN9u3rl89fvnx54NqaaDkI16y5nuc5JUR1QiEjSOUqNzZOnRoSEhBKie0HQhSI+Hw6nUzySdT1Bs7u4kYJdbCQSF8y88OjToxMhRhmAuBCKIQtvO2HPgK1BDujafSKxtJ5Fdx+fOn445nOODxAkiZBsDpJVlcpTdOK0dXVicfHj186/uLy5QsXLty9cPnyC6zA0G2zO3FidPPJk5uoGku0NiBN4s6SLFWVmepcYqloLYNFYLvG922cAlJnRA0QRW4XScmkwyg1TTIYDODs0B7QMCRUOC6TKS4dNhwHYUtTZkrmmNJjhDOfIoUM6fE0xVEvXb50vEqCMAgpgKxDQCIsg/F4NjsOXLoMKnc/Mjn+GHkOy2nAeUA9nziiKZrYiJAN1EfsrFuitXY9XU1NvBh4YJo8Nc2MswD2cwQniSLf1tGiQ7GxRBd3SlkCr0xLOVnm+EJJuFn2nHLkIpVMKCIp5QOOXJRS+lTBofqVyJGtIerq6vglfe2Xbt9evTpIAHS6nT0G0xeXoQIuv4A/bs9mM2jTBJkOEC/0PFjgKDija2ZValb4C4mm+JJZu2KZ17owSUOHyE7kob5927fR2LgQAj6i86Xl0FSZHKEmOHOkGyGF/FzaeUQIDk8sgclwDWXEgXcZsUmINBowJ6SoPiFhgLPfhgfuakazh7MZmDwcP3yIsblTzt698AJegerhw/F4HIxxkvFYVyxMNRHTpLAWSOJwzipupvxEmp2oqio9sXrd2lXLtFatWrXyb/FTC//A+irN6ltin5TfsYf2J5tgyapFh0Z3S9KioC5pIXy1CfqFsBhcrFgsX8z8PivQmgPqj8DIEn6uWQGzCxt/Cqz9EgtLWlzofrLBit8AJqFZGlsW0f9w9bKM7pdzv130sxn/8R//8R//8RO8B5HntV+IjNJSAAAAAElFTkSuQmCC", "public": true}]}