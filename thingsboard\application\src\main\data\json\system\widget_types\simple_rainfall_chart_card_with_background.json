{"fqn": "simple_rainfall_chart_card_with_background", "name": "Simple rainfall chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_rainfall_chart_card_with_background_system_widget_image.png", "description": "Displays historical rainfall values as a simplified chart with background. Optionally may display the corresponding latest rainfall value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'rainfall', label: 'Rainfall', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'rainfall', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Rainfall\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 8) {\\n\\tvalue = 8;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 8) {\\n\\tvalue = 8;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#6083EC\"},{\"from\":0,\"to\":2.5,\"color\":\"#4369DD\"},{\"from\":2.5,\"to\":7.6,\"color\":\"#2B54CE\"},{\"from\":7.6,\"to\":null,\"color\":\"#224AC2\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_rainfall_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Rainfall\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:weather-pouring\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":1,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"mm\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "rain", "precipitation", "downpour", "rain shower", "drizzle", "raindrop", "cloudburst", "rainwater"], "resources": [{"link": "/api/images/system/simple_rainfall_chart_card_with_background_system_widget_background.png", "title": "\"Simple rainfall chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_rainfall_chart_card_with_background_system_widget_background.png", "publicResourceKey": "GO7qS9RrTWqHpvyaq2TZHAI0X3yYwUwJ", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_rainfall_chart_card_with_background_system_widget_image.png", "title": "\"Simple rainfall chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_rainfall_chart_card_with_background_system_widget_image.png", "publicResourceKey": "E1TBKBeOELyWTUnXVHL41JjPTWrSnC1N", "mediaType": "image/png", "data": "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", "public": true}]}