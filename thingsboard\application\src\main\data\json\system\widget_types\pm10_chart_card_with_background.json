{"fqn": "pm10_chart_card_with_background", "name": "PM10 chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/pm10_chart_card_with_background_system_widget_image.png", "description": "Displays a fine and coarse particulate matter (PM10) data by combining the latest and aggregated values with the background image and optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pm10', label: 'PM10', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'µg/m³', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'pm10', 'µg/m³', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM10\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#7CC322\"},{\"from\":20,\"to\":50,\"color\":\"#F89E0D\"},{\"from\":50,\"to\":150,\"color\":\"#F77410\"},{\"from\":150,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nif (value < -10) {\\n\\tvalue = -10;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/pm10_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"PM10\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"bubble_chart\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "particulate", "matter", "air", "pm10", "coarse particulates", "coarse particles", "particulate matter 10", "inhalable particles", "larger particulates", "dust", "airborne coarse particles"], "resources": [{"link": "/api/images/system/pm10_chart_card_with_background_system_widget_background.png", "title": "\"PM10 chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "pm10_chart_card_with_background_system_widget_background.png", "publicResourceKey": "zSEHFZ9bNc7MQkoXH4cLKaQj74mQtrXO", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/pm10_chart_card_with_background_system_widget_image.png", "title": "\"PM10 chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "pm10_chart_card_with_background_system_widget_image.png", "publicResourceKey": "VYAneqmolQh1huyDPAVvqwW0GAHi3aJP", "mediaType": "image/png", "data": "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", "public": true}]}