{"fqn": "control_widgets.update_attributes", "name": "Update device attribute", "deprecated": false, "image": "tb-image;/api/images/system/update_device_attribute_system_widget_image.png", "description": "Allows to send shared attribute update when user press the button.", "descriptor": {"type": "rpc", "sizeX": 4, "sizeY": 2, "resources": [], "templateHtml": "<div class=\"tb-rpc-button flex flex-col\">\n  <div class=\"title-container flex max-w-20% flex-full flex-row items-center justify-center\"\n       [class.!hidden]=\"!showTitle\">\n    <span class=\"button-title\">{{title}}</span>\n  </div>\n  <div class=\"button-container flex flex-full flex-col items-center justify-center\"\n       [class]=\"{\n          'max-w-80%': showTitle,\n          'max-w-100%': !showTitle\n       }\"\n       [style.padding-top]=\"showTitle ? '5px': '10px'\">\n    <div>\n      <button mat-button (click)=\"sendUpdate()\"\n              [class.mat-mdc-raised-button]=\"styleButton?.isRaised\"\n              [color]=\"styleButton?.isPrimary ? 'primary' : ''\"\n              [style]=\"customStyle\">\n        {{buttonLable}}\n      </button>\n    </div>\n  </div>\n  <div class=\"error-container flex flex-row items-center justify-center\" [style.background]=\"error?.length ? 'rgba(255,255,255,0.25)' : 'none'\">\n    <span class=\"button-error\">{{ error }}</span>\n  </div>\n</div>", "templateCss": ".tb-rpc-button {\n    width: 100%;\n    height: 100%;\n}\n\n.tb-rpc-button .title-container {\n    font-weight: 500;\n    white-space: nowrap;\n    margin: 10px 0;\n}\n\n.tb-rpc-button .button-container div{\n    min-width: 80%\n}\n\n.tb-rpc-button .button-container .mat-mdc-button{\n    width: 100%;\n    margin: 0;\n}\n\n.tb-rpc-button .error-container {\n    position: absolute;\n    top: 2%;\n    right: 0;\n    left: 0;\n    z-index: 4;\n    height: 14px;\n}\n\n.tb-rpc-button .error-container .button-error {\n    color: #ff3315;\n    white-space: nowrap;\n}", "controllerScript": "self.onInit = function() {\n    self.ctx.ngZone.run(function() {\n       init(); \n       self.ctx.detectChanges();\n    });\n};\n\nfunction init() {\n    self.ctx.$scope.buttonLable = self.ctx.settings.buttonText;\n    self.ctx.$scope.showTitle = self.ctx.settings.title &&\n        self.ctx.settings.title.length ? true : false;\n    self.ctx.$scope.title = self.ctx.settings.title;\n    self.ctx.$scope.styleButton = self.ctx.settings.styleButton;\n    let entityAttributeType = self.ctx.settings.entityAttributeType;\n    let entityParameters = JSON.parse(self.ctx.settings.entityParameters);\n\n    if (self.ctx.settings.styleButton.isPrimary ===\n        false) {\n        self.ctx.$scope.customStyle = {\n            'background-color': self.ctx.$scope.styleButton\n                .bgColor,\n            'color': self.ctx.$scope.styleButton.textColor\n        };\n    }\n\n    let attributeService = self.ctx.$scope.$injector.get(self.ctx.servicesMap.get('attributeService'));\n\n    self.ctx.$scope.sendUpdate = function() {\n        let attributes = [];\n        for (let key in entityParameters) {\n            attributes.push({\n                \"key\": key,\n                \"value\": entityParameters[key]\n            });\n        }\n        \n        let entityId = {\n            entityType: \"DEVICE\",\n            id: self.ctx.defaultSubscription.targetDeviceId\n        };\n        attributeService.saveEntityAttributes(entityId,\n            entityAttributeType, attributes).subscribe(\n            function success() {\n                self.ctx.$scope.error = \"\";\n                self.ctx.detectChanges();\n            },\n            function fail(rejection) {\n                if (self.ctx.settings.showError) {\n                    self.ctx.$scope.error =\n                        rejection.status + \": \" +\n                        rejection.statusText;\n                        self.ctx.detectChanges();\n                }\n            }\n\n        );\n    };\n}\n", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-update-device-attribute-widget-settings", "defaultConfig": "{\"showTitle\":false,\"backgroundColor\":\"#e6e7e8\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"styleButton\":{\"isRaised\":true,\"isPrimary\":false},\"entityParameters\":\"{}\",\"entityAttributeType\":\"SERVER_SCOPE\",\"buttonText\":\"Update device attribute\"},\"title\":\"Update device attribute\",\"dropShadow\":true,\"enableFullscreen\":false,\"widgetStyle\":{},\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{},\"targetDeviceAliases\":[]}"}, "tags": ["command", "downlink", "device configuration", "device control", "invocation", "remote method", "remote function", "interface", "subroutine call", "inter-process communication", "server request"], "resources": [{"link": "/api/images/system/update_device_attribute_system_widget_image.png", "title": "\"Update device attribute\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "update_device_attribute_system_widget_image.png", "publicResourceKey": "WhJC8NtPUdmMbUqrAnjVXK4t3pDnH1oi", "mediaType": "image/png", "data": "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", "public": true}]}