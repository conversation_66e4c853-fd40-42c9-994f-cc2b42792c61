<?xml version="1.0" encoding="utf-8"?>
<!--BSD 3-Clause License

Copyright (c) 2019, Cloudminds
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>EC<PERSON>L, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON>
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>RCU</Name>
    <Description1><![CDATA[This LWM2M Object includes RCU(Robotic Control Unit) information and the link to RCU APPs.]]></Description1>
    <ObjectID>10316</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10316</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Single</MultipleInstances>
    <Mandatory>Mandatory</Mandatory>
    <Resources>
      <Item ID="1">
        <Name>RCU ID</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>0..127</RangeEnumeration>
        <Units/>
        <Description><![CDATA[The RCU identity.]]></Description>
      </Item>
      <Item ID="2">
        <Name>RCU Serial Number</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>0..63</RangeEnumeration>
        <Units/>
        <Description><![CDATA[Serial Number of the RCU.]]></Description>
      </Item>
      <Item ID="3">
        <Name>RCU Software Version</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>0..63</RangeEnumeration>
        <Units/>
        <Description><![CDATA[Software Version of the RCU.]]></Description>
      </Item>
      <Item ID="4">
        <Name>RCU OS Version</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>0..127</RangeEnumeration>
        <Units/>
        <Description><![CDATA[OS type and version of the RCU.]]></Description>
      </Item>	  
	  <Item ID="5">
        <Name>RCU CPU Info</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>64</RangeEnumeration>
        <Units/>
        <Description><![CDATA[Cpu information of the RCU.]]></Description>
      </Item>
	  <Item ID="6">
        <Name>RCU RAM Info</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>64</RangeEnumeration>
        <Units/>
        <Description><![CDATA[RAM information of the RCU.]]></Description>
      </Item>
	  <Item ID="7">
        <Name>RCU ROM Size</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration/>
        <Units>GB</Units>
        <Description><![CDATA[ROM Size of the RCU (expressed in gigabyte).]]></Description>
      </Item>
	  <Item ID="8">
        <Name>RCU ROM Available Size</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration/>
        <Units>GB</Units>
        <Description><![CDATA[Available ROM Size of the RCU (expressed in gigabyte).]]></Description>
      </Item>
	  <Item ID="9">
        <Name>SD Storage</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration/>
        <Units>GB</Units>
        <Description><![CDATA[Total storage of the RCU SD card, for example: 128G.]]></Description>
      </Item>
	  <Item ID="10">
        <Name>SD Available Storage</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration/>
        <Units>GB</Units>
        <Description><![CDATA[Available storage of the RCU SD card, for example: 63G.]]></Description>
      </Item>
	  <Item ID="11">
        <Name>RCU GPS Location</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Objlnk</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[RCU GPS location, contains the referrence to Location(6).]]></Description>
      </Item>
	  <Item ID="12">
        <Name>Wi-Fi MAC</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>12</RangeEnumeration>
        <Units/>
        <Description><![CDATA[Wi-Fi MAC of the RCU.]]></Description>
      </Item>
	  <Item ID="13">
        <Name>Bluetooth MAC</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>12</RangeEnumeration>
        <Units/>
        <Description><![CDATA[Bluetooth MAC of the RCU, for example: .]]></Description>
      </Item>
	  <Item ID="14">
        <Name>Camera Info</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>64</RangeEnumeration>
        <Units/>
        <Description><![CDATA[Camera info of the RCU.]]></Description>
      </Item>
	  
	  <Item ID="51">
        <Name>Battery Level</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>0..100</RangeEnumeration>
        <Units>/100</Units>
        <Description><![CDATA[The current Battery Level as a percentage (with a range from 0 to 100).]]></Description>
      </Item>
	  
	  <Item ID="5852">
        <Name>On time</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration/>
        <Units>s</Units>
        <Description><![CDATA[The time in seconds that the device has been on. Writing a value of 0 resets the counter.]]></Description>
      </Item>
	  
	  <Item ID="60">
        <Name>Downloaded APP Packages</Name>
        <Operations>R</Operations>
        <MultipleInstances>Multiple</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration/>
        <Units/>
        <Description><![CDATA[Each item contains APP Name/Version info/Package Name.]]></Description>
      </Item>
	  
	  <Item ID="100">
		<Name>RCU APPs</Name>
		<Operations>R</Operations>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Type>Objlnk</Type>
		<RangeEnumeration/>
		<Units/>
		<Description>
		  <![CDATA[Contains the reference to the APPs belongs to RCU.]]>
		</Description>
	  </Item>
    </Resources>
    <Description2 />
  </Object>
</LWM2M>
