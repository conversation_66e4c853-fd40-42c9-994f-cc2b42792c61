{"fqn": "gpio_widgets.raspberry_pi_gpio_panel", "name": "Raspberry Pi GPIO Panel", "deprecated": false, "image": "tb-image;/api/images/system/raspberry_pi_gpio_panel_system_widget_image.png", "description": "Allows to change the state of the GPIO for Raspberry Pi devices using RPC commands. Requires handling of the RPC commands in the device firmware. Uses 'getGpioStatus' and 'setGpioStatus' RPC calls", "descriptor": {"type": "latest", "sizeX": 7, "sizeY": 10.5, "resources": [], "templateHtml": "<div class=\"gpio-panel\" style=\"height: 100%;\">\n  <section class=\"flex flex-row\" *ngFor=\"let row of rows\">\n    <section class=\"flex flex-1 flex-row\" *ngFor=\"let cell of row; let $index = index\">\n      <section class=\"flex flex-1 flex-row\" [class.justify-end]=\"$index===0\" [class.justify-start]=\"$index!==0\" *ngIf=\"cell\">\n        <span class=\"gpio-left-label\" [class.!hidden]=\"$index!==0\">{{ cell.label }}</span>\n        <section class=\"led-panel flex flex-row\" [class.col-0]=\"$index===0\" [class.col-1]=\"$index!==0\"\n                 [style.background-color]=\"ledPanelBackgroundColor\">\n          <span class=\"pin\" [class.!hidden]=\"$index!==0\">{{cell.pin}}</span>\n          <span class=\"led-container\">\n                        <tb-led-light [size]=\"prefferedRowHeight\"\n                                      [colorOn]=\"cell.colorOn\"\n                                      [colorOff]=\"cell.colorOff\"\n                                      [offOpacity]=\"'0.9'\"\n                                      [enabled]=\"cell.enabled\">\n                        </tb-led-light>\n                    </span>\n          <span class=\"pin\" [class.!hidden]=\"$index!==1\">{{cell.pin}}</span>\n        </section>\n        <span class=\"gpio-right-label\" [class.!hidden]=\"$index!==1\">{{ cell.label }}</span>\n      </section>\n      <section class=\"flex flex-1 flex-row\" *ngIf=\"!cell\">\n        <span class=\"flex-1\" [class.!hidden]=\"$index!==0\"></span>\n        <span class=\"led-panel\"\n              [style.height.px]=\"prefferedRowHeight\"\n              [style.background-color]=\"ledPanelBackgroundColor\"></span>\n        <span class=\"flex-1\" [class.!hidden]=\"$index!==1\"></span>\n      </section>\n    </section>\n  </section>\n</div>", "templateCss": ".error {\n    font-size: 14px !important;\n    color: maroon;/*rgb(250,250,250);*/\n    background-color: transparent;\n    padding: 6px;\n}\n\n.error span {\n    margin: auto;\n}\n\n.gpio-panel {\n    padding-top: 10px;\n    white-space: nowrap;\n}\n\n.gpio-panel section.flex-1 {\n    min-width: 0px;\n}\n\n\n.gpio-panel tb-led-light > div {\n    margin: auto;\n}\n\n.led-panel {\n    margin: 0;\n    width: 66px;\n    min-width: 66px;\n}\n\n.led-container {\n    width: 48px;\n    min-width: 48px;\n}\n\n.pin {\n    margin-top: auto;\n    margin-bottom: auto;\n    color: white;\n    font-size: 12px;\n    width: 16px;\n    min-width: 16px;\n}\n\n.led-panel.col-0 .pin {\n    margin-left: auto;\n    padding-left: 2px;\n    text-align: right;\n}\n\n.led-panel.col-1 .pin {\n    margin-right: auto;\n\n    text-align: left;\n}\n\n.gpio-left-label {\n    margin-right: 8px;\n}\n\n.gpio-right-label {\n    margin-left: 8px;\n}", "controllerScript": "var namespace;\nvar cssParser = new cssjs();\n\nself.onInit = function() {\n    var utils = self.ctx.$injector.get(self.ctx.servicesMap.get('utils'));\n    namespace = 'gpio-panel-' + utils.guid();\n    cssParser.testMode = false;\n    cssParser.cssPreviewNamespace = namespace;\n    self.ctx.$container.addClass(namespace);\n    self.ctx.ngZone.run(function() {\n       init(); \n    });\n}\n\nfunction init() {\n    var i, gpio;\n    \n    var scope = self.ctx.$scope;\n    var settings = self.ctx.settings;\n    \n    scope.gpioList = [];\n    scope.gpioByPin = {};\n    for (var g = 0; g < settings.gpioList.length; g++) {\n        gpio = settings.gpioList[g];\n        scope.gpioList.push(\n            {\n                row: gpio.row,\n                col: gpio.col,\n                pin: gpio.pin,\n                label: gpio.label,\n                enabled: false,\n                colorOn: tinycolor(gpio.color).lighten(20).toHexString(),\n                colorOff: tinycolor(gpio.color).darken().toHexString()\n            }\n        );\n        scope.gpioByPin[gpio.pin] = scope.gpioList[scope.gpioList.length-1];\n    }\n\n    scope.ledPanelBackgroundColor = settings.ledPanelBackgroundColor || tinycolor('green').lighten(2).toRgbString();\n\n    scope.gpioCells = {};\n    var rowCount = 0;\n    for (i = 0; i < scope.gpioList.length; i++) {\n        gpio = scope.gpioList[i];\n        scope.gpioCells[gpio.row+'_'+gpio.col] = gpio;\n        rowCount = Math.max(rowCount, gpio.row+1);\n    }\n    \n    scope.prefferedRowHeight = 32;\n    scope.rows = [];\n    for (i = 0; i < rowCount; i++) {\n        var row = [];\n        for (var c =0; c<2;c++) {\n            if (scope.gpioCells[i+'_'+c]) {\n                row[c] = scope.gpioCells[i+'_'+c];\n            } else {\n                row[c] = null;\n            }\n        }\n        scope.rows.push(row);\n    }    \n    \n    self.onResize();\n}\n\nself.onDataUpdated = function() {\n    var changed = false;\n    for (var d = 0; d < self.ctx.data.length; d++) {\n        var cellData = self.ctx.data[d];\n        var dataKey = cellData.dataKey;\n        var gpio = self.ctx.$scope.gpioByPin[dataKey.label];\n        if (gpio) {\n            var enabled = false;\n            if (cellData.data.length > 0) {\n                var tvPair = cellData.data[cellData.data.length - 1];\n                enabled = (tvPair[1] === true || tvPair[1] === 'true');\n            }\n            if (gpio.enabled != enabled) {\n                changed = true;\n                gpio.enabled = enabled;\n            }\n        }\n    }\n    if (changed) {\n        self.ctx.detectChanges();\n    }    \n}\n\nself.onResize = function() {\n    var rowCount = self.ctx.$scope.rows.length;\n    var prefferedRowHeight = (self.ctx.height - 35)/rowCount;\n    prefferedRowHeight = Math.min(32, prefferedRowHeight);\n    prefferedRowHeight = Math.max(12, prefferedRowHeight);\n    self.ctx.$scope.prefferedRowHeight = prefferedRowHeight;\n    \n    var ratio = prefferedRowHeight/32;\n    \n    var css = '.gpio-left-label, .gpio-right-label {\\n' +\n        '   font-size: ' + 16*ratio+'px;\\n'+\n    '}\\n';\n    var pinsFontSize = Math.max(9, 12*ratio);\n    css += '.pin {\\n' +\n        '   font-size: ' + pinsFontSize+'px;\\n'+\n    '}\\n';\n   \n    cssParser.createStyleElement(namespace, css); \n   \n    self.ctx.detectChanges();\n}\n\nself.onDestroy = function() {\n}\n", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-gpio-panel-widget-settings", "defaultConfig": "{\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"gpioList\":[{\"pin\":1,\"label\":\"3.3V\",\"row\":0,\"col\":0,\"color\":\"#fc9700\",\"_uniqueKey\":0},{\"pin\":2,\"label\":\"5V\",\"row\":0,\"col\":1,\"color\":\"#fb0000\",\"_uniqueKey\":1},{\"pin\":3,\"label\":\"GPIO 2 (I2C1_SDA)\",\"row\":1,\"col\":0,\"color\":\"#02fefb\",\"_uniqueKey\":2},{\"color\":\"#fb0000\",\"pin\":4,\"label\":\"5V\",\"row\":1,\"col\":1},{\"color\":\"#02fefb\",\"pin\":5,\"label\":\"GPIO 3 (I2C1_SCL)\",\"row\":2,\"col\":0},{\"color\":\"#000000\",\"pin\":6,\"label\":\"GND\",\"row\":2,\"col\":1},{\"color\":\"#00fd00\",\"pin\":7,\"label\":\"GPIO 4 (GPCLK0)\",\"row\":3,\"col\":0},{\"color\":\"#fdfb00\",\"pin\":8,\"label\":\"GPIO 14 (UART_TXD)\",\"row\":3,\"col\":1},{\"color\":\"#000000\",\"pin\":9,\"label\":\"GND\",\"row\":4,\"col\":0},{\"color\":\"#fdfb00\",\"pin\":10,\"label\":\"GPIO 15 (UART_RXD)\",\"row\":4,\"col\":1},{\"color\":\"#00fd00\",\"pin\":11,\"label\":\"GPIO 17\",\"row\":5,\"col\":0},{\"color\":\"#00fd00\",\"pin\":12,\"label\":\"GPIO 18\",\"row\":5,\"col\":1},{\"color\":\"#00fd00\",\"pin\":13,\"label\":\"GPIO 27\",\"row\":6,\"col\":0},{\"color\":\"#000000\",\"pin\":14,\"label\":\"GND\",\"row\":6,\"col\":1},{\"color\":\"#00fd00\",\"pin\":15,\"label\":\"GPIO 22\",\"row\":7,\"col\":0},{\"color\":\"#00fd00\",\"pin\":16,\"label\":\"GPIO 23\",\"row\":7,\"col\":1},{\"color\":\"#fc9700\",\"pin\":17,\"label\":\"3.3V\",\"row\":8,\"col\":0},{\"color\":\"#00fd00\",\"pin\":18,\"label\":\"GPIO 24\",\"row\":8,\"col\":1},{\"color\":\"#fd01fd\",\"pin\":19,\"label\":\"GPIO 10 (SPI_MOSI)\",\"row\":9,\"col\":0},{\"color\":\"#000000\",\"pin\":20,\"label\":\"GND\",\"row\":9,\"col\":1},{\"color\":\"#fd01fd\",\"pin\":21,\"label\":\"GPIO 9 (SPI_MISO)\",\"row\":10,\"col\":0},{\"color\":\"#00fd00\",\"pin\":22,\"label\":\"GPIO 25\",\"row\":10,\"col\":1},{\"color\":\"#fd01fd\",\"pin\":23,\"label\":\"GPIO 11 (SPI_SCLK)\",\"row\":11,\"col\":0},{\"color\":\"#fd01fd\",\"pin\":24,\"label\":\"GPIO 8 (SPI_CE0)\",\"row\":11,\"col\":1},{\"color\":\"#000000\",\"pin\":25,\"label\":\"GND\",\"row\":12,\"col\":0},{\"color\":\"#fd01fd\",\"pin\":26,\"label\":\"GPIO 7 (SPI_CE1)\",\"row\":12,\"col\":1},{\"color\":\"#ffffff\",\"pin\":27,\"label\":\"ID_SD\",\"row\":13,\"col\":0},{\"color\":\"#ffffff\",\"pin\":28,\"label\":\"ID_SC\",\"row\":13,\"col\":1},{\"color\":\"#00fd00\",\"pin\":29,\"label\":\"GPIO 5\",\"row\":14,\"col\":0},{\"color\":\"#000000\",\"pin\":30,\"label\":\"GND\",\"row\":14,\"col\":1},{\"color\":\"#00fd00\",\"pin\":31,\"label\":\"GPIO 6\",\"row\":15,\"col\":0},{\"color\":\"#00fd00\",\"pin\":32,\"label\":\"GPIO 12\",\"row\":15,\"col\":1},{\"color\":\"#00fd00\",\"pin\":33,\"label\":\"GPIO 13\",\"row\":16,\"col\":0},{\"color\":\"#000000\",\"pin\":34,\"label\":\"GND\",\"row\":16,\"col\":1},{\"color\":\"#00fd00\",\"pin\":35,\"label\":\"GPIO 19\",\"row\":17,\"col\":0},{\"color\":\"#00fd00\",\"pin\":36,\"label\":\"GPIO 16\",\"row\":17,\"col\":1},{\"color\":\"#00fd00\",\"pin\":37,\"label\":\"GPIO 26\",\"row\":18,\"col\":0},{\"color\":\"#00fd00\",\"pin\":38,\"label\":\"GPIO 20\",\"row\":18,\"col\":1},{\"color\":\"#000000\",\"pin\":39,\"label\":\"GND\",\"row\":19,\"col\":0},{\"color\":\"#00fd00\",\"pin\":40,\"label\":\"GPIO 21\",\"row\":19,\"col\":1}],\"ledPanelBackgroundColor\":\"#008a00\"},\"title\":\"Raspberry Pi GPIO Panel\",\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"7\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.22518255793320163,\"funcBody\":\"var period = time % 1500;\\nreturn period < 500;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"11\",\"color\":\"#4caf50\",\"settings\":{},\"_hash\":0.7008206860666621,\"funcBody\":\"var period = time % 1500;\\nreturn period >= 500 && period < 1000;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"12\",\"color\":\"#f44336\",\"settings\":{},\"_hash\":0.42600325102193426,\"funcBody\":\"var period = time % 1500;\\nreturn period >= 1000;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"13\",\"color\":\"#ffc107\",\"settings\":{},\"_hash\":0.48362241571415243,\"funcBody\":\"var period = time % 1500;\\nreturn period < 500;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"29\",\"color\":\"#607d8b\",\"settings\":{},\"_hash\":0.7217670147518815,\"funcBody\":\"var period = time % 1500;\\nreturn period >= 500 && period < 1000;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}}}"}, "resources": [{"link": "/api/images/system/raspberry_pi_gpio_panel_system_widget_image.png", "title": "\"Raspberry Pi GPIO Panel\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "raspberry_pi_gpio_panel_system_widget_image.png", "publicResourceKey": "vRQB1qFlhZGYEEMumWS0QiVm91ACDc0G", "mediaType": "image/png", "data": "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", "public": true}]}