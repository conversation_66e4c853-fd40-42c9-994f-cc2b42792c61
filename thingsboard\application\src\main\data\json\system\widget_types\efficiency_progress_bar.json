{"fqn": "efficiency_progress_bar", "name": "Efficiency progress bar", "deprecated": false, "image": "tb-image;/api/images/system/efficiency_progress_bar.svg", "description": "Displays efficiency reading as a horizontal progress bar. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'efficiency', label: 'Efficiency', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Efficiency\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 30;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":60,\"color\":\"#FFA600\"},{\"from\":60,\"to\":80,\"color\":\"#3FA71A\"},{\"from\":80,\"to\":null,\"color\":\"#305AD7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":100,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":60,\"color\":\"#FFA600\"},{\"from\":60,\"to\":80,\"color\":\"#3FA71A\"},{\"from\":80,\"to\":null,\"color\":\"#305AD7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Efficiency\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"%\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["productivity", "effectiveness", "performance", "capability"], "resources": [{"link": "/api/images/system/efficiency_progress_bar.svg", "title": "efficiency_progress_bar.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "efficiency_progress_bar.svg", "publicResourceKey": "SkZ9zEMzo54vOgCqfLHy4krkvoXAVA0O", "mediaType": "image/svg+xml", "data": "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", "public": true}]}