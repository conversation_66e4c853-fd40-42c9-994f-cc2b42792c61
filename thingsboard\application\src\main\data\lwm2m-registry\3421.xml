<?xml version="1.0" encoding="UTF-8"?>

<!-- BSD-3 Clause License

Copyright 2020 uCIFI Alliance

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>Single-phase electrical meter</Name>
		<Description1>The uCIFI single-phase electrical meter measures the electrical consumption of loads on one electrical line, such as electrical cabinets in city infrastructures, street lighting networks, homes and buildings.</Description1>
		<ObjectID>3421</ObjectID>
		<ObjectURN>urn:oma:lwm2m:ext:3421</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="1">
				<Name>Voltage</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>V</Units>
				<Description>Instantaneous voltage measured between line and neutral.</Description>
			</Item>
			<Item ID="2">
				<Name>Low voltage threshold</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>V</Units>
				<Description>Set to True when voltage is below low voltage threshold.</Description>
			</Item>
			<Item ID="3">
				<Name>Low voltage</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Set to True when voltage is below low voltage threshold.</Description>
			</Item>
			<Item ID="4">
				<Name>High voltage threshold</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>V</Units>
				<Description>Threshold above which the high voltage resource is set to True.</Description>
			</Item>
			<Item ID="5">
				<Name>High voltage</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Set to True when voltage is above high voltage threshold.</Description>
			</Item>
			<Item ID="6">
				<Name>Current</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>A</Units>
				<Description>Instantaneous current measured.</Description>
			</Item>
			<Item ID="7">
				<Name>Low current threshold</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>A</Units>
				<Description>Threshold below which the low current resource is set to True.</Description>
			</Item>
			<Item ID="8">
				<Name>Low current</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Set to True when current is below low current threshold.</Description>
			</Item>
			<Item ID="9">
				<Name>High current threshold</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>V</Units>
				<Description>Threshold above which the high current resource is set to True.</Description>
			</Item>
			<Item ID="10">
				<Name>High current</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Set to True when current is above high current threshold.</Description>
			</Item>
			<Item ID="11">
				<Name>Frequency</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>Hz</Units>
				<Description>Instantaneous frequency measured.</Description>
			</Item>
			<Item ID="12">
				<Name>Angle of I-U</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>deg</Units>
				<Description>Instantaneous phase angle measured.</Description>
			</Item>
			<Item ID="13">
				<Name>Instantaneous Power Factor</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Instantaneous power factor overall.</Description>
			</Item>
			<Item ID="14">
				<Name>Low power factor threshold</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Threshold below which the low power factor resource is set to True.</Description>
			</Item>
			<Item ID="15">
				<Name>Low power factor</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Set to True when power factor is below low power factor threshold.</Description>
			</Item>
			<Item ID="16">
				<Name>Active power+ (QI+QIV)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>W</Units>
				<Description>Active power import</Description>
			</Item>
			<Item ID="17">
				<Name>Active power- (QII+QIII)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>W</Units>
				<Description>Active power export.</Description>
			</Item>
			<Item ID="18">
				<Name>Reactive power+ (QI+QII)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration />
				<Units>var</Units>
				<Description>Reactive power import.</Description>
			</Item>
			<Item ID="19">
				<Name>Reactive power- (QIII+QIV)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>var</Units>
				<Description>Reactive power export</Description>
			</Item>
			<Item ID="20">
				<Name>Instantaneous Apparent Power import (+VA)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>VA</Units>
				<Description>Instantaneous apparent power import.</Description>
			</Item>
			<Item ID="21">
				<Name>Instantaneous Apparent Power export (-VA)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration />
				<Units>VA</Units>
				<Description>Instantaneous apparent power export.</Description>
			</Item>
			<Item ID="22">
				<Name>Instantaneous Active Power (|+A|+|-A|)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>W</Units>
				<Description>Instantaneous active power.</Description>
			</Item>
			<Item ID="23">
				<Name>Instantaneous Net Active Power (|+A|-|-A|)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration />
				<Units>W</Units>
				<Description>Instantaneous net active power.</Description>
			</Item>
			<Item ID="24">
				<Name>Measurement period of Instantaneous value</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description>Measurement period of instantaneous value.</Description>
			</Item>
			<Item ID="25">
				<Name>Active energy import (+A) Un</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>Wh</Units>
				<Description>Active energy import (unified rate).</Description>
			</Item>
			<Item ID="26">
				<Name>Active energy export (-A) Un</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>Wh</Units>
				<Description>Active energy export (unified rate).</Description>
			</Item>
			<Item ID="27">
				<Name>Active energy (|+A|+|-A|) Combined total</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>Wh</Units>
				<Description>Active energy (|+A|+|-A|) Combined total.</Description>
			</Item>
			<Item ID="28">
				<Name>Active energy (|+A|-|-A|) Combined total</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>Wh</Units>
				<Description>Active energy (|+A|-|-A|) Combined total.</Description>
			</Item>
			<Item ID="29">
				<Name>Reactive energy import (+R) Un</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>varh</Units>
				<Description>Reactive energy import (unified rate)</Description>
			</Item>
			<Item ID="30">
				<Name>Reactive energy export (-R) Un</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>varh</Units>
				<Description>Reactive energy export (unified rate).</Description>
			</Item>
			<Item ID="31">
				<Name>Reactive energy QI (+Ri) Un</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>varh</Units>
				<Description>Reactive energy QI (+Ri) (unified rate).</Description>
			</Item>
			<Item ID="32">
				<Name>Reactive energy QII (+Rc) Un</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>varh</Units>
				<Description>Reactive energy QII (+Rc) (unified rate).</Description>
			</Item>
			<Item ID="33">
				<Name>Reactive energy QIII (-Ri) Un</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration />
				<Units>varh</Units>
				<Description>Reactive energy QIII (-Ri) (unified rate).</Description>
			</Item>
			<Item ID="34">
				<Name>Reactive energy QIV (-Rc) Un</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>varh</Units>
				<Description>Reactive energy QIV (-Rc) (unified rate).</Description>
			</Item>
			<Item ID="35">
				<Name>Apparent energy import Un</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>VAh</Units>
				<Description>Apparent energy import (unified rate).</Description>
			</Item>
			<Item ID="36">
				<Name>Apparent energy export Un</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>VAh</Units>
				<Description>Apparent energy export (unified rate).</Description>
			</Item>
			<Item ID="37">
				<Name>Number of power failures</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Number of power failures.</Description>
			</Item>
			<Item ID="38">
				<Name>Number of long power failures</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Number of long power failures.</Description>
			</Item>
			<Item ID="39">
				<Name>Time threshold for long power failure</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description>Time threshold for long power failure.</Description>
			</Item>
			<Item ID="40">
				<Name>Duration of last long power failure</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description>Duration of last long power failure.</Description>
			</Item>
			<Item ID="41">
				<Name>Threshold for voltage sag</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>V</Units>
				<Description>Threshold for voltage sag.</Description>
			</Item>
			<Item ID="42">
				<Name>Time threshold for voltage sag</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description>Time threshold for voltage sag.</Description>
			</Item>
			<Item ID="43">
				<Name>Number of voltage sags</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Number of voltage sags.</Description>
			</Item>
			<Item ID="44">
				<Name>Duration of last voltage sag</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description>Duration of last voltage sag.</Description>
			</Item>
			<Item ID="45">
				<Name>Magnitude of last voltage sag</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>V</Units>
				<Description>Magnitude of last voltage sag.</Description>
			</Item>
			<Item ID="46">
				<Name>Threshold for voltage swell</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>V</Units>
				<Description>Threshold for voltage sag.</Description>
			</Item>
			<Item ID="47">
				<Name>Time threshold for voltage swell</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description>Time threshold for voltage sag.</Description>
			</Item>
			<Item ID="48">
				<Name>Number of voltage swells</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Number of voltage sags.</Description>
			</Item>
			<Item ID="49">
				<Name>Duration of last voltage swell</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description>Duration of last voltage sag.</Description>
			</Item>
			<Item ID="50">
				<Name>Magnitude of last voltage swell</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>V</Units>
				<Description>Magnitude of last voltage swell.</Description>
			</Item>
			<Item ID="51">
				<Name>Threshold for missing voltage (voltage cut)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>V</Units>
				<Description>Threshold for missing voltage (voltage cut).</Description>
			</Item>
			<Item ID="52">
				<Name>Time threshold for voltage cut</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description>Time threshold for voltage cut.</Description>
			</Item>
			<Item ID="53">
				<Name>Voltage cut</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Set to True if a voltage cut for a time above the time threshold and for a voltage below the voltage threshold is detected.</Description>
			</Item>
			<Item ID="54">
				<Name>CT Numerator Parameter</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Transformer ratio - current (numerator).</Description>
			</Item>
			<Item ID="55">
				<Name>CT Denominator Parameter</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Transformer ratio - voltage (denominator).</Description>
			</Item>
			<Item ID="56">
				<Name>VT Numerator Parameter</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Transformer ratio - current (numerator).</Description>
			</Item>
			<Item ID="57">
				<Name>VT Denominator Parameter</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Transformer ratio - voltage (denominator).</Description>
			</Item>
		</Resources>
		<Description2></Description2>
	</Object>
</LWM2M>
