{"fqn": "simple_visibility_chart_card_with_background", "name": "Simple visibility chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_visibility_chart_card_with_background_system_widget_image.png", "description": "Displays historical visibility values as a simplified chart with background. Optionally may display the corresponding latest visibility value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'visibility', label: 'Visibility', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'visibility', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Visibility\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":1,\"color\":\"#DE2343\"},{\"from\":1,\"to\":4,\"color\":\"#F89E0D\"},{\"from\":4,\"to\":null,\"color\":\"#7CC322\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_visibility_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Visibility\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"visibility\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"km\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "visibility", "sight", "view", "clarity", "transparency", "perceptibility", "discernibility", "range of view", "clearness"], "resources": [{"link": "/api/images/system/simple_visibility_chart_card_with_background_system_widget_background.png", "title": "\"Simple visibility chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_visibility_chart_card_with_background_system_widget_background.png", "publicResourceKey": "ze56TnXbuNNKiqp55lh3xHKq19j2BcCR", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAQoAAACACAMAAADESdfxAAADAFBMVEXGz9TEzdPI0dbK09jBytDCy9HEzNLCzNK9xsq+x83Byc6/yM6yur66wsaksbmZp7CvtLfAyMzAx8q3wci3v8O5w8q4wcSotb29xci7xcyms7uvu8OcoZ+TmpW0vMChr7e6xMm1vsKqt7+ZnputsrWsucGyvsajqaigrbWVm5i1v8eOj4GbqbOdq7Snr7Geo6GytrmmrK+9wsafpaSaoJ22vcChpqWyuLulo6SXnpiRk4arsbSrsLGVnp+XpK2vsLKrq62OlYyPkoqvtrmXoKGyvcOzsbOmq6uRlYu1ubyprq+ztLeura+bpKhnZlO/xMi4uLuWnZuvuLyZoqSRmpmVmpGHgG2SmZBPUUBWWEWxrKySlo53cFmjq6yfp6mFgnJraFWopKRZWkmPl46NkYSPj32MjHq6vsG2tLSirLBdXU2ppqeWnZWWmZOOl5qPl5aFhXi7wMOPiXZNTju3ur6jn56SmJOxr6+ioaKMlJVwalWSm5yenZtTU0WrqaqlpqeDfGq5treVoaips7aRjYF2a1W1trmlsLWKgm9ma1auq6yChXW9urqHkIq6u76TnqNfZVmtqamspqWCi4idmpiCgm52dWenqaqttbh/iYWgqayloJ+fn5+ooqGVl4+UlYq2sbGdqrGEjYxiZ1tyblicp6yKj3+Ih3eBeGZTWEuSkYJ+dWVqbVhkYlHCv75iYE69t7V8emxycV5WXE6Qm599hoONi36NhnNYX1JeX1F3dF2SjHmMkYePjYaLjYOJi31pbWVUU0COlZGJhnGBfnCHj49scVxbYlZjZFVjaFKst72anJaUkIaqtLqGiXtHSTl0fnpoa153gX5tbF2EemZrZE9nYE56hIBkZ2GJkpO5s7JvZlFQVEeinJmCf2p+fGZfWkhbWESUj3t5e2dxcmZ9dV6HfWq+vb2KlIxxenZvd3Brcmt7cluwqaeKh3vAu7qLkpB+gnOAeGJjXEu1rq1wdWB1d2J7eGF7cWJ9gGt8fHGal5bFwsFcZlGblY+liVpejnXRAAA2o0lEQVR42oSZ227bMBBE5dSSYxtoUBRBkPahF6BBYDS/0Dd/aP+0Z4crjpdG3NGSdhI0lk5mh5Q6PTw8/A69/X57e3t9ff3W9BF9kZ6enn4+/Qz9kr42vXSdTp/RY+o5tN/vj6Htcbvd7ra73WZDNU3TpJeiu7vpLl+lO6r/4FIfqFR+HWr/kEKbKn3uxGCm/H1Oaoc4O85xZkwPSCikVyQSBUWSgEWDIRwmAQoEBaNAgsGHBAmj4FQ5qEsY4sAUM0oqcVQJA8Nq30EdxabDMH2D4DAL8RAGSa4Qi0RByRZCkSxQQREgEsWJ+hwwoPBZHB6DxDMg9vtmCmRLIGaz0Lsg0IkwNOsrM4jjWvp+qlvK1igkuoxiK1swJ4reIpCoKD6Kg1okZVe8DK5wdzQUIUigWVa8+MtQ10oP2Bh2CSBivCubJtlSdoUu2iisXdN21fQHEEYhEJ2FG4SjpMULNaAwiwSh/kDCr/OanBHdEjUvKF/UDUcYQ+8PjQtXMHYc0sJrlX7YNO/cIMFCclqkKxKF46Kp5iYobAtgOCwSheQOKRrjE3UohqELvsZQW2RgwcgLZrqWOLXYnO2KFURn4SVkRJESCWeFZBJ7jqNJgJ1PpVJLiwebuZIwg+mmJ0zEKjDEYv0r6MXKH8gSjNlZYVNcr6dPa26ahlxxbiiUmkbBkQ1yTBY7pcX6B7KEIOOTSiRG0N59EIybIAwjGDqYh4u+joqeFrMbBOXOAlUSVNqioPh16QoEicaiaW0PyvsKjnlelBkapUdqYljCcJNFQaEp1XAnCQbTreCkQQQiBYgQwVk3WUZRVlNHhXNTAoRjExLNFfNmnoPHMnlXcXcVG0axbhputkV10TSg8GZK8xgVTT022xLiHhGJRFFyk7rccSYIySwaCSlBODcjNMAhS6SJdeVXyVlt8X5fmIc1WXLfkq3ANCaoSVDhCuVm2XGOuUmNrvB6eonie915S4kCL9AehAZDp1h6ZOiUlcLtVVSc/gMjtNNQzYbh9qDcIG6RFcXr6grKvhAKKlG8GMXpqkWSgyQSyxziTUNRd1eDEoSmmxnBqPIWXq5YdMHuifJCOTXTFajYImPTHUKpQX55BZEnzCJz85lC6Ql3SEQEUlwsfa/VJje4fZEQGNakY9xW2B02RZLo0nKe0c1sNNcNInkJQUYRqgvIT6FwcBZTPHcUopEkBGEzb2dQLMuyWYotzMLqHTJqxJGZEZNko5ETayMsjDmzUyiyQiz11RXeW/gu5GoNKWGBgoOXEMH4rvYQichNo9iBYicIxmDdacrhlWS9fBtilJ0hjVBXFjHz+et60i3Rzq5mxTu5icrdKSookGNTKB7dH4lCoanTkCG8q7gt2eK/EgTBMA0vIGgJpQeoHMwOTbni2FB4Ob1ukEDRODDQeJ9+6iwem/r9mFikZoQt5sjPPLmoKU1CoQFQX0in95aQfB0XkFa541ySgrRb1bvDAsXaIV5BvMWq92QDi7LHOkGCUnB6Z0GfHLZJ5BBAKAkgVKciMlJFkWExlahAqx+YUn7uYzUWJT7LFiM6wzrWBhnvTce0qGEhDm4RWNRdljcXYEgJB9MiFqnm4+ybcRWZppWC52RUvOAbdUuIl8U7byQKaOvIhAN/MMVmuQ/xdrMH5xOHoyK03o+d6yM9xWZKJHIpORwPhy0VSlfEuFTawjtyCsW1axiD6QSrwqOSSEPsAno+mZC0uPcbMardLhUUcCgriGRHDKkpCOJwKo95KRsjcXyCBeKDxYLhLjELpmmZupyaxRTU+KBXCIZ9JmPhAIA2dnCgeC8UiLPoknXLYprdcb1+SCOJlFgYRqijYKDjgZIIDCQSbSq+0KkLxsZbC1+/IUz2RDFFUUaQ9vsaef20hKBwGskhUHCa04NT0zssSAwsxqyQzudyQ9Zx2BaODMFgEgqKo7KQnROHV1OnZMfB4SdYTcOexA+2+y435BVjpoojwrtyhe/IvK8QDLOo/weQJFDJipYWlBLjPkxxT923JkkUh4zObos5QxNDIy2AhYWvv2TG9b2HYehXaMkgKQRCn2cCqKJAY2yGrlrkiRKIGH6M1XQZm5R9sQ8ccLi/WEnkCiadVvfE1IOTWeUW0ZXnoDoV77XHrKTkLT1HTEd44TSCAoJmBsUKo96ZIu8rxrwQCSpJFFsw1B6yxr1gKC+ChNRQdBjUmBitWxwXU41MfW0/FBDSgghMPR2RtlToQHn1ZGRzKNYCBQ2SKP6+/X6VBMK2GB9vuj/OZ9+l+54sYQCiCRSfAgUVJyNrqEGMwEp31+QcZRIMG4LKvNGagRtMQlJiId6KQoLIBilryN/aIFLxxNghpxfBEAYqjjUxMEaiyMe+orE9JIm+hPTJMCQuM2HUtUJv80fuC0Y6IgYRkdoKfkIAQCpWDesZ1QbBF9pYAMIsSmyahWFI3RQMgaBBIEFJxx4Y2SPACGMsxrCpu89pqY86//+8Kv2g35cYkKZDSLaUF9DKQJM0ZX94s+nl1HdkQmEWXxuLc6I4n370DvFC8vn790d8YWfE53/65DWVMRcEVVpHpHSHpe/UlXOhkL1ACcGajL56BblCnWkVTT00yF81iFi8f0eWIM6gkHKTZRZNoOBIElpJyAtx8CZc5z9HKUEriukd+RYrOc6Xsg2Y0P4Qm10+XRDuiTDFuSjcBwQqTjRQmIVR+N4UDZusf3SazYrTUBiGZ6H4Mw2YGmNRKFUQxJUIZXbdlFkWIXfR25guupzt4M5F78CrKPSefN43X86XVHzOOamdnzbnmff7JpE5nyfdIhvnxEVVrYIwIW7VPuvhGty7Z8RurKOogbxZ1UrymQPh746Gk4Ts1xEJJKBCAxWrEVHNnHWqsIlpKvIqq9g4vy8uZuHiul+0fqwqKmUVPoqM7BbPTSjwolyYxQhH30coBFMcCpY1/JsKJsXXv488iJdmxQyqfnCaVQtv3zYNKkSosIuUIRc2oVW65/9yYRUlHW1bvavoFxUixskgFGbYrvD5u3eIsjkHgyMqCEhccLBwNDXwzKgiLME12N8Ty0JiB4U2aNpGfLp52PRMY3H1X71M41x8PqtffHQshlBEkTB55eJiVCQriXDrrGvLiGtu78pJsAk+XCiB8ddwBR2gIkoiq0Ey3SRKi8CDfFgFyz+NkYmwIOwBbjbhIlIBee0t0oZdXC4XiuQ9uRCzj/fFRuhoWoEJP8jGYpG/RnQzggru2Ye9ApvgNyvIhffHnlwwIBNMD7CFOJQKk9lngAUiwcQDAsD7D3oFrKY1IQENZqlUpAtFAxeyodYZLoyL5HJhXs6m+9JRIxkMJiLgW9sUFXrnH9W4edavMFHXt8+8iRdunNnuewV5BTYNiC15xnV04baHLNTujknRwGo1LYEpQoLGEhUPyGDq8sIiTPlDpFQB8sAiE7QLgnGWimydn+7veeHGb6UjI6tk8RK4oLEM14g27AJgS0lakQ+LubJROi62pAMnDoPx64tRn8RC1IRWOChxWJpQETUiEVrCJZIygsiFZRhioYaRIMM0ohUVyEZUiU+VVb/SFUZ/c8q+UWPqaymuFU2wnskXOQdQqxEN8FbGEtpKhARGaLCC8HA/W86YUpEuDP3CMgAX1nEUdnG4mHMQf6L2jwivBnQOECXCBFw4F06Gw87ubr1HhhNTi5DjwaPhsXBbz+f1PBUsFsxVIgXZIBmfRKmKAhoMKsyg4qRUmD0mzHGAVBwOh3AhHeqeswC3MozqQB0pbEDUiIMB9bwOVA7y8l/yc8lcA6xABoyiJxd+PzQwRP6WMOuRBB9MN+tGKoqNkY6vX/dffx+PIx246G1k/+y6mXDKgvTROBXSwXku1DHq+UtvhR2Bd6aZ5JN87kNKsAbmQiNg+6aNY2tGHpbrdZHAHNExu5vHhysZJwgZ+z1rEo0DE4qMp3CBjSBVcIA1Z6F8omLhApnbRAEzMP2BD9gW00vfikWPeabBRBTa6ZVTO00DNbFODWMPnP3d3febR7tIGafNychEr0MyWMfkAy6SsY3OLUhjhNPRqki8Abb1atib3QBHkwlg6jmfCAhUdoWxhkUV9PGzBStID2tY2sRsCgowIb5bRcrYbTa73WlgqBKDi7ChYDDNk1QQDeiCWSIlKUM14gqZR3VIBc9ZKcXoH8VRtgTlIEkDWQ+pQYO5pjWgIcOQDjS7ge32bosKkTrsgnBACce+cNxf6biYqQwnLikyVCTqnaUi2GAGvTzkR1mejCsqRBRsQNMOrACkYWJiLAFSAyIAFX8egxKN3U4qUsav37/2ybFwCC5kA3i1bnvmldNI2uCMGgVD+3LXFFHz4idjwBIYSQpwGH4UCZp2IBV55bQeiCbJsINw0RUP5y086fDmTaoYRDBwsYNi4y+jZazaNhSF4Y4dGgjFdCwEDN5U2cbWYIcWXDA2hAzp6q6iS0GgJXTIkKFjx5g8h1+mb9Pv/3V0b9Q2pd8990iuS6X76b9yHwEJiV6FhjhqJiw5hAxcLHiBnnmt8RsiIeyRATO0YCx/PlOdUQPeGI4SAeHBWQCu5h0xpNKsMFFtsKGbtIkjdUTF6XT6MxjICBc0y5CO7KP4XqDCDRkr+xgq0VUso/L1exsXo/6FIR2gaETgZ0Eslj80DsGQJv1QWoMUyHNIcAz+ooHVo0GprTYZazgCKpKLrOPm6uaKCh/PhqMA2VgV9wMfRwoiHFVHhKP1K+PMNrxTVC+dhNAw0zqbxvEXfvT5rUh50ppR0wcCkGALQxGVsQM9HEYvgEmBEjE+H49RkWXkaNzAVeI5GQUV3KMDHwxjGeGjqiiz23Fzi4smtgmxsAm45k0hEQ0FI5bpF2Hd0DWAll8KTB+8ISIN1mAqlR1EBDiGhGSiiwJ9fLwfr8ao+HrqyTI+IkI2rqiArfL5c8iQjwPDFJkVExn2QR25hK5GlZsSOh2v2sXojDfCa4ZkBC+v0YGHWHhdL+rFiKFfgboewaLmMwGo64u0/PxmBLUdFpRBVcmAcgMlA9zPFQgkaDgQYBVfT7IRyINt3IA3ylM+C6s4HOQi0DYpEutVwQB8jMf3Y12uPN+cl6bbKpZxfR2pyEiGNIxYZVomp3W7eELbfcPJq0X7tm3bVwGpY+4q0x/LHmyc44FJkwIjEVnFEy7t4fLHR3EjIhzZBhU2KHxMv0ynxR+sOsbogPPA6dBtto1U2MVABcxmzWKx27Uq1sZsu44BigZ8MrtMleC0pJ7A2geMNVRmJdYrVIjexEkiLkOFbPQ+BipwYeSCElP4q417CRFJyabkbt+Si2vIycgqzkYX7a4Lt3t6zNbiRdOGEkrNBMbxTkFaffaQHGiEiPXaKvIW+Xa67GGjWEYm2RCPmpENhyNkMBPrdbEGXShAS9h49cYqWD5DbwqbiFhctBVPkmIxlNgoUJ4ewSZ1LZ2/M1h33gK0IavMes+t7vd7qTChIuuQh9goiZwMsAtMiCml0bFPTvbrYG98yo1xz+2omSHDZA8w0w5pK918LOs5/NJjGv/nYKyT8YAcANoQ7sUPrJgiY/rhxW2vgvH1m7nsVdASEiEVNBxomn6jUGAhFHM/ZUDX9If+sN7bxbHizWkVQxszh6JZ7BChLPlXjjDF/mIE75lk7P9Zd50yhZMgBYkXt7eSYZKK06V02Idk/LjJ2bhKychYxeQwOQTb6WG73U63U9jCwU19v+XyWtmmrQlFluAOkQqpUJSY75nAeUTK/f36PV8MKQT9H0z73jnYMwuV7xMVuLgduLAODUE2LGKwTaiBkQkltoigad1MN5mYmC1MpQIX1aKemdChQ4hQKuq2HK8VogwWTWy3rqWvHDnhMPq0YObjkEIpzRxAKkzORbaRX6DCfeBjzggViFhOerbucsJZYhk2nIvzql3UTTOjeiwEQkWFCqWo44E5YRC1PUhS+uphcsdX1MEXACvRUplDDtEPQDOPhqf54ufPHAvz6dOnUJFfG5q0335f54nl5PPSRjI6XwZzd9+oXazKqq21QyhLyOlo/K5gh5SrPXmCpNI2tz2Th4e7u+Vv6E5kRfLogRfOQTzG8inwQb+GNKmwC6ANZISQhKMxsDGfv5s/AR3LCUumT0LBnDL60IXlQCjKnVPRMFMqKKZoulhw9xGxsCl0rn/o7o6zuXhHCd+LDnz/LAdl52HyyOnjI4/LHjqWv9gwexUnojAM7w0IuYBMNQvLVImY7BKNZCeC+xOLBA1TTBnBZsZtZFgxsF0qN43CIiwRvAe71WLAKp2NeAWm3CsQn/ecLxMjvvOdv4l7fp55zxkTUAiGqYLxhmsLBnnFw9Oo1582uKQKBiy0aCUym5wTTUmP9u5urXkgFu023y5YuSQUJJXSi3azu3aElm/L9r0b26rzer2hi9iMJWGaDUMPSPtpj/AOZleY1J1QbNM429CoYPzjDsJODrI6LBj+byL2kMiZInLT5QaBNzBF8+RErtAmQZU5hEWZUGiDwMD2gPp0gXyHRsHUapGZhObeGtS+H9XkwJBZSRsKBvp859u3b//CeHl29gwWXgBRiMXoLxymOpeKBplmwIVs/dUkqVH6x7a3d/pALDgskDFg9YSyO3Zunhxgiz3P4LxaN8lLK7+eWpUhwrBl0o1/uCByc4wVPqTG+fm988b1+fnOJ1gAYwvHmRdEKkUYYyQJCFiqvdKiYGiPRS6QDELoZjgiU0VMeFxH3V1ni1eSHRkvrOBraVty/9vs3t3bdx1eX9enrTUBt1ylKQorBCHRCZFq2/qPb9CaEgnYxPXOJ2QwjMUah7YK+QbHmxEXKMhM8kmLQKCQNCnJcYhGoygkOiDUpDULTCEUjoSzBVEZhKpvwqJZ6x7dq9MNMUWtcFqt191Zdjqhuu84aQQN849ayjzB+pauzU8O47W08w4SBNqQ+L8z3rhMvpBCy5S3BMOe2EO36BAIUSeKooCkymjpn+D+FfuDb5VsEJbMIWkvUWnzAvGv093TRos/XbL6pdd0CoqOYskVOQXPGEQadTqRwv0TEVMyFC1jsc3D7qPwOgyFQjC+eRi3t7eeRHlW6W9n2JtlZIoMhS4/qH3AzLIoWCvKaeuJ1Rt3T3u1JqYYyhXzyhTttt4iFKq4gsPiwd17of/zbBllLN4pi7KsA58sqNeDgEGKwg9COIcIUQiRSrZtDIsXDcKawrZ8v/PunWA4Hk4GA5kprEAVjmj0RhBMoaxZyT+pYjJJkrJglkVe5BLWYMSjU30Z46QYzvnlYf7Cq22bhfSqLTTzF/OT2e7pVWPEQrVUOljmDoRqWZYt85zMky7cOIJR0agUkjJziGQ8/rozUozoeucDLIDheDgUP29/Eq9/wsK0zQJtecLqMPCVjAmVq8vL1WpVlkW6yBd5XowdjBam6HJqDmSLYXM293I/SlGQqAGCynzWO7xojCJWmSslRU4fkoosyApIZNS5AQwuM0eWRQ4HyUjAgoT88q1uYYqW79/vfP7wARqEOUO+EA6Zw1TBIJSiSN6I3rjFk0UbBRGTLycrUCji32maLpJksRhrpq29o8PD7oPeQL/HkGYgcTo4EQ1XO3gFCuq17uFFHXsheKaLiaAS40WCJowxScbjJTBExkRF40TsIG0j3AOHyFBUDIxTqJwF0OJAWoLiMzDMGiQkGARA/DG6MUYQEKBYE4HJiKKSHg8ggHBJf6CYxGm8SCcpYopRY++KbyC9XSAMWTYm8FsEJ7RJiFK2mJ+cNAe9o/0QR7BwoUC/08WYAhCTSRyvVjEw0AJjYI0cMbyHkYHCS55Q2lJkqipLueLHjx/QqHBIl1hD6ScBDcMRmDPIwBFECDAbDKgoi7IsRYJExmzjcpLGcZoIRXhxdXgIClgMBvhiOLT9QdYWFR2h7BCaQzR4/OTeNC9YOHIs0pT4CNf4OJ6s4stVCqcxPEyZ7GE0iixQUzB8kLy8jQNDwYamoSNo57uxAAaxxsFCvnnhDaI0YwRIIHxJ0yBodDDEcdyHgnGwoh/Hx2maBMG9K8+hVpvNDnRczH/pYNBRIYNAgqutxvDgEbuntnvUCMZJigdgIRofPx73j4/7pJgcFLCQcMsaxnjM+seBv18kNCTjECDLSVJk8ByK7xsYXtCAhXYKhX/J3pYlUZYOgMt84U4rpTORiPtORuLyhuKmf3Nzwz2eZxE0cMUTZwmWCQ/niiHHBMHLdfhqOHx1wl0+RYNar7sflIkoxOwxiMCB6N/vm1Z9MaZv7RqyjT9yeaUokyRIgnFGEwJLLifHQUdMTkElKGh4FIJhOJwzBMPsYf64lUokd5RAVA0Qa5Woj1i21s4xAYWby6+++UUsFvl0/+LqtNsbODUHs7eAYDNAYtaEhqsdcIpQE6JZrde7aGXjElOwxaARp8eof3MMC4IqdjuOMd3k40K2wAcqSLlKOJA5MtyDhSlDyxzvCMk2CuRQCIb5YgNDNJBoOCKCUBgUygL0FM4RX25I5F+ld1+p3hcISKA0P7+QK570TLsDqUbsYoHZEDfMmgdDbKH3R63JR70ne40OCNR5PNGiheLj/ef36Vgo/I0+SITDgUjYUcJAkNhflW0EJSOJCSiWWUAjKJKxSPBu+sOV2bQ2EUVhuD/BH+BON5KFgiAxCkm7GKPJoi5EsIsgzgyBGgiCpgtJLAxdJCoWP4IojSAkMAuxm3GRZEbUXUPwY+NKoQp+QaDgRt34vHfGRH1z77l3Jtb2PnnPOaOde4lmNKQEh0kURECqpDEKhjiIRuXSuWeYkM9MIBhDxjBkotEokxmFtp0BEKrcb7ePxSQgYFkW3RQAnH/N5Ao5YqTWsohPDhQKoMgd3Do3P5gnz+bzmIGZ6WTQwB4MMvZAqcLF58683KHiCgB0LokKaJ70ouSIhiiJRuU8G1oOoqSoJIMi0b80hEOa1VFgIFhIdApEpL/znRYQFDQSFCMjLxWGdmZBGVJJN4QCEEqRvbusxBI4ARQwQUoN2BxfXOMaRFYx195/af7zoGPjrpFNBITP3s6YF5zRQl7mIEg0Xnxw7m99Zi4xhUUchEJMtmDABffVk0Dx5cuXl19iFlMcypVEiT/iNCFgDhJlVjvO0d8NCUkYRkPPUPBGvIa27fOp4eY9DaVHrpg7kcMYZQsIDI5PTTiShUUhvsNUXF0rl8vF9sH9e84tXNMnb+vsGGFkQ4QpEL6v1XgF5RMceQ6m8gIC/Gp2vPKCIhyJNZK4sYRZNkCVX+qAQjBizXJlhgISSbJcu3kNHnF7YBjNf8YSA1wQc5CAIMXRtlOhPs38nv0raZUKMsSCg9JEDUTl0YiLWMcXE0KFcq3Is/flCiflr0llMilbRJCNWHw/xW0QgUJSGc0zOiD5PM+kvmg5J7cARavAyAcQIWVOgYKsih/h5r4aFrE3Xk9ZJJo1WAZ9QZMx4zH4zCeeYBAFHzsgbZDPCUJEJ9nifwgOGWdI7UPCcuKotVbYS54c1e+6MIMYqIruPVDYmy0WT5xotw/t2Via19m7PgFxfJ/T+zbLyIeG75viocBjRyxhIIBCLSaut4bOgmEhLLppevSGZr7T6cx9lWIe/1kDILExZmXUBIDE9givhXY4DNHQC5h/iZ/X830v5XlBwEe3kK+c3I0toFBEOZCgYzx6nihm+cWXKojEPv59T9bKZov86cahw0sLA0whQYCdzt61u1o9Db2BYhSjTp5MocDq0DyCwAE8NGHOqpazJKuQQ1T6PBDyp4k8uUmZua/fDIqvUxaKsw6L/sbBQIZHGEZhMAmjiE0QBGHgzaQf0PMVwygVpjKVW/wv0Up6uV7M1izEObOkySKiOmT3YoUju9aQLvV0VZAsaLT3zVMlIYANulp4MbpdCIz0LbY9GcMfZaY8NGJ7dNRmeUrfSC4Ri3bqr0tcnZMvwMAYzX2TAMFINDXGv1WUcDeGMr57N7pbjaqTyaQ6IUaTSTDxkA7uyxIpBxQOP7Bthxl7+AsMcoTOX4aDKuN1ic5Z0COESgVDWtSzqDLFkCi22+nLW3kbDrFSyHcUtHMcL/AcJ86SUcbWaqTTSXkDRTvZgiFxY0NAlC1KmwXBzgznnr59+vSpwSF9YUhx3SAka8LktaCMd6rVnVarWhII1JsYkQpRRDb4QdTFK92om+qm7JDX8HIaQ9RBgSyjLAT4/K0CRYKsyFI2LGMSPHK0aBGtXWX6SCHXMCi6UxImMxyp23WcbcdNBZjS8UzZ4BsS0RQHa16bRCNuJ0+qAxBQ6hAZblPT5t5KCQ4DRDMBYqK5SHC83In18+fOz9LPF6VqqVTtAaMHFXfS6/Ui4YjGkjKnCxSK5lLl/sqhFUplO9fO1Wo1kx9Za3GxrKdOTHL90afVVewgbxRwDXHt0yehKfNwka5kKJvCQTC54bhOyhGJvusGbhS42IPhpxL5UkZAVF4UpZE0tIcqsoIiDAsCwVeQ4EIxpRHzSJDM9PPrF8bLlyCYCg4vXpRKL6q9VqlVBclOtQQKmGCVcZU4joQjGkfhr8sX02fP0j7q9fp6s0nRpGI2i7k6JmnWCOSNZZXxANWiYIkOONYKq4urZatWbJ69tzUIRSIChRYsETiuy+LqRZZw0RUIOQNWbG01GqMwBUC9yXuZFOmrW6NRmKGKYVph8MJxAIoZCOONPzRwhwSIO4bH5k9tEw6bm3AAhFTaae20zOZ2qdSaYBJIQAfBAnsMbq1QKpYB0W60lSfH2pKpHbGO8M9VcNBWmXuRpY5CiVXfbTRWLm5t/FUtug7FIskQOPQd54LrQoQ3KCJKIY+tyaVYdowioK4QuKG2FqSigPPPNJl7+PbtQ2aC4xUTgeRvh3xkfPz69Y60eefn5gtQAOPKJmLFIi+gU4JKFWOQMkG11YthVMfhs8v3V+6nG1K6UV9upFfSkBCTREXS5oTWInBMiFUDWoM/fnlraWB3HSzmOrAYux4EGAQVCxT0tWdyg/ShjjqRAxJv24+JeAwUEB03kJTGVHtltBeo7gdzNwDxj/7A+PgxdsjHjx/vMImMO5ydOdUHXoAxdG6TM1ilh1xV1J5B0dnaQ/9Ay8v19XrzzJkc7qjDgpMnHQU/lNfKdI2rn9Cinr2vPvqkB9HV1VWrWT97v7Ige0Vu1O0Cw5WEwt3e3nb621xd6LNz+30zHfZ62/cc3wucKODwXEupyHOCCKnCBOMJaTwRlgk0QCEJBxO9emsSZQrkKRDAIRJ3JFDcgcYHMHDFhl2sndKT1u3nvWqphTPAwRyPU78uXrx3Mb3cXG+CYXn9zJkz9fXlxvr6+vfHj8vF2oMHD2q1cvlBeRWVEWRqvMxV1tKNWj29EQqFy18q9V2dXmGbXtLvM7b7Ty6g/oUn7oR0QYo6P1YyX8eCYKCO44Cxq1oWazzpTXqgMAKDEuXVq7evEDQIIvIGGCwxC2DEAgJBUfsf795vbl7ZxBHoOfVip4cbWuPWmG893KjcukWGUDdxBkVj5f79s2eXl0mVetNUUdZms/bAKlvlx6sPaBhNimnuGJ3mQa14pllv0E5/LYXjcc8Fhtu7wIfO8ZUYqhqOQOCFPjch0X/eu7DtGiYXgjGnNAhbfC0WjfjBEFYAjHa9yVglvqfZ+02l2bw2UsZxvP+Nhz2IKBK8bNmDgoawFweJC0kWlZIglIWQUmo7hUWStMgmZLqHNLVgsalJ2m0KtonsKiSkI5VCyWwPxc4c0p0edDdsNElnq9XP79mo+O28PDNp3T6f+f5enqlj+0riDXYlYcGOxBUKxj/hUV0anUGwPQLS0Rq24dl6M5RMhgyhsbCAKxYW5HRt5cNcPl8gV/giMYERERyISw5yluEcimVmYyI+h5QIbvLN8Rn69i8/OpRf9846jx4RDyMebJRUQHBLrLH7EgMiUFWlpwWS3+VwQfT8UIaT6hYfjLRAHQyN7e3t1fb3zX1ljhGPEYp2++joDB4iLtpV2autX5bO+mdLL7z+0pIHCW/gdQzPse2QFjK8kGKhSPDvffLTFZ6YiU8B4w1JGWrjEMtkJXUwGJfWS4mUmSBpvkIaEaXHsYbS3Oss1g9uHE6uf3dnndkqEEoyorlg9iDggs8wBtdcrjcEBgEzSYUjn9MOEbnq9xJxKXoJBDODolar7e2JL4DBLiwUDfli+1eKBIe+3Oy3jvr91lln0Ol3nMEAILZneHrSMIxkaDcEeEXi8Mur3OXlZS7HAkTkU74QB8QyaTyAIliCTpTCct83m/YnWKKkpQGZm00QG6BKJ2anltHByo1JMsBuowEMhUGBQBCAhhDiqKTQ7MoGDnKH/C531kMILYS2aRChgIznMKExwhPiCkgIihGMkTU4KJE/lECBlCla9746ah0dtTBFZwCQfqvV7w+cjtfZ1hqGFkJGKGlIZb12deMyd0mqyGezsUJ+KpLNEiDxQiQ/FQsGMwDJZlSAZCMgyWTmSKcqjLKZ2QzDLEPiampmOR6PHxzcxRMNo9FgrioxkDWQjJQN1NyTu0kVJeoKh+ALvh+ASDqhkZ43n4PDMzgIEEO6IgKkVtuHxgiGMoYcOSkgHDmpaGkfnfX+qqZS3EilUoM+IFIthNrVQWfgYYpQQwvpWrMpSWPy4Q0JkPzUDMqr6pHOMHcpq8HfM2qeGUTHmaCU8ClVJj33Jq5IjzPOzMWk/cAyvkI2f3r6/mGjwT/RYGLfUUXuqHiAQ2NXKSlfjaSWZNgQDCBLrvPBdxr2aHCPHyQUKPlak+ML3LHd9JovaASgo4/tbOzs7LEJDIUDCYt9lTcUFMWifCReKD9ZK6cULJMQKbePzHKrT7iIM4aDgeN5nm3otrGNMyafPXz2CBRwwBcRooFqAYYEb6dYlVEy3pbhdYJiguK5SPnkMPEefzaaX1ycP19cXHygCmwiPXcaL3x6evX1uoKdVEFCdHBE8si1ZDKpIUDodiMp04aIOEQZBe2yrStn8N+AhS4OMdSVput6U2uObSjBQmCA4z+JMUQKRao8HS4flVN/1swdvrFm7pupVLlcLafKnWoVFAPH7XR0sqfd1A0qipZ8xuLny6vTeD7io4s4eVtEA0EH8fn8TXS+uXm+eHMeCizPz883WX7RYX3B+ObmH6LFm+ef04L7/fffiR2cHrC8JNkR18mGkWzge2YsHJIKxUsKmq7prqu5mi1sIAEATY66JpuGeEiGrYWamsGAyOD3bepsL8Z+HrFAeyIVLIoJCDgg8949AmKamZumubdDdvlhr2V+VW6JqoOB7NWlvqRPW/e2t6Eh6RMUvODMncapGrPB4Cp9Aj6goZp/73OeOE/+Ji9zqRsTrLqY8mvjCRyCT+i2KSAJ7iZYhfApbzl9uS+vfXIICDJyY72BxAKhXU5Qd7UkAoKruzxit9hxbA0swsMVR2jsIWEGDcM2+KldBkQGHjawiMZw7Gf0fxiggAcczHumiTNMVK4eBVI/mmbFrOxs1Gops9Upt/vVar3eGcKiX69W+4NhqePQfNJreKSNuw8frVx9ekXSJD7IjDFSZGRKRDnxIY6UDwaRzGxEqggr1rT/7cQ4R8qHPwGfBE05LPzj8curu99TBpFhLDR4nuIEQ3dlcjhB48u1Xd1xiza749g6gOAAHjmNxE/puq0jV+PcpAXQdUaaqxdfomBDwmKDpz7KoWbNNAOm+VXbDNwzOQTkhlmLbuybZuqrVivVMgPhW2v1evVWq9runyFpyxHPxGtOPvppZSY+E8/nI3lai0IkQw9FmaSZlpQwL+vxt/33feN02vM3SQmL89dlaYJrcAyWwTjcxUjUGl88n8utPPp6HRJQMEga+AIlvyMhuKVS0Sq6luW6llu0bEsvYQ7FwBVSSTZE8uBCABZ1iJR0IllRxFm2boECCQollUSRwDBrlVqN6QcqFYZQ4IoRR/FLOBAIRGuBcHhtOry2RragxqjuXFYnjrP97O5PVwcz8XhumcOy2qaoihRUWvA5Hw2mtFtxeefJIo3SQUaN4BNVXoIJ1izUFt+bMWpMMB2L5OO5y5Ubj76nDzA8A2leyLOJgobmaNZF0XJLrlt03ZJeLJYsq8g8NT4Fj8XzJ24UD76dBsh2iqVS6aJkFyWwdL2InJKuUIwwyEY1kfaTxDgSxqhUYIJD5DLK1m4HTCBUopWKoGC7dat8VD1rt6QxV6tX7+5pIZ6byRfycUTrXcjnl5cLhUK8kM0En2ayPtrr7FOw0HI8fRr8nSfP5IMZRmrlRm45WT05eYBOWLJxO/i0cHr18ULD8JiMju8ajm05juEws6JbLPFVLF6g4oWrl0rctogcuLiWZmk6aCyQ7BIfdsnRnQtwAAExQEVQCIjRDglliprsaKu2Fd2qBAI1YITDFSzCKDC9Fg6Ea/uB6EbUNIHxeHp6bU3ajqpCgVi4P3uYy2cLwd9XT5hP90G3u+oLdrsniw8Y/rG5Ojc+wYArVUHPqR+L1BNW6ZuciI6uGnW7m1250V2cfzAxEcwUTr9Z8NAdLUSl4hlbHVA4IGG+GIAZWYTKhVXSLxxnOPQkZ8jHRT7TtF5RB4bGLQEHNHbZuJDT2Gef/fyPxBmiqPCoRUW//VYJMPvjra3pylYFmZXpcJu0Ea1FyZ+g2QqHw9PT4SrdmFqmwIIVWzP07eThh5f0FK8uq75CidPUm2/6SI+zsXGap9h9mg3CY5wWKy1vL0gNfuk3+AuaWqavkixOut3ug0QaJWBxzQCFcSfpSHcABc3pDAY2GIYyX+uCr57luBYGGTqd+nDYcwaOTiLRHaeHN1CpSJLsgU04cEAcBAUa4VDmuH17R5DUdgCxtQWDwOPAcfi4Eg4cP54OkyHYiZWNnejtHYLluFJ5HIBGmaiBxFkVFIPmt1eX+QIdRZzKwFQTqxPSVlEZ5f9A8r/mp8GQCsrE/RQKpk6ZmH/vOhO/rgSWBG813x2/7n83ARoWJhmqcSZ/dW3Bo8UnSmzPGQ7qA3s4rA+GvZ5l9YZur/ekd6t3YTmWhSk69eqwXh8+GQ4diR3dGToEg24z1AQbXlAUVHQIig8++5+it29TT2ChUESPj48fHx9XHleOMUUAHhwIGEJlZw9bRCV+JEgQbmGJUl0aLNl3yQ3ZLFlRvb+KkRTJBKSCWQ7+YIKXEPfxAWch4QcMfsADkhToL8bn7nObdxVB6UN51yt/WOY7eJ81tfKWodkdJtWxe4NqfdB36pT04fACGvUeRH79ddh7YjHowWiInjwZ3qoOe2QQqTIiiijgQNFTYWJRTyxJMn83bTavjZRxHO//4tGTINKb4mFVLMGTNHHAyXgpKUKxxJYhiRaMmJqDDVEP6WAOA1Y23cmmIG3Al/EQHAwuss4cMh2dsIZeEqudtF2pCX5+T4P67WReu7t9Pv3+Xp4n2aXVf1msGuIOwzDYfXFkMMw4cYIACArHCckzJdFiv6XnAIVxBBe1heqCWR6KfvqWdPH5TSSqY4sGJaTOgfS5LweqB/PMZZlcyNR8t0Qx2V7H+ziG2br0E+svLCOSqExZ0Iocsln+dDT/+ncmEI8eERM4/4pZYE/U6vHLxwDd0Xg0Ho9G9rg7urj4dXRxPRp3ua/p19fa3ccXoxGwDn6+++vdC2AIjgNYkDcQh1sU6eIpr7ScGbGBM9gHTi4hRJRyRMohB9lOvtdPQAEqJTKIWAJjLKaud+/3o0aj/uZNParnqRw0FZHFkXIInidu6o1sdpKZ4IDpdLKczxeWmY+qCiFFA5Fnd5rV853m+b2/752RN6vn7eY0m5/3WY3d+POvX/+6urrqXlxfqglhq9ei/7e7nd7DbnesKQmVETuk0fyNtE53rEDA6voAOHgI43ADGMJDJc8lt7hQmi1toKJhnOTiODZQzGANtSFy5fERFBzdyB0qDInjkDgJHNoMgNgdUHyyETEZl0l3U1bkCI0p2Y8AaMsgq22qAqqen1WlOJxV71AwKBdKf6N7crHDDEVdVPmOnZdXMo03P/vqm/6GdBW4QlhcyuoAHJgTAEOvdHo2MjVcYXc1ZAOCMt/pAKY7FhCQ0EYXHU0b4xsRXjqQ+6ODxyNQLDiIIKFgMErZc0FuNByVNYwvYqkv6mGgbgAqUbZAKkYqsrpz9frcysrSrnQUEiaUkfpypj3IZGtMzz/d3Vqrtpu01ytrVUoDyZBiQeVAEh8ECJNzVj7JIC9XwXa2c6dE51Vbl1DDGB88+u0KFD1SROuyIyA8WFR0jzOba7tsm2bXVlvHLpdNZZNrMFzAgy+tMxppSvYYz2hA4tnj0WjJdYurKkhcTMH2BcNfEEEwidOy8UBt6kkIIIQSxyB+JKWmqCsSIO/Ooyw/+YT1mYa0T7X89nJprzmARSZzh7w4aO5gD8zRvlNtkjuYjbXP1Rds1lZKJRbApdkgMiBRpeuoMk0nm5T4a61Cf+P+I4mMDo7o2ZhC2YJ/veJ5XHkmEouGqvmDBU6h78EJEIAEhmAHBCwEI/HPaPyYWxoVZFUpXRQeJMy0kiE6YuTQOT2V2KGspIfchIpvGMOcfE8SO7lDPZccggNbECF3+zf5ZbKkxe8xM2jL0j7jyOycy4SCF3PP5opIliTEHvjhFRn232zEi4oZ3jolaHgv+ZUql2d8kJNPaL3ENPb9Z7df+/rrPyEBCL6EBAKGmfK8SgocuITkTuOnozAEgw6bsmlrF2PNBgajJ6mM2Wy7DA1sIxFzUV7aXN0EhNpB5XToGox/NS0AZCtyj5egUJ44TRvDdGDkhn4S5xJB4SSUXOdE/nlQvDWvTQfNWmY6beKEhQaD6WSaGQzOOUPch9EaLMQfzZLUTBqqgeqw91jufqa0V8uoBMot2gkJm731RqF+88Nn/f6XH3YvVZaAg95CAPBMiROdEy5SSOfl0RLJFAkc5sIZZYICEhDRQKFxly9BwWtpEwmJhdy0yE2rc/bEjwDhXnEokCSpppOcAQZKrJ8ElNskCf3ATCH99a8+o3JEapkbITIFS728UEH1m8tWFm2r5UzSq6xe7m+/k81GFvN1Lmo8svJc5PfpT5iy0ZxYvAqi155/7atvPr5stR6qDKG3Kin22EFMIc7oESzS++jezPFSng4TMzTLQkOh0Ni6UIHJuGuaZb7Csg2L8tKDzc0HOONfFgoC2ky7rivXBNDpalEQ4ZrhkNiIYz/2nCTQfN8HhBYEehLoRKj5eB5Zk2y2tjeZiC9wRpMucZqhVsrqJTOOKaFCOEi4ECLEyL2qhIYqFlU5LOoGD1QpYY9kxUuVkjvrbzy9/+Z793sP9Qr6npEKCuxAsgCOXrm8fAgPYmbmLaIlxY9p+qEOC0KkzB4UIMAXmiBAGrTMpQciUCzCZHEAgru56TJ4TkGijAKNeOjEgoIDXigHgRx8TOFIMe3+Po8ilSwnE+ppTcmy1PV0WqvRO0wn9BjEz2QvI99iZSWzWlkrqlmcTKxCg8tG4Ym8mtTy7dtWLc80Jiv24PsKBZwSfUZ86ASHbvoSmGYrVQkrkjdSrV6nIlEyE3nwYA+Nsu+nQsyh6otGYIR4AQoLFPKoDIpNjMEeqZ2YgYPwcVHsuuniCd6AhwABRDwM4mEuiP0wSOIkTBJCRE3YX+/353Oig3hQzWaEOJWT2/Jal9m6BR51yMoxT4AQKzy1WN9C+W1KT+GmUCDI5O0TFU4QY/2GK2IsT5jcv5K3IXB9OSQ/khNtE1NcwgAYPQ8Cldksl1NAcMyhz4/nhypvEBGXkNDKbIKGPVIo0ObtTqyxeCk47tBNu8NhOs3muhw4N4aiJBAiBAco/ET2sePohIc02YyYVpM2Mqp/98ebCgySoan3h2R82XW1nidzVPVZPdpwpdKnLOlw57ld3lAV8egp7ssHX+Xjeu8Ai+2Jt3+86nYuO4Q8ScDutWyqqt3Re5UK6YJcOpsde96MCfYMKDki5cST/BGWw8APy2r4UBCQyiD4g0egAMECxUKcI6IEYQOAIEHBxiWKsYV4AyCOCQiqSOKYG/15RHQgAkIaaXlNZaGG0+mUlMGeGGm2/9XaYI1CQU9RrdKBosEOHQXnrGacqcWKKvWUveo6X+FjOFuD3bxVf/K607nq9GTmQRJs2aSMFkrNUh6/fwAcHx0aRyLv+Hh2Mjv+yPN8xwlD3w/DEAZjgsMGCMaS4GDn/4cCqfMFE8kR2GLInkwqUSJcyJu5WAgkoEiER5wEXIHEP3ivX59HNyokmHfklaJFbDT26/vLFpYhMljaWwcPZ/t5C/Gb5lokwMiye1nFEGqq9A4ox/JgMOX5ZGLlG1H/A9ZTe7rdBQUp0qSW9nAECVwmj87MMdDpYi3KMNgde5Lj6TfAUVZaZAn84Asi/38BwiYgJF0qFhyHUkRA8IB6soohKChpAOUIEUUh4RULilTOMTugYNgUTymktVoDc4g48D6hRPsu03P6bLT7XCmzu5Vh4RcILGpmZKxbpRKElHBPs33bovIuIkWWjNkgscCZKIui+Xsd5mCdMR21ZnoVXZIE43E8jwTGIXck02zVRstC5SHLLyBCt75gUyjEHGIJoRQs/WcKMCgxdOGgqglUsAdPwJJ2E2IFJG6RDEKqIEJgkmC8gEj8ZINFPMmZZDzUyEwXS5MsTjIia6Iu6DB3pLdiVY9p2XQvs/xpZotpOAV4SnpYoREjLAgZFUXNNeosutemFrcHW3tZ8qr8J+T5ixuv/sI6jUyw9ArTQs/RiXoyBaOi9Y2/KCLaALpD5gu5Y6ZSPAEBvFJBAAoRjjA15Q2e/QNNIK2tH5DYwAAAAABJRU5ErkJggg==", "public": true}, {"link": "/api/images/system/simple_visibility_chart_card_with_background_system_widget_image.png", "title": "\"Simple visibility chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_visibility_chart_card_with_background_system_widget_image.png", "publicResourceKey": "ZXtKPhpQdoEHmIN9qNWWEWJcdDoko1kB", "mediaType": "image/png", "data": "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", "public": true}]}