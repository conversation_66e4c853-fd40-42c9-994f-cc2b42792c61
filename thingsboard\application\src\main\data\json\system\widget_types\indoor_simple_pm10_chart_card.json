{"fqn": "indoor_simple_pm10_chart_card", "name": "Indoor simple PM10 chart card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_simple_pm10_chart_card_system_widget_image.png", "description": "Displays historical indoor fine and coarse particulate matter (PM10) values as a simplified chart. Optionally may display the corresponding latest indoor PM10 value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pm10', label: 'PM10', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'pm10', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM10\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 50 - 25;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 200) {\\n\\tvalue = 200;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 50 - 25;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 200) {\\n\\tvalue = 200;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":150,\"color\":\"#FFA600\"},{\"from\":150,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"PM10\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:broom\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"µg/m³\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["environment", "particulate", "matter", "air", "pm10", "coarse particulates", "coarse particles", "particulate matter 10", "inhalable particles", "larger particulates", "dust", "airborne coarse particles"], "resources": [{"link": "/api/images/system/indoor_simple_pm10_chart_card_system_widget_image.png", "title": "\"Indoor simple PM10 chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_simple_pm10_chart_card_system_widget_image.png", "publicResourceKey": "JK5uh49MtLpnhIre3X9iCTKOxJ5CViTk", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAilBMVEUAAADf39/g4ODf39/f39/g4OD////k5OT/pgDg4OAhISH/9N9YWFj/sSD/6b//3Z+QkJD/vEB0dHT/0oDy8vL/qxD/+u8vLy//x2A9PT3Hx8fV1dWrq6s8PDyCgoL/2JCsrKyenp7/zXD/78+6urqdnZ1mZmb/wlBKSkr/tzD/x1//+e//46/29vaFHg9VAAAABnRSTlMAIL9AEN/GQiaNAAAEpUlEQVR42uzPwQ2AIBAAsBMQc3/WYP/1TJzAJ5B2gwYAAAAAAMB/7dpc+xq15LO5vGvElbNvb2SNMvoBZonsR0iRxYisRuRlvw5WJISBKIruXsGjFqEoCNEoCirz/184tt0wNrSzTZzJhUC2B0Koqq0Gqa0Gqa0Gqa0Gqa1/AskZrxYzG3tgsh57Zg6gmwF4UBMU71dIjj1eBYawcYFRHwJSEOJxt2HaUvkt8xLifnYgEIAqLEUHLD4gnSrgzBB2KNo1xGWlHo4zZFOYxhmII+UJeyI4oWDXkF6ZJFBxhoRgnGEaFNPQ3QOykofkDaJqGTB1dmrTPSAeD8n6BsGRKdQoZ8gC30/BriFYuZf8M2Sh4gcC1T6w/P/7GQLjnpxpCUfjBv/KWJIAsA2ADIwZxbuAIJCGO3UFgeTyz6XNWneuQWqrQWqrQWqrQWqrQWqrQWqrdoj7X4B4iGQa5e4QSdzWdWOSe0MkDYegH5LfGvLNntk2JwoDAfg+LXsJCQRM8A2taItv///33SYxp3biFTrDFWd8ZkrCJu3sI7vGqRuWgGPONk8tsk/DLE2fWSS5PoecTZ5Y5I0t6Bpq6/lEJotL/h/1NThLn05kaU8Op1IVADe19WQiC7bfLesqsbnPAW5qa/Qik7d8k0AgrScAST37SFkBN8zSsYssKkbk4EnY0g3Ffm9DkdrqKZIJTwMebqdf0woTiUqFWnGIklT1nPIOlVOwBKLM2epbIiV6puARdvolHA/RqG6OqCDKymeeVl7L11N8Y243rCpWFUl3kRaVtPDg1UlkjVkkerJ/BcU/T+93NvdDAo9NqnRPFnlRs7yziMLy7jUV+oGI2crrjcCLOJdbILbSeJVS4Tre6Sz3Yz0jhV1VwGPmRfqRT1wHsTTpKHLA9f2dCCIZIhDlZdCIgkLORrr6QaS0KcozTUPjvJQQJcTYhffYRc1mFdsn0In3arbrJkLZNKrNQsUIiIvQVSuBF5GjqywKuZigJY0UMtzY18LE6+X6vVJRLKErSb3vJHJCz8G4dyzkcRGOqIwV8CLCJUsKPtbSBk1XiQcpNT5qke/STeSAzVke0XXKka5xEZrbXpBeRPot7pfCUmurrUFEvX7QIoOKEMa3vLYpCfNApKT1IGKFz3cinIapaxt+PpsHp8N8aJGQNOcCG2lLo5X8s8gUxVXkRDdRkUC8Rf6bCP0Eys8ize0TyXDaW2SWDiwyRXHJ1pyVQ6NWWZCL9ohC2VckZ8uBRc5ozxEuQhr3PdIAGGFFTtptaK0IJ/eeIvn3W73HyY72KSCPifgldCcMCjeXFC97iASPwUVORySEhJiIQNRtaBWaN1bkgLyPSPAYvtmNlByucG7+itCSMZyHT1XmjLjlqO52unU/xCnIY3iROEHkRm8NRIsCttkWuhI8RiRyQFGWyhZTT1bkMSYRLtB//OgtsoGfFuFZBjfI9TozMAZ+/xrVv4NeIi+R0fESGRsvkbHxEhkbL5Gx8aedO7gBEIaBIGhsEcl/2qD/9mgBiQdJNNPBFXBryGwMmU1HbpAOGuPKqP7/1vnZ3UdEnetnqbJ2CoUBAAAAAAC88gDG/flsHc1jFAAAAABJRU5ErkJggg==", "public": true}]}