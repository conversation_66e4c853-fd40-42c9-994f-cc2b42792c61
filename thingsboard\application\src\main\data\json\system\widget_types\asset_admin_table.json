{"fqn": "entity_admin_widgets.asset_admin_table", "name": "Asset admin table", "deprecated": false, "image": "tb-image;/api/images/system/asset_admin_table_system_widget_image.png", "description": "Customized entity table widget with preconfigured actions to create, update and delete assets.", "descriptor": {"type": "latest", "sizeX": 7.5, "sizeY": 6.5, "resources": [], "templateHtml": "<tb-entities-table-widget \n    [ctx]=\"ctx\">\n</tb-entities-table-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n}\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.entitiesTableWidget.onDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.entitiesTableWidget.onEditModeChanged();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        hasDataPageLink: true,\n        warnOnPageDataOverflow: false,\n        dataKeysOptional: true,\n        supportsUnitConversion: true\n    };\n}\n\nself.actionSources = function() {\n    return {\n        'actionCellButton': {\n            name: 'widget-action.action-cell-button',\n            multiple: true,\n            hasShowCondition: true\n        },\n        'rowClick': {\n            name: 'widget-action.row-click',\n            multiple: false\n        },\n        'rowDoubleClick': {\n            name: 'widget-action.row-double-click',\n            multiple: false\n        },\n        'cellClick': {\n            name: 'widget-action.cell-click',\n            multiple: true\n        }\n    };\n}\n\nself.onDestroy = function() {\n}\n", "settingsSchema": "", "dataKeySettingsSchema": "", "settingsDirective": "tb-entities-table-widget-settings", "dataKeySettingsDirective": "tb-entities-table-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-entities-table-basic-config", "defaultConfig": "{\"timewindow\":{\"realtime\":{\"interval\":1000,\"timewindowMs\":86400000},\"aggregation\":{\"type\":\"NONE\",\"limit\":200}},\"showTitle\":true,\"backgroundColor\":\"rgb(255, 255, 255)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"4px\",\"settings\":{\"entitiesTitle\":\"Asset admin table\",\"enableSearch\":true,\"enableSelectColumnDisplay\":true,\"enableStickyHeader\":true,\"enableStickyAction\":true,\"showCellActionsMenu\":true,\"reserveSpaceForHiddenAction\":\"true\",\"displayEntityName\":false,\"displayEntityLabel\":false,\"displayEntityType\":false,\"displayPagination\":true,\"defaultPageSize\":10,\"defaultSortOrder\":\"entityName\",\"useRowStyleFunction\":false},\"title\":\"Asset admin table\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400,\"padding\":\"5px 10px 5px 10px\"},\"useDashboardTimewindow\":false,\"showLegend\":false,\"datasources\":[{\"type\":\"function\",\"name\":\"Simulated\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Entity name\",\"color\":\"#4caf50\",\"settings\":{},\"_hash\":0.6985800247727296,\"funcBody\":\"return 'Simulated';\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Entity type\",\"color\":\"#f44336\",\"settings\":{},\"_hash\":0.10073938422359707,\"funcBody\":\"return 'Device';\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#f44336\",\"settings\":{\"columnWidth\":\"0px\",\"useCellStyleFunction\":false,\"cellStyleFunction\":\"\",\"useCellContentFunction\":false,\"cellContentFunction\":\"\"},\"_hash\":0.6401141393938932,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"showTitleIcon\":false,\"titleIcon\":\"more_horiz\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"displayTimewindow\":true,\"actions\":{\"headerButton\":[{\"name\":\"Add asset\",\"icon\":\"add\",\"type\":\"customPretty\",\"customHtml\":\"<form #addAssetForm=\\\"ngForm\\\" [formGroup]=\\\"addAssetFormGroup\\\"\\n      (ngSubmit)=\\\"save()\\\" style=\\\"width: 480px;\\\">\\n  <mat-toolbar class=\\\"flex flex-row\\\" color=\\\"primary\\\">\\n    <h2>Add asset</h2>\\n    <span class=\\\"flex-1\\\"></span>\\n    <button mat-icon-button\\n            (click)=\\\"cancel()\\\"\\n            type=\\\"button\\\">\\n      <mat-icon class=\\\"material-icons\\\">close</mat-icon>\\n    </button>\\n  </mat-toolbar>\\n  <mat-progress-bar color=\\\"warn\\\" mode=\\\"indeterminate\\\" *ngIf=\\\"isLoading$ | async\\\">\\n  </mat-progress-bar>\\n  <div style=\\\"height: 4px;\\\" *ngIf=\\\"!(isLoading$ | async)\\\"></div>\\n  <div mat-dialog-content>\\n    <div class=\\\"mat-padding flex flex-col\\\">\\n      <mat-form-field class=\\\"mat-block\\\">\\n        <mat-label>Asset name</mat-label>\\n        <input matInput formControlName=\\\"assetName\\\" required>\\n        <mat-error *ngIf=\\\"addAssetFormGroup.get('assetName').hasError('required')\\\">\\n          Asset name is required.\\n        </mat-error>\\n      </mat-form-field>\\n      <div class=\\\"flex flex-row gap-2\\\">\\n        <tb-entity-subtype-autocomplete\\n            class=\\\"max-w-50% flex-full\\\"\\n            formControlName=\\\"assetType\\\"\\n            [required]=\\\"true\\\"\\n            [entityType]=\\\"'ASSET'\\\"\\n        ></tb-entity-subtype-autocomplete>\\n        <mat-form-field class=\\\"mat-block max-w-50% flex-full\\\">\\n          <mat-label>Label</mat-label>\\n          <input matInput formControlName=\\\"assetLabel\\\">\\n        </mat-form-field>\\n      </div>\\n      <div formGroupName=\\\"attributes\\\" class=\\\"flex flex-row gap-2\\\">\\n        <mat-form-field class=\\\"mat-block max-w-50% flex-full\\\">\\n          <mat-label>Latitude</mat-label>\\n          <input type=\\\"number\\\" step=\\\"any\\\" matInput formControlName=\\\"latitude\\\">\\n        </mat-form-field>\\n        <mat-form-field class=\\\"mat-block max-w-50% flex-full\\\">\\n          <mat-label>Longitude</mat-label>\\n          <input type=\\\"number\\\" step=\\\"any\\\" matInput formControlName=\\\"longitude\\\">\\n        </mat-form-field>\\n      </div>\\n    </div>\\n  </div>\\n  <div mat-dialog-actions class=\\\"flex flex-row\\\">\\n    <span class=\\\"flex-1\\\"></span>\\n    <button mat-button color=\\\"primary\\\"\\n            type=\\\"button\\\"\\n            [disabled]=\\\"(isLoading$ | async)\\\"\\n            (click)=\\\"cancel()\\\" cdkFocusInitial>\\n      Cancel\\n    </button>\\n    <button mat-button mat-raised-button color=\\\"primary\\\"\\n            style=\\\"margin-right: 20px;\\\"\\n            type=\\\"submit\\\"\\n            [disabled]=\\\"(isLoading$ | async) || addAssetForm.invalid || !addAssetForm.dirty\\\">\\n      Create\\n    </button>\\n  </div>\\n</form>\\n\",\"customCss\":\"\",\"customFunction\":\"let $injector = widgetContext.$scope.$injector;\\nlet customDialog = $injector.get(widgetContext.servicesMap.get('customDialog'));\\nlet assetService = $injector.get(widgetContext.servicesMap.get('assetService'));\\nlet attributeService = $injector.get(widgetContext.servicesMap.get('attributeService'));\\n\\nopenAddAssetDialog();\\n\\nfunction openAddAssetDialog() {\\n    customDialog.customDialog(htmlTemplate, AddAssetDialogController).subscribe();\\n}\\n\\nfunction AddAssetDialogController(instance) {\\n    let vm = instance;\\n    \\n    vm.addAssetFormGroup = vm.fb.group({\\n      assetName: ['', [vm.validators.required]],\\n      assetType: ['', [vm.validators.required]],\\n      assetLabel: [''],\\n      attributes: vm.fb.group({\\n          latitude: [null],\\n          longitude: [null]\\n      })      \\n    });\\n    \\n    vm.cancel = function() {\\n        vm.dialogRef.close(null);\\n    };\\n    \\n    vm.save = function() {\\n        vm.addAssetFormGroup.markAsPristine();\\n        let asset = {\\n            name: vm.addAssetFormGroup.get('assetName').value,\\n            type: vm.addAssetFormGroup.get('assetType').value,\\n            label: vm.addAssetFormGroup.get('assetLabel').value\\n        };\\n        assetService.saveAsset(asset).subscribe(\\n            function (asset) {\\n                saveAttributes(asset.id).subscribe(\\n                    function () {\\n                        widgetContext.updateAliases();\\n                        vm.dialogRef.close(null);\\n                    }\\n                );\\n            }\\n        );\\n    };\\n    \\n    function saveAttributes(entityId) {\\n        let attributes = vm.addAssetFormGroup.get('attributes').value;\\n        let attributesArray = [];\\n        for (let key in attributes) {\\n            attributesArray.push({key: key, value: attributes[key]});\\n        }\\n        if (attributesArray.length > 0) {\\n            return attributeService.saveEntityAttributes(entityId, \\\"SERVER_SCOPE\\\", attributesArray);\\n        } else {\\n            return widgetContext.rxjs.of([]);\\n        }\\n    }\\n}\",\"customResources\":[],\"id\":\"70837a9d-c3de-a9a7-03c5-dccd14998758\"}],\"actionCellButton\":[{\"name\":\"Edit asset\",\"icon\":\"edit\",\"type\":\"customPretty\",\"customHtml\":\"<form #editAssetForm=\\\"ngForm\\\" [formGroup]=\\\"editAssetFormGroup\\\"\\n      (ngSubmit)=\\\"save()\\\" style=\\\"width: 480px;\\\">\\n  <mat-toolbar class=\\\"flex flex-row\\\" color=\\\"primary\\\">\\n    <h2>Edit asset</h2>\\n    <span class=\\\"flex-1\\\"></span>\\n    <button mat-icon-button\\n            (click)=\\\"cancel()\\\"\\n            type=\\\"button\\\">\\n      <mat-icon class=\\\"material-icons\\\">close</mat-icon>\\n    </button>\\n  </mat-toolbar>\\n  <mat-progress-bar color=\\\"warn\\\" mode=\\\"indeterminate\\\" *ngIf=\\\"isLoading$ | async\\\">\\n  </mat-progress-bar>\\n  <div style=\\\"height: 4px;\\\" *ngIf=\\\"!(isLoading$ | async)\\\"></div>\\n  <div mat-dialog-content>\\n    <div class=\\\"mat-padding\\\" class=\\\"flex flex-col\\\">\\n      <mat-form-field class=\\\"mat-block\\\">\\n        <mat-label>Asset name</mat-label>\\n        <input matInput formControlName=\\\"assetName\\\" required>\\n        <mat-error *ngIf=\\\"editAssetFormGroup.get('assetName').hasError('required')\\\">\\n          Asset name is required.\\n        </mat-error>\\n      </mat-form-field>\\n      <div class=\\\"flex flex-row gap-2\\\">\\n        <tb-entity-subtype-autocomplete\\n            class=\\\"max-w-50% flex-full\\\"\\n            formControlName=\\\"assetType\\\"\\n            [required]=\\\"true\\\"\\n            [entityType]=\\\"'ASSET'\\\"\\n        ></tb-entity-subtype-autocomplete>\\n        <mat-form-field class=\\\"mat-block max-w-50% flex-full\\\">\\n          <mat-label>Label</mat-label>\\n          <input matInput formControlName=\\\"assetLabel\\\">\\n        </mat-form-field>\\n      </div>\\n      <div formGroupName=\\\"attributes\\\" class=\\\"flex flex-row gap-2\\\">\\n        <mat-form-field class=\\\"mat-block max-w-50% flex-full\\\">\\n          <mat-label>Latitude</mat-label>\\n          <input type=\\\"number\\\" step=\\\"any\\\" matInput formControlName=\\\"latitude\\\">\\n        </mat-form-field>\\n        <mat-form-field class=\\\"mat-block max-w-50% flex-full\\\">\\n          <mat-label>Longitude</mat-label>\\n          <input type=\\\"number\\\" step=\\\"any\\\" matInput formControlName=\\\"longitude\\\">\\n        </mat-form-field>\\n      </div>\\n    </div>\\n  </div>\\n  <div mat-dialog-actions class=\\\"flex flex-row\\\">\\n    <span class=\\\"flex-1\\\"></span>\\n    <button mat-button color=\\\"primary\\\"\\n            type=\\\"button\\\"\\n            [disabled]=\\\"(isLoading$ | async)\\\"\\n            (click)=\\\"cancel()\\\" cdkFocusInitial>\\n      Cancel\\n    </button>\\n    <button mat-button mat-raised-button color=\\\"primary\\\"\\n            type=\\\"submit\\\"\\n            style=\\\"margin-right: 20px;\\\"\\n            [disabled]=\\\"(isLoading$ | async) || editAssetForm.invalid || !editAssetForm.dirty\\\">\\n      Update\\n    </button>\\n  </div>\\n</form>\\n\",\"customCss\":\"\",\"customFunction\":\"let $injector = widgetContext.$scope.$injector;\\nlet customDialog = $injector.get(widgetContext.servicesMap.get('customDialog'));\\nlet assetService = $injector.get(widgetContext.servicesMap.get('assetService'));\\nlet attributeService = $injector.get(widgetContext.servicesMap.get('attributeService'));\\n\\nopenEditAssetDialog();\\n\\nfunction openEditAssetDialog() {\\n    customDialog.customDialog(htmlTemplate, EditAssetDialogController).subscribe();\\n}\\n\\nfunction EditAssetDialogController(instance) {\\n    let vm = instance;\\n    \\n    vm.asset = null;\\n    vm.attributes = {};\\n    \\n    vm.editAssetFormGroup = vm.fb.group({\\n      assetName: ['', [vm.validators.required]],\\n      assetType: ['', [vm.validators.required]],\\n      assetLabel: [''],\\n      attributes: vm.fb.group({\\n          latitude: [null],\\n          longitude: [null]\\n      })      \\n    });\\n    \\n    vm.cancel = function() {\\n        vm.dialogRef.close(null);\\n    };\\n    \\n    vm.save = function() {\\n        vm.editAssetFormGroup.markAsPristine();\\n        vm.asset.name = vm.editAssetFormGroup.get('assetName').value,\\n        vm.asset.type = vm.editAssetFormGroup.get('assetType').value,\\n        vm.asset.label = vm.editAssetFormGroup.get('assetLabel').value\\n        assetService.saveAsset(vm.asset).subscribe(\\n            function () {\\n                saveAttributes().subscribe(\\n                    function () {\\n                        widgetContext.updateAliases();\\n                        vm.dialogRef.close(null);\\n                    }\\n                );\\n            }\\n        );\\n    };\\n    \\n    getEntityInfo();\\n    \\n    function getEntityInfo() {\\n        assetService.getAsset(entityId.id).subscribe(\\n            function (asset) {\\n                attributeService.getEntityAttributes(entityId, 'SERVER_SCOPE',\\n                                                    ['latitude', 'longitude']).subscribe(\\n                   function (attributes) {\\n                        for (let i = 0; i < attributes.length; i++) {\\n                            vm.attributes[attributes[i].key] = attributes[i].value; \\n                        }\\n                        vm.asset = asset;\\n                        vm.editAssetFormGroup.patchValue(\\n                            {\\n                                assetName: vm.asset.name,\\n                                assetType: vm.asset.type,\\n                                assetLabel: vm.asset.label,\\n                                attributes: {\\n                                    latitude: vm.attributes.latitude,\\n                                    longitude: vm.attributes.longitude\\n                                }\\n                            }, {emitEvent: false}\\n                        );\\n                   } \\n                );\\n            }\\n        );    \\n    }\\n    \\n    function saveAttributes() {\\n        let attributes = vm.editAssetFormGroup.get('attributes').value;\\n        let attributesArray = [];\\n        for (let key in attributes) {\\n            attributesArray.push({key: key, value: attributes[key]});\\n        }\\n        if (attributesArray.length > 0) {\\n            return attributeService.saveEntityAttributes(entityId, 'SERVER_SCOPE', attributesArray);\\n        } else {\\n            return widgetContext.rxjs.of([]);\\n        }\\n    }\\n}\",\"customResources\":[],\"id\":\"93931e52-5d7c-903e-67aa-b9435df44ff4\"},{\"name\":\"Delete asset\",\"icon\":\"delete\",\"type\":\"custom\",\"customFunction\":\"let $injector = widgetContext.$scope.$injector;\\nlet dialogs = $injector.get(widgetContext.servicesMap.get('dialogs'));\\nlet assetService = $injector.get(widgetContext.servicesMap.get('assetService'));\\n\\nopenDeleteAssetDialog();\\n\\nfunction openDeleteAssetDialog() {\\n    let title = \\\"Are you sure you want to delete the asset \\\" + entityName +  \\\"?\\\";\\n    let content = \\\"Be careful, after the confirmation, the asset and all related data will become unrecoverable!\\\";\\n    dialogs.confirm(title, content, 'Cancel', 'Delete').subscribe(\\n        function (result) {\\n            if (result) {\\n                deleteAsset();\\n            }\\n        }\\n    );\\n}\\n\\nfunction deleteAsset() {\\n    assetService.deleteAsset(entityId.id).subscribe(\\n        function () {\\n            widgetContext.updateAliases();\\n        }\\n    );\\n}\\n\",\"id\":\"ec2708f6-9ff0-186b-e4fc-7635ebfa3074\"}]},\"configMode\":\"basic\",\"titleFont\":null,\"titleColor\":null}"}, "tags": ["provisioning", "management", "administration", "admin"], "resources": [{"link": "/api/images/system/asset_admin_table_system_widget_image.png", "title": "\"Asset admin table\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "asset_admin_table_system_widget_image.png", "publicResourceKey": "ziZHgGTpj2P5zp1TT7omH8W7cB882Sbf", "mediaType": "image/png", "data": "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", "public": true}]}