{"fqn": "simple_uv_index_chart_card", "name": "Simple UV Index chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_uv_index_chart_card_system_widget_image.png", "description": "Displays historical UV index values as a simplified chart. Optionally may display the corresponding latest UV index value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'uv', label: 'UV Index', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'uv', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"UV Index\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.ceil(Math.random() * 4 - 2);\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 14) {\\n\\tvalue = 14;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.ceil(Math.random() * 4 - 2);\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 14) {\\n\\tvalue = 14;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#80C32C\"},{\"from\":2,\"to\":5,\"color\":\"#FFA600\"},{\"from\":5,\"to\":7,\"color\":\"#F36900\"},{\"from\":7,\"to\":10,\"color\":\"#F04022\"},{\"from\":10,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"UV Index\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"light_mode\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":null,\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "uv", "ultraviolet", "sunburn"], "resources": [{"link": "/api/images/system/simple_uv_index_chart_card_system_widget_image.png", "title": "\"Simple UV Index chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_uv_index_chart_card_system_widget_image.png", "publicResourceKey": "dFrdbjoeiG539StdkUeM4re8fmJEE5bN", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAXVBMVEUAAADg4ODf39/g4ODg4OD////g4OAhISHj4+PHx8c8PDysrKzx8fFYWFh0dHSQkJAvLy+Awyzf8MrV1dW6urqCgoJKSkqdnZ2enp5mZmbv7++43Yj3+/KIxzmg0mFA+mXkAAAABXRSTlMA7yC/r1EOHTEAAAOxSURBVHja7dztVtpAEIBhQIeZ2dkvyRdo2/u/zG6wNJSQhlAbNzjvOUKC/PBxd5McQVaapmmapmmapmmapmmapmmapmlfrs3Terv0njfJsX6FxWfXm9WzhQfo9Wm1hYdo/SiQrUIySyG5pZDcUkhuKSS3FJJbXwDSFLCkhiFO4Kxyn25EbOB0XwRKt5RuuwgZ5m4cUgYAIaCDhV9509ocRHfaAUbOHkK1tcwgHi4hISab8SdISSzUbsuRxVIClOlLZlKNTy0yiB2jgzBKKzhBjKlNolXo6rQX0CWjYCFIMNZMkBpT+z4EnG+3f0M8EAqlZxbIdEQSOGcqGGsOSIkOsM0XGC8hIVrjO0gAQGEskocZ6+PAEEYL480wImTfR0TS1iXEYkC6gBTvkCKRmC0Izrbwx6dWYRCd7fblOG2k1WANFxAbK2iQ2zsbgExI4rHmgdDBUskgoXuoQsQKUg3KJQQkqZGP+nYJWYp7GGsWiPj380h9NiaWLAxm6feknL/xM/uSGoYEhiX1Ba5+F5ZCckshuaWQ3MoBYqmwy4dQMJgynpYNCRFdI9IcEMOSIR69hWOUNpcLqc6HIaBfKkRax7lkv0wIGQN/5CItEuKRLmTRLRFCWF2ZawuEGGOvrH5eHCSgQC+bdAuDUPRwJcZqYRAfaeDUUuYL4X0IJcF5zeCydpE+C2JZuBj+boh4zMn54ckNzzn3ORBx+LfLVzbo2tEoGoOnp9gKawtDNbj/BAjV6JqSWA5o5BoTDXfHKTzsuRQfsbIw3AGL2SEUI3ek/rQv4vmvnrzBVKwYuiYdg6dDdm/fdzddLVG357HsOQxdricmGIv718GFiPBkyLe3Hy8vL7vJV0t1tH3nPYU/5h61k7Et+nIaJClughCG/tns3x2t5LTibFmZViBERelNa5kA+f62290C8Zc/aIUy3TF0sIvO+xrTXVN0D3eWm6YWwC0Q7q/uswXPJhL8Q1w5Yw6BLXT1LCMQuA3iDPSq0OyLdlo7NAQfXmexHwcRFOhXOjwWg4X/GH3giBgDV6MyhHzewjEOEczqRca7IWQM5NS9EDKRIKfuhEiMmb3B7h6IDQZdXuNxD8SGiC6/1+CnQviAOTJ6kJHYYQx5/pfcBIgNJlvGFEiT5dK4A3LImdFCPv/FUIUoJOcUklsKyS2F5JZCckshuaWQ3Nqu1vAQbVdPD/DRQQD2ebVZ5/r3hAm9rtuPc3reLr3102alaZqmaZqmaZqmaZqmaZqmaZr21foJlfySPp88UEgAAAAASUVORK5CYII=", "public": true}]}