{"fqn": "horizontal_pump_vibration_card", "name": "Horizontal vibration card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_vibration_card.svg", "description": "Displays the latest vibration telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "#container {\n    overflow: auto;\n}\n\n.tbDatasource-container {\n    margin: 5px;\n    padding: 8px;\n}\n\n.tbDatasource-title {\n    font-size: 1.200rem;\n    font-weight: 500;\n    padding-bottom: 10px;\n}\n\n.tbDatasource-table {\n    width: 100%;\n    box-shadow: 0 0 10px #ccc;\n    border-collapse: collapse;\n    white-space: nowrap;\n    font-size: 1.000rem;\n    color: #757575;\n}\n\n.tbDatasource-table td {\n    position: relative;\n    border-top: 1px solid rgba(0, 0, 0, 0.12);\n    border-bottom: 1px solid rgba(0, 0, 0, 0.12);\n    padding: 0px 18px;\n    box-sizing: border-box;\n}", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'vibration', label: 'Vibration', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Vibration\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 3.3 - 1.7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"horizontal\",\"autoScale\":true,\"showLabel\":true,\"labelFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"#000000DE\",\"rangeList\":null,\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"waves\",\"iconColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":0,\"to\":2.8,\"color\":\"#3FA71A\"},{\"from\":2.8,\"to\":4.5,\"color\":\"#FFA600\"},{\"from\":4.5,\"to\":7.1,\"color\":\"#F36900\"},{\"from\":7.1,\"to\":null,\"color\":\"#F04022\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":0,\"to\":2.8,\"color\":\"#3FA71A\"},{\"from\":2.8,\"to\":4.5,\"color\":\"#FFA600\"},{\"from\":4.5,\"to\":7.1,\"color\":\"#F36900\"},{\"from\":7.1,\"to\":null,\"color\":\"#F04022\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Horizontal vibration card\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"units\":\"mm/s\",\"decimals\":1,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"showTitleIcon\":false,\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\"}"}, "tags": ["vibration", "pump vibration", "motion", "resonance", "dynamic balancing", "mechanical integrity", "pulsation", "frequency"], "resources": [{"link": "/api/images/system/horizontal_vibration_card.svg", "title": "horizontal_vibration_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_vibration_card.svg", "publicResourceKey": "lR14iDd1ocR7k4FeZQS77mLIXGugOkH4", "mediaType": "image/svg+xml", "data": "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", "public": true}]}