<?xml version="1.0" encoding="UTF-8"?>

<!--
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-XML_9-V1_0_2-20210119-A.xml
   Path: http://www.openmobilealliance.org/release/LWM2M_SWMGMT

OMNA LwM2M Registry
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 9.xml

NORMATIVE INFORMATION

  Information about this file can be found in the latest revision of

    OMA-TS-LightweightM2M-V1_0

  This is available at http://www.openmobilealliance.org/release/LightweightM2M/

  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

LEGAL DISCLAIMER

  Copyright 2021 Open Mobile Alliance.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

  1. Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
  3. Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived
  from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.

  The above license is used as a license under copyright only.  Please
  reference the OMA IPR Policy for patent licensing terms:
  https://www.omaspecworks.org/about/intellectual-property-rights/

-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>LWM2M Software Management</Name>
		<Description1><![CDATA[This LwM2M objects provides the resources needed to perform software management on the device. Each software component is managed via a dedicated Software Management Object instance.]]></Description1>
		<ObjectID>9</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:9</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>PkgName</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..255 bytes</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Name of the software package]]></Description>
			</Item>
			<Item ID="1">
				<Name>PkgVersion</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..255 bytes</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Version of the software package]]></Description>
			</Item>
			<Item ID="2">
				<Name>Package</Name>
				<Operations>W</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Software package
The package can be in one single software component, or any delivery material used by the Device to determine the software component(s) and proceed to their installation.
Can be archive file, executable, manifest. This resource to be used when it is single block of delivery.
]]>				</Description>
			</Item>
			<Item ID="3">
				<Name>Package URI</Name>
				<Operations>W</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..255 bytes</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[URI from where the device can download the software package by an alternative mechanism. As soon the device has received the Package URI it performs the download at the next practical opportunity.
Can be direct link to a single software component or link to archive file, executable, or manifest, used by the Device to determine, then access to the software component(s). This resource to be used when it is single block of delivery.
]]>				</Description>
			</Item>

			<Item ID="4">
				<Name>Install</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Installs software from the package either stored in Package resource, or, downloaded from the Package URI. This Resource is only executable when the value of the State Resource is DELIVERED.]]></Description>
			</Item>
			<Item ID="5">
				<Name>Checkpoint</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Objlnk</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Link to a "Checkpoint" object which allows to specify conditions/dependencies for a software update. E.g. power connected, sufficient memory, target system.]]></Description>
			</Item>
			<Item ID="6">
				<Name>Uninstall</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Uninstalls the software package.
This executable resource may have one argument.
If used with no argument or argument is 0, the Package is removed i from the Device. If the argument is 1 ("ForUpdate"), the Client MUST prepare itself for receiving a Package used to upgrade the Software already in place. Update State is set back to INITIAL state.
]]>				</Description>
			</Item>
			<Item ID="7">
				<Name>Update State</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..4</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates current state with respect to this software update. This value is set by the LwM2M Client.
0: INITIAL
Before downloading.
(see *******)
1: DOWNLOAD STARTED
The downloading process has started and is on-going.
(see *******)
2: DOWNLOADED
The package has been completely downloaded 
(see *******)
3: DELIVERED
In that state, the package has been correctly downloaded and is ready to be installed. 
(see *******)
If executing the Install Resource failed, the state remains at DELIVERED.
If executing the Install Resource was successful, the state changes from DELIVERED to INSTALLED.
After executing the UnInstall Resource, the state changes to INITIAL.
4: INSTALLED
In that state the software is correctly installed and can be activated or deactivated according to the Activation State Machine.
(see *******)
]]>				</Description>
			</Item>
			<Item ID="8">
				<Name>Update Supported Objects</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[If this value is true, the LwM2M Client MUST inform the registered LwM2M Servers of Objects and Object Instances parameter by sending an Update or Registration message after the software update operation at the next practical opportunity if supported Objects in the LwM2M Client have changed, in order for the LwM2M Servers to promptly manage newly installed Objects. 
If false, Objects and Object Instances parameter MUST be reported at the next periodic Update message.
The default value is false.
]]>				</Description>
			</Item>
			<Item ID="9">
				<Name>Update Result</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..200</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Contains the result of downloading or installing/uninstalling the software
0: Initial value. Prior to download any new package in the Device, Update Result MUST be reset to this initial value. One side effect of executing the Uninstall resource is to reset Update Result to this initial value "0".
1: Downloading. The package downloading process is on-going.
2: Software successfully installed.
3: Successfully Downloaded and package integrity verified
(( 4-49, for expansion, of other scenarios))
50: Not enough storage for the new software package.
51: Out of memory during downloading process.
52: Connection lost during downloading process.
53: Package integrity check failure.
54: Unsupported package type.
56: Invalid URI
57: Device defined update error
58: Software installation failure
59: Uninstallation Failure during forUpdate(arg=0)
60-200 : (for expansion, selection to be in blocks depending on new introduction of features)
This Resource MAY be reported by sending Observe operation.
]]>				</Description>
			</Item>
			<Item ID="10">
				<Name>Activate</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This action activates the software previously successfully installed (the Package Installation State Machine is currently in the INSTALLED state)]]></Description>
			</Item>
			<Item ID="11">
				<Name>Deactivate</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This action deactivates softwareif the Package Installation State Machine is currently in the INSTALLED state.]]></Description>
			</Item>
			<Item ID="12">
				<Name>Activation State</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates the current activation state of this software:
0: DISABLED
Activation State is DISABLED if the Software Activation State Machine is in the INACTIVE state or not alive.
1: ENABLED
Activation State is ENABLED only if the Software Activation State Machine is in the ACTIVE state
]]>				</Description>
			</Item>
			<Item ID="13">
				<Name>Package Settings</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Objlnk</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Link to "Package Settings" object which allows to modify at any time software configuration settings. This is an application specific object. 
Note: OMA might provide a template for a Package Settings object in a future release of this specification.
]]>				</Description>
			</Item>
			<Item ID="14">
				<Name>User Name</Name>
				<Operations>W</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..255 bytes</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[User Name for access to SW Update Package in pull mode.
Key based mechanism can alternatively use for talking to the component server instead of user name and password combination.
]]>				</Description>
			</Item>
			<Item ID="15">
				<Name>Password</Name>
				<Operations>W</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..255 bytes</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Password for access to SW Update Package in pull mode.]]></Description>
			</Item>
			<Item ID="16">
				<Name>Status Reason</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Contains the status of the actions done by the client on the SW Component(s) referred by the present SW Update Package. The status is defined in Appendix B.]]></Description>
			</Item>
			<Item ID="17">
				<Name>Software Component Link</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Objlnk</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Reference to SW Components downloaded and installed in scope of the present SW Update Package Note: When resource 17 objlink exist, resources 2, 3 and 12 in this table are ignored.]]></Description>
			</Item>
			<Item ID="18">
				<Name>Software Component tree length</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Software Component tree length indicates the number of instances existing for this software package in the Software Component Object.]]></Description>
			</Item>
		</Resources>
		<Description2><![CDATA[]]></Description2>
	</Object>
</LWM2M>
