<?xml version="1.0" encoding="UTF-8"?>

<!--
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-XML_LWM2M_EventLog-V2_0-20230131-A
   Path: https://openmobilealliance.org/release/LwM2M_EventLog/

OMNA LwM2M Registry
   File: 20.xml
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry

NORMATIVE INFORMATION

  Information about this file can be found in the latest revision of

    OMA-TS-LwM2M_EventLog-V2_0

  This is available at https://www.openmobilealliance.org/release/LwM2M_EventLog/

  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

LEGAL DISCLAIMER

  Copyright 2023 Open Mobile Alliance.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

  1. Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
  3. Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived
  from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.

  The above license is used as a license under copyright only.  Please
  reference the OMA IPR Policy for patent licensing terms:
  https://www.omaspecworks.org/about/intellectual-property-rights/

-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">

	<Object ObjectType="MODefinition">
		<Name>Event Log</Name>
		<Description1><![CDATA[The Event Log Object is a multiple Instances Object defined for logging data in a straightforward and generic way.
The Resources of that Object are based on the OMA LwM2M set of reusable Resources dedicated to logging event activity.
]]></Description1>
		<ObjectID>20</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:20:2.0</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>2.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="4010"><Name>LogClass</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[ Define the Log Event Class: 0: generic (default)  1: system   2: security  3: event   4: trace   5: panic   6: charging [7-99]: reserved [100-255]: vendor specific ]]></Description>
			</Item>
			<Item ID="4011"><Name>LogStart</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Actions:
a) Start data collection(DC)
b) LogStatus is set to 0 (running)
c) DC is emptied (default) or extended according arg'0' value 
Arguments definitions are described in the table below.]]></Description>
			</Item>
			<Item ID="4012"><Name>LogStop</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Actions: a) Stop data collection(DC) b)  1st LSB of LogStatus is set to "1"(stopped) c) DC is kept (default) or emptied according arg'0' value Arguments definitions are described in the table below.  ]]></Description>
			</Item>
			<Item ID="4013"><Name>LogStatus</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>8-Bits</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Data Collection process status: Each bit of this Resource Instance value defines specific status: 1st LSB 0=running, 1=stopped 2nd LSB 1=LogData contains Valid Data 0=LogData doesn't contain Valid Data 3rd LSB 1=Error occurred during Data Collection 0=No error [4th -7th ] LSB:reserved 8th LSB: vendor specific.]]></Description>
			</Item>
			<Item ID="4014"><Name>LogData</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Read Access on that Resource returns the Data Collection associated to the current Object Instance.  ]]></Description>
			</Item>
			<Item ID="4015"><Name>LogDataFormat</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[ when set by the Server, this Resource indicates to the Client, what is the Server preferred data format to use when the LogData Resource is returned
. when retrieved by the Server, this Resource indicates which specific data format is used when the LogData Resource is returned to the Server 
0  or Resource not present : no specific data format (sequence of bytes)
1 : OMA-LwM2M TLV format
2 : OMA-LwM2M JSON format
3:  OMA-LwM2M CBOR format
[4..99] reserved
[100..255] vendor specific data format
 ]]></Description>
			</Item></Resources>
		<Description2 />
	</Object>
</LWM2M>
