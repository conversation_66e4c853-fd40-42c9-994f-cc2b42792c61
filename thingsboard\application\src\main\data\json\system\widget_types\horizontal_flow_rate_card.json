{"fqn": "horizontal_flow_rate_card", "name": "Horizontal flow rate card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_flow_rate_card.svg", "description": "Displays the latest flow rate telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "#container {\n    overflow: auto;\n}\n\n.tbDatasource-container {\n    margin: 5px;\n    padding: 8px;\n}\n\n.tbDatasource-title {\n    font-size: 1.200rem;\n    font-weight: 500;\n    padding-bottom: 10px;\n}\n\n.tbDatasource-table {\n    width: 100%;\n    box-shadow: 0 0 10px #ccc;\n    border-collapse: collapse;\n    white-space: nowrap;\n    font-size: 1.000rem;\n    color: #757575;\n}\n\n.tbDatasource-table td {\n    position: relative;\n    border-top: 1px solid rgba(0, 0, 0, 0.12);\n    border-bottom: 1px solid rgba(0, 0, 0, 0.12);\n    padding: 0px 18px;\n    box-sizing: border-box;\n}", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'flowRate', label: 'Flow rate', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Flow rate\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"horizontal\",\"autoScale\":true,\"showLabel\":true,\"labelFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"#000000DE\",\"rangeList\":null,\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:hydro-power\",\"iconColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#305AD7\"},{\"from\":10,\"to\":30,\"color\":\"#3FA71A\"},{\"from\":30,\"to\":50,\"color\":\"#F36900\"},{\"from\":50,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#305AD7\"},{\"from\":10,\"to\":30,\"color\":\"#3FA71A\"},{\"from\":30,\"to\":50,\"color\":\"#F36900\"},{\"from\":50,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Horizontal flow rate card\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"units\":\"m³/hr\",\"decimals\":0,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"showTitleIcon\":false,\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\"}"}, "tags": ["liquid", "fluid", "flow rate", "fluid dynamics", "velocity", "mass flow", "volume flow"], "resources": [{"link": "/api/images/system/horizontal_flow_rate_card.svg", "title": "horizontal_flow_rate_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_flow_rate_card.svg", "publicResourceKey": "AV3yD6GSFVM0eO3jBh8PeV9zTg3nlUAP", "mediaType": "image/svg+xml", "data": "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", "public": true}]}