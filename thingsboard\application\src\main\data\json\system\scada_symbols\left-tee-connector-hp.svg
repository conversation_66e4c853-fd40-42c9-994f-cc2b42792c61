<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="200" height="200" fill="none" version="1.1" viewBox="0 0 200 200"><tb:metadata xmlns=""><![CDATA[{
  "title": "HP Left tee connector",
  "description": "Left tee connector",
  "widgetSizeX": 1,
  "widgetSizeY": 1,
  "stateRenderFunction": "const {\n    flowAnimationWidth: lineWidth,\n    flowAnimationColor: lineColor,\n    flowStyleDash: dashWidth,\n    flowStyleGap: dashGap,\n    flowDashCap: dashCap\n} = ctx.properties;\n\nconst leftLine = \"M0 100H97\";\nconst leftLineReversed = \"M 97,100 H 0\";\nconst topLine = \"M100 100L100 0\";\nconst topLineReversed = \"M 100,0 V 100\";\nconst bottomLine = \"M 100,200 V 100\";\nconst bottomLineReversed = \"M 100,100 V 200\";\n\nprepareFlowAnimation('left', leftLine, leftLineReversed);\nprepareFlowAnimation('top', topLine, topLineReversed);\nprepareFlowAnimation('bottom', bottomLine, bottomLineReversed);\n\nfunction prepareFlowAnimation(prefix, line, reversedLine) {\n    const flowAnimation = ctx.values[prefix + 'Flow'];\n    const flowDirection = ctx.values[prefix + 'FlowDirection'];\n    const flowAnimationSpeed = ctx.values[prefix + 'FlowAnimationSpeed'];\n\n    const animation = ctx.tags[prefix + 'Line'][0];\n    const duration = 1 / flowAnimationSpeed;\n    \n    let animateFlow = ctx.api.connectorAnimation(animation);\n    \n    if (flowAnimation) {\n        if (!animateFlow) {\n            animateFlow = ctx.api.connectorAnimate(animation, line, reversedLine).flowAppearance(lineWidth, lineColor, dashCap, dashWidth, dashGap).duration(duration).direction(flowDirection).play();\n        } else {\n            animateFlow.duration(duration).direction(flowDirection).play();\n        }\n    } else {\n        if (animateFlow) {\n            animateFlow.finish();\n        }\n    }\n}",
  "tags": [
    {
      "tag": "line",
      "stateRenderFunction": "element.stroke(ctx.properties.lineColor);\nelement.attr({'stroke-width': ctx.properties.mainLineSize});",
      "actions": null
    },
    {
      "tag": "line-color",
      "stateRenderFunction": "element.fill(ctx.properties.lineColor);\nelement.stroke(ctx.properties.lineColor);",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "leftFlow",
      "name": "{i18n:scada.symbol.flow-animation}",
      "hint": "{i18n:scada.symbol.flow-animation-hint}",
      "group": "{i18n:scada.symbol.left-connector}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.present}",
      "falseLabel": "{i18n:scada.symbol.absent}",
      "stateLabel": "{i18n:scada.symbol.flow-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "leftFlowDirection",
      "name": "{i18n:scada.symbol.arrow-direction}",
      "hint": "{i18n:scada.symbol.arrow-direction-hint}",
      "group": "{i18n:scada.symbol.left-connector}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.forward}",
      "falseLabel": "{i18n:scada.symbol.reverse}",
      "stateLabel": "{i18n:scada.symbol.forward}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": true,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "leftFlowAnimationSpeed",
      "name": "{i18n:scada.symbol.flow-animation-speed}",
      "hint": "{i18n:scada.symbol.flow-animation-speed-hint}",
      "group": "{i18n:scada.symbol.left-connector}",
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": 1,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "topFlow",
      "name": "{i18n:scada.symbol.flow-animation}",
      "hint": "{i18n:scada.symbol.flow-animation-hint}",
      "group": "{i18n:scada.symbol.top-connector}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.present}",
      "falseLabel": "{i18n:scada.symbol.absent}",
      "stateLabel": "{i18n:scada.symbol.fluid-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "topFlowDirection",
      "name": "{i18n:scada.symbol.arrow-direction}",
      "hint": "{i18n:scada.symbol.arrow-direction-hint}",
      "group": "{i18n:scada.symbol.top-connector}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.forward}",
      "falseLabel": "{i18n:scada.symbol.reverse}",
      "stateLabel": "{i18n:scada.symbol.forward}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": true,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "topFlowAnimationSpeed",
      "name": "{i18n:scada.symbol.flow-animation-speed}",
      "hint": "{i18n:scada.symbol.flow-animation-speed-hint}",
      "group": "{i18n:scada.symbol.top-connector}",
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": 1,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "bottomFlow",
      "name": "{i18n:scada.symbol.flow-animation}",
      "hint": "{i18n:scada.symbol.flow-animation-hint}",
      "group": "{i18n:scada.symbol.bottom-connector}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.present}",
      "falseLabel": "{i18n:scada.symbol.absent}",
      "stateLabel": "{i18n:scada.symbol.flow-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "bottomFlowDirection",
      "name": "{i18n:scada.symbol.arrow-direction}",
      "hint": "{i18n:scada.symbol.arrow-direction-hint}",
      "group": "{i18n:scada.symbol.bottom-connector}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.forward}",
      "falseLabel": "{i18n:scada.symbol.reverse}",
      "stateLabel": "{i18n:scada.symbol.forward}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": true,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "bottomFlowAnimationSpeed",
      "name": "{i18n:scada.symbol.flow-animation-speed}",
      "hint": "{i18n:scada.symbol.flow-animation-speed-hint}",
      "group": "{i18n:scada.symbol.bottom-connector}",
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": 1,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    }
  ],
  "properties": [
    {
      "id": "mainLineSize",
      "name": "{i18n:scada.symbol.line}",
      "type": "number",
      "default": 6,
      "required": true,
      "divider": false,
      "fieldSuffix": "px",
      "min": 0,
      "max": 99,
      "step": 1,
      "disabled": false,
      "visible": true
    },
    {
      "id": "lineColor",
      "name": "{i18n:scada.symbol.line}",
      "type": "color",
      "default": "#1A1A1A",
      "disabled": false,
      "visible": true
    },
    {
      "id": "flowAnimationWidth",
      "name": "{i18n:scada.symbol.flow-line}",
      "group": "{i18n:scada.symbol.flow}",
      "type": "number",
      "default": 4,
      "divider": false,
      "fieldSuffix": "px",
      "min": 1,
      "step": 1,
      "disabled": false,
      "visible": true
    },
    {
      "id": "flowAnimationColor",
      "name": "{i18n:scada.symbol.flow-line}",
      "group": "{i18n:scada.symbol.flow}",
      "type": "color",
      "default": "#C8DFF7",
      "disabled": false,
      "visible": true
    },
    {
      "id": "flowStyleDash",
      "name": "{i18n:scada.symbol.flow-line-style}",
      "hint": "{i18n:scada.symbol.flow-style-hint}",
      "group": "{i18n:scada.symbol.flow}",
      "type": "number",
      "default": 10,
      "required": true,
      "subLabel": "{i18n:scada.symbol.dash}",
      "divider": false,
      "fieldSuffix": "px",
      "min": 0,
      "step": 1,
      "disabled": false,
      "visible": true
    },
    {
      "id": "flowStyleGap",
      "name": "{i18n:scada.symbol.flow-line-style}",
      "hint": "{i18n:scada.symbol.flow-style-hint}",
      "group": "{i18n:scada.symbol.flow}",
      "type": "number",
      "default": 10,
      "subLabel": "{i18n:scada.symbol.gap}",
      "fieldSuffix": "px",
      "min": 0,
      "step": 1,
      "disabled": false,
      "visible": true
    },
    {
      "id": "flowDashCap",
      "name": "{i18n:scada.symbol.flow-dash-cap}",
      "group": "{i18n:scada.symbol.flow}",
      "type": "select",
      "default": "butt",
      "items": [
        {
          "value": "butt",
          "label": "{i18n:scada.symbol.dash-cap-butt}"
        },
        {
          "value": "round",
          "label": "{i18n:scada.symbol.dash-cap-round}"
        },
        {
          "value": "square",
          "label": "{i18n:scada.symbol.dash-cap-square}"
        }
      ],
      "disabled": false,
      "visible": true
    }
  ]
}]]></tb:metadata>
<path d="M100 0V200" stroke="#1A1A1A" stroke-width="6" id="path6" tb:tag="line"/><path d="M100 113V87C100 87 100 100 86 100C100 100 100 113 100 113Z" fill="#1A1A1A" stroke="#1A1A1A" stroke-width="2" id="path8" tb:tag="line-color"/><path d="M0 100H85C93.2843 100 100 93.2843 100 85V0" stroke="#1A1A1A" stroke-width="6" id="path4" tb:tag="line"/><path d="M0 100H85C93.2843 100 100 106.716 100 115V200" stroke="#1A1A1A" stroke-width="6" id="path2" tb:tag="line"/><g tb:tag="animationGroup"><g tb:tag="leftLine"/><g tb:tag="topLine"> </g><g tb:tag="bottomLine"> </g></g>
</svg>