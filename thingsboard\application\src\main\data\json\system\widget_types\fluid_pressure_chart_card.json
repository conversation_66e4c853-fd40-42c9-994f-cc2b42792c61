{"fqn": "fluid_pressure_chart_card", "name": "Pressure chart card", "deprecated": false, "image": "tb-image;/api/images/system/pressure_chart_card.svg", "description": "Displays fluid pressure data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pressure', label: 'Pressure', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'bar', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'pressure', 'bar', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 15 - 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"bar\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":5,\"color\":\"#305AD7\"},{\"from\":5,\"to\":10,\"color\":\"#3FA71A\"},{\"from\":10,\"to\":15,\"color\":\"#F36900\"},{\"from\":15,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 15 - 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"bar\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"bar\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Pressure\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"compress\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"margin\":\"0px\"}"}, "tags": ["fluid pressure", "liquid pressure", "pump pressure", "hydraulic", "pump", "compressibility", "compressive stress", "pipe", "pipeline"], "resources": [{"link": "/api/images/system/pressure_chart_card.svg", "title": "pressure_chart_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "pressure_chart_card.svg", "publicResourceKey": "p0puRbdd6NCt8Uw2dy2FtnBZ1oMhJIcn", "mediaType": "image/svg+xml", "data": "PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMTYwIiBmaWxsPSJub25lIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE2MCIgZmlsbD0iI2ZmZiIgcng9IjQiLz48cmVjdCB3aWR0aD0iMTk5IiBoZWlnaHQ9IjE1OSIgeD0iLjUiIHk9Ii41IiBzdHJva2U9IiMwMDAiIHN0cm9rZS1vcGFjaXR5PSIuMSIgcng9IjMuNSIvPjxnIGNsaXAtcGF0aD0idXJsKCNhKSI+PHBhdGggZmlsbD0iIzAwMCIgZmlsbC1vcGFjaXR5PSIuOSIgZD0iTTE5LjMgMjYuOHYtMi4xbC0xIDEtMS4xLTFMMjAgMjJsMi44IDIuOC0xIDEtMS0xdjJoLTEuNVptLTQuNi01LjV2LTEuNGgxMC42djEuNEgxNC43Wm0wLTJ2LTEuNGgxMC42djEuNEgxNC43Wm01LjMtMi0yLjgtMi44IDEtMSAxIDF2LTJoMS41djJsMS0xIDEuMSAxLTIuOCAyLjhaIi8+PC9nPjxwYXRoIGZpbGw9IiMwMDAiIGZpbGwtb3BhY2l0eT0iLjkiIGQ9Ik0zNi44IDIxLjNoLTIuNnYtMS40aDIuNmMuNCAwIC44IDAgMS0uMi40LS4xLjYtLjMuNy0uNmwuMi0uOWMwLS4zIDAtLjYtLjItLjktLjEtLjItLjMtLjUtLjYtLjZhMiAyIDAgMCAwLTEuMS0uM2gtMlYyNUgzM1YxNWgzLjhjLjggMCAxLjQuMiAyIC41LjUuMiAxIC42IDEuMiAxIC4zLjYuNCAxIC40IDEuNyAwIC42LS4xIDEuMi0uNCAxLjdhMyAzIDAgMCAxLTEuMiAxYy0uNi4zLTEuMi40LTIgLjRabTYuOS0yLjN2Nkg0MnYtNy40aDEuNlYxOVptMi4zLTEuNFYxOWEzLjIgMy4yIDAgMCAwLTEuNSAwIDEuNCAxLjQgMCAwIDAtLjggMWwtLjIuNmgtLjRjMC0uNCAwLS44LjItMS4yIDAtLjQuMi0uOC40LTEgLjItLjQuNC0uNi43LS44YTEuOSAxLjkgMCAwIDEgMS4zLS4yaC4zWm00LjQgNy41Yy0uNiAwLTEgMC0xLjUtLjJhMy4zIDMuMyAwIDAgMS0xLjgtMiA0IDQgMCAwIDEtLjMtMS4zdi0uM2MwLS42IDAtMS4xLjMtMS42LjEtLjUuNC0uOS43LTEuMmEzIDMgMCAwIDEgMS0uOGwxLjQtLjJjLjUgMCAxIDAgMS40LjJsMSAuOC41IDEuMWMuMi41LjIgMSAuMiAxLjV2LjdoLTUuN3YtMS4yaDQuMXYtLjFjMC0uMyAwLS42LS4yLS45IDAtLjItLjItLjQtLjUtLjZsLS44LS4yYy0uMyAwLS42IDAtLjguMi0uMiAwLS40LjItLjUuNS0uMS4yLS4zLjQtLjMuN2wtLjEgMXYxLjNsLjUuNy42LjUuOS4xYTIuMyAyLjMgMCAwIDAgMS44LS45bC45LjktLjYuNi0xIC41Yy0uMy4yLS44LjItMS4yLjJaTTU5IDIzbC0uMS0uNGExIDEgMCAwIDAtLjUtLjRsLTEtLjMtMS0uMy0xLS41LS41LS42LS4xLS44YzAtLjMgMC0uNi4yLS45IDAtLjIuMy0uNS41LS43bDEtLjUgMS4xLS4xYy42IDAgMS4yIDAgMS42LjMuNC4yLjguNSAxIC44LjMuNC40LjguNCAxLjJoLTEuN2wtLjEtLjVhMSAxIDAgMCAwLS41LS41bC0uNy0uMS0uNy4xYTEgMSAwIDAgMC0uNC40LjkuOSAwIDAgMCAwIC44bC4yLjIuNC4yLjguMiAxLjQuNWMuNS4yLjguNCAxIC43LjIuMy4zLjcuMyAxLjFhMiAyIDAgMCAxLS44IDEuNiAzIDMgMCAwIDEtMSAuNWwtMS4yLjFjLS42IDAtMS4yIDAtMS43LS4zLS40LS4zLS44LS42LTEtMS0uMy0uMy0uNC0uNy0uNC0xLjFoMS42YzAgLjMuMS41LjMuN2wuNS40LjcuMS44LS4xYy4yIDAgLjMtLjIuNC0uMy4yLS4yLjItLjMuMi0uNVptNy41IDBjMC0uMiAwLS4zLS4yLS40YTEgMSAwIDAgMC0uNC0uNGwtMS0uMy0xLjEtLjMtLjktLjVhMSAxIDAgMCAxLS41LS42bC0uMi0uOGMwLS4zIDAtLjYuMi0uOWwuNi0uNy45LS41IDEuMi0uMWMuNiAwIDEuMSAwIDEuNi4zbDEgLjguMyAxLjJoLTEuNmMwLS4yIDAtLjQtLjItLjVhMSAxIDAgMCAwLS40LS41bC0uNy0uMS0uNy4xYTEgMSAwIDAgMC0uNS40LjkuOSAwIDAgMCAwIC44bC4yLjIuNS4yLjcuMiAxLjUuNSAxIC43Yy4yLjMuMy43LjMgMS4xYTIgMiAwIDAgMS0uOCAxLjYgMyAzIDAgMCAxLTEgLjVsLTEuMi4xYy0uNyAwLTEuMyAwLTEuNy0uMy0uNS0uMy0uOC0uNi0xLTEtLjMtLjMtLjQtLjctLjQtMS4xaDEuNmMwIC4zIDAgLjUuMi43bC42LjQuNy4xLjctLjFjLjIgMCAuNC0uMi41LS4zbC4yLS41Wm03LjcuM3YtNS43SDc2VjI1aC0xLjZ2LTEuN1ptLjMtMS42aC41YzAgLjUgMCAxLS4yIDEuNCAwIC40LS4yLjgtLjUgMS0uMi40LS41LjYtLjguOGEzIDMgMCAwIDEtMS4zLjIgMyAzIDAgMCAxLTEtLjEgMiAyIDAgMCAxLS44LS41IDIgMiAwIDAgMS0uNS0uOSA0IDQgMCAwIDEtLjItMS4ydi00LjhoMS43djUuNWwuMy40LjQuMi41LjFjLjUgMCAuOCAwIDEtLjMuNC0uMi42LS40LjctLjdsLjItMVptNS0yLjd2NmgtMS43di03LjRoMS42VjE5Wm0yLjItMS40VjE5YTMuMiAzLjIgMCAwIDAtMS40IDAgMS40IDEuNCAwIDAgMC0uOSAxbC0uMS42aC0uNGMwLS40IDAtLjguMi0xLjIgMC0uNC4yLS44LjQtMSAuMi0uNC40LS42LjctLjhhMS45IDEuOSAwIDAgMSAxLjMtLjJoLjJabTQuNCA3LjVjLS41IDAtMSAwLTEuNC0uMmEzLjMgMy4zIDAgMCAxLTEuOS0yIDQgNCAwIDAgMS0uMi0xLjN2LS4zYzAtLjYgMC0xLjEuMi0xLjZzLjQtLjkuNy0xLjJhMyAzIDAgMCAxIDEuMS0uOGwxLjMtLjJjLjYgMCAxIDAgMS40LjJsMSAuOGMuMy4zLjUuNy42IDEuMWwuMiAxLjV2LjdoLTUuN3YtMS4yaDR2LS4xbC0uMS0uOS0uNS0uNi0uOS0uMmMtLjIgMC0uNSAwLS43LjItLjIgMC0uNC4yLS41LjVsLS40Ljd2Mi4zbC41LjcuNi41LjguMWEyLjMgMi4zIDAgMCAwIDEuOS0uOWwuOS45LS43LjZhMiAyIDAgMCAxLS45LjVjLS40LjItLjguMi0xLjMuMloiLz48cGF0aCBmaWxsPSIjRjc3NDEwIiBkPSJNODMuNSA1OC4zVjg0aC00LjJWNjMuM0w3MyA2NS41VjYybDEwLTMuN2guNVptMjYuMSAyMi4zVjg0SDkyLjV2LTIuOWw4LjMtOWMxLTEuMSAxLjctMiAyLjItMi44LjUtLjcuOS0xLjQgMS0yYTUuMiA1LjIgMCAwIDAgMC0zLjljLS4zLS42LS44LTEtMS4zLTEuNC0uNi0uNC0xLjMtLjYtMi4xLS42LTEgMC0xLjguMi0yLjQuNi0uNy40LTEuMiAxLTEuNSAxLjctLjMuNy0uNSAxLjYtLjUgMi41SDkyYzAtMS41LjMtMi45IDEtNGE3LjUgNy41IDAgMCAxIDMtMyA5LjIgOS4yIDAgMCAxIDQuNi0xLjFjMS43IDAgMy4yLjIgNC40LjggMS4yLjYgMiAxLjQgMi43IDIuNWE3LjYgNy42IDAgMCAxIC42IDYuMWwtMS4xIDIuM2E0MS41IDQxLjUgMCAwIDEtMy44IDQuN2wtNS42IDYuMWgxMS44Wm01LjQtNy4xaDEuMlY4NEgxMTVWNzMuNVptNi4yIDYuN3YuMmMwIC41IDAgMS0uMiAxLjUgMCAuNC0uMy44LS41IDEuMmwtMSAuOC0xLjIuMmEzIDMgMCAwIDEtMS4yLS4ybC0uOS0uNy0uNi0xLjEtLjMtMS41VjgwbC4zLTEuNS42LTFjLjMtLjQuNi0uNiAxLS44LjMtLjIuNy0uMiAxLjEtLjIuNSAwIDEgMCAxLjMuMmwuOS44LjUgMS4yYy4yLjUuMiAxIC4yIDEuNVptLTEuMi4ydi0uMmwtLjEtMS0uNC0uOWExLjYgMS42IDAgMCAwLTEuNS0uOCAxLjggMS44IDAgMCAwLTEuNC42bC0uNC43LS4yLjd2MS42YzAgLjQuMi43LjMgMWwuNy43Yy4zLjIuNi4zIDEgLjNsMS0uMi41LS42LjMtLjkuMi0xWm03LjQgMi4zVjc5YzAtLjMgMC0uNS0uMi0uN2wtLjUtLjVjLS4yLS4yLS41LS4yLS45LS4yYTIgMiAwIDAgMC0uOC4xbC0uNi41YTEgMSAwIDAgMC0uMi42SDEyM2MwLS4zIDAtLjYuMi0uOC4xLS4zLjMtLjUuNi0uN2EzIDMgMCAwIDEgLjktLjVjLjQtLjIuOC0uMiAxLjItLjIuNSAwIDEgMCAxLjQuMi40LjIuOC41IDEgLjkuMi4zLjQuOC40IDEuM3Y0LjNsLjIuN3YuMWgtMS4zbC0uMi0uNnYtLjdabS4yLTMuMnYuOWgtMi4zbC0uNi4zLS41LjUtLjEuNi4xLjYuNS41LjguMWEyIDIgMCAwIDAgMS43LS44bC4zLS43LjYuNi0uMy42YTMgMyAwIDAgMS0xLjQgMS4yYy0uMy4yLS43LjItMSAuMi0uNiAwLTEgMC0xLjQtLjMtLjQtLjItLjctLjQtLjktLjdhMiAyIDAgMCAxLS4zLTEuMmMwLS40IDAtLjcuMi0xbC43LS44IDEtLjQgMS40LS4yaDEuNVptNC41LTEuN1Y4NGgtMS4zdi03LjRoMS4zdjEuMlptMi4zLTEuMnYxLjFoLS42YTIgMiAwIDAgMC0uOC4xbC0uNS40LS40LjYtLjIuNy0uMy4ydi0xLjJsLjUtMWMuMi0uNC40LS42LjctLjhhMiAyIDAgMCAxIDEuNi0uMVoiLz48cGF0aCBmaWxsPSJ1cmwoI2IpIiBmaWxsLW9wYWNpdHk9Ii4zIiBkPSJNMTQ3LjQgNzEuOGg3LjJ2OC45bC0zLjYgMi42LTMuNi0yLjZ2LTlaIi8+PHBhdGggZmlsbD0iIzAwMCIgZmlsbC1vcGFjaXR5PSIuOSIgZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMTUwLjcgODMuOWMuMi4yLjQuMi42IDBsMy4zLTMuMi0uNy0uNy0xLjQgMS40Yy0uNC40LTEgLjEtMS0uNHYtMy45aC0xdjRjMCAuNC0uNi43LTEgLjNsLTEuNC0xLjQtLjcuNyAzLjMgMy4yWiIgY2xpcC1ydWxlPSJldmVub2RkIi8+PHBhdGggZmlsbD0iIzAwMCIgZmlsbC1vcGFjaXR5PSIuOSIgZD0iTTE2Mi42IDc5Ljh2MS4xaC0zLjF2LTEuMWgzWm01LjItNC40Vjg0aC0xLjR2LTYuOWwtMiAuN3YtMS4xbDMuMy0xLjNoLjFabTcuNSAwaC4ydjEuMmgtLjFjLS41IDAtMSAwLTEuMy4ybC0xIC42LS40IDFjLS4yLjQtLjIuOC0uMiAxLjJ2MS4zbC4xIDEgLjMuNi41LjRoMS4xbC41LS40LjItLjZhMi41IDIuNSAwIDAgMC0uMi0ybC0uNS0uMy0uNi0uMWMtLjMgMC0uNSAwLS43LjItLjMuMS0uNC4zLS42LjVsLS4yLjctLjUtLjJjMC0uNCAwLS43LjItMWwuNS0uNy44LS41LjktLjJjLjQgMCAuOC4xIDEgLjMuNC4xLjYuMy44LjZsLjUgMWEzLjcgMy43IDAgMCAxIDAgMi4xbC0uNiAxLS45LjYtMS4xLjJjLS41IDAtLjkgMC0xLjMtLjJsLS45LS44LS41LTFjLS4yLS40LS4yLS45LS4yLTEuM3YtLjZjMC0uNyAwLTEuMy4yLTEuOC4yLS42LjQtMS4xLjgtMS42LjMtLjQuOC0uOCAxLjMtMWE0IDQgMCAwIDEgMi0uNFptMy4zIDIuMlY3N2MwLS4zIDAtLjYuMi0uOS4xLS4yLjMtLjUuNi0uNi4zLS4yLjYtLjMgMS0uM3MuNy4xIDEgLjNsLjYuNi4yIDF2LjRjMCAuMyAwIC42LS4yLjhsLS42LjctMSAuMmEyIDIgMCAwIDEtMS0uMmMtLjMtLjItLjUtLjQtLjYtLjdsLS4yLS44Wm0xLS41di45bC4zLjMuNS4yYy4yIDAgLjMgMCAuNS0uMmwuMi0uMy4xLS40di0uOWwtLjQtLjRhLjguOCAwIDAgMC0uNC0uMWwtLjUuMS0uMi40LS4xLjRabTMuMSA1LjN2LS41YzAtLjMgMC0uNi4yLS45bC42LS42Yy4zLS4yLjYtLjIgMS0uMnMuNyAwIDEgLjJsLjYuNi4yIDF2LjRjMCAuMyAwIC42LS4yLjgtLjEuMy0uMy41LS42LjdsLTEgLjJhMiAyIDAgMCAxLTEtLjJsLS42LS43LS4yLS44Wm0xLS41di45bC40LjRoLjlsLjMtLjR2LTEuM2EuOC44IDAgMCAwLS44LS41bC0uNS4xLS4yLjQtLjEuNFptMS4yLTUuMi00LjIgNi43LS43LS41IDQuMS02LjYuOC40WiIvPjxnIGNsaXAtcGF0aD0idXJsKCNjKSI+PHBhdGggZmlsbD0iIzAwMCIgZmlsbC1vcGFjaXR5PSIuMSIgZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMTg4IDEyNUgxMnYtMWgxNzZ2MVoiIGNsaXAtcnVsZT0iZXZlbm9kZCIvPjxwYXRoIHN0cm9rZT0iIzAwMCIgc3Ryb2tlLW9wYWNpdHk9Ii45IiBzdHJva2Utd2lkdGg9IjEuNSIgZD0iTTEyIDEzMy44aDkuM2wxMy4zIDkuMkw0NSAxMTIuMmwyMS42IDIyLjYgMjMuNy0yMi42IDcuMiAxIDE0LjQgMjkuOGgxNC40bDEyLjQtMjYuN2g5LjNsOS0xNC4zIDEyLjQgNC4xIDE4LjUgMTAuMyIvPjwvZz48ZGVmcz48Y2xpcFBhdGggaWQ9ImEiPjxwYXRoIGZpbGw9IiNmZmYiIGQ9Ik0xMiAxMmgxNnYxNkgxMnoiLz48L2NsaXBQYXRoPjxjbGlwUGF0aCBpZD0iYyI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTEyIDkwaDE3NnY2MEgxMnoiLz48L2NsaXBQYXRoPjxsaW5lYXJHcmFkaWVudCBpZD0iYiIgeDE9IjE1MC43IiB4Mj0iMTUwLjciIHkxPSI4NCIgeTI9IjcxLjgiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj48c3RvcCBvZmZzZXQ9IjAiIHN0eWxlPSJzdG9wLWNvbG9yOiNmZmY7c3RvcC1vcGFjaXR5OjEiLz48c3RvcCBvZmZzZXQ9IjEiIHN0eWxlPSJzdG9wLWNvbG9yOiNmZmY7c3RvcC1vcGFjaXR5OjAiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48L3N2Zz4=", "public": true}]}