{"fqn": "rotational_speed_progress_bar_with_background", "name": "Rotational speed progress bar with background", "deprecated": false, "image": "tb-image;/api/images/system/rotational_speed_progress_bar_with_background.svg", "description": "Displays rotational speed reading as a horizontal progress bar with background. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'rotationalSpeed', label: 'Rotational speed', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Rotational speed\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 4000 - 2000;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 4000) {\\n\\tvalue = 4000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":500,\"color\":\"#2B54CE\"},{\"from\":500,\"to\":1500,\"color\":\"#3B911C\"},{\"from\":1500,\"to\":3000,\"color\":\"#F89E0D\"},{\"from\":3000,\"to\":null,\"color\":\"#F04022\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":5000,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/rotational_speed_progress_bar_background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":500,\"color\":\"#2B54CE\"},{\"from\":500,\"to\":1500,\"color\":\"#3B911C\"},{\"from\":1500,\"to\":3000,\"color\":\"#F89E0D\"},{\"from\":3000,\"to\":null,\"color\":\"#F04022\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Rotational speed\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"RPM\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["angular speed", "spin rate", "revolutions", "rotational frequency", "spin motion"], "resources": [{"link": "/api/images/system/rotational_speed_progress_bar_background.png", "title": "rotational_speed_progress_bar_background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "rotational_speed_progress_bar_background.png", "publicResourceKey": "I1DleRviOv5Dec6C817PeQoSe0Hx3XvP", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/rotational_speed_progress_bar_with_background.svg", "title": "rotational_speed_progress_bar_with_background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "rotational_speed_progress_bar_with_background.svg", "publicResourceKey": "0qyv5MlMgqFZBnns14OUDRCBMpvRuAQN", "mediaType": "image/svg+xml", "data": "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", "public": true}]}