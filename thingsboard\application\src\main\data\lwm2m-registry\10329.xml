<?xml version="1.0" encoding="utf-8"?>
<!--BSD 3-Clause License

Copyright (c) 2019, Cloudminds
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>EC<PERSON>L, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON>
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>Robot Control</Name>
    <Description1><![CDATA[This LWM2M Object includes all operations on robots.]]></Description1>
    <ObjectID>10329</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10329</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Single</MultipleInstances>
    <Mandatory>Mandatory</Mandatory>
    <Resources>
		<Item ID="1">
			<Name>Collision Detection</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>Boolean</Type>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[If Collision Detection is enabled or not, 
				the setting of which is a Boolean value (1,0) where 1 is Enabled and 0 is Disabled.]]></Description>
		</Item>
		<Item ID="2">
			<Name>Drop Detection</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>Boolean</Type>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[If Drop Detection is enabled or not,
				the setting of which is a Boolean value (1,0) where 1 is Enabled and 0 is Disabled.]]></Description>
		</Item>
		<Item ID="3">
			<Name>Automatic Navigation</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>Boolean</Type>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[If Automatic Navigation is enabled or not,
				the setting of which is a Boolean value (1,0) where 1 is Enabled and 0 is Disabled.]]></Description>
		</Item>
		<Item ID="100">
			<Name>Robot Shutdown</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Shut down the robot.]]></Description>
		</Item>
		<Item ID="101">
			<Name>Robot Reboot</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Reboot the robot.]]></Description>
		</Item>
		<Item ID="102">
			<Name>Robot Reset</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Reset the robot to factory settings.]]></Description>
		</Item>
		<Item ID="103">
			<Name>Robot Wakeup </Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Let the robot to wake up.]]></Description>
		</Item>	
		<Item ID="104">
			<Name>Robot Sleep</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Let the robot to sleep.]]></Description>
		</Item>
		<Item ID="105">
			<Name>Robot Self-checking</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Let the robot to do self-checking.]]></Description>
		</Item>
		<Item ID="106">
			<Name>Emergency Braking</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Bark the robot emergently.]]></Description>
		</Item>
		<Item ID="107">
			<Name>Emergency Braking Release</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Release Emergency Braking for the robot.]]></Description>
		</Item>
		<Item ID="110">
			<Name>Action Execution</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Let the robot to execute a task, parameter: Action name|possible parameters if any.]]></Description>
		</Item>	
		<Item ID="111">
			<Name>Action List Upload</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Upload action list, parameter:select action list to be uploaed.]]></Description>
		</Item>
		<Item ID="112">
			<Name>Action List Download</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Download action list, parameter: 
				download path if present, else store in default directory with default name.]]></Description>
		</Item>
		<Item ID="113">
			<Name>Group Dancing Program Control</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Change playbill for group dancing, parameter:playbill.]]></Description>
		</Item>
		<Item ID="114">
			<Name>Navigation Map Upload</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Upload navigation map, parameter:select navigation map to be uploaded.]]></Description>
		</Item>		
		<Item ID="115">
			<Name>Group Dancing Program Control</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Change playbill for group dancing, parameter:playbill.]]></Description>
		</Item>	
		<Item ID="116">
			<Name>Navigation Map Download</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Download navigation map, parameter: 
				download path if present, else store in default directory with default name.]]></Description>
		</Item>
		<Item ID="117">
			<Name>Route list Execution</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Execute route list , parameter:if any.]]></Description>
		</Item>
		<Item ID="118">
			<Name>Route list Upload</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Upload route list, parameter:select route list to be uploaded.]]></Description>
		</Item>	
		<Item ID="119">
			<Name>Route list Download</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Download route list, parameter: 
				download path if present, else store in default directory with default name.]]></Description>
		</Item>
		<Item ID="120">
			<Name>Automatic Navigation Control</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Enable/Disable Automatic Navigation: Parameter: 1:Enable, 0:Disable]]></Description>
		</Item>
		<Item ID="121">
			<Name>Manual Navigation</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Manual Navigation: Parameter: route info.]]></Description>
		</Item>
		<Item ID="122">
			<Name>Moving to Charging Station</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Moving to Charging Station.]]></Description>
		</Item>
		<Item ID="123">
			<Name>Moving to Specified location</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Manual Navigation: Parameter: route info.]]></Description>
		</Item>
		<Item ID="124">
			<Name>Low Frequency Patrol Broadcasting</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Patrol broadcast in low frequency.]]></Description>
		</Item>
		<Item ID="125">
			<Name>Task Start</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Start a task, parameter:task name.]]></Description>
		</Item>
		<Item ID="126">
			<Name>Task Stop</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Stop a task, parameter:task name.]]></Description>
		</Item>
		<Item ID="127">
			<Name>Task Suspend</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Suspend a task, parameter:task name.]]></Description>
		</Item>
		<Item ID="128">
			<Name>Task Resume</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Resume a task, parameter:task name.]]></Description>
		</Item>
		<Item ID="129">
			<Name>Video Upload</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Upload a video, parameter:select video to be uploaded.]]></Description>
		</Item>	
		<Item ID="130">
			<Name>Picture Upload</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Upload picture, parameter:select picture to be uploaded.]]></Description>
		</Item>
		<Item ID="131">
			<Name>Default Language Switching</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Upload picture, parameter:Language name.]]></Description>
		</Item>
		<Item ID="132">
			<Name>Intonation Change</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Change intonation of the robot, parameter:Intonation name.]]></Description>
		</Item>
		<Item ID="133">
			<Name>Intonation Change</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Change intonation of the robot, parameter:Intonation name.]]></Description>
		</Item>
		<Item ID="134">
			<Name>Speaking with Action</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Robot speaks with specific action, parameter:key words|action name.]]></Description>
		</Item>
		<Item ID="135">
			<Name>Collision Detection Control</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Enable/Disable Collision Detection: Parameter: 1:Enable, 0:Disable]]></Description>
		</Item>
		<Item ID="136">
			<Name>Drop Detection Control</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Enable/Disable Drop Detection: Parameter: 1:Enable, 0:Disable]]></Description>
		</Item>
    </Resources>
    <Description2 />
  </Object>
</LWM2M>
