<?xml version="1.0" encoding="utf-8" ?>

<!-- 
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-XML_501-V1_0-20200604-A
   Type: xml
   Date: 2020-Jun-04

Public Reachable Information
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 501.xml

NORMATIVE INFORMATION
  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

LEGAL DISCLAIMER

Copyright 2020 Open Mobile Alliance. 

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

The above license is used as a license under copyright only.  Please
reference the OMA IPR Policy for patent licensing terms:
https://www.omaspecworks.org/about/intellectual-property-rights/
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
    <Object ObjectType="MODefinition">
        <Name>LTE-MTC Band Config</Name>
        <Description1>
            <![CDATA[This object is used to configure the cellular band used by the device as well as to change the band. It is intended to be used on LTE bands, as in NB-IoT and LTE Cat M1 frequency bands.]]>
        </Description1>
        <ObjectID>501</ObjectID>
        <ObjectURN>urn:oma:lwm2m:oma:501</ObjectURN>
        <LWM2MVersion>1.0</LWM2MVersion>
        <ObjectVersion>1.0</ObjectVersion>
        <MultipleInstances>Multiple</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Resources>
            <Item ID="1">
                <Name>EARFCN</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration>0..65535</RangeEnumeration>
                <Units></Units>
                <Description>
                    <![CDATA[E-UTRA Absolute Radio Frequency Channel Number (EARFCN), the carrier frequency of the device radio.]]>
                </Description>
            </Item>
            <Item ID="2">
                <Name>Current Frequency (Downlink)</Name>
                <Operations>R</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Float</Type>
                <RangeEnumeration />
                <Units>MHz</Units>
                <Description>
                    <![CDATA[The current frequency used in the current band in MHz (Downlink).]]>
                </Description>
            </Item>
            <Item ID="3">
                <Name>Current Frequency (Uplink)</Name>
                <Operations>R</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Float</Type>
                <RangeEnumeration />
                <Units>MHz</Units>
                <Description>
                    <![CDATA[The current frequency used in the current band in MHz (Uplink).]]>
                </Description>
            </Item>
            <Item ID="4">
                <Name>Prioritized Channels</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Multiple</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration />
                <Units></Units>
                <Description>
                    <![CDATA[Indicates a list of currently supported EARFCNs in priority order. Each Resource Instance has a value from the band list.]]>
                </Description>
            </Item>
            <Item ID="5750">
                <Name>Application Type</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>String</Type>
                <RangeEnumeration />
                <Units></Units>
                <Description>
                    <![CDATA[The application type of the sensor or actuator as a string depending on the use case.]]>
                </Description>
            </Item>
            <Item ID="5">
                <Name>Reset Timeout</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Float</Type>
                <RangeEnumeration></RangeEnumeration>
                <Units>s</Units>
                <Description>
                    <![CDATA[Time that the device will wait until reset once the Reset Resource is triggered.]]>
                </Description>
            </Item>
            <Item ID="6">
                <Name>Reset</Name>
                <Operations>E</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type></Type>
                <RangeEnumeration></RangeEnumeration>
                <Units></Units>
                <Description>
                    <![CDATA[Execuring this resource resets the radio interface of the device.]]>
                </Description>
            </Item>
            <Item ID="48">
                <Name>Vendor specific extensions</Name>
                <Operations>R</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Objlnk</Type>
                <RangeEnumeration></RangeEnumeration>
                <Units></Units>
                <Description>
                    <![CDATA[This resource links to a vendor specific object.]]>
                </Description>
            </Item>
        </Resources>
        <Description2 />
    </Object>
</LWM2M>
