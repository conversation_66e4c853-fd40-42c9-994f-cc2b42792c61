{"fqn": "horizontal_wind_speed_card_with_background", "name": "Horizontal wind speed card with background", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_wind_speed_card_with_background_system_widget_image.png", "description": "Displays the latest wind speed telemetry in a scalable horizontal layout with the background image.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'speed', label: 'Wind Speed', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Wind Speed\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 16 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 26) {\\n\\tvalue = 26;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:windsock\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0.2,\"color\":\"#6083EC\"},{\"from\":0.2,\"to\":3.4,\"color\":\"#5579E5\"},{\"from\":3.4,\"to\":8,\"color\":\"#4369DD\"},{\"from\":8,\"to\":10.8,\"color\":\"#2B54CE\"},{\"from\":10.8,\"to\":17.2,\"color\":\"#224AC2\"},{\"from\":17.2,\"to\":24.5,\"color\":\"#F04022\"},{\"from\":24.5,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":0.2,\"color\":\"#6083EC\"},{\"from\":0.2,\"to\":3.4,\"color\":\"#5579E5\"},{\"from\":3.4,\"to\":8,\"color\":\"#4369DD\"},{\"from\":8,\"to\":10.8,\"color\":\"#2B54CE\"},{\"from\":10.8,\"to\":17.2,\"color\":\"#224AC2\"},{\"from\":17.2,\"to\":24.5,\"color\":\"#F04022\"},{\"from\":24.5,\"to\":null,\"color\":\"#DE2343\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/horizontal_wind_speed_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal wind speed card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"m/s\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "wind", "speed", "airspeed", "flow", "gust"], "resources": [{"link": "/api/images/system/horizontal_wind_speed_card_with_background_system_widget_background.png", "title": "\"Horizontal wind speed card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_wind_speed_card_with_background_system_widget_background.png", "publicResourceKey": "P7M82SS0rtjFJn68DySXhorDG4gwmT4Q", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/horizontal_wind_speed_card_with_background_system_widget_image.png", "title": "\"Horizontal wind speed card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_wind_speed_card_with_background_system_widget_image.png", "publicResourceKey": "ibr16U1ljrgMZ5eEScsNeruGC1bTMKfR", "mediaType": "image/png", "data": "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", "public": true}]}