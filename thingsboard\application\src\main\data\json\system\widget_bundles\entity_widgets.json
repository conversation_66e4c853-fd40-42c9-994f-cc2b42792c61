{"widgetsBundle": {"alias": "entity_widgets", "title": "Entity widgets", "image": "data:image/png;base64,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", "description": "Visualize entity properties and hierarchy using table and tree widgets.", "order": 17000, "externalId": null, "name": "Entity widgets"}, "widgetTypeFqns": ["cards.entities_table", "entity_count", "cards.entities_hierarchy"]}