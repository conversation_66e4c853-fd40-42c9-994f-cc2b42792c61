{"fqn": "solar_radiation_chart_card_with_background", "name": "Solar radiation chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/solar_radiation_chart_card_with_background_system_widget_image.png", "description": "Displays a solar radiation data by combining the latest and aggregated values with the background image and optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'radiation', label: 'Solar Radiation', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'W/m²', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'radiation', 'W/m²', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Solar Radiation\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 1100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"W/m²\",\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":48,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#5579E5\"},{\"from\":0,\"to\":250,\"color\":\"#7CC322\"},{\"from\":250,\"to\":500,\"color\":\"#F89E0D\"},{\"from\":500,\"to\":1000,\"color\":\"#F77410\"},{\"from\":1000,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 1100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"W/m²\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < -120) {\\n\\tvalue = -120;\\n} else if (value > 120) {\\n\\tvalue = 120;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"W/m²\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/solar_radiation_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Solar Radiation\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:radioactive\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "solar", "radiation", "sunlight"], "resources": [{"link": "/api/images/system/solar_radiation_chart_card_with_background_system_widget_background.png", "title": "\"Solar radiation chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "solar_radiation_chart_card_with_background_system_widget_background.png", "publicResourceKey": "97YYQMIxcZ10ULlFcYytSzEwoirGHgcv", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/solar_radiation_chart_card_with_background_system_widget_image.png", "title": "\"Solar radiation chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "solar_radiation_chart_card_with_background_system_widget_image.png", "publicResourceKey": "Ff0X5eRN8IbrsH6kwr9OKGxD9zABqD4N", "mediaType": "image/png", "data": "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", "public": true}]}