<?xml version="1.0" encoding="utf-8"?><!--Copyright 2021 AVSystem.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, E<PERSON>EM<PERSON>AR<PERSON>, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

-->
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>Modbus Register Cluster</Name>
		<Description1><![CDATA[This LwM2M Object represents a set of Modbus registers and acts as an interface to read and write their values.]]></Description1>
		<ObjectID>10375</ObjectID>
		<ObjectURN>urn:oma:lwm2m:x:10375</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>Connection</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Objlnk</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Link to an instance of the "Modbus Connection" object to use.]]></Description>
			</Item>
			<Item ID="1">
				<Name>Register Type</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>1..4</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Type of Modbus objects to access.
1: Coil
2: Discrete Input
3: Holding Register
4: Input Register
Note: For convenience, the values are equal to function codes corresponding to Modbus "read" operations for each object type.]]></Description>
			</Item>
			<Item ID="2">
				<Name>Unit ID</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..247, 255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Server unit ID that this register cluster addresses.
Special non-significant value of 255 is valid only for Modbus TCP connections.]]></Description>
			</Item>
			<Item ID="3">
				<Name>Starting Register Address</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..65535</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Address of the first register that this register cluster addresses.]]></Description>
			</Item>
			<Item ID="4">
				<Name>Quantity of Registers</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>1..2000</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Number of registers in this register cluster.
If the Register Type is Holding Register or Input Register, the valid range for this resource is 1..125. Larger values are only supported for Coils and Discrete Inputs.
If this resource is not present, the default value of 1 is assumed.]]></Description>
			</Item>
			<Item ID="5">
				<Name>Values</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..65535</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[List of values of the registers addressed by this register cluster.
The Resource Instance IDs shall range from 0 to "Quantity of Registers - 1" (note: absolute register address cannot be used, because 65535 is a valid Modbus register ID, but not a valid LwM2M Resource Instance ID).
When the Register Type is Coil or Discrete Input, "0" and "1" are the only valid values for each of the Resource Instances.
When the Register Type is Discrete Input or Input Register, this resource shall behave as a read-only resource, i.e. as if the "Operations" attribute was "R".
Even if the Register Type is Coil or Holding Register, any write operation that would result in the set of valid Resource Instance IDs being changed, shall result in a Bad Request error.]]></Description>
			</Item>
			<Item ID="6">
				<Name>Minimum Measured Values</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..65535</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Minimum values recorded by each of the registers in this cluster.
If this resource is supported, the Resource Instance IDs shall range from 0 to "Quantity of Registers - 1". Each Resource Instance shall hold the minimum value recorded by the corresponding Resource Instance of the "Values" resource since the configuration of this cluster (resources 0-4) has been last changed, or since the "Reset Minimum and Maximum Measured Values" resource has been last executed, whichever was later.]]></Description>
			</Item>
			<Item ID="7">
				<Name>Maximum Measured Values</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..65535</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Maximum values recorded by each of the registers in this cluster.
If this resource is supported, the Resource Instance IDs shall range from 0 to "Quantity of Registers - 1". Each Resource Instance shall hold the maximum value recorded by the corresponding Resource Instance of the "Values" resource since the configuration of this cluster (resources 0-4) has been last changed, or since the "Reset Minimum and Maximum Measured Values" resource has been last executed, whichever was later.]]></Description>
			</Item>
			<Item ID="8">
				<Name>Reset Minimum and Maximum Measured Values</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Resets the "Minimum Measured Values" and "Maximum Measured Values" resources.]]></Description>
			</Item>
		</Resources>
		<Description2><![CDATA[]]></Description2>
	</Object>
</LWM2M>
