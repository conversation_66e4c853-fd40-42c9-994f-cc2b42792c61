{"fqn": "input_widgets.update_server_double_attribute", "name": "Update server double attribute", "deprecated": true, "image": "tb-image;/api/images/system/update_double_timeseries_system_widget_image.png", "description": "Simple form to input new double value for pre-defined server-side attribute key.\nThe widget is deprecated. Use \"Update Multiple Attributes\" widget. Attribute type and double value type can be selected in widgets data key configuration.", "descriptor": {"type": "latest", "sizeX": 7.5, "sizeY": 3, "resources": [], "templateHtml": "<div tb-toast toastTarget=\"{{ toastTargetId }}\" style=\"width: 100%; height: 100%;\">\n    <form *ngIf=\"attributeUpdateFormGroup\"\n          class=\"attribute-update-form\"\n          [formGroup]=\"attributeUpdateFormGroup\"\n          (ngSubmit)=\"updateAttribute()\">\n        <div style=\"padding: 0 8px; margin: auto 0;\">\n            <div class=\"attribute-update-form__grid\" [class.!hidden]=\"!entityDetected || !isValidParameter || !dataKeyDetected\">\n                <div class=\"grid__element\">\n                    <mat-form-field class=\"mat-block\" style=\"width: 100%;\"\n                                    floatLabel=\"{{settings.showLabel ? 'auto' : 'always'}}\"\n                                    [hideRequiredMarker]=\"!settings.showLabel\">\n                        <mat-label>{{ settings.showLabel ? labelValue : '' }}</mat-label>\n                        <input matInput\n                               formControlName=\"currentValue\"\n                               [required]=\"settings.isRequired\"\n                               type=\"number\"\n                               (focus)=\"isFocused = true\"\n                               (blur)=\"changeFocus()\"\n                               max=\"{{settings.maxValue}}\"\n                               min=\"{{settings.minValue}}\"/>\n                        <mat-error *ngIf=\"attributeUpdateFormGroup.get('currentValue').hasError('required') && settings.isRequired\">\n                            {{requiredErrorMessage}}\n                        </mat-error>\n                    </mat-form-field>    \n                </div>\n    \n                <div class=\"grid__element\">\n                    <button mat-icon-button class=\"applyChanges\"\n                               type=\"submit\"\n                               [disabled]=\"(originalValue === attributeUpdateFormGroup.get('currentValue').value || attributeUpdateFormGroup.invalid || !attributeUpdateFormGroup.dirty) && (originalValue === attributeUpdateFormGroup.get('currentValue').value || settings.isRequired)\"\n                               matTooltip=\"{{ 'widgets.input-widgets.update-attribute' | translate }}\"\n                               matTooltipPosition=\"above\">\n                        <mat-icon>check</mat-icon>\n                    </button>\n                    <button mat-icon-button class=\"discardChanges\"\n                               type=\"button\"\n                               [disabled]=\"originalValue === attributeUpdateFormGroup.get('currentValue').value\"\n                               (click)=\"attributeUpdateFormGroup.get('currentValue').patchValue(originalValue); isFocused = false\"\n                               matTooltip=\"{{ 'widgets.input-widgets.discard-changes' | translate }}\"\n                               matTooltipPosition=\"above\">\n                        <mat-icon>close</mat-icon>\n                    </button>\n                </div>\n            </div>\n    \n            <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\" [class.!hidden]=\"entityDetected\" >\n                {{ 'widgets.input-widgets.no-entity-selected' | translate }}\n            </div>\n            <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\"\n                 [class.!hidden]=\"!entityDetected || dataKeyDetected\">\n                {{ 'widgets.input-widgets.no-attribute-selected' | translate }}\n            </div>\n            <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\"\n                 [class.!hidden]=\"!entityDetected || isValidParameter\">\n                {{ 'widgets.input-widgets.timeseries-not-allowed' | translate }}\n            </div>\n        </div>\n    </form>\n</div>", "templateCss": ".attribute-update-form {\n    overflow: hidden;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n}\n\n.attribute-update-form__grid {\n    display: flex;\n}\n.grid__element:first-child {\n    flex: 1;\n}\n.grid__element:last-child {\n    margin-top: 19px;\n    margin-left: 7px;\n}\n.grid__element {\n    display: flex;\n}\n\n.attribute-update-form .mat-button.mat-icon-button {\n    width: 32px;\n    min-width: 32px;\n    height: 32px;\n    min-height: 32px;\n    padding: 0 !important;\n    margin: 0 !important;\n    line-height: 20px;\n}\n\n.attribute-update-form .mat-icon-button mat-icon {\n    width: 20px;\n    min-width: 20px;\n    height: 20px;\n    min-height: 20px;\n    font-size: 20px;\n}\n\n.tb-toast {\n    font-size: 14px!important;\n}", "controllerScript": "let $scope;\nlet settings;\nlet attributeService;\nlet utils;\nlet translate;\n\nself.onInit = function() {\n    self.ctx.ngZone.run(function() {\n       init(); \n       self.ctx.detectChanges(true);\n    });\n};\n\n\nfunction init() {\n\n    $scope = self.ctx.$scope;\n    attributeService = $scope.$injector.get(self.ctx.servicesMap.get('attributeService'));\n    utils = $scope.$injector.get(self.ctx.servicesMap.get('utils'));\n    translate = $scope.$injector.get(self.ctx.servicesMap.get('translate'));\n    $scope.toastTargetId = 'input-widget' + utils.guid();\n    settings = utils.deepClone(self.ctx.settings) || {};\n    settings.showLabel = utils.defaultValue(settings.showLabel, true);\n    settings.showResultMessage = utils.defaultValue(settings.showResultMessage, true);\n    settings.isRequired = utils.defaultValue(settings.isRequired, true);\n    $scope.settings = settings;\n    $scope.isValidParameter = true;\n    $scope.dataKeyDetected = false; \n\n    $scope.requiredErrorMessage = utils.customTranslation(settings.requiredErrorMessage, settings.requiredErrorMessage) || translate.instant('widgets.input-widgets.entity-attribute-required');\n    $scope.labelValue = utils.customTranslation(settings.labelValue, settings.labelValue) || translate.instant('widgets.input-widgets.value');\n    \n    var validators = [$scope.validators.min(settings.minValue),\n        $scope.validators.max(settings.maxValue)];\n    \n    if (settings.isRequired) {\n        validators.push($scope.validators.required);\n    }\n    \n    $scope.attributeUpdateFormGroup = $scope.fb.group({\n        currentValue: [undefined, validators]\n    });\n\n    if (self.ctx.datasources && self.ctx.datasources.length) {\n        var datasource = self.ctx.datasources[0];\n        if (datasource.type === 'entity') {\n            if (datasource.entityType && datasource.entityId) {\n                $scope.entityName = datasource.entityName;\n                if (settings.widgetTitle && settings.widgetTitle.length) {\n                    $scope.titleTemplate = utils.customTranslation(settings.widgetTitle, settings.widgetTitle);\n                } else {\n                    $scope.titleTemplate = self.ctx.widgetConfig.title;\n                }\n\n                $scope.entityDetected = true;\n            }\n        }\n        if (datasource.dataKeys.length) {\n            if (datasource.dataKeys[0].type !== \"attribute\") {\n                $scope.isValidParameter = false;\n            } else {\n                $scope.currentKey = datasource.dataKeys[0].name;\n                $scope.dataKeyType = datasource.dataKeys[0].type;\n                $scope.dataKeyDetected = true;\n            }\n        }\n    }\n\n    self.ctx.widgetTitle = utils.createLabelFromDatasource(self.ctx.datasources[0], $scope.titleTemplate);\n\n    $scope.updateAttribute = function () {\n        $scope.isFocused = false;\n        if ($scope.entityDetected) {\n            var datasource = self.ctx.datasources[0];\n\n            attributeService.saveEntityAttributes(\n                datasource.entity.id,\n                'SERVER_SCOPE',\n                [\n                    {\n                        key: $scope.currentKey,\n                        value: $scope.attributeUpdateFormGroup.get('currentValue').value\n                    }\n                ]\n            ).subscribe(\n                function success() {\n                    $scope.originalValue = $scope.attributeUpdateFormGroup.get('currentValue').value;\n                    if (settings.showResultMessage) {\n                        $scope.showSuccessToast(translate.instant('widgets.input-widgets.update-successful'), 1000, 'bottom', 'left', $scope.toastTargetId);\n                    }\n                },\n                function fail() {\n                    if (settings.showResultMessage) {\n                        $scope.showErrorToast(translate.instant('widgets.input-widgets.update-failed'), 'bottom', 'left', $scope.toastTargetId);\n                    }\n                }\n            );\n        }\n    };\n\n    $scope.changeFocus = function () {\n        if ($scope.attributeUpdateFormGroup.get('currentValue').value === $scope.originalValue) {\n            $scope.isFocused = false;\n        }\n    }\n}\n\nself.onDataUpdated = function() {\n\n    try {\n        if ($scope.dataKeyDetected) {\n            if (!$scope.isFocused) {\n                $scope.originalValue = self.ctx.data[0].data[0][1];\n                $scope.attributeUpdateFormGroup.get('currentValue').patchValue(correctValue($scope.originalValue));\n                self.ctx.detectChanges();\n            }\n        }\n    } catch (e) {\n        console.log(e);\n    }\n}\n\nfunction correctValue(value) {\n    if (typeof value !== \"number\") {\n        return 0;\n    }\n    return value;\n}\n\nself.onResize = function() {\n\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true\n    }\n}\n\nself.onDestroy = function() {\n\n}", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-update-double-attribute-widget-settings", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"showResultMessage\":true,\"showLabel\":true,\"isRequired\":true},\"title\":\"Update server double attribute\",\"dropShadow\":true,\"enableFullscreen\":false,\"widgetStyle\":{},\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{}}"}, "resources": [{"link": "/api/images/system/update_double_timeseries_system_widget_image.png", "title": "\"Update double timeseries\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "update_double_timeseries_system_widget_image.png", "publicResourceKey": "Qp6mHlHuCMNnvkPYHndt6kGUvr8jDSbW", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAAAAABslHx1AAAAAmJLR0QA/4ePzL8AAANMSURBVHja7d3fS5NRGAfw/SsrhSCKKIugburCi6KCLrropi4izME0c1kLUxquHybmKNPAi4qMJkm9hOkswhVkpaLMfhmlbjHYu+3Vaf7a3r3fLjad5nZh6NiZ3+diO+fAe/HhOc85Z+/Fjg4R2T0ieLjlMHQRT0iF4KGGPBGdHEIWREjWudVsgKhu3QiyIkYIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQghZS8jbbwDQOT9slJc/5HGJAOmoAaAUjKWERCO9z7RI5kPGC8cBxy3A5/wUBoyy1qNirg+I9jtlAPBcvGkt7xWgRmyvgKpu9JR3NF4HjHLk9DSCBmi2eqnsFwCEK8vGRSj2j9fgM85h0Ito4VgC0m/R4GwEgHZ72xMRIOHiwIv7wJ/HN6oKfAmIdL62tsoCAMps1CvE8vuwvWIIaHgehTEGmULQgPYGv9+vCLWP/Cg1a4ClG18LvDDKKPmJPgNGzwXgdQkF0S5JAPrPXqgzDcAo402R2WYAukorzUMi7uzR6YWqmY0BJzQeUQghhBBC1h9kZllDDIj2cumBaKZaijWk6hmRIFqLybl0RDJJi75EgWitJse/Y20mKf4hDkRrWe4AJJOUPB8ZC0mWj1hOUjgyDjJ9zw1Ae5rcsUKIbK9rmVzovatv+hJvfh5ec4hirfidYl6teGp1bd1/Km9XfDhasOnksZxGAIGmA/rWtU/J2NXL7tT5WEmxz+0sUqHsNsR69lwXcDc3AC13b0U6IFCsZanzsZLld1D/AYApP/5wDYBBfTfwPupPCwSKNbnj/zbE44WLOvYcLwCkCYLpVTyiODb2zDe/P7qypQnphKzioXF0myUxMw9t3+MUFBLcd2LxP1qo1bnDQkKmjhydWjqw4YGIkMiJ/MT5ufggAL9eEhCilWzudLlcLgWvbYBDfyfoPpOnCAiZ1MeiGeYdKtCcp9cfHhB11YoXeewFo2+Cv9kJIYQQQghZf5DbaQ1mhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCEkMyFZc0FwdlzZPC7rwtlxibaqy45rzVX8BUWnY6Q1/jMIAAAAAElFTkSuQmCC", "public": true}]}