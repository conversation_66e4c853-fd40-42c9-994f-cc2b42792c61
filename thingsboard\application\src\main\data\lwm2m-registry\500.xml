<?xml version="1.0" encoding="utf-8" ?>

<!-- 
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-XML_500-V1_0-20200604-A
   Type: xml
   Date: 2020-Jun-04

Public Reachable Information
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 500.xml

NORMATIVE INFORMATION
  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

LEGAL DISCLAIMER

Copyright 2020 Open Mobile Alliance. 

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

The above license is used as a license under copyright only.  Please
reference the OMA IPR Policy for patent licensing terms:
https://www.omaspecworks.org/about/intellectual-property-rights/
  
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
    <Object ObjectType="MODefinition">
        <Name>CoAP Config</Name>
        <Description1>
            <![CDATA[This object is used to configure the CoAP transmission parameters (RFC7252) and other parameters that affect CoAP transmission.]]>
        </Description1>
        <ObjectID>500</ObjectID>
        <ObjectURN>urn:oma:lwm2m:oma:500</ObjectURN>
        <LWM2MVersion>1.0</LWM2MVersion>
        <ObjectVersion>1.0</ObjectVersion>
        <MultipleInstances>Multiple</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Resources>
            <Item ID="0">
                <Name>Network Bearer</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration>0..255</RangeEnumeration>
                <Units></Units>
                <Description>
                    <![CDATA[Identify the network bearer for which these parameters are applicable. The value is from the network bearer list defined in the Connectivity Monitoring Object (ID: 4). Please, verify the list of currently reserved values.]]>
                </Description>
            </Item>
            <Item ID="1">
                <Name>ACK_TIMEOUT</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration></RangeEnumeration>
                <Units>s</Units>
                <Description>
                    <![CDATA[The initial value of retransmission timeout in CoAP. Default is set to that of RFC7252, that is 2 seconds.]]>
                </Description>
            </Item>
            <Item ID="2">
                <Name>ACK_RANDOM_FACTOR</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Float</Type>
                <RangeEnumeration></RangeEnumeration>
                <Units></Units>
                <Description>
                    <![CDATA[The initial value of retransmission timeout in CoAP. Default is set to that of RFC7252, that is a value of 1.5.]]>
                </Description>
            </Item>
            <Item ID="3">
                <Name>MAX_RETRANSMIT</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration></RangeEnumeration>
                <Units></Units>
                <Description>
                    <![CDATA[The maximum number of retransmissions. Default is set to that of RFC7252, that is 4 retransmissions.]]>
                </Description>
            </Item>
            <Item ID="4">
                <Name>NSTART</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration></RangeEnumeration>
                <Units></Units>
                <Description>
                    <![CDATA[Number of simultaneous outstanding interactions that the LwM2M client can accept from a given LwM2M Server. An outstanding interaction is either a CON for which an ACK has not yet been received but is still expected (message layer) or a request for which neither a response nor an Acknowledgment message has yet been received but is still expected. Default is set to that of RFC7252, that is 1 interaction.]]>
                </Description>
            </Item>
            <Item ID="5">
                <Name>DEFAULT_LEISURE</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration></RangeEnumeration>
                <Units>s</Units>
                <Description>
                    <![CDATA[If a server does decide to respond to a multicast request, it should not respond immediately. Instead, it should pick based on RFC7252, Section 8.2. If the LwM2M Client does not have data to compute the Leisure time it should use that of Default Leisure as specified in RFC7252.]]>
                </Description>
            </Item>
            <Item ID="6">
                <Name>PROBING_RATE</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration></RangeEnumeration>
                <Units>B/s</Units>
                <Description>
                    <![CDATA[This resource represents the maximum probing rate accepted by this LwM2M Client in bytes per second.]]>
                </Description>
            </Item>
            <Item ID="7">
                <Name>DTLS Retransmission Timer</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration></RangeEnumeration>
                <Units>s</Units>
                <Description>
                    <![CDATA[The LwM2M Server may modify the initial DTLS retransmission timer value of 1 second into a different value. This will have effects on latency as well as on congestion, thus the timer value should be handled carefully.]]>
                </Description>
            </Item>
            <Item ID="8">
                <Name>Max Response Waiting Time</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration></RangeEnumeration>
                <Units></Units>
                <Description>
                    <![CDATA[The maximum time in seconds the device will wait for a separate response to a request.]]>
                </Description>
            </Item>
            <Item ID="9">
                <Name>Apply</Name>
                <Operations>E</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type></Type>
                <RangeEnumeration></RangeEnumeration>
                <Units></Units>
                <Description>
                    <![CDATA[Applies the parameters specifed in the resources to the CoAP connections on this interface.]]>
                </Description>
            </Item>
        </Resources>
        <Description2 />
    </Object>
</LWM2M>
