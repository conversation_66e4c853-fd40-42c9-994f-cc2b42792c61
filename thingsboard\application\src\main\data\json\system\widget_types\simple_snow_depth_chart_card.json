{"fqn": "simple_snow_depth_chart_card", "name": "Simple snow depth chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_snow_depth_chart_card_system_widget_image.png", "description": "Displays historical snow depth values as a simplified chart. Optionally may display the corresponding latest snow depth value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'snow', label: 'Snow depth', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'snow', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Snow depth\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 120) {\\n\\tvalue = 120;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 120) {\\n\\tvalue = 120;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":1,\"color\":\"#7191EF\"},{\"from\":1,\"to\":10,\"color\":\"#4B70DD\"},{\"from\":10,\"to\":30,\"color\":\"#305AD7\"},{\"from\":30,\"to\":60,\"color\":\"#234CC7\"},{\"from\":60,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Snow depth\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"ac_unit\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":1,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"cm\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "snow", "snowfall", "flurry", "blizzard", "snowstorm", "snowflake", "sleet", "whiteout", "snowdrift"], "resources": [{"link": "/api/images/system/simple_snow_depth_chart_card_system_widget_image.png", "title": "\"Simple snow depth chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_snow_depth_chart_card_system_widget_image.png", "publicResourceKey": "y6S1w1PUvxyssGqH1Zotf2wapMTp59Q8", "mediaType": "image/png", "data": "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", "public": true}]}