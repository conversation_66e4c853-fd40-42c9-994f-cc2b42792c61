<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="200" height="200" fill="none" version="1.1" viewBox="0 0 200 200"><tb:metadata xmlns=""><![CDATA[{
  "title": "Horizontal pipe",
  "description": "Horizontal pipe with fluid and leak visualizations.",
  "searchTags": [
    "pipe",
    "horizontal pipe"
  ],
  "widgetSizeX": 1,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "fluid",
      "stateRenderFunction": "var fluid = ctx.values.fluid && !ctx.values.leak;\nvar flow = ctx.values.flow;\nvar flowDirection = ctx.values.flowDirection;\n\nvar elementFluid = element.remember('fluid');\nvar elementFlow = null;\nvar elementFlowDirection = null;\n\nif (fluid !== elementFluid) {\n    element.remember('fluid', fluid);\n    elementFlow = null;\n    elementFlowDirection = null;\n} else {\n    elementFlow = element.remember('flow');\n    elementFlowDirection = element.remember('flowDirection');\n}\n\nvar liquidPattern = element.reference('fill').first();\n\nvar fluidAnimation = ctx.api.cssAnimation(liquidPattern);\n\n\nif (fluid) {\n    element.show();\n    if (flow !== elementFlow) {\n        element.remember('flow', flow);\n        if (flow) {\n            if (elementFlowDirection !== flowDirection || !fluidAnimation) {\n                element.remember('flowDirection', flowDirection);\n                fluidAnimation = animateFlow(liquidPattern, flowDirection);\n            } else {\n                fluidAnimation.play();\n            }\n        } else {\n            if (fluidAnimation) {\n                fluidAnimation.pause();\n            }\n        }\n    } else if (flow && elementFlowDirection !== flowDirection) {\n        element.remember('flowDirection', flowDirection);\n        fluidAnimation = animateFlow(liquidPattern, flowDirection);\n    }\n    if (flow) {\n        if (fluidAnimation) {\n            fluidAnimation.speed(ctx.values.flowAnimationSpeed);\n        }\n    }\n} else {\n    if (fluidAnimation) {\n        fluidAnimation.pause();\n    }\n    element.hide();\n}\n\nfunction animateFlow(liquidPattern, forwardElseReverse) {\n    ctx.api.resetCssAnimation(liquidPattern);\n    var deltaX = forwardElseReverse ? 172 : -172;\n    return ctx.api.cssAnimate(liquidPattern, 1000).relative(deltaX, 0).loop();\n}",
      "actions": null
    },
    {
      "tag": "fluid-background",
      "stateRenderFunction": "var fluid = ctx.values.fluid && !ctx.values.leak;\nif (fluid) {\n    var color = ctx.properties.fluidColor;\n    element.attr({fill: color});\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "leak",
      "stateRenderFunction": "var leak = ctx.values.leak;\nif (leak) {\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "pipe-background",
      "stateRenderFunction": "var color = ctx.properties.pipeColor;\nelement.attr({fill: color});",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "fluid",
      "name": "{i18n:scada.symbol.fluid-presence}",
      "hint": "{i18n:scada.symbol.fluid-presence-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.present}",
      "falseLabel": "{i18n:scada.symbol.absent}",
      "stateLabel": "{i18n:scada.symbol.fluid-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "flow",
      "name": "{i18n:scada.symbol.flow-presence}",
      "hint": "{i18n:scada.symbol.flow-presence-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.present}",
      "falseLabel": "{i18n:scada.symbol.absent}",
      "stateLabel": "{i18n:scada.symbol.flow-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "flowDirection",
      "name": "{i18n:scada.symbol.flow-direction}",
      "hint": "{i18n:scada.symbol.flow-direction-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.forward}",
      "falseLabel": "{i18n:scada.symbol.reverse}",
      "stateLabel": "{i18n:scada.symbol.forward}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": true,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "flowAnimationSpeed",
      "name": "{i18n:scada.symbol.flow-animation-speed}",
      "hint": "{i18n:scada.symbol.flow-animation-speed-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": 1,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "leak",
      "name": "{i18n:scada.symbol.leak}",
      "hint": "{i18n:scada.symbol.leak-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.leak-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    }
  ],
  "properties": [
    {
      "id": "fluidColor",
      "name": "{i18n:scada.symbol.fluid-color}",
      "type": "color",
      "default": "#1EC1F480",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "pipeColor",
      "name": "{i18n:scada.symbol.pipe-color}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata>
<path d="m186 136h-172v-72h172z" fill="#fff" tb:tag="pipe-background"/><path d="m186 136h-172v-72h172z" fill="url(#paint0_linear_955_27716)"/><g stroke-width="3">
  <path d="m184.5 134.5h-169v-69h169z" stroke="#000" stroke-opacity=".12"/>
  <rect transform="scale(-1)" x="-198.5" y="-148.5" width="11.286" height="97" rx="5.6429" fill="#d9d9d9" stroke="#727171"/>
  <rect transform="scale(-1)" x="-12.5" y="-148.5" width="11" height="97" rx="5.5" fill="#d9d9d9" stroke="#727171"/>
 </g><defs>
  <linearGradient id="paint0_linear_955_27716" x1="48.475" x2="48.475" y1="136" y2="64" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <pattern id="baseLiquid" width="172" height="72" patternTransform="translate(14,-8)" patternUnits="userSpaceOnUse">
   <circle transform="rotate(-90)" cx="-15" cy="21" r="8" fill="url(#paint28_linear_1182_32781-5)"/>
   <circle transform="rotate(-90)" cx="-15" cy="150" r="8" fill="url(#paint29_linear_1182_32781-1)"/>
   <circle transform="rotate(-90)" cx="-16" cy="113" r="8" fill="url(#paint30_linear_1182_32781-7)"/>
   <circle transform="rotate(-90)" cx="-58" cy="34" r="8" fill="url(#paint31_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-58" cy="155" r="8" fill="url(#paint32_linear_1182_32781-3)"/>
   <circle transform="rotate(-90)" cx="-26" cy="33" r="5" fill="url(#paint33_linear_1182_32781-1)"/>
   <circle transform="rotate(-90)" cx="-26" cy="162" r="5" fill="url(#paint34_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-37" cy="5" r="5" fill="url(#paint35_linear_1182_32781-6)"/>
   <circle transform="rotate(-90)" cx="-8" cy="94" r="4" fill="url(#paint36_linear_1182_32781-9)"/>
   <circle transform="rotate(-90)" cx="-60" cy="72" r="4" fill="url(#paint37_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-37" cy="112" r="5" fill="url(#paint38_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-39" cy="59" r="5" fill="url(#paint39_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-62" cy="115" r="5" fill="url(#paint40_linear_1182_32781-6)"/>
   <circle transform="rotate(-90)" cx="-42" cy="139" r="5" fill="url(#paint41_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-21" cy="76" r="5" fill="url(#paint42_linear_1182_32781-1)"/>
   <circle transform="rotate(-90)" cx="-50.5" cy="126.5" r="2.5" fill="url(#paint43_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-46.5" cy="169.5" r="2.5" fill="url(#paint44_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-9.5" cy="57.5" r="2.5" fill="url(#paint45_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-35.5" cy="96.5" r="2.5" fill="url(#paint46_linear_1182_32781-9)"/>
   <circle transform="rotate(-90)" cx="-23.5" cy="91.5" r="2.5" fill="url(#paint47_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-40.5" cy="22.5" r="2.5" fill="url(#paint48_linear_1182_32781-88)"/>
   <circle transform="rotate(-90)" cx="-23.5" cy="124.5" r="2.5" fill="url(#paint49_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-47.5" cy="86.5" r="2.5" fill="url(#paint50_linear_1182_32781-6)"/>
   <circle transform="rotate(-90)" cx="-21.5" cy="51.5" r="2.5" fill="url(#paint51_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-51.5" cy="48.5" r="2.5" fill="url(#paint52_linear_1182_32781-3)"/>
   <circle transform="rotate(-90)" cx="-64" cy="14" r="4" fill="url(#paint53_linear_1182_32781-83)"/>
   <circle transform="rotate(-90)" cx="-64" cy="135" r="4" fill="url(#paint54_linear_1182_32781-33)"/>
   <circle transform="rotate(-90)" cx="-58.5" cy="95.5" r="9.5" fill="url(#paint55_linear_1182_32781-8)"/>
   <path d="m0 0h172v72h-172z" fill="url(#paint84_linear_1182_32781-8)" stroke-width=".57735"/>
  </pattern>
  <pattern id="liquid" width="172" height="72" patternUnits="userSpaceOnUse"><rect width="688" height="72" x="-172" y="0" stroke-width="0" fill="url(#baseLiquid)"/></pattern>
  <linearGradient id="paint28_linear_1182_32781-5" x1="19.316" x2="21" y1="8.2632" y2="23" gradientTransform="translate(-36 6)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint29_linear_1182_32781-1" x1="148.32" x2="150" y1="8.2632" y2="23" gradientTransform="translate(-165,135)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint30_linear_1182_32781-7" x1="111.32" x2="113" y1="9.2632" y2="24" gradientTransform="translate(-129 97)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint31_linear_1182_32781-4" x1="32.316" x2="34" y1="51.263" y2="66" gradientTransform="translate(-92 -24)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint32_linear_1182_32781-3" x1="153.32" x2="155" y1="51.263" y2="66" gradientTransform="translate(-213 97)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint33_linear_1182_32781-1" x1="31.947" x2="33" y1="21.789" y2="31" gradientTransform="translate(-59 7)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint34_linear_1182_32781-4" x1="160.95" x2="162" y1="21.789" y2="31" gradientTransform="translate(-188,136)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint35_linear_1182_32781-6" x1="3.9474" x2="5" y1="32.79" y2="42" gradientTransform="translate(-42 -32)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint36_linear_1182_32781-9" x1="93.158" x2="94" y1="4.6316" y2="12" gradientTransform="translate(-102,86)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint37_linear_1182_32781-4" x1="71.158" x2="72" y1="56.632" y2="64" gradientTransform="translate(-132 12)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint38_linear_1182_32781-2" x1="110.95" x2="112" y1="32.79" y2="42" gradientTransform="translate(-149 75)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint39_linear_1182_32781-2" x1="57.947" x2="59" y1="34.79" y2="44" gradientTransform="translate(-98 20)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint40_linear_1182_32781-6" x1="113.95" x2="115" y1="57.79" y2="67" gradientTransform="translate(-177 53)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint41_linear_1182_32781-4" x1="137.95" x2="139" y1="37.79" y2="47" gradientTransform="translate(-181 97)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint42_linear_1182_32781-1" x1="74.947" x2="76" y1="16.789" y2="26" gradientTransform="translate(-97 55)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint43_linear_1182_32781-2" x1="125.97" x2="126.5" y1="48.395" y2="53" gradientTransform="translate(-177 76)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint44_linear_1182_32781-8" x1="168.97" x2="169.5" y1="44.395" y2="49" gradientTransform="translate(-216,123)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint45_linear_1182_32781-8" x1="56.974" x2="57.5" y1="7.3947" y2="12" gradientTransform="translate(-67 48)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint46_linear_1182_32781-9" x1="95.974" x2="96.5" y1="33.395" y2="38" gradientTransform="translate(-132 61)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint47_linear_1182_32781-2" x1="90.974" x2="91.5" y1="21.395" y2="26" gradientTransform="translate(-115 68)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint48_linear_1182_32781-88" x1="21.974" x2="22.5" y1="38.395" y2="43" gradientTransform="translate(-63 -18)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint49_linear_1182_32781-8" x1="123.97" x2="124.5" y1="21.395" y2="26" gradientTransform="translate(-148,101)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint50_linear_1182_32781-6" x1="85.974" x2="86.5" y1="45.395" y2="50" gradientTransform="translate(-134 39)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint51_linear_1182_32781-8" x1="50.974" x2="51.5" y1="19.395" y2="24" gradientTransform="translate(-73 30)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint52_linear_1182_32781-3" x1="47.974" x2="48.5" y1="49.395" y2="54" gradientTransform="translate(-100 -3)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint53_linear_1182_32781-83" x1="13.158" x2="14" y1="60.632" y2="68" gradientTransform="translate(-78 -50)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint54_linear_1182_32781-33" x1="134.16" x2="135" y1="60.632" y2="68" gradientTransform="translate(-199 71)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint55_linear_1182_32781-8" x1="93.5" x2="95.5" y1="50.5" y2="68" gradientTransform="translate(-154 37)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint84_linear_1182_32781-8" x1="248" x2="248" y1="1.8513e-7" y2="72" gradientTransform="scale(.33333 1)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" offset=".10895"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".11331"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".88736"/>
   <stop stop-color="#727171" offset=".89138"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
 </defs><rect x="14" y="64" width="172" height="72" fill="#1ec1f4" stroke-width="0" style="display: none;" tb:tag="fluid-background"/><rect x="14" y="64" width="172" height="72" fill="url(#liquid)" stroke-width="0" style="display: none;" tb:tag="fluid"/><g transform="translate(5.0806 -.22515)" style="display: none;" tb:tag="leak">
  <path d="m34.253 100.01c0.5162 0.38 1.0932 0.926 1.6625 1.191 1.6625 1.191 3.6513 1.98 5.617 1.926 0.8955-0.136 1.8442-0.387 2.6789-0.691 2.2843-0.737 4.5155-1.36 6.7467-1.983 2.0111-0.448 4.2423-1.0713 5.3196-2.7869-0.1747-0.2201-0.2354-0.387-0.41-0.6071 3.1037-1.6023 5.7289-4.2595 7.671-7.2353 1.3504-2.004 2.3515-4.4482 4.2789-5.906 0.6601-0.5239 1.381-0.8808 1.9881-1.2909 1.0471-0.7592 1.9804-1.5716 2.7391-2.604 0.5994-0.6909 1.0241-1.6018 1.5096-2.3457 1.4035-2.1179 3.0347-4.1297 5.0683-5.8152-0.2579 0.8501-0.5156 1.7002-0.7734 2.5504 0 0 0.1823 0.5008 0.1215 0.3339-0.2808 0.0077-0.4401 0.3492-0.4324 0.63 1.0094-0.0837 2.0188-0.1675 2.9143-0.3044 4.8193-0.5251 9.6917-1.1641 14.496-2.2508-4.2801 1.7469-8.8563 2.9398-13.554 3.7988-1.0625 0.1977-2.0719 0.2815-3.1267 0.7599-2.5575 1.0254-4.0594 3.6519-5.2806 6.2707l-0.0531 0.1138c-0.1062 0.2278-0.2655 0.5694-0.0909 0.7894 0.1216 0.3339 0.6301 0.4324 1.0248 0.4778 2.0265 0.1133 3.9999 0.3404 5.9733 0.5675 2.4819 0.3256 4.8045 0.9928 7.3396 1.2046 2.2542 0.2194 4.5007 0.1581 6.6334 0.0436 1.0094-0.0838 1.9657-0.0537 2.8536-0.4714 1.0017-0.3646 1.8288-0.9493 2.5952-1.7009 0.9863-0.9262 1.9193-1.7386 2.7923-2.7179 5.076-5.5344 20.298-6.8695 24.508-13.223-1.38 2.9604-2.928 5.9816-4.308 8.942 3.824-1.9593 8.128-2.8637 12.484-3.882 1.229-0.2585 2.459-0.5169 3.68-1.0561 1.002-0.3646 1.996-1.01 2.823-1.5947 1.655-1.1693 3.248-2.5055 3.984-4.3804-0.304 1.2448-0.834 2.3834-1.426 3.3551 0.394 0.0454 0.903 0.1439 1.298 0.1893 3.385 0.4696 6.717 1.053 9.988 1.4695-1.396 0.3192-2.914 0.3044-4.379 0.1758-1.973-0.2271-3.848-0.9627-5.806-0.6282-2.239 0.3422-3.992 2.02-6.041 3.1439-3.21 1.8301-7.073 2.3853-10.761 3.1605-3.688 0.7753-6.999 0.6235-12.935 2.3663-4.278 1.256-11.158 3.4266-13.449 5.9623-0.767 0.7516-1.472 1.6701-2.064 2.6418-0.4326 0.6301-0.7511 1.3132-1.1228 2.1103-0.4247 0.9108-0.7964 1.7079-1.335 2.5657-0.2655 0.5693-0.6448 1.0855-1.0849 1.4348-0.4932 0.4631-1.2749 0.6531-1.8896 0.7823-2.1781 0.5091-4.3562 1.0183-6.5343 1.5274 0.3339-0.1215 0.8348-0.3038 1.1687-0.4254 2.6713-0.9722 5.2287-1.9976 7.2015-3.8501 0.4401-0.3492 0.8194-0.8654 0.9179-1.374 0.0531-0.1138 0.1062-0.2277-0.0076-0.2808 0.0377-0.6755-0.7139-1.4418-1.2679-1.1457-0.7439-0.4855-1.9279-0.6217-2.7173-0.7126-6.1933-0.3928-12.44-0.6718-18.572-0.8977-0.6755-0.0377-1.351-0.0755-2.1326 0.1145-0.7817 0.19-1.3357 0.4861-1.9427 0.8961-4.591 2.7109-9.3413 5.7634-12.482 10.121-0.0531 0.1139-0.1062 0.2277-0.1593 0.3416 0 0 0.1138 0.0531 0.2277 0.1062-0.2731 0.288-0.607 0.41-0.8802 0.698-1.8589 1.906-2.7916 4.798-3.4511 7.401-0.3281 2.459-0.3109 3.65 0.2287 4.356 0.6125 0.801 2.6907 3.528 4.9877 3.483 1.0094-0.084 2.3108-1.242 2.7709-1.18 0.1625 0.022 1.8643 1.516 0.0794 0.644 0.7057-0.539 9.3274-3.495 14.168-7.337 0.4401-0.35 0.9333-0.813 1.3126-1.329 0.2732-0.288 0.4325-0.63 0.4779-1.025 0.0531-0.113-0.0077-0.28-0.0684-0.447-0.0608-0.167-0.2278-0.107-0.3416-0.16-4.9261-1.326-10.398-2.076-14.263-5.6801 0.2809-0.0077 0.3947 0.0454 0.6755 0.0377 0.1139 0.0531 0.2808-0.0077 0.5086 0.0985 2.8235 0.4849 5.67 1.8119 8.4251 1.8499 1.2371 0.022 2.5273-0.07 3.719 0.348 1.5333 0.576 2.9227 2.056 4.5014 2.237 0.7894 0.091 1.6849-0.046 2.3527-0.289 1.8365-0.668 3.6046-1.785 5.5019-2.286 0.9486-0.251 2.0719-0.281 3.0205-0.532 0.167-0.061 0.2809-8e-3 0.4478-0.069 2.1781-0.509 10.278-1.0219 12.04-2.4189-1.541 1.2224-9.1936 1.667-10.559 3.1099 2.9374 0.538 5.7609 1.023 8.8043 1.333-2.58 0.183-5.1684 0.085-7.7642-0.294-1.2979-0.189-2.5958-0.379-3.886-0.287-2.4666 0.236-4.7733 2.21-4.5373 4.676 0.0077 0.281 0.0154 0.562 0.19 0.782 0.433 1.45 1.5339 2.656 2.5209 3.809 3.0672 3.233 6.1267 6.184 9.414 9.242 2.4216 2.238 4.8966 4.363 7.9106 5.629 0.227 0.106 0.508 0.099 0.614-0.129 0.084 1.009 0.942 1.548 1.906 1.859 0.911 0.425 1.928 0.622 2.892 0.932l2.55 0.774c4.023 1.183 8.099 2.252 12.22 2.926-2.132 0.114-4.386-0.105-6.542-0.833-1.587-0.463-3.067-1.153-4.592-1.448-1.64-0.349-3.264-0.136-4.903-0.485-0.737-0.204-1.587-0.462-2.376-0.553 0.881 1.381 1.815 2.648 2.695 4.029-0.865-0.819-1.731-1.638-2.596-2.458-1.04-1.039-2.133-1.965-3.227-2.891-2.869-2.169-6.5581-3.474-9.1014-6.046-0.1746-0.22-0.3493-0.44-0.5239-0.66 0.2808-8e-3 0.5086 0.098 0.7894 0.091-0.0608-0.167-0.2885-0.273-0.3493-0.44-1.8678-2.535-4.2896-4.773-6.7037-6.73-2.0725-1.799-4.3727-3.703-7.0216-3.968-0.7894-0.091-1.6318-0.068-2.4212-0.158-0.7894-0.091-1.6471-0.63-2.0041-1.351-2.7999 2.437-5.4747 5.667-9.2392 5.713-2.922 0.024-4.9354 2.323-6.6372 0.828 1.2453 0.166-0.2492 1.868-2.2002 2.242-1.951 0.373-7.5425-5.058-8.0062-7.631-0.403-2.406 0.0896-4.949 1.273-6.892l0.1062-0.228c-3.5745 0.828-7.316 1.718-11.073 2.045-1.1233 0.031-2.2996 0.175-3.3775-0.189-1.7156-1.077-3.2264-2.89-4.6235-4.651z" fill="#5c5a5a" style=""/>
  <g fill="#8b8b8b" style="">
   <path d="m60.493 117.31c0.8195-0.865 1.1148-1.496 2.2835-1.921 2.3374-0.851 3.3482-0.479 5.8071-0.996 2.679-0.691 3.756-1.458 6.1312-2.985 1.2141-0.82 2.3143-1.693 3.3007-2.619 0.4785 1.055 3.2567 0.986 4.4408 1.122 1.184 0.136 2.4742 0.045 3.4459 0.637 1.0855 0.644 1.6778 1.752 2.4902 2.686 0.9262 0.986 2.1256 1.684 3.2719 2.496 2.2395 1.737 3.7657 4.112 5.125 6.548-0.1139-0.053-0.1139-0.053-0.2277-0.106-1.8678-2.535-4.2896-4.773-6.7037-6.73-2.0726-1.798-4.3728-3.703-7.0217-3.968-0.7893-0.09-1.6318-0.067-2.4211-0.158-0.7894-0.091-3.0668-0.204-3.4237-0.925-2.7999 2.437-4.4002 4.623-8.1647 4.67-2.9221 0.023-3.4312 0.654-6.2164 1.573-0.5616 0.016-1.3964 0.319-2.1173 0.676z" style=""/>
   <path d="m63.131 99.331c0.2809-0.0077 0.3947 0.0454 0.6755 0.0377 0.1139 0.0531 0.2808-0.0077 0.5086 0.0985l0.1138 0.0531c0.744 0.4857 1.4879 0.9707 2.4519 1.2817 0.6832 0.319 1.5333 0.577 2.2695 0.781 3.1728 0.925 6.6642 1.167 9.6322 2.828-0.3716 0.797-0.7433 1.595-1.2896 2.172 0.2732-0.289 0.4324-0.631 0.4779-1.025 0.0531-0.114-0.0077-0.281-0.0685-0.448-0.0607-0.167-0.2277-0.106-0.3416-0.159-5.1461-1.152-10.565-2.016-14.43-5.62z" style=""/>
   <path d="m86.705 104.64c0.4247-0.911 1.2442-1.777 2.1851-2.308 0.941-0.532 2.1705-0.79 3.2253-1.269 0.1669-0.06 0.2808-7e-3 0.4478-0.068 2.1781-0.509 9.3419-0.924 11.102-2.321-1.54 1.2224-8.256 1.569-9.6217 3.011 2.9373 0.538 6.0157 0.898 9.0587 1.208-2.58 0.183-5.4229 0.21-8.0186-0.168-1.2979-0.19-2.5958-0.379-3.8861-0.288-2.4665 0.236-4.7732 2.21-4.5372 4.677 0.0077 0.281 0.0153 0.562 0.1899 0.782-0.2961-0.554-0.4784-1.055-0.4938-1.617-0.0684-0.447 0.0832-1.07 0.3487-1.639z" style=""/>
   <path d="m95.641 122.41c0.8879-0.418 2.1327-0.115 2.7628 0.318 1.1463 0.811 2.2399 1.737 3.4469 2.716 1.032 0.758 2.011 1.631 3.097 2.276 2.74 1.555 5.905 2.199 9.002 2.395 0 0 0.114 0.053 0.167-0.06 4.023 1.182 8.099 2.251 12.22 2.926-2.132 0.114-4.387-0.105-6.542-0.833-1.587-0.463-3.067-1.153-4.593-1.449-1.639-0.348-3.263-0.135-4.903-0.484-0.736-0.205-1.586-0.462-2.375-0.553 0.88 1.381 1.814 2.648 2.695 4.029-0.865-0.819-1.731-1.639-2.596-2.458-1.04-1.04-2.134-1.965-3.227-2.891-3.089-1.995-6.6114-3.36-9.1547-5.932z" style=""/>
   <path d="m34.254 100.01c0.5162 0.379 1.0932 0.926 1.6625 1.191 0.8655 0.82 1.6702 1.472 2.8619 1.889 1.4194 0.524 2.9981 0.705 4.4022 0.667 2.5274-0.069 4.9255-0.753 7.3237-1.437 1.1156-0.311 2.2919-0.456 3.3167 0.022-0.167 0.061-0.167 0.061-0.3339 0.122l0.1062-0.228c-3.5745 0.828-7.316 1.717-11.073 2.045-1.1233 0.03-2.2997 0.175-3.3775-0.189-1.9811-0.508-3.4919-2.321-4.889-4.082z" style=""/>
   <path d="m59.351 97.017c0.6525-0.8047 1.5935-1.3363 2.246-2.141 0.7663-0.7516 1.3049-1.6094 1.9043-2.3002 1.2595-1.2148 2.8152-1.8755 4.0217-2.9764 0.7132-0.6377 1.4796-1.3893 2.3067-1.974 1.548-0.9416 3.5745-0.8283 5.3201-0.7074 6.588 0.4383 13.062 0.8234 19.65 1.2617 1.0701 0.0831 2.4819 0.3256 2.7934 1.4412 0.0684 0.4478-0.0301 0.9563-0.1894 1.2979 0.0531-0.1139 0.1062-0.2277-0.0076-0.2808 0.0377-0.6755-0.7139-1.4419-1.2678-1.1457-0.744-0.4855-1.928-0.6218-2.7174-0.7126-6.1933-0.3929-12.44-0.6718-18.572-0.8977-0.6755-0.0378-1.351-0.0755-2.1326 0.1145-0.7817 0.1899-1.3357 0.4861-1.9427 0.8961-4.591 2.7109-9.3413 5.7634-12.482 10.121-0.0531 0.1138-0.1062 0.2277-0.1593 0.3415 0 0 0.1139 0.0531 0.2277 0.1062-0.2731 0.2885-0.607 0.4101-0.8802 0.6982 0.531-1.1383 1.0619-2.2769 1.8813-3.1423z" style=""/>
   <path d="m76.081 77.786c1.0849-1.4348 2.9214-2.1032 4.6441-2.8247 0.1669-0.0608 0.1669-0.0608 0.3339-0.1216 4.8193-0.5251 9.6917-1.164 14.496-2.2508-4.2801 1.7469-8.8564 2.9399-13.554 3.7989-1.0625 0.1976-2.0719 0.2814-3.1268 0.7599-2.5574 1.0253-4.0594 3.6518-5.2805 6.2706 0.0985-0.5085 0.417-1.1917 0.5232-1.4194 0.3186-0.6832 0.5841-1.2525 0.9026-1.9357 0.144-0.9032 0.4625-1.5863 1.0619-2.2772z" style=""/>
   <path d="m89.324 96.916c0.334-0.1215 0.8348-0.3038 1.1687-0.4253 0.1139 0.0531 0.2808-0.0077 0.2808-0.0077 1.0625-0.1977 2.125-0.3953 3.0737-0.646 1.8365-0.6685 3.0748-1.3986 4.099-3.0004 0.5386-0.8578 0.9515-2.7078 1.3763-3.6187 1.2208-2.6188 3.7278-4.199 5.6398-6.2184 1.692-1.8448 8.045-2.0951 10.702-3.0081 2.657-0.9129 3.279-0.8298 5.279-1.2162 3.204-0.6191 3.127-0.7599 4.69-1.1398 2.064-0.5623 3.832-1.6785 5.896-2.2407 1.397-0.3192 2.915-0.3045 4.197-0.6768 2.178-0.5091 3.992-2.02 5.874-3.0831 1.055-0.4785 2.11-0.9569 3.172-1.1546 3.385 0.4696 6.717 1.053 9.989 1.4695-1.397 0.3191-2.915 0.3044-4.38 0.1758-1.973-0.2271-3.848-0.9627-5.806-0.6282-2.239 0.3422-3.992 2.02-6.04 3.1439-3.21 1.8301-7.073 2.3853-10.762 3.1605-3.688 0.7752-6.519 0.9046-9.319 3.3417-2.633 2.3764-12.862 2.2885-15.153 4.8241-0.767 0.7517-1.472 1.6702-2.064 2.6419-0.432 0.6301-0.751 1.3132-1.123 2.1102-0.4243 0.9109-0.7959 1.708-1.3346 2.5658-0.2654 0.5693-0.6448 1.0855-1.0849 1.4347-0.4931 0.4632-1.2748 0.6531-1.8896 0.7823-2.1781 0.5092-4.3562 1.0183-6.4812 1.4136z" style=""/>
  </g>
 </g>
</svg>