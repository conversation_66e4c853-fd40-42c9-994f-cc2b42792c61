{"fqn": "indoor_horizontal_pm10_card", "name": "Indoor horizontal PM10 card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_horizontal_pm10_card_system_widget_image.png", "description": "Displays the latest indoor fine and coarse particulate matter (PM10) telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pm10', label: 'PM10', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM10\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 50 - 25;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 200) {\\n\\tvalue = 200;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:broom\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":150,\"color\":\"#FFA600\"},{\"from\":150,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":150,\"color\":\"#FFA600\"},{\"from\":150,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal temperature card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"µg/m³\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["environment", "particulate", "matter", "air", "pm10", "coarse particulates", "coarse particles", "particulate matter 10", "inhalable particles", "larger particulates", "dust", "airborne coarse particles"], "resources": [{"link": "/api/images/system/indoor_horizontal_pm10_card_system_widget_image.png", "title": "\"Indoor horizontal PM10 card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_horizontal_pm10_card_system_widget_image.png", "publicResourceKey": "vZYwH0iRMi2O3Di8zXlI6vWoowFU68yj", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAn1BMVEUAAADf39/f39/g4ODg4ODf39/////k5OT/pgDg4OD/9N//6b//3p//sSD/vED/04D/+u//x1/Ozs7IyMjz8/P5+fm2trbCwsJYWFja2trV1dW8vLz/zW/n5+etra3/2JCQkJD/tzD/qxD/46//78//wk87Ozv19fX/+e/t7e16enpmZmZKSkrx8fGenp7b29v/tjD9/f3Pz8+dnZ3/2Y/zJp/4AAAABnRSTlMAIEDfv1C6kOEmAAADdElEQVR42u3aCVPaQBjGcVvtw8tmNyEHVxIIp/W21/f/bN3dRGkR044MHaTPb0bmdcOM/F1iZOCMiIiIiIiIiIiIiIiIiIiIiIiIiIiI6F/68O75jI/nnfeue/7JdnQ+49373Pl4dpHhBHy+OOviFHztnkgIGHJsGHJsGHJsGHJs/ouQnkJtOpt1MC2A/AFIc7fQwZFpCenNTQ/e6CG9zEeXwM0dbkYzFLf5Hfay0l4PnhtaqZd36Omlag/ZdEiEJiTF7WB0m3ZczHSG2QPu9tuSSLwATuWGVrHBlqUYs1i9CGnvwOhuNMJodjOdNiFTjPYLWUukLQVLmT+GLOIXIRGURH8KqUq96fA74m9uL+/rkO8PuLzHPkJReBLL4jlEKX9u9mAFS4V60hK4Rb8CpQM4ZWhUa0ig642PsB3y/QZ1yP3d7Q32YgQ91LRE5ilEifHPu9iOcxGJJfT7Z1cDmbuVyD02f+5WsQQtIUEoErmSL2hzj/3IYi4SKv/EMtgVEstcV8aHKIldiJTL0kZUSyMRqhKQ+PWQShxfEuBwrkTWkfG/2VhWu0KUiM1c+hAtKxviRyPafb+2X2Yhy9dDAnku0Tig3grAXCqsJMauEP+4m9vwy2Z099RuVJXutf35DZ9LFA4tktI2RFovpAy2QvQmRIneEeK1nSPyVHJIK72qQ5Q0wq2QpTxvQyTqDSGIpBZgI8MO2fX2Qoa/1DyUueirwDFSrZoQ8adNjKuFqOZ+JsRbQlCKY4CNQR2T4XoMr99Hv5u4hS76ACbTCfrjcX2g35S3uDKyjsLmh/x2joiJSrEhKN20kNAW6DeFNHuit0LyPBmnaWKnIYbjYdEfDJM8T5Pi+lvRB5JJVhRjZN/8gSQt0E6tRSRUO0IWsghdCEo7u+tILL03hkDFImo7JBlkaTJJ6pBJ/mhnuzYZJ8PUh3THYxuSDiZpZnOHGdr5K/ULSsxm3U5a1jDxHv/9qgC/miaPw2SQp8PM7UhSjIeDbJAmdu26mKQJXEjf7cikmLoD+XCIN/AhaFRrhcCIPugLq/EEr8vTR1j7hvwQJ8ZBQ/r9toPZHlf8qNpERXEZ8KXu+8WQY8OQY8OQY2NDvuIUdM8uTuBtdiC7OJ0PDJx9Ou923rvzj6f0oRoiIiIiIiIiIiIiIiIiIiIiIiIiIiKif+Qnt477Z3V+66gAAAAASUVORK5CYII=", "public": true}]}