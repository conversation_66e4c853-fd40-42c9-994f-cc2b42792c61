{"fqn": "rainfall_chart_card", "name": "Rainfall chart card", "deprecated": false, "image": "tb-image;/api/images/system/rainfall_chart_card_system_widget_image.png", "description": "Displays a rainfall data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'rainfall', label: 'Rainfall', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'mm', decimals: 1 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'rainfall', 'mm', 1);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Rainfall\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 8) {\\n\\tvalue = 8;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"mm\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#7191EF\"},{\"from\":0,\"to\":2.5,\"color\":\"#4B70DD\"},{\"from\":2.5,\"to\":7.6,\"color\":\"#305AD7\"},{\"from\":7.6,\"to\":null,\"color\":\"#234CC7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 8) {\\n\\tvalue = 8;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"mm\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 2 - 1;\\nif (value < -3) {\\n\\tvalue = -3;\\n} else if (value > 3) {\\n\\tvalue = 3;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"mm\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Rainfall\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:weather-pouring\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "rain", "precipitation", "downpour", "rain shower", "drizzle", "raindrop", "cloudburst", "rainwater"], "resources": [{"link": "/api/images/system/rainfall_chart_card_system_widget_image.png", "title": "\"Rainfall chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "rainfall_chart_card_system_widget_image.png", "publicResourceKey": "7G4CuGyVXGwmAmbRTQjkuj8DklfvgnpA", "mediaType": "image/png", "data": "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", "public": true}]}