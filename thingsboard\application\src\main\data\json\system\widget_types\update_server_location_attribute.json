{"fqn": "input_widgets.update_server_location_attribute", "name": "Update server location attribute", "deprecated": false, "image": "tb-image;/api/images/system/update_shared_location_attribute_system_widget_image.png", "description": "Simple form to input new location for pre-defined server attribute key.", "descriptor": {"type": "latest", "sizeX": 7.5, "sizeY": 3, "resources": [], "templateHtml": "<div tb-toast toastTarget=\"{{ toastTargetId }}\" style=\"width: 100%; height: 100%;\">\n  <form *ngIf=\"attributeUpdateFormGroup\"\n        class=\"attribute-update-form\"\n        [formGroup]=\"attributeUpdateFormGroup\"\n        [class.small-width]=\"smallWidthContainer\"\n        (ngSubmit)=\"updateAttribute()\">\n    <div style=\"padding: 0 8px; margin: auto 0;\">\n      <div class=\"attribute-update-form__grid\" [class.!hidden]=\"!entityDetected || !isValidParameter || !dataKeyDetected\">\n        <div class=\"grid__element\"\n             [class.horizontal-alignment]=\"isHorizontal && !changeAlignment\">\n          <mat-form-field class=\"mat-block\"\n                          floatLabel=\"{{settings.showLabel ? 'auto' : 'always'}}\"\n                          [hideRequiredMarker]=\"!settings.showLabel\">\n            <mat-label>{{ settings.showLabel ? latLabel : '' }}</mat-label>\n            <input matInput\n                   formControlName=\"currentLat\"\n                   [required]=\"settings.isLatRequired\"\n                   type=\"number\"\n                   (focus)=\"isFocused = true\"\n                   (blur)=\"changeFocus()\"\n                   max=\"90\"\n                   min=\"-90\"/>\n            <mat-error *ngIf=\"attributeUpdateFormGroup.get('currentLat').hasError('required') && settings.isLatRequired\">\n              {{requiredErrorMessage}}\n            </mat-error>\n          </mat-form-field>\n\n          <mat-form-field class=\"mat-block\"\n                          floatLabel=\"{{settings.showLabel ? 'auto' : 'always'}}\"\n                          [hideRequiredMarker]=\"!settings.showLabel\">\n            <mat-label>{{ settings.showLabel ? lngLabel : '' }}</mat-label>\n            <input matInput\n                   formControlName=\"currentLng\"\n                   [required]=\"settings.isLngRequired\"\n                   type=\"number\"\n                   (focus)=\"isFocused = true\"\n                   (blur)=\"changeFocus()\"\n                   max=\"180\"\n                   min=\"-180\"/>\n            <mat-error *ngIf=\"attributeUpdateFormGroup.get('currentLng').hasError('required') && settings.isLngRequired\">\n              {{requiredErrorMessage}}\n            </mat-error>\n          </mat-form-field>\n        </div>\n\n        <div class=\"grid__element\">\n          <button mat-icon-button class=\"getLocation\"\n                  type=\"button\"\n                  (click)=\"getCoordinate()\"\n                  *ngIf=\"settings.showGetLocation\"\n                  matTooltip=\"{{ 'widgets.input-widgets.get-location' | translate }}\"\n                  matTooltipPosition=\"above\">\n            <mat-icon>my_location</mat-icon>\n          </button>\n          <button mat-icon-button class=\"applyChanges\"\n                  type=\"submit\"\n                  [disabled]=\"disableButton() || attributeUpdateFormGroup.invalid || attributeUpdateFormGroup.pristine\"\n                  matTooltip=\"{{ 'widgets.input-widgets.update-attribute' | translate }}\"\n                  matTooltipPosition=\"above\">\n            <mat-icon>check</mat-icon>\n          </button>\n          <button mat-icon-button class=\"discardChanges\"\n                  type=\"button\"\n                  [disabled]=\"disableButton()\"\n                  (click)=\"discardChange()\"\n                  matTooltip=\"{{ 'widgets.input-widgets.discard-changes' | translate }}\"\n                  matTooltipPosition=\"above\">\n            <mat-icon>close</mat-icon>\n          </button>\n        </div>\n      </div>\n\n      <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\" [class.!hidden]=\"entityDetected\">\n        {{ 'widgets.input-widgets.no-entity-selected' | translate }}\n      </div>\n      <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\"\n           [class.!hidden]=\"!entityDetected || dataKeyDetected\">\n        {{ 'widgets.input-widgets.no-attribute-selected' | translate }}\n      </div>\n      <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\"\n           [class.!hidden]=\"!entityDetected || isValidParameter\">\n        {{ 'widgets.input-widgets.no-coordinate-specified' | translate }}\n      </div>\n    </div>\n  </form>\n</div>", "templateCss": ".attribute-update-form {\n    overflow: hidden;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n}\n\n.attribute-update-form__grid {\n    display: flex;\n}\n.grid__element:first-child {\n    flex-direction: column;\n    flex: 1;\n}\n\n.grid__element.horizontal-alignment {\n    flex-direction: row;\n}\n\n.grid__element:last-child {\n    align-items: center;\n    margin-left: 7px;\n}\n.grid__element {\n    display: flex;\n}\n\n.attribute-update-form .mat-button.mat-icon-button {\n    width: 32px;\n    min-width: 32px;\n    height: 32px;\n    min-height: 32px;\n    padding: 0 !important;\n    margin: 0;\n    line-height: 20px;\n}\n\n.attribute-update-form .mat-button.getLocation {\n    margin-right: 10px;\n}\n\n.attribute-update-form .mat-icon-button mat-icon {\n    width: 20px;\n    min-width: 20px;\n    height: 20px;\n    min-height: 20px;\n    font-size: 20px;\n}\n\n.attribute-update-form mat-form-field{\n    width: 100%;\n    padding-right: 5px;\n}\n\n.attribute-update-form.small-width mat-form-field{\n    width: 150px;\n}\n\n.tb-toast {\n    font-size: 14px!important;\n}", "controllerScript": "let $scope;\r\nlet settings;\r\nlet attributeService;\r\nlet utils;\r\nlet translate;\r\n\r\nself.onInit = function() {\r\n    self.ctx.ngZone.run(function() {\r\n       init(); \r\n       self.ctx.detectChanges(true);\r\n    });\r\n};\r\n\r\nfunction init() {\r\n    $scope = self.ctx.$scope;\r\n    attributeService = $scope.$injector.get(self.ctx.servicesMap.get('attributeService'));\r\n    utils = $scope.$injector.get(self.ctx.servicesMap.get('utils'));\r\n    translate = $scope.$injector.get(self.ctx.servicesMap.get('translate'));\r\n    $scope.toastTargetId = 'input-widget' + utils.guid();\r\n    settings = utils.deepClone(self.ctx.settings) || {};\r\n    \r\n    settings.showLabel = utils.defaultValue(settings.showLabel, true);\r\n    settings.showResultMessage = utils.defaultValue(settings.showResultMessage, true);\r\n    settings.showGetLocation = utils.defaultValue(settings.showGetLocation, true);\r\n    settings.enableHighAccuracy = utils.defaultValue(settings.enableHighAccuracy, false);\r\n    settings.isLatRequired = utils.defaultValue(settings.isLatRequired, true);\r\n    settings.isLngRequired = utils.defaultValue(settings.isLngRequired, true);\r\n    $scope.settings = settings;\r\n    $scope.isValidParameter = true;\r\n    $scope.dataKeyDetected = false; \r\n\r\n    $scope.isHorizontal = (settings.inputFieldsAlignment === 'row');\r\n    $scope.requiredErrorMessage = utils.customTranslation(settings.requiredErrorMessage, settings.requiredErrorMessage) || translate.instant('widgets.input-widgets.entity-coordinate-required');\r\n    $scope.latLabel = utils.customTranslation(settings.latLabel, settings.latLabel) || translate.instant('widgets.input-widgets.latitude');\r\n    $scope.lngLabel = utils.customTranslation(settings.lngLabel, settings.lngLabel) || translate.instant('widgets.input-widgets.longitude');\r\n    \r\n    var validatorsLat = [$scope.validators.min(-90), $scope.validators.max(90)];\r\n    var validatorsLng = [$scope.validators.min(-180), $scope.validators.max(180)];\r\n    \r\n    if (settings.isLatRequired) {\r\n        validatorsLat.push($scope.validators.required);\r\n    }\r\n    \r\n    if (settings.isLngRequired) {\r\n        validatorsLng.push($scope.validators.required);\r\n    }\r\n\r\n    $scope.attributeUpdateFormGroup = $scope.fb.group({\r\n        currentLat: [undefined, validatorsLat],\r\n        currentLng: [undefined, validatorsLng]\r\n    });\r\n\r\n    if (self.ctx.datasources && self.ctx.datasources.length) {\r\n        var datasource = self.ctx.datasources[0];\r\n        if (datasource.type === 'entity') {\r\n            if (datasource.entityType && datasource.entityId) {\r\n                $scope.entityName = datasource.entityName;\r\n                if (settings.widgetTitle && settings.widgetTitle.length) {\r\n                    $scope.titleTemplate = utils.customTranslation(settings.widgetTitle, settings.widgetTitle);\r\n                } else {\r\n                    $scope.titleTemplate = self.ctx.widgetConfig.title;\r\n                }\r\n\r\n                $scope.entityDetected = true;\r\n            }\r\n        }\r\n        if (datasource.dataKeys.length > 1) {\r\n            $scope.dataKeyDetected = true;\r\n            for (let i = 0; i < datasource.dataKeys.length; i++) {\r\n                if (datasource.dataKeys[i].type != \"attribute\") {\r\n                    $scope.isValidParameter = false;\r\n                }\r\n                if (datasource.dataKeys[i].name !== settings.latKeyName && datasource.dataKeys[i].name !== settings.lngKeyName){\r\n                    $scope.dataKeyDetected = false;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    self.ctx.widgetTitle = utils.createLabelFromDatasource(self.ctx.datasources[0], $scope.titleTemplate);\r\n\r\n    $scope.updateAttribute = function () {\r\n        $scope.isFocused = false;\r\n        if ($scope.entityDetected) {\r\n            var datasource = self.ctx.datasources[0];\r\n\r\n            attributeService.saveEntityAttributes(\r\n                datasource.entity.id,\r\n                'SERVER_SCOPE',\r\n                [\r\n                    {\r\n                        key: settings.latKeyName,\r\n                        value: $scope.attributeUpdateFormGroup.get('currentLat').value\r\n                    },{\r\n                        key: settings.lngKeyName,\r\n                        value: $scope.attributeUpdateFormGroup.get('currentLng').value\r\n                    }\r\n                ]\r\n            ).subscribe(\r\n                function success() {\r\n                    $scope.originalLat = $scope.attributeUpdateFormGroup.get('currentLat').value;\r\n                    $scope.originalLng = $scope.attributeUpdateFormGroup.get('currentLng').value;\r\n                    if (settings.showResultMessage) {\r\n                        $scope.showSuccessToast(translate.instant('widgets.input-widgets.update-successful'), 1000, 'bottom', 'left', $scope.toastTargetId);\r\n                    }\r\n                },\r\n                function fail() {\r\n                    if (settings.showResultMessage) {\r\n                        $scope.showErrorToast(translate.instant('widgets.input-widgets.update-failed'), 'bottom', 'left', $scope.toastTargetId);\r\n                    }\r\n                }\r\n            );\r\n        }\r\n    };\r\n\r\n    $scope.changeFocus = function () {\r\n        if ($scope.attributeUpdateFormGroup.get('currentLat').value === $scope.originalLat && $scope.attributeUpdateFormGroup.get('currentLng').value === $scope.originalLng) {\r\n            $scope.isFocused = false;\r\n        }\r\n    };\r\n    \r\n    $scope.discardChange = function() {\r\n        $scope.attributeUpdateFormGroup.setValue({\r\n            'currentLat': $scope.originalLat,\r\n            'currentLng': $scope.originalLng\r\n        });\r\n        $scope.isFocused = false;\r\n        $scope.attributeUpdateFormGroup.markAsPristine();\r\n        self.onDataUpdated();\r\n    };\r\n    \r\n    $scope.disableButton = function () {\r\n        return $scope.attributeUpdateFormGroup.get('currentLat').value === $scope.originalLat && $scope.attributeUpdateFormGroup.get('currentLng').value === $scope.originalLng || $scope.currentLng === null || $scope.currentLat === null;\r\n    };\r\n    \r\n    $scope.getCoordinate = function() {\r\n        if (navigator.geolocation) {\r\n            navigator.geolocation.getCurrentPosition(showPosition, function (){\r\n                $scope.showErrorToast(translate.instant('widgets.input-widgets.blocked-location'), \r\n                                    'bottom', 'left', $scope.toastTargetId);\r\n            }, {\r\n                enableHighAccuracy: settings.enableHighAccuracy\r\n            });\r\n        } else {\r\n            $scope.showErrorToast(translate.instant('widgets.input-widgets.no-support-geolocation'), 'bottom', 'left', $scope.toastTargetId);\r\n        }\r\n    };\r\n    \r\n    function showPosition(position) {\r\n        $scope.attributeUpdateFormGroup.setValue({\r\n            currentLat: correctValue(position.coords.latitude),\r\n            currentLng: correctValue(position.coords.longitude)\r\n        });\r\n        $scope.attributeUpdateFormGroup.markAsDirty();\r\n        $scope.isFocused = true;\r\n    }\r\n    \r\n    self.onResize();\r\n}\r\n\r\nself.onDataUpdated = function() {\r\n    try {\r\n        if ($scope.dataKeyDetected) {\r\n            if (!$scope.isFocused) {\r\n                for(let i = 0; i < self.typeParameters().maxDataKeys; i++){\r\n                    if(self.ctx.data[i].dataKey.name === self.ctx.settings.latKeyName && $scope.attributeUpdateFormGroup.get('currentLat').pristine){\r\n                        $scope.originalLat = self.ctx.data[i].data[0][1];\r\n                        $scope.attributeUpdateFormGroup.get('currentLat').patchValue(correctValue($scope.originalLat));\r\n                    } else if(self.ctx.data[i].dataKey.name === self.ctx.settings.lngKeyName && $scope.attributeUpdateFormGroup.get('currentLng').pristine){\r\n                        $scope.originalLng = self.ctx.data[i].data[0][1];\r\n                        $scope.attributeUpdateFormGroup.get('currentLng').patchValue(correctValue($scope.originalLng));\r\n                    }\r\n                }\r\n                self.ctx.detectChanges();\r\n            }\r\n        }\r\n    } catch (e) {\r\n        console.log(e);\r\n    }\r\n};\r\n\r\nfunction correctValue(value) {\r\n    if (typeof value !== \"number\") {\r\n        return 0;\r\n    }\r\n    return value;\r\n}\r\n\r\nself.onResize = function() {\r\n    $scope.smallWidthContainer = (self.ctx.$container && self.ctx.$container[0].offsetWidth < 320);\r\n    $scope.changeAlignment = ($scope.isHorizontal && self.ctx.$container && self.ctx.$container[0].offsetWidth < 480);\r\n    self.ctx.detectChanges();\r\n};\r\n\r\nself.typeParameters = function() {\r\n    return {\r\n        maxDatasources: 1,\r\n        maxDataKeys: 2,\r\n        singleEntity: true\r\n    };\r\n};\r\n\r\nself.onDestroy = function() {\r\n\r\n};", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-update-location-attribute-widget-settings", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"widgetTitle\":\"\",\"showResultMessage\":true,\"latKeyName\":\"latitude\",\"lngKeyName\":\"longitude\",\"showGetLocation\":true,\"enableHighAccuracy\":false,\"showLabel\":true,\"latLabel\":\"\",\"lngLabel\":\"\",\"inputFieldsAlignment\":\"column\",\"isLatRequired\":true,\"isLngRequired\":true,\"requiredErrorMessage\":\"\"},\"title\":\"Update server location attribute\",\"dropShadow\":true,\"enableFullscreen\":false,\"widgetStyle\":{},\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{}}"}, "resources": [{"link": "/api/images/system/update_shared_location_attribute_system_widget_image.png", "title": "\"Update shared location attribute\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "update_shared_location_attribute_system_widget_image.png", "publicResourceKey": "MYpPNf9VpeEsUOFMgp6tbT39oFQ0jqL3", "mediaType": "image/png", "data": "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", "public": true}]}