{"fqn": "pm2_5_chart_card", "name": "PM2.5 chart card", "deprecated": false, "image": "tb-image;/api/images/system/pm2_5_chart_card_system_widget_image.png", "description": "Displays a fine particulate matter (PM2.5) data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pm2.5', label: 'PM2.5', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'µg/m³', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'pm2.5', 'µg/m³', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM2.5\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 120 - 60;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#80C32C\"},{\"from\":10,\"to\":35,\"color\":\"#FFA600\"},{\"from\":35,\"to\":75,\"color\":\"#F36900\"},{\"from\":75,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 120 - 60;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nif (value < -10) {\\n\\tvalue = -10;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"PM2.5\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"bubble_chart\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "pm2.5", "particulate", "matter", "air", "fine particulates", "fine particles", "particulate matter 2.5", "airborne particles", "microscopic particles", "respirable particles", "aerosol particles", "dust particles"], "resources": [{"link": "/api/images/system/pm2_5_chart_card_system_widget_image.png", "title": "\"PM2.5 chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "pm2_5_chart_card_system_widget_image.png", "publicResourceKey": "0sFDKanScx2msa5dAPDz35wEqUYS6pld", "mediaType": "image/png", "data": "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", "public": true}]}