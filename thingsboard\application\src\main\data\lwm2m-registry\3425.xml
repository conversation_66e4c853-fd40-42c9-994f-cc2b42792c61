<?xml version="1.0" encoding="UTF-8"?>

<!-- BSD-3 Clause License

Copyright 2020 uCIFI Alliance

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>Irrigation valve</Name>
		<Description1>The uCIFI irrigation valve provides a way to control and monitor an irrigation valve.</Description1>
		<ObjectID>3425</ObjectID>
		<ObjectURN>urn:oma:lwm2m:ext:3425</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="1">
				<Name>Default status</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..100</RangeEnumeration>
				<Units>/100</Units>
				<Description>Default status (level of opening of the valve from 0 to 100%) that is applied when the device is powered ON.</Description>
			</Item>
			<Item ID="2">
				<Name>Status</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..100</RangeEnumeration>
				<Units>/100</Units>
				<Description>Actual level of opening of the valve (from 0 to 100%).</Description>
			</Item>
			<Item ID="3">
				<Name>Command</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..100</RangeEnumeration>
				<Units>/100</Units>
				<Description>Command (e.g. manual override, scheduler) sent to the irrigation valve.</Description>
			</Item>
			<Item ID="4">
				<Name>Command in action</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..100</RangeEnumeration>
				<Units>/100</Units>
				<Description>For devices connected on slow LPWAN networks, the command in action (this resource) may differ from a command that was sent (resource ID: 3). The command in action is the actual value of the command in action on the irrigation valve.</Description>
			</Item>
			<Item ID="5">
				<Name>Scheduler ID</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Identifiers of the schedulers that are assigned to the Command of this irrigation valve.</Description>
			</Item>
			<Item ID="6">
				<Name>Invalid scheduler</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Set to True when one of the schedulers can’t be executed or is not supported by this irrigation valve. Otherwise set to False.</Description>
			</Item>
		</Resources>
		<Description2></Description2>
	</Object>
</LWM2M>
