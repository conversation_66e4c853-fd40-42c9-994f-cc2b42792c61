{"fqn": "horizontal_capsule_tank", "name": "Horizontal capsule tank", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_capsule_tank_system_widget_image.png", "description": "Widget indicates the level of liquid in Horizontal capsule tank.", "descriptor": {"type": "latest", "sizeX": 4, "sizeY": 4, "resources": [], "templateHtml": "<tb-liquid-level-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-liquid-level-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.liquidLevelWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.liquidLevelWidget.update();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true\n    };\n};\n\nself.onDestroy = function() {\n}\n\nself.actionSources = function() {    \n    return {        \n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false        \n        }    \n    };\n}", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-liquid-level-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-liquid-level-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"return Math.floor(Math.random() * 101);\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"tankSelectionType\":\"static\",\"selectedShape\":\"Horizontal Capsule\",\"shapeAttributeName\":\"tankShape\",\"tankColor\":{\"type\":\"range\",\"color\":\"#242770\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E73535DE\"},{\"from\":20,\"to\":null,\"color\":\"#242770\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E73535DE';\\n  }\\n}\\nreturn '#242770';\"},\"datasourceUnits\":\"%\",\"layout\":\"percentage\",\"volumeSource\":\"static\",\"volumeConstant\":500,\"volumeAttributeName\":\"volume\",\"volumeUnits\":\"L\",\"volumeFont\":{\"family\":\"Roboto\",\"size\":14,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"volumeColor\":\"rgba(0, 0, 0, 0.18)\",\"units\":\"%\",\"widgetUnitsSource\":\"static\",\"widgetUnitsAttributeName\":\"units\",\"liquidColor\":{\"type\":\"range\",\"color\":\"#7A8BFF\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E27C7CDE\"},{\"from\":20,\"to\":null,\"color\":\"#7A8BFF\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FF0000DE\"},{\"from\":20,\"to\":null,\"color\":\"rgba(0,0,0,0.87)\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FF0000DE';\\n  }\\n}\\nreturn '#000000DE';\"},\"showBackgroundOverlay\":true,\"backgroundOverlayColor\":{\"type\":\"range\",\"color\":\"#FFFFFFC2\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FFEFEFDE\"},{\"from\":20,\"to\":null,\"color\":\"#FFFFFFC2\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FFEFEFDE';\\n  }\\n}\\nreturn '#FFFFFFC2';\"},\"showTooltip\":true,\"showTooltipLevel\":true,\"tooltipUnits\":\"%\",\"tooltipLevelDecimals\":0,\"tooltipLevelFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipLevelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.76)\",\"rangeList\":[],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"showTooltipDate\":true,\"tooltipDateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"tooltipDateFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipDateColor\":\"rgba(0, 0, 0, 0.76)\",\"tooltipBackgroundColor\":\"rgba(255, 255, 255, 0.76)\",\"tooltipBackgroundBlur\":3,\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Liquid level\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"titleFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"1.5\"},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"showTitleIcon\":false,\"titleIcon\":\"water_drop\",\"iconColor\":\"#5469FF\",\"decimals\":0,\"enableDataExport\":false,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\"}"}, "tags": ["reservoir", "container", "vessel", "storage unit", "cistern", "canister", "vat", "basin", "repository", "bin", "hopper"], "resources": [{"link": "/api/images/system/horizontal_capsule_tank_system_widget_image.png", "title": "\"Horizontal capsule tank\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_capsule_tank_system_widget_image.png", "publicResourceKey": "jzPQc4BHycU3CDVn8nKZJ7u6vDXcKeIt", "mediaType": "image/png", "data": "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", "public": true}]}