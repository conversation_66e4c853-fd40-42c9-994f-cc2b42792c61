{"fqn": "input_widgets.update_shared_image_attribute", "name": "Update shared image attribute", "deprecated": false, "image": "tb-image;/api/images/system/update_shared_image_attribute_system_widget_image.png", "description": "Simple form to input new image for pre-defined shared attribute key.", "descriptor": {"type": "latest", "sizeX": 7.5, "sizeY": 3.5, "resources": [], "templateHtml": "<div tb-toast toastTarget=\"{{ toastTargetId }}\" style=\"width: 100%; height: 100%;\">\n  <form *ngIf=\"attributeUpdateFormGroup\"\n        class=\"attribute-update-form\"\n        [formGroup]=\"attributeUpdateFormGroup\"\n        (ngSubmit)=\"updateAttribute()\">\n    <div style=\"padding: 0 8px; margin: auto 0;\">\n      <div class=\"attribute-update-form__grid\" [class.!hidden]=\"!entityDetected || !isValidParameter || !dataKeyDetected\">\n        <div class=\"grid__element\">\n          <tb-image-input class=\"flex-1\"\n                          formControlName=\"currentValue\"\n                          [showPreview]=\"settings.displayPreview\"\n                          [showClearButton]=\"settings.displayClearButton\"\n          >\n          </tb-image-input>\n        </div>\n\n        <div class=\"grid__element\">\n          <button *ngIf=\"settings.displayApplyButton\" mat-icon-button class=\"applyChanges\"\n                  type=\"submit\"\n                  [disabled]=\"originalValue === attributeUpdateFormGroup.get('currentValue').value || attributeUpdateFormGroup.invalid || !attributeUpdateFormGroup.dirty\"\n                  matTooltip=\"{{ 'widgets.input-widgets.update-attribute' | translate }}\"\n                  matTooltipPosition=\"above\">\n            <mat-icon>check</mat-icon>\n          </button>\n          <button *ngIf=\"settings.displayDiscardButton\" mat-icon-button class=\"discardChanges\"\n                  type=\"button\"\n                  [disabled]=\"originalValue === attributeUpdateFormGroup.get('currentValue').value || !attributeUpdateFormGroup.dirty && settings.displayApplyButton\"\n                  (click)=\"attributeUpdateFormGroup.get('currentValue').patchValue(originalValue)\"\n                  matTooltip=\"{{ 'widgets.input-widgets.discard-changes' | translate }}\"\n                  matTooltipPosition=\"above\">\n            <mat-icon>close</mat-icon>\n          </button>\n        </div>\n      </div>\n\n      <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\" [class.!hidden]=\"entityDetected\" [innerHtml]=\"message\"></div>\n      <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\"\n           [class.!hidden]=\"!entityDetected || dataKeyDetected\">\n        {{ 'widgets.input-widgets.no-attribute-selected' | translate }}\n      </div>\n      <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\"\n           [class.!hidden]=\"!entityDetected || isValidParameter\">\n        {{ 'widgets.input-widgets.timeseries-not-allowed' | translate }}\n      </div>\n    </div>\n  </form>\n</div>", "templateCss": ".attribute-update-form {\n    overflow: hidden;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n}\n\n.attribute-update-form__grid {\n    display: flex;\n}\n.grid__element:first-child {\n    flex: 1;\n}\n.grid__element:last-child {\n    align-items: center;\n    margin-left: 7px;\n}\n.grid__element {\n    display: flex;\n}\n\n.attribute-update-form .mat-button.mat-icon-button {\n    width: 32px;\n    min-width: 32px;\n    height: 32px;\n    min-height: 32px;\n    padding: 0 !important;\n    margin: 0 !important;\n    line-height: 20px;\n}\n\n.tb-image-preview-container div,\n.tb-flow-drop label {\n    font-size: 16px !important;\n}\n\n.attribute-update-form .mat-icon-button mat-icon {\n    width: 20px;\n    min-width: 20px;\n    height: 20px;\n    min-height: 20px;\n    font-size: 20px;\n}\n\n.tb-toast {\n    font-size: 14px!important;\n}", "controllerScript": "let $scope;\r\nlet settings;\r\nlet attributeService;\r\nlet utils;\r\nlet translate;\r\n\r\nself.onInit = function() {\r\n    self.ctx.ngZone.run(function() {\r\n       init(); \r\n       self.ctx.detectChanges(true);\r\n    });\r\n};\r\n\r\nfunction init() {\r\n\r\n    $scope = self.ctx.$scope;\r\n    attributeService = $scope.$injector.get(self.ctx.servicesMap.get('attributeService'));\r\n    utils = $scope.$injector.get(self.ctx.servicesMap.get('utils'));\r\n    translate = $scope.$injector.get(self.ctx.servicesMap.get('translate'));\r\n    $scope.toastTargetId = 'input-widget' + utils.guid();\r\n    settings = utils.deepClone(self.ctx.settings) || {};\r\n    settings.showResultMessage = utils.defaultValue(settings.showResultMessage, true);\r\n    settings.displayPreview = utils.defaultValue(settings.displayPreview, true);\r\n    settings.displayClearButton = utils.defaultValue(settings.displayClearButton, false);\r\n    settings.displayApplyButton = utils.defaultValue(settings.displayApplyButton, true);\r\n    settings.displayDiscardButton = utils.defaultValue(settings.displayDiscardButton, true);\r\n    $scope.settings = settings;\r\n    $scope.isValidParameter = true;\r\n    $scope.dataKeyDetected = false;\r\n    $scope.entityDetected = false;\r\n    $scope.message = translate.instant('widgets.input-widgets.no-entity-selected');\r\n    \r\n    $scope.attributeUpdateFormGroup = $scope.fb.group(\r\n        {currentValue: [undefined, []]}\r\n    );\r\n    \r\n    $scope.attributeUpdateFormGroup.valueChanges.subscribe( () => {\r\n        self.ctx.detectChanges();\r\n         if (!settings.displayApplyButton) {\r\n            $scope.updateAttribute();\r\n        }\r\n    });\r\n\r\n    if (self.ctx.datasources && self.ctx.datasources.length) {\r\n        var datasource = self.ctx.datasources[0];\r\n        \r\n        if (datasource.type === 'entity') {\r\n            if (datasource.entityType === 'DEVICE') {\r\n                if (datasource.entityType && datasource.entityId) {\r\n                    $scope.entityName = datasource.entityName;\r\n                    if (settings.widgetTitle && settings.widgetTitle.length) {\r\n                        $scope.titleTemplate = utils.customTranslation(settings.widgetTitle, settings.widgetTitle);\r\n                    } else {\r\n                        $scope.titleTemplate = self.ctx.widgetConfig.title;\r\n                    }\r\n    \r\n                    $scope.entityDetected = true;\r\n                }\r\n            } else {\r\n                $scope.message = translate.instant('widgets.input-widgets.not-allowed-entity');\r\n            }\r\n        }\r\n        if (datasource.dataKeys.length) {\r\n            if (datasource.dataKeys[0].type !== \"attribute\") {\r\n                $scope.isValidParameter = false;\r\n            } else {\r\n                $scope.currentKey = datasource.dataKeys[0].name;\r\n                $scope.dataKeyType = datasource.dataKeys[0].type;\r\n                $scope.dataKeyDetected = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    self.ctx.widgetTitle = utils.createLabelFromDatasource(self.ctx.datasources[0], $scope.titleTemplate);\r\n    \r\n    $scope.updateAttribute = function () {\r\n        if ($scope.entityDetected) {\r\n            var datasource = self.ctx.datasources[0];\r\n\r\n            attributeService.saveEntityAttributes(\r\n                datasource.entity.id,\r\n                'SHARED_SCOPE',\r\n                [\r\n                    {\r\n                        key: $scope.currentKey,\r\n                        value: $scope.attributeUpdateFormGroup.get('currentValue').value\r\n                    }\r\n                ]\r\n            ).subscribe(\r\n                function success() {\r\n                    if (settings.displayApplyButton) {\r\n                        $scope.originalValue = $scope.attributeUpdateFormGroup.get('currentValue').value;\r\n                    }\r\n                    if (settings.showResultMessage) {\r\n                        $scope.showSuccessToast(translate.instant('widgets.input-widgets.update-successful'), 1000, 'bottom', 'left', $scope.toastTargetId);\r\n                    }\r\n                },\r\n                function fail() {\r\n                    if (settings.showResultMessage) {\r\n                        $scope.showErrorToast(translate.instant('widgets.input-widgets.update-failed'), 'bottom', 'left', $scope.toastTargetId);\r\n                    }\r\n                }\r\n            );\r\n        }\r\n    };\r\n}\r\n\r\nself.onDataUpdated = function() {\r\n    try {\r\n        if ($scope.dataKeyDetected) {\r\n            var value = self.ctx.data[0].data[0][1];\r\n            if (settings.displayApplyButton || !$scope.originalValue) {\r\n                $scope.originalValue = value;\r\n            }\r\n            $scope.attributeUpdateFormGroup.get('currentValue').patchValue(value, {emitEvent: false});\r\n            self.ctx.detectChanges();\r\n        }\r\n    } catch (e) {\r\n        console.log(e);\r\n    }\r\n};\r\n\r\nself.onResize = function() {\r\n\r\n};\r\n\r\nself.typeParameters = function() {\r\n    return {\r\n        maxDatasources: 1,\r\n        maxDataKeys: 1,\r\n        singleEntity: true\r\n    };\r\n};\r\n\r\nself.onDestroy = function() {\r\n    $scope.attributeUpdateFormGroup.valueChanges.unsubscribe();\r\n}", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-update-image-attribute-widget-settings", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Sin\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.23592248334107624,\"funcBody\":\"return Math.round(1000*Math.sin(time/5000));\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"showResultMessage\":true,\"displayPreview\":true,\"displayClearButton\":false,\"displayApplyButton\":true,\"displayDiscardButton\":true},\"title\":\"Update shared image attribute\",\"dropShadow\":true,\"enableFullscreen\":false,\"enableDataExport\":false,\"widgetStyle\":{},\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{}}"}, "resources": [{"link": "/api/images/system/update_shared_image_attribute_system_widget_image.png", "title": "\"Update shared image attribute\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "update_shared_image_attribute_system_widget_image.png", "publicResourceKey": "zniV26hjYvld0fu4lSa8gAaYrPbm2Tct", "mediaType": "image/png", "data": "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", "public": true}]}