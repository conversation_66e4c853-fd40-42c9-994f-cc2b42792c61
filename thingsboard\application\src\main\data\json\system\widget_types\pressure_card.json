{"fqn": "pressure_card", "name": "Pressure card", "deprecated": false, "image": "tb-image;/api/images/system/pressure_card_system_widget_image.png", "description": "Displays the latest pressure telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pressure', label: 'Pressure', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < 980) {\\n\\tvalue = 980;\\n} else if (value > 1040) {\\n\\tvalue = 1040;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"compress\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1000,\"color\":\"#D81838\"},{\"from\":1000,\"to\":1020,\"color\":\"#80C32C\"},{\"from\":1020,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":1000,\"color\":\"#D81838\"},{\"from\":1000,\"to\":1020,\"color\":\"#80C32C\"},{\"from\":1020,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Pressure card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"hPa\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "barometry"], "resources": [{"link": "/api/images/system/pressure_card_system_widget_image.png", "title": "\"Pressure card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "pressure_card_system_widget_image.png", "publicResourceKey": "gVJAQX0VhbNORS1kpuUrMJ3Y2feFRaYo", "mediaType": "image/png", "data": "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", "public": true}]}