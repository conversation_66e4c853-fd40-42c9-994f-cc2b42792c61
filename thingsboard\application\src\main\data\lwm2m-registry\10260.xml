<?xml version="1.0" encoding="utf-8"?>

<!-- BSD-3 Clause License

Copyright 2019 Casa Systems. 

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
-->
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>RDB</Name>
    <Description1><![CDATA[This object allows manipulation of Runtime Database variables.]]></Description1>
    <ObjectID>10260</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10260:2.0</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>2.0</ObjectVersion>
    <MultipleInstances>Multiple</MultipleInstances>
    <Mandatory>Optional</Mandatory>
    <Resources>
      <Item ID="0">
        <Name>Key</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[The name of the RDB variable that this instance currently represents. Writing to this resource will not rename or otherwise modify the associated RDB variable, rather it will re-assign the instance to represent a different variable.]]></Description>
      </Item>
      <Item ID="1">
        <Name>Value</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>String</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[The current value of the RDB variable associated with this instance. Reading this resource will return a "Not Found" response code if the associated variable does not exist. Writing to this resource will create the associated variable if it does not already exist.]]></Description>
      </Item>
      <Item ID="2">
        <Name>Exists</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Boolean</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[Indicates whether the RDB variable associated with this instance currently exists. Writing FALSE to this resource will delete the associated variable if it exists. Writing TRUE to this resource will create the associated variable if it does not already exist. Variables created by this resource will be initialised to an empty string.]]></Description>
      </Item>
      <Item ID="3">
        <Name>Persistent</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Boolean</Type>
        <RangeEnumeration>
        </RangeEnumeration>
        <Units>
        </Units>
        <Description><![CDATA[Configure whether or not the RDB variable associated with this instance will be saved to persistent storage.]]></Description>
      </Item>
    </Resources>
    <Description2 />
  </Object>
</LWM2M>
