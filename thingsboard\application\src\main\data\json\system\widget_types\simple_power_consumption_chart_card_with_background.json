{"fqn": "simple_power_consumption_chart_card_with_background", "name": "Simple power consumption chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_power_consumption_chart_card_with_background.svg", "description": "Displays historical power consumption values as a simplified chart with background. Optionally may display the corresponding latest power consumption value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'powerConsumption', label: 'Power consumption', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'powerConsumption', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Power consumption\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":5,\"color\":\"#3B911C\"},{\"from\":5,\"to\":15,\"color\":\"#F77410\"},{\"from\":15,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/simple_power_consumption_chart_card_background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":4}}},\"title\":\"Power consumption\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"bolt\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"units\":\"kW\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true},\"margin\":\"0px\"}"}, "tags": ["power", "energy", "energy usage", "electric load", "electricity", "power efficiency", "load profile"], "resources": [{"link": "/api/images/system/simple_power_consumption_chart_card_background.png", "title": "simple_power_consumption_chart_card_background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_power_consumption_chart_card_background.png", "publicResourceKey": "IcUAYfeTP643CzpZsDlrr04rP3RSrPqZ", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_power_consumption_chart_card_with_background.svg", "title": "simple_power_consumption_chart_card_with_background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_power_consumption_chart_card_with_background.svg", "publicResourceKey": "1gLSDUQbNMbBdU0qvGoOE3V5puucIG0A", "mediaType": "image/svg+xml", "data": "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", "public": true}]}