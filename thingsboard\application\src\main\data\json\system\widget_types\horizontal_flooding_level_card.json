{"fqn": "horizontal_flooding_level_card", "name": "Horizontal flooding level card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_flooding_level_card_system_widget_image.png", "description": "Displays the latest flooding level telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'flooding', label: 'Flooding level', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Flooding level\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 2 - 1;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 5) {\\n\\tvalue = 5;\\n}\\nreturn value;\\n\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"flood\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"#234CC7\"},{\"from\":1,\"to\":3,\"color\":\"#F36900\"},{\"from\":3,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"#234CC7\"},{\"from\":1,\"to\":3,\"color\":\"#F36900\"},{\"from\":3,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal flooding level card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"m\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "flood", "flooding", "water height", "flood depth", "flood stage", "inundation level", "water rise", "overflow level", "flood peak", "high water mark"], "resources": [{"link": "/api/images/system/horizontal_flooding_level_card_system_widget_image.png", "title": "\"Horizontal flooding level card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_flooding_level_card_system_widget_image.png", "publicResourceKey": "WeqBDM4mXSg2xCjuSHkJjhO4khphvWwd", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAn1BMVEUAAADf39/f39/g4ODg4ODf39/////k5OQjTMfg4OCRpePk6fisrKzx8fHIyMg9PT11j9zOzs7y9PuQkJD09PT5+fmtvOqsvOpYWFjI0vHV1dXb29u3t7c+Ys4wV8vCwsK8vLy6x+5aedXn5+dohNlmZmaDmt+enp5aeNUrKyt0dHSfsedMbtGdnZ0/Ys6CgoJLS0vW3fTW3fUxV8pKSkpA7yNPAAAABnRSTlMAIEDfv1C6kOEmAAADcUlEQVR42u3a53KbQBSGYUV2DivKUgQSRTR1F9lOuf9ry9kVUkjGQyyT8WDN91iGBVx4h93xD3kEAAAAAAAAAAAAAAAAAAAAAAAAAAAA8JG+fHo6Y3xjfHaTm6/cYUT06YXGeHQb0hWIbkcTugoThAwMQoYGIUODkKFByNC8FmI9egm12dOpLW1quaep0z40qEV/aX/9Q3IhRD636CzeydSekqNu1+AAx3HpwCM11CddQ503+Ihf3E29BA9m67fXM+0dIVtxtJ6fQ2zDsadGvIlpunGl8bK5I5fce9dWQx0yjV0ZGwfbjn9WPUPmguXnkqXQLg8xxVn+0IRs7iVPrx3F6R3JDQ9c/WFPn3moQ+7s5/vDLjZeprtNvxBLiOS7KZbUeBLveyKJOFvu6yZEqnVyUCHuXyH2KWQhDeclJnehZmG/B+LxthAWHYmcfgusIEhqvvRtlnSHWHkrhKgdYvCD4an1M+XBXROihjrk4G4kxTbZbrzrF+IJNQ/WYk5aIPZktabLWt1grebbY1dI0HQUa978COhPjv7Um4ZRSfd0sbXvFzJvtto38aMQwgvO896crTli9lCIuiNkK/bNSit4n9C/OPZzSp36hxTbbS6W5yeiHpKw1K2aHSHW3BKM+z3ebamv/iEUBETWacmYegEJoYadIexJzUGimV4kvfUPYXrJXBaSLAuhrWvB5gF9uK1e7EuRnP4eWvqwvizkSazVItmrtV54efPTQnpF6Px9IqT/IRFLPZfUjNJd5numVh0QJbOEyJrNeFhbpCx0DL+cjLQoomjiqxMTivh4Za8oyrLjBT7RKykXnvmo1ue8SVKHHl0WQsGsJm6prSboFJKmfialz6OSyqysokXpp6n0K8evIiJ/FVZVRqGvL/iyovez1JzYBipkzYffczXTgwtDtoUQuae/dalWu/U7ZBFKf+UfQ1ZpxmM+t8r8UuqQSZZxiKxWMuTcMqQeAis47luHr+laI/t6novCTDxRbBOzSEix/az0F6ksQ/VE/CorF+FC+nzO4Rv3SYVE6omsKltdSMuSPpgOuVi26riYyozeYBAhUdR1MaS3GETIECFkaBAyNAgZGoQMzWR0ewVvsxOFt6OxcQVvtEfGeDT6ejMxPrub8TX9Uw0AAAAAAAAAAAAAAAAAAAAAAAAAAADAB/kFV7oA4xEmmqQAAAAASUVORK5CYII=", "public": true}]}