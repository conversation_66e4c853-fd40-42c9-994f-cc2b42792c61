{"fqn": "horizontal_visibility_card", "name": "Horizontal visibility card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_visibility_card_system_widget_image.png", "description": "Displays the latest visibility telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'visibility', label: 'Visibility', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Visibility\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"visibility\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":1,\"color\":\"#D81838\"},{\"from\":1,\"to\":4,\"color\":\"#FFA600\"},{\"from\":4,\"to\":null,\"color\":\"#80C32C\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":1,\"color\":\"#D81838\"},{\"from\":1,\"to\":4,\"color\":\"#FFA600\"},{\"from\":4,\"to\":null,\"color\":\"#80C32C\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal visibility card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"km\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "visibility", "sight", "view", "clarity", "transparency", "perceptibility", "discernibility", "range of view", "clearness"], "resources": [{"link": "/api/images/system/horizontal_visibility_card_system_widget_image.png", "title": "\"Horizontal visibility card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_visibility_card_system_widget_image.png", "publicResourceKey": "DkJjN2pKvOqCy7NIpdgYQOMW58g9ffU6", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAolBMVEUAAADf39/f39/g4ODg4ODf39/////k5OSAwyzg4OCg0mHf8Mrz8/PP6bCQy0bv9+XOzs75+fn3+/K3t7erq6vCwsLA4Za8vLyQkJDa2trV1dXHx8ew2Xvn5+fn9NjIyMi43YhYWFg8PDx0dHTY7L2YzlPH5aOIxzmenp6Ixzrt7e2CgoKn1m5mZmYhISHb29u43Ymo1m6tra3H5aJKSkovLy/b7uh5AAAABnRSTlMAIEDfv1C6kOEmAAADOUlEQVR42u3aCU/bMBjG8a6wN27jxM7dXE3Tm2uw6/t/tb02ZWEbdFWpqoCenxCx7EjwT0xboAMAAAAAAAAAAAAAAAAAAAAAAAAAAACAc/r07tmM4YXz3o0uPnOHE9C7J53h4FLSBxBcDkb0IYwQ0jMI6RuE9A1C+gYhfXNIiNwSleXWIZaVccaHG1rQVsoevUw7JIR+SmrLUpIjqSgmMRfFNKdY3nALOXRK7jr11NNoSTueGB8R4la+EJuxp+jJZOvMaVFOFvNyEk/au5a+0JzbfrTRggcnlArmT3+P1PEhaix2fI92yjZe2JBMcgjfDWlDHDPklNPxhO9OU+ETKTOaifHRIdMNF6xDmt5yyox22rbgkKK8i23Izy7k5kdEpzMWZjddCUUz88XDKxE+C1EqDBV/a0vFS567N0SZDkUun1s9K5nMiUO2d23EIXftDT2GxHOnnNMJLT1lQ8Jd0kpMu5AHsQpdsTI7Jk3tvtsT4vMJ3+nBnBUK9me27A5PI95zp+aJFXGIS/bz75DUXmG+uMuZ2TRLX6Svh3iCuVPBZrQS4r9bUy4cOjHli+kLIbaDQ8b2Rnlm7vb1kLUNUV2IT+cWmiv9b8hGmDwbYqft8PWQ0DcbkCrB+TbHozMLV6bj3xA2OziETa+E2ChaeiFVtuncKjHbHb0/ftg3yyvhHhbSlfic4Y6fPWhJeoG8/ntC0ps9PG38tfhqn0y655Gl8MODQ5haiZ3Nd9qJbAx/XOdkBQEFI20mRhQQUR3XFOT54wJPHJu0FuJrxTyzyavUF2kXQrfcdngIc6sVV4zXIT0LKQqdN43mUUJJnmRBlOiiaHR2rbOASNcyy3KS2i7oJjt2Yz2q7IOX2MyoCzFt6/0he3QhOpKNrvVjSF3c85jn6lwnjQ0Z5TmHNFHdSM5NJL1ZqMJTv/r9pu8THRVNIs0d0VmeRDJqNM9dZ3WjyYQE5o7U2TezUCQJHeH8v1jlNb2uaO7pEH0ICYJ9i5IO0YuQPkJI3yCkbxDSNwjpm9Hg8gP8m51IXg6GTo/+gnuswBkOBp8vRs57dzH8SG+qAQAAAAAAAAAAAAAAAAAAAAAAAAAAADiTXxBy+4qYs+NTAAAAAElFTkSuQmCC", "public": true}]}