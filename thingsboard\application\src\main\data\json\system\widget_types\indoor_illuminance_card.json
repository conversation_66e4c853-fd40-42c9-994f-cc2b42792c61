{"fqn": "indoor_illuminance_card", "name": "Indoor illuminance card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_illuminance_card_system_widget_image.png", "description": "Displays the latest indoor illuminance telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'illuminance', label: 'Illuminance', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Illuminance\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 400 - 200;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\\n\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:lightbulb-on\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":100,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":100,\"to\":300,\"color\":\"#FFA600\"},{\"from\":300,\"to\":500,\"color\":\"#F36900\"},{\"from\":500,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":52,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":100,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":100,\"to\":300,\"color\":\"#FFA600\"},{\"from\":300,\"to\":500,\"color\":\"#F36900\"},{\"from\":500,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Illuminance card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"lx\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["environment", "illuminance", "indoor", "brightness", "luminance", "luminosity", "light", "light level", "light intensity", "lux", "candela", "foot-candle"], "resources": [{"link": "/api/images/system/indoor_illuminance_card_system_widget_image.png", "title": "\"Indoor illuminance card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_illuminance_card_system_widget_image.png", "publicResourceKey": "AXvH84p8slcrdveOTyyq4Pp5N6I07Epy", "mediaType": "image/png", "data": "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", "public": true}]}