{"fqn": "wind_speed_chart_card_with_background", "name": "Wind speed chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/wind_speed_chart_card_with_background_system_widget_image.png", "description": "Displays a wind speed data by combining the latest and aggregated values with the background image and optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'speed', label: 'Wind Speed', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'm/s', decimals: 1 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'speed', 'm/s', 1);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Wind Speed\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 16 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 26) {\\n\\tvalue = 26;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m/s\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0.2,\"color\":\"#6083EC\"},{\"from\":0.2,\"to\":3.4,\"color\":\"#5579E5\"},{\"from\":3.4,\"to\":8,\"color\":\"#4369DD\"},{\"from\":8,\"to\":10.8,\"color\":\"#2B54CE\"},{\"from\":10.8,\"to\":17.2,\"color\":\"#224AC2\"},{\"from\":17.2,\"to\":24.5,\"color\":\"#F04022\"},{\"from\":24.5,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 16 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 26) {\\n\\tvalue = 26;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m/s\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m/s\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/wind_speed_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Wind Speed\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:windsock\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "wind", "speed", "airspeed", "flow", "gust"], "resources": [{"link": "/api/images/system/wind_speed_chart_card_with_background_system_widget_background.png", "title": "\"Wind speed chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "wind_speed_chart_card_with_background_system_widget_background.png", "publicResourceKey": "vmggIUmLdvs36GuDoM4xNqdLqbslc4Xh", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/wind_speed_chart_card_with_background_system_widget_image.png", "title": "\"Wind speed chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "wind_speed_chart_card_with_background_system_widget_image.png", "publicResourceKey": "YhmaxBia9iVPLdpQPOucI3Ktzc6aHBPy", "mediaType": "image/png", "data": "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", "public": true}]}