<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="600" height="400" fill="none" version="1.1" viewBox="0 0 600 400"><tb:metadata xmlns=""><![CDATA[{
  "title": "Small right motor pump",
  "description": "Small right motor pump with configurable states.",
  "searchTags": [
    "motor",
    "pump"
  ],
  "widgetSizeX": 3,
  "widgetSizeY": 2,
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.critical) {\n    color = ctx.properties.criticalColor;\n} else if (ctx.values.warning) {\n    color = ctx.properties.warningColor;\n} else if (ctx.values.running) {\n    color = ctx.properties.runningColor;\n}\nelement.attr({fill: color});\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = ctx.values.critical && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}",
      "actions": null
    },
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    }
  ],
  "behavior": [
    {
      "id": "running",
      "name": "{i18n:scada.symbol.running}",
      "hint": "{i18n:scada.symbol.running-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.running}",
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "running"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warning",
      "name": "{i18n:scada.symbol.warning-state}",
      "hint": "{i18n:scada.symbol.warning-state-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.warning}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "MAJOR",
            "MINOR",
            "WARNING",
            "INDETERMINATE"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "critical",
      "name": "{i18n:scada.symbol.critical-state}",
      "hint": "{i18n:scada.symbol.critical-state-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.critical}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "CRITICAL"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "criticalAnimation",
      "name": "{i18n:scada.symbol.critical-state-animation}",
      "hint": "{i18n:scada.symbol.critical-state-animation-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.animation}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "openInSeparateDialog": false,
        "openInPopover": false
      }
    }
  ],
  "properties": [
    {
      "id": "runningColor",
      "name": "{i18n:scada.symbol.running-color}",
      "type": "color",
      "default": "#1C943E",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "stoppedColor",
      "name": "{i18n:scada.symbol.stopped-color}",
      "type": "color",
      "default": "#696969",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "warningColor",
      "name": "{i18n:scada.symbol.warning-color}",
      "type": "color",
      "default": "#FAA405",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "criticalColor",
      "name": "{i18n:scada.symbol.critical-color}",
      "type": "color",
      "default": "#D12730",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata>
<path d="m27.729 14c-1.5063 0-2.7285 1.2222-2.7285 2.7285v34.545c0 1.5063 1.2222 2.7266 2.7285 2.7266h14.816s26.033 11.127 26.033 23.992c1e-5 12.865-25.579 54.008-25.578 89.008 3e-4 27 0 5e-5 0 50v136.03c1e-4 14.912 12.088 26.967 27 26.967h-42.271c-1.5063 0-2.7285 1.2206-2.7285 2.7266v14.547c0 1.506 1.2222 2.7266 2.7285 2.7266h519.54c1.506 0 2.7266-1.2206 2.7266-2.7266v-14.547c0-1.506-1.2206-2.7266-2.7266-2.7266h-27.023l-19.656-26h70.09c16.192 0 29.316-13.126 29.316-29.318v-207.36c0-16.192-13.124-29.318-29.316-29.318h-98.434v-20.908c0-10.544-8.5458-19.092-19.09-19.092h-195.07c-10.544 0-19.092 8.5482-19.092 19.092v20.908h-69.455c-5.271 0-9.5449 4.2731-9.5449 9.5449v18.455h-7.9551c-1.129 0-2.0449 0.91592-2.0449 2.0449v31c0-1.129-0.91592-2.0449-2.0449-2.0449h-5.9551v-69.008c0-12.865 26-23.992 26-23.992h4.2734c1.506 0 2.7266-1.2203 2.7266-2.7266v-34.545c0-1.5063-1.2206-2.7285-2.7266-2.7285zm122.27 269.96v40c0 1.129 0.91592 2.0449 2.0449 2.0449h7.9551v18.455c0 5.271 4.2739 9.5449 9.5449 9.5449h41.113l-19.658 26h-71c12.15 0 22-9.8383 22-21.988v-72.012h5.9551c1.129 0 2.0449-0.91593 2.0449-2.0449z" fill="#1C943E" tb:tag="background"/><path d="m14 264h29v72h-29v-72z" fill="#fff"/><path d="m14 264h29v72h-29v-72z" fill="url(#paint0_linear_1595_97144)"/><path d="m15.5 265.5h26v69h-26v-69z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><path d="m42.545 54h125.46s-26 11.127-26 23.992v280.02c0 12.15-9.85 21.989-22 21.989h-50c-14.912 0-27-12.055-27-26.967 0-32.852-1e-4 -89.703-1e-4 -136.03 0-50 5e-4 -23 2e-4 -50-4e-4 -35 25.578-76.142 25.578-89.008 0-12.865-26.033-23.992-26.033-23.992z" fill="url(#paint1_linear_1595_97144)"/><path d="m163.48 55.023-0.012 6e-3c-2.461 1.2563-5.749 3.07-9.044 5.2954-3.289 2.2208-6.622 4.8771-9.143 7.8273-2.512 2.9396-4.308 6.2773-4.308 9.8408v280.02c0 11.584-9.391 20.966-20.977 20.966h-50c-14.348 0-25.977-11.598-25.977-25.944l-2e-4 -136.03c0-26.945 2e-4 -31.528 3e-4 -34.252 0-2.33 1e-4 -3.299-1e-4 -15.748-2e-4 -17.29 6.3256-36.167 12.718-52.43 1.5487-3.94 3.1178-7.767 4.5957-11.371 1.5411-3.7581 2.983-7.2745 4.1991-10.425 1.2029-3.1168 2.2144-5.9482 2.9261-8.4152 0.7076-2.4533 1.1391-4.6131 1.1391-6.3663 0-3.564-1.7981-6.9018-4.3133-9.8412-2.5243-2.9502-5.8616-5.6065-9.154-7.8273-3.2991-2.2253-6.5919-4.039-9.0562-5.2953-0.0038-0.0019-0.0075-0.0038-0.0112-0.0057h116.42z" stroke="#000" stroke-opacity=".12" stroke-width="2.0454"/><path d="m196 88h-26.455c-5.271 0-9.545 4.2736-9.545 9.5455v246.91c0 5.271 4.274 9.545 9.545 9.545h26.455v-266z" fill="url(#paint2_linear_1595_97144)"/><path d="m194.98 89.023h-25.432c-4.707 0-8.522 3.8158-8.522 8.5228v246.91c0 4.706 3.815 8.522 8.522 8.522h25.432v-263.95z" stroke="#000" stroke-opacity=".12" stroke-width="2.0454"/><path d="m150 149.04c0-1.129-0.916-2.045-2.045-2.045h-5.955v139h5.955c1.129 0 2.045-0.916 2.045-2.045v-134.91z" fill="url(#paint3_linear_1595_97144)"/><path d="m148.98 149.04c0-0.564-0.458-1.022-1.022-1.022h-4.932v136.95h4.932c0.564 0 1.022-0.458 1.022-1.022v-134.91z" stroke="#000" stroke-opacity=".12" stroke-width="2.0454"/><path d="m160 116h-7.955c-1.129 0-2.045 0.916-2.045 2.045v205.91c0 1.129 0.916 2.045 2.045 2.045h7.955v-210z" fill="url(#paint4_linear_1595_97144)"/><path d="m158.98 117.02h-6.932c-0.564 0-1.022 0.458-1.022 1.022v205.91c0 0.564 0.458 1.022 1.022 1.022h6.932v-207.95z" stroke="#000" stroke-opacity=".12" stroke-width="2.0454"/><rect transform="rotate(-90 25 54)" x="25" y="54" width="40" height="150" rx="2.7273" fill="url(#paint5_linear_1595_97144)"/><rect transform="rotate(-90 26.023 52.977)" x="26.023" y="52.977" width="37.954" height="147.96" rx="1.7046" stroke="#000" stroke-opacity=".12" stroke-width="2.0454"/><rect transform="rotate(-90 25 400)" x="25" y="400" width="20" height="525" rx="2.7273" fill="url(#paint6_linear_1595_97144)"/><rect transform="rotate(-90 26.023 398.98)" x="26.023" y="398.98" width="17.954" height="522.96" rx="1.7046" stroke="#000" stroke-opacity=".12" stroke-width="2.0454"/><path d="m239 88v-20.909c0-10.544 8.547-19.091 19.091-19.091h195.07c10.544 0 19.091 8.5473 19.091 19.091v20.909h-233.25z" fill="url(#paint7_linear_1595_97144)"/><path d="m240.02 86.977v-19.886c0-9.9788 8.089-18.068 18.068-18.068h195.07c9.979 0 18.068 8.0894 18.068 18.068v19.886h-231.2z" stroke="#000" stroke-opacity=".12" stroke-width="2.0454"/><path d="m191 380 19.657-26h289.94l19.657 26h-329.25z" fill="url(#paint8_linear_1595_97144)"/><path d="m500.08 355.02 18.111 23.954h-325.14l18.111-23.954h288.92z" stroke="#000" stroke-opacity=".12" stroke-width="2.0454"/><path d="M196 88H520V354H196V88Z" fill="url(#paint9_linear_1595_97144)"/><path d="M196 88H520V107H196V88Z" fill="url(#paint10_linear_1595_97144)"/><path d="m196 107h324v19h-324v-19z" fill="url(#paint11_linear_1595_97144)"/><path d="m196 126h324v19h-324v-19z" fill="url(#paint12_linear_1595_97144)"/><path d="m196 145h324v19h-324v-19z" fill="url(#paint13_linear_1595_97144)"/><path d="m196 164h324v19h-324v-19z" fill="url(#paint14_linear_1595_97144)"/><path d="m196 183h324v19h-324v-19z" fill="url(#paint15_linear_1595_97144)"/><path d="m196 202h324v19h-324v-19z" fill="url(#paint16_linear_1595_97144)"/><path d="m196 221h324v19h-324v-19z" fill="url(#paint17_linear_1595_97144)"/><path d="m196 240h324v19h-324v-19z" fill="url(#paint18_linear_1595_97144)"/><path d="m196 259h324v19h-324v-19z" fill="url(#paint19_linear_1595_97144)"/><path d="m196 278h324v19h-324v-19z" fill="url(#paint20_linear_1595_97144)"/><path d="m196 297h324v19h-324v-19z" fill="url(#paint21_linear_1595_97144)"/><path d="m196 316h324v19h-324v-19z" fill="url(#paint22_linear_1595_97144)"/><path d="m196 335h324v19h-324v-19z" fill="url(#paint23_linear_1595_97144)"/><path d="M600 117.318C600 101.126 586.874 88 570.682 88H520V354H570.682C586.874 354 600 340.874 600 324.682V117.318Z" fill="url(#paint24_linear_1595_97144)"/><path d="m598.98 117.32c0-15.627-12.668-28.295-28.295-28.295h-49.659v263.95h49.659c15.627 0 28.295-12.668 28.295-28.295v-207.36z" stroke="#000" stroke-opacity=".12" stroke-width="2.0454"/><rect transform="rotate(-90 51.5 12.5)" x="51.5" y="12.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/><rect x="1.5" y="251.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/><path d="m451 380.29v-7.143c0-3.945 3.198-7.143 7.143-7.143h7.143c3.945 0 7.143 3.198 7.143 7.143v7.143h-21.429z" fill="url(#paint25_linear_1595_97144)"/><path d="m452.5 378.79v-5.643c0-3.116 2.526-5.643 5.643-5.643h7.143c3.116 0 5.643 2.527 5.643 5.643v5.643h-18.429z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><path d="m233 380.29v-7.143c0-3.945 3.198-7.143 7.143-7.143h7.143c3.945 0 7.143 3.198 7.143 7.143v7.143h-21.429z" fill="url(#paint26_linear_1595_97144)"/><path d="m234.5 378.79v-5.643c0-3.116 2.526-5.643 5.643-5.643h7.143c3.116 0 5.643 2.527 5.643 5.643v5.643h-18.429z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><defs>
  <linearGradient id="paint0_linear_1595_97144" x1="23.201" x2="22.277" y1="264" y2="335.99" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint1_linear_1595_97144" x1="82.833" x2="77.664" y1="79.035" y2="357.13" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint2_linear_1595_97144" x1="186.64" x2="199.02" y1="88" y2="353.44" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint3_linear_1595_97144" x1="147.92" x2="162.98" y1="147" y2="284.36" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint4_linear_1595_97144" x1="157.4" x2="184.75" y1="116" y2="322.39" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint5_linear_1595_97144" x1="35.4" x2="31.852" y1="54" y2="203.93" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint6_linear_1595_97144" x1="30.2" x2="-54.456" y1="400" y2="911.02" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint7_linear_1595_97144" x1="239" x2="471.95" y1="77.6" y2="86.173" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint8_linear_1595_97144" x1="191" x2="518.19" y1="373.24" y2="399.39" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".35" offset="0"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#020202" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#020202" stop-opacity=".13333" offset=".49829"/>
   <stop stop-color="#020202" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#fff" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint9_linear_1595_97144" x1="280.24" x2="278.86" y1="88" y2="354.02" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint10_linear_1595_97144" x1="361.38" x2="361.38" y1="107" y2="88" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint11_linear_1595_97144" x1="361.38" x2="361.38" y1="126" y2="107" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint12_linear_1595_97144" x1="361.38" x2="361.38" y1="145" y2="126" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint13_linear_1595_97144" x1="361.38" x2="361.38" y1="164" y2="145" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint14_linear_1595_97144" x1="361.38" x2="361.38" y1="183" y2="164" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint15_linear_1595_97144" x1="361.38" x2="361.38" y1="202" y2="183" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint16_linear_1595_97144" x1="361.38" x2="361.38" y1="221" y2="202" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint17_linear_1595_97144" x1="361.38" x2="361.38" y1="240" y2="221" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint18_linear_1595_97144" x1="361.38" x2="361.38" y1="259" y2="240" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint19_linear_1595_97144" x1="361.38" x2="361.38" y1="278" y2="259" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint20_linear_1595_97144" x1="361.38" x2="361.38" y1="297" y2="278" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint21_linear_1595_97144" x1="361.38" x2="361.38" y1="316" y2="297" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint22_linear_1595_97144" x1="361.38" x2="361.38" y1="335" y2="316" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint23_linear_1595_97144" x1="361.38" x2="361.38" y1="354" y2="335" gradientUnits="userSpaceOnUse">
   <stop stop-color="#D9D9D9" stop-opacity="0" offset="0"/>
   <stop stop-color="#0C0C0C" stop-opacity=".8" offset="1"/>
  </linearGradient>
  <linearGradient id="paint24_linear_1595_97144" x1="579.2" x2="584.78" y1="88" y2="353.9" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint25_linear_1595_97144" x1="451" x2="472.43" y1="376.57" y2="376.78" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint26_linear_1595_97144" x1="233" x2="254.43" y1="376.57" y2="376.78" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
 </defs><path d="m27.733 14.003c-1.5063 0-2.7285 1.2222-2.7285 2.7285v34.545c0 1.5063 1.2222 2.7266 2.7285 2.7266h14.816s26.033 11.127 26.033 23.992c1e-5 12.865-25.579 54.008-25.578 89.008 3e-4 27 0 5e-5 0 50v136.03c1e-4 14.912 12.088 26.967 27 26.967h-42.271c-1.5063 0-2.7285 1.2206-2.7285 2.7266v14.547c0 1.506 1.2222 2.7266 2.7285 2.7266h519.54c1.506 0 2.7266-1.2206 2.7266-2.7266v-14.547c0-1.506-1.2206-2.7266-2.7266-2.7266h-27.023l-19.656-26h70.09c16.192 0 29.316-13.126 29.316-29.318v-207.36c0-16.192-13.124-29.318-29.316-29.318h-98.434v-20.908c0-10.544-8.5458-19.092-19.09-19.092h-195.07c-10.544 0-19.092 8.5482-19.092 19.092v20.908h-69.455c-5.271 0-9.5449 4.2731-9.5449 9.5449v18.455h-7.9551c-1.129 0-2.0449 0.91592-2.0449 2.0449v31c0-1.129-0.91592-2.0449-2.0449-2.0449h-5.9551v-69.008c0-12.865 26-23.992 26-23.992h4.2734c1.506 0 2.7266-1.2203 2.7266-2.7266v-34.545c0-1.5063-1.2206-2.7285-2.7266-2.7285zm122.27 269.96v40c0 1.129 0.91592 2.0449 2.0449 2.0449h7.9551v18.455c0 5.271 4.2739 9.5449 9.5449 9.5449h41.113l-19.658 26h-71c12.15 0 22-9.8383 22-21.988v-72.012h5.9551c1.129 0 2.0449-0.91593 2.0449-2.0449z" fill="#000" fill-opacity="0" tb:tag="clickArea"/>
</svg>