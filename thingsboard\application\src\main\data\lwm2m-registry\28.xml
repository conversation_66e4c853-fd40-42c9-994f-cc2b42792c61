<?xml version="1.0" encoding="UTF-8"?>

<!--
FILE INFORMATION

OMNA LwM2M Registry
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 28.xml

NORMATIVE INFORMATION

  Information about this file can be found in the latest revision of

    OMA-TS-LightweightM2M_Core-V1_2

  This is available at http://www.openmobilealliance.org/release/LightweightM2M/

  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

LEGAL DISCLAIMER

  Copyright 2021 Open Mobile Alliance.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

  1. Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
  3. Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived
  from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.

  The above license is used as a license under copyright only.  Please
  reference the OMA IPR Policy for patent licensing terms:
  https://www.omaspecworks.org/about/intellectual-property-rights/

-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.openmobilealliance.org/tech/profiles/LWM2M-v1_1.xsd">
	<Object ObjectType="MODefinition">
		<Name>Device RF Capabilities</Name>
		<Description1><![CDATA[This is a device management object that should be used for capturing the RF capabilities of the device.]]></Description1>
		<ObjectID>28</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:28</ObjectURN>
		<LWM2MVersion>1.1</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Single</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>Supported LTE Band Information</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates the list of supported LTE bands]]></Description>
			</Item>
			<Item ID="1">
				<Name>Supported EGPRS Band Information</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates the list of supported EGPRS bands]]></Description>
			</Item>
			<Item ID="2">
				<Name>Supported TD-SCDMA Band Information</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates the list of supported TD-SCDMA bands]]></Description>
			</Item>
			<Item ID="3">
				<Name>Supported WCDMA Band Information</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates the list of supported WCDMA bands]]></Description>
			</Item>
			<Item ID="4">
				<Name>Supported CDMA-2000 Band Information</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates the list of supported CDMA-2000 bands]]></Description>
			</Item>
			<Item ID="5">
				<Name>Supported WiMAX Band Information</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates the list of supported WiMAX bands]]></Description>
			</Item>
			<Item ID="6">
				<Name>Supported NB-IoT Band Information</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates the list of supported NB-IoT bands]]></Description>
			</Item>
			<Item ID="7">
				<Name>Supported 5G Band Information</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates the list of supported 5G-NR bands]]></Description>
			</Item>
    </Resources>
  	<Description2></Description2>
	</Object>
</LWM2M>
