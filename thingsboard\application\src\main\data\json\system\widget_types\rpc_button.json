{"fqn": "control_widgets.rpcbutton", "name": "RPC <PERSON>", "deprecated": false, "image": "tb-image;/api/images/system/rpc_button_system_widget_image.png", "description": "Allows to send RPC command when the user presses the button.", "descriptor": {"type": "rpc", "sizeX": 4, "sizeY": 2, "resources": [], "templateHtml": "<div class=\"tb-rpc-button flex flex-col\">\n  <div class=\"title-container flex max-w-20% flex-full flex-row items-center justify-center\"\n       [class.!hidden]=\"!showTitle\">\n    <span class=\"button-title\">{{title}}</span>\n  </div>\n  <div class=\"button-container flex flex-full flex-col items-center justify-center\"\n       [class]=\"{\n          'max-w-80%': showTitle,\n          'max-w-100%': !showTitle\n       }\"\n       [style.padding-top]=\"showTitle ? '5px': '10px'\">\n    <div>\n      <button mat-button (click)=\"sendCommand()\"\n              [class.mat-mdc-raised-button]=\"styleButton?.isRaised\"\n              [color]=\"styleButton?.isPrimary ? 'primary' : ''\"\n              [style]=\"customStyle\">\n        {{buttonLable}}\n      </button>\n    </div>\n  </div>\n  <div class=\"error-container flex flex-row items-center justify-center\" [style.background]=\"error?.length ? 'rgba(255,255,255,0.25)' : 'none'\">\n    <span class=\"button-error\">{{ error }}</span>\n  </div>\n</div>", "templateCss": ".tb-rpc-button {\n    width: 100%;\n    height: 100%;\n}\n\n.tb-rpc-button .title-container {\n    font-weight: 500;\n    white-space: nowrap;\n    margin: 10px 0;\n}\n\n.tb-rpc-button .button-container div{\n    min-width: 80%\n}\n\n.tb-rpc-button .button-container .mat-mdc-button{\n    width: 100%;\n    margin: 0;\n}\n\n.tb-rpc-button .error-container {\n    position: absolute;\n    top: 2%;\n    right: 0;\n    left: 0;\n    z-index: 4;\n    height: 14px;\n}\n\n.tb-rpc-button .error-container .button-error {\n    color: #ff3315;\n    white-space: nowrap;\n}", "controllerScript": "var requestPersistent = false;\nvar persistentPollingInterval = 5000;\n\nself.onInit = function() {\n    if (self.ctx.settings.requestPersistent) {\n        requestPersistent = self.ctx.settings.requestPersistent;\n    }\n    if (self.ctx.settings.persistentPollingInterval) {\n        persistentPollingInterval = self.ctx.settings.persistentPollingInterval;\n    }\n    \n    self.ctx.ngZone.run(function() {\n       init(); \n       self.ctx.detectChanges();\n    });\n};\n\nfunction init() {\n    let rpcEnabled = self.ctx.defaultSubscription.rpcEnabled;\n\n    self.ctx.$scope.buttonLable = self.ctx.settings.buttonText;\n    self.ctx.$scope.showTitle = self.ctx.settings.title &&\n        self.ctx.settings.title.length ? true : false;\n    self.ctx.$scope.title = self.ctx.settings.title;\n    self.ctx.$scope.styleButton = self.ctx.settings.styleButton;\n\n    if (self.ctx.settings.styleButton.isPrimary ===\n        false) {\n        self.ctx.$scope.customStyle = {\n            'background-color': self.ctx.$scope.styleButton.bgColor,\n            'color': self.ctx.$scope.styleButton.textColor\n        };\n    }\n\n    if (!rpcEnabled) {\n        self.ctx.$scope.error =\n            'Target device is not set!';\n    }\n\n    self.ctx.$scope.sendCommand = function() {\n        var rpcMethod = self.ctx.settings.methodName;\n        var rpcParams = self.ctx.settings.methodParams;\n        if (rpcParams.length) {\n            try {\n                rpcParams = JSON.parse(rpcParams);\n            } catch (e) {}\n        }\n        var timeout = self.ctx.settings.requestTimeout;\n        var oneWayElseTwoWay = self.ctx.settings.oneWayElseTwoWay ?\n            true : false;\n\n        var commandPromise;\n        if (oneWayElseTwoWay) {\n            commandPromise = self.ctx.controlApi.sendOneWayCommand(\n                rpcMethod, rpcParams, timeout, requestPersistent, persistentPollingInterval);\n        } else {\n            commandPromise = self.ctx.controlApi.sendTwoWayCommand(\n                rpcMethod, rpcParams, timeout, requestPersistent, persistentPollingInterval);\n        }\n        commandPromise.subscribe(\n            function success() {\n                self.ctx.$scope.error = \"\";\n                self.ctx.detectChanges();\n            },\n            function fail(rejection) {\n                if (self.ctx.settings.showError) {\n                    self.ctx.$scope.error =\n                        rejection.status + \": \" +\n                        rejection.statusText;\n                    self.ctx.detectChanges();\n                }\n            }\n        );\n    };\n}\n\nself.onDestroy = function() {\n    self.ctx.controlApi.completedCommand();\n}\n", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-send-rpc-widget-settings", "defaultConfig": "{\"targetDeviceAliases\":[],\"showTitle\":false,\"backgroundColor\":\"#e6e7e8\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"requestTimeout\":5000,\"oneWayElseTwoWay\":true,\"buttonText\":\"Send RPC\",\"styleButton\":{\"isRaised\":true,\"isPrimary\":false},\"methodName\":\"rpcCommand\",\"methodParams\":\"{}\"},\"title\":\"RPC Button\",\"dropShadow\":true,\"enableFullscreen\":false,\"widgetStyle\":{},\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{}}"}, "tags": ["command", "downlink", "device configuration", "device control", "invocation", "remote method", "remote function", "interface", "subroutine call", "inter-process communication", "server request"], "resources": [{"link": "/api/images/system/rpc_button_system_widget_image.png", "title": "\"RPC Button\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "rpc_button_system_widget_image.png", "publicResourceKey": "JhGGJdXCQOU6sES6GO3OBc3A7NkMZgBw", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAAAAABslHx1AAAAAmJLR0QA/4ePzL8AAAN8SURBVHja7dvbSxRRHAfw/X9+bhcvrJZSmllhZFSCWBDaY4FUhBRZCQXR7akLyoJYlqxU0g0tukF0oYQM3bxsYVRgbuuyu7k7szNn9vSb2Wx9qB56ac72/T7MHGYZmA/nMjvDbzzSTkbpOASPw7CUjkPx2AwhTNNQNKYphE1hiCXMtK5pWkrB8GXraZMl0sMOQ9dEdqApmIzQdIMlDDH1lLIMh5LSTYZYIp1MS6XDAGF5uEPiltoQK85dwhAtKhVPVLMhRj5ADAcyqzpkNgtJqQ9JAQIIIIAAAggggAACCCB/8wog8btfAjd+f9Y3t0GmW5bS8rO/fvavaXB2jWVlZbVHwjLC+7L1x2P8rN1ZQcWtX90EyWws7r7bSuf+BKnz+f1t3norTPV+/0HvNiFP0+5b54sahYsgn+mQrdksZfL6hYcZGe2betoxyG/LtP7OiXlIJW/a6F2Y9nPjKI3MLtrBjbM05CJIsqT6tdOI1BbWFuyRQVpbupw7yGygqqLCBZCTNJqFdNGTAQpwY2bwo5vmyL1iqr+uS3l4yaTsoaEg7bT0VXWyn/xyquQHpDz4JuCrFWHaG48OrSuJXqLHLly1Yl01VDkuq6r7+i5Qd5ABsqlctlMiN0eI0/BZhu09rXggu10J4TVoYMlWWeKr51x2IM3lcp83k4NUfAqVbrQYsn3w/lvuvTt0lQ9Hn027CNJLt3m7YaXctIaXYCHnIWfog5Src3Okg/pldo7Ys8O7S9qHXrgIEllWee35uYJ9MkAHXgVq3s9Dxguan56iHCRZUa3/hPDAa3/U7dtiuGloTTZ7aXFrnO9xPio8Zs5D5JViaqpbsGpdpK4cxDxRRItbpl02R1Jfsje2zIy+8LCI/ekkc0a4b7Lj3y8ggAACCCCAAAIIIIAA8v9C8qaoRosqXngmsmVOXHgWVxsSyxaemXosJFR2mBMxpxRQpOPB0YihKsOIjAbjdnEml8vOhUaGX97s7VEwvTdfDo+E5kyn7pe7JDQWVDZjIbtDMk5JuZGcSyQScQXDlz2XNJyS8vwp8s+bzy6kzJsPYfIigAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggg/yTfATinNXQTM7TQAAAAAElFTkSuQmCC", "public": true}]}