{"fqn": "rotational_speed_chart_card", "name": "Rotational speed chart card", "deprecated": false, "image": "tb-image;/api/images/system/rotational_speed_chart_card.svg", "description": "Displays rotational speed data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'rotationalSpeed', label: 'Rotational speed', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'RPM', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'rotationalSpeed', 'RPM', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Rotational speed\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 4000 - 2000;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 4000) {\\n\\tvalue = 4000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":500,\"color\":\"#305AD7\"},{\"from\":500,\"to\":1500,\"color\":\"#3FA71A\"},{\"from\":1500,\"to\":3000,\"color\":\"#FFA600\"},{\"from\":3000,\"to\":null,\"color\":\"#F04022\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 4000 - 2000;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 4000) {\\n\\tvalue = 4000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"RPM\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0,0,0,0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"RPM\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Rotational speed\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"360\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"margin\":\"0px\"}"}, "tags": ["angular speed", "spin rate", "revolutions", "rotational frequency", "spin motion"], "resources": [{"link": "/api/images/system/rotational_speed_chart_card.svg", "title": "rotational_speed_chart_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "rotational_speed_chart_card.svg", "publicResourceKey": "VgFPvxFBmS1xnbrjPnCadf0VdgTTseyK", "mediaType": "image/svg+xml", "data": "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", "public": true}]}