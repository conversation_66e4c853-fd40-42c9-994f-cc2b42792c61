{"fqn": "simple_leaf_wetness_chart_card", "name": "Simple leaf wetness chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_leaf_wetness_chart_card_system_widget_image.png", "description": "Displays historical leaf wetness values as a simplified chart. Optionally may display the corresponding latest leaf wetness value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'leaf', label: 'Leaf wetness', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'leaf', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Leaf wetness\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#4B70DD\"},{\"from\":10,\"to\":50,\"color\":\"#FFA600\"},{\"from\":50,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Leaf wetness\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:leaf\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"%\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "dew", "leaf moisture", "foliage dampness", "leaf humidity", "foliar moisture"], "resources": [{"link": "/api/images/system/simple_leaf_wetness_chart_card_system_widget_image.png", "title": "\"Simple leaf wetness chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_leaf_wetness_chart_card_system_widget_image.png", "publicResourceKey": "5RTwOnaBgHqHW4DXdhdta1Na7NUtLkdP", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAllBMVEUAAADf39/g4ODf39/f39/g4OD////k5OQhISHg4ODi4uJLcN2QkJDy8vLHx8c9PT1YWFgvLy90dHQ8PDysrKxhguHV1dVKSkqCgoLo7furq6u6urr19/2enp5mZma7yfK5ubl4lOWdnZ3S2/alt+6ar+yOpepWed/H0vSwwPBti+OOpuqDnefd5PmwwfAtLS3FxcWcnJygPPf3AAAABnRSTlMAIL9AEN/GQiaNAAAEwUlEQVR42uzPyw2AIBQAsMdHDCRs4P5zmjiBRyDtBg0AAAAAAID/atpc/Rolj3tz4yoRaTxte3OUyLMdYObo7QhdZDEiqxF52a23VTthKArDd4PBZM4cNCSiKOILtH3/l6u69truU2lLi0TwR0QzycVHLrS2bkht3ZDauiG1dUNq698gOaOW/hQydPjcxIJa+iXk+w9dM7ykHp8S9vgqKn7fSZAcuDd/hDhNBsioA6xj47alEVgErt+nAmUYTPOoAtioad+l8rLr6AyIRD5KHyAamzZAYtMw2cRpgw40oWKa9qlHw9IL90eUtmk7pNhN0cSvu0YcnQAp/Bpi7JHpJAGlgdA9Fl2KAbHfpkYH6j4aKOu1KqwpMM2OGek4kjMgmWuTJ+neQxyj90ym6/0VgjCGJTpKfkyfEEdReu8puY0hAQ3LaHh2BsSRHD3X8ntIZhIRS1EQDogWWpgK5DF9A1mYRQRirqMzkT52eHYGREjtuNbigLg1tCW7ybqYnT8gjgELGyCUnCdDbOwFYrETpwiTZc5LFAsBr50AQWjtG9cUzxa/FiCFUbd72U7EO+yVHuZnQAJjByibbZS9YG4ZR0jZ1zsyCI5OgMg8kKzoc/e3kKM+8pviQt0/jbV1Q2rrhtTWDamtG1JbN+T/Z9k5uzzENHArpGtDXGRYZjdrSy8XhiiLw6O+jfmykIGN4Zm0US4KEd++e43hohCl4G09x0tCfrJvd7uNwkAYhs8+jTw2mIADBlJCEshv9/4vb5fSirAJC2wqQSWeO3gzmLEiIWiFNu2pnxiyIoG2NUU/MKQZSEOS8/NCmoE0lK9nH+JspEzU/UAkHm1pM++QwKWKK+9evQJPaE9NHqKk9sjTgcCDiPRWAc7q6yLiuB17XFA0dUjikY5kFJKbPJ5hiVrsk5Yy0uQ3Hf3n/fWQ1GKo+OvaJHyK0bK7Ow4iDok8vVPooHxXvR6SmVpRZ9yYzQmDCAoVair0RPvqEWIEh3SrZB0EiRgbcuBaicqVi7M5poC16PPmi8dLU3Osx9g1N3qVrDyquIEYFXLjfF+xqFwMkHEGGNM/kLjrOdckMM7WJx04Yr2pXh5RIIQTa6K3ZERIzgc0TF6H/OJs3HZTviuaqA3GEju/nkPk4JNY+eSuxNCQC5/RqB6rA5+sKcdeN9aeG6AiNEn8j7UTbAVatm9EOhkWwnwo8luG2pnNlQ2uxo6+bojq95ORJq9nHmMnFQ4KSbl2SfEhy01pMz6jh3JX+FusPaJQKnwvNSzkwsVpX/L9SUmNQZ+AHEyieyGm9ZE/3r+RrS2uhcU/uBoT6N/sGbPFJ8sHezxe2KToJCnBYFOFGJMWfMKeD+gSU4RJdIeUbD73e4patUJyBiyX6BBRqDCJ7pAT8xmwhnPUPlbIjVO8d4UELkUTdPRvds7zY/Nk2dxWAyrei+fL3QlJO5hAX0ha8h9mj5aC+elAHE3+RBn9hz2tb4xt73v7PCPG5JqQkZoMT87hA8AXQ+RMMl4OiWaSMYc/6JaQJWTelpC5WULmZgmZmyVkbpaQ3+3cMQ0AIAwAwUDDUg341wkSGEtz5+ANfDVCqmkU0ma5E9mgZOe4W6r1/5ZqRqdRGAAAAAAAwJMDbJv6K5dRMTYAAAAASUVORK5CYII=", "public": true}]}