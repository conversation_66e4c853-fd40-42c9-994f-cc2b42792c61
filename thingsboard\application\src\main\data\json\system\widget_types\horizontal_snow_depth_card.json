{"fqn": "horizontal_snow_depth_card", "name": "Horizontal snow depth card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_snow_depth_card_system_widget_image.png", "description": "Displays the latest snow depth telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'snow', label: 'Snow depth', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Snow depth\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 120) {\\n\\tvalue = 120;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"ac_unit\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":1,\"color\":\"#7191EF\"},{\"from\":1,\"to\":10,\"color\":\"#4B70DD\"},{\"from\":10,\"to\":30,\"color\":\"#305AD7\"},{\"from\":30,\"to\":60,\"color\":\"#234CC7\"},{\"from\":60,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":1,\"color\":\"#7191EF\"},{\"from\":1,\"to\":10,\"color\":\"#4B70DD\"},{\"from\":10,\"to\":30,\"color\":\"#305AD7\"},{\"from\":30,\"to\":60,\"color\":\"#234CC7\"},{\"from\":60,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal snow depth card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"cm\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "snow", "snowfall", "flurry", "blizzard", "snowstorm", "snowflake", "sleet", "whiteout", "snowdrift"], "resources": [{"link": "/api/images/system/horizontal_snow_depth_card_system_widget_image.png", "title": "\"Horizontal snow depth card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_snow_depth_card_system_widget_image.png", "publicResourceKey": "qF4B57Dzr4LGDTGVn4UiddS1oYps19da", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAxlBMVEUAAADf39/f39/g4ODg4ODf39/////k5OQwWtfg4OCXrOsjTMfl6/qxwfDx8fFKb9zOzs5+mObz8/O7u7tYWFjy9f3k6fjj4+PIyMisrKw+Ys48PDz5+fnL1vXb29vV1dVaedXCwsKQkJA9ZNkwV8u2trZkg+Hn5+eKoul2j9zY4Pe+zPPW3fWCgoKkt+51j9x0dHSRpuMvLy9wjuRXed9mZmbt7e1KSkr39/dMbdGenp7I0vG6x+2fsebJyclLS0udnZ0hISFSqBZZAAAABnRSTlMAIEDfv1C6kOEmAAADuklEQVR42u3aeXOaQBjHcdukT1ZuOQKogIBXtRrrEdv0fv9vqvuANU7GsZVOMsT5fSZRZP1jv1k2OqMNAAAAAAAAAAAAAAAAAAAAAAAAAAAAgJf05tVrsLdXN6+df/VOdtx8olfPvnnbuLbpAny6bjTpIjQRUjMIqRuE1A1C6gYhdXMkxCO6U4luac94yHw6YvZ41qcgpAqeMeS2/ZFDPvS/0R/uQ/CDDMMgeVP8kE987Pp/HhmdJOTYSqa6RlI0V6Ye7UWKMq0ewt6rH+/UD/327WNI5vu0XHQSZ+s+OLPANbZE/naz9DO3E2TbzcJZzkJ341IVuhA6ESlCMjUqaevioVc55PaWS/pq0fGVSkbYyahDi9BNqGO4M9eZEclf1++Es0UW0pI6fshPqUAzi5BI5NFkLIZUWomWNlmLceWQz+2iRC3vvlBhQXKiHLKRIeRuwk5AFHLI0gn8/wwZC5NDFNEi8oQgiQ9yvuaESTvedKrxneZ5ER9N9Oh0SHlJ9dSi4yOVsh/ugjgkcN2Mspnxi5dp6y79n52Nk7mubFpUDJmI8ZBD5mJ8GKJPeZvsF2ieCyHGRa4pj/SVvFmfDClL7tSDDmY8PWBGeZOF5X01pqkVIV6e69qYJ/uoxQNlbT6fmkLhjaRwhanLtOmJkLter622e32191nt93q9D/R3TkDVKUInDuHJSkM6MN9fWSt+hiYfKmJVrJvGl6RyImQoQ2RBm0PUIuR5abwzOIT3fEsxDyc3yXONSrmY8JO1cieREMWfQKlwaT0XOXNdX4vWlFrFHsmFt2/k2e/wErBTIac3+zPTxI5JQxHRbnEYr5D+2Hs85Ox/vzYdYd8/PWHT2SK2FuMJrYp5mZyjlR3K4a5XuHr9JOTsF8RBGWPTfUyFbpe6TYtPNKlLRKNwRN04LgfkifOShuULotwjQ2HyHm9xUd5i0W67lBvoSci5b1E4JEms+Pt3Sx6llMap0x2kVpIElnNvOV0ia2Q7Tky2VQxYgXN2COkm/9fSdq8oYkenQiQH8zmdFeIdvGk8DBnYgTWyypBREstjeW4UW2lQhDTjWIYEziiwZW5q0/k0zSPmHR+s+jbeo73QilNrkASpzStiOXE6sAeBJc/dy4lbxCFdXpGRE/JAkqb00jjkbPHoxGASxPQv6hDS7Z4atOlf1CKkjhBSNwipG4TUDULqptm4voCP2Ynsa/mFgQv4oP2T/MJA492Vf/PaXb29pC/VAAAAAAAAAAAAAAAAAAAAAAAAAAAAALyQ32C4FBab7K0VAAAAAElFTkSuQmCC", "public": true}]}