<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="400" height="200" fill="none" version="1.1" viewBox="0 0 400 200">
<tb:metadata xmlns=""><![CDATA[{
  "title": "Right elbow drain pipe",
  "description": "Right elbow drain pipe with configurable fluid and leak visualizations.",
  "searchTags": [
    "pipe",
    "drain"
  ],
  "widgetSizeX": 2,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "fluid-background",
      "stateRenderFunction": "var color = ctx.properties.fluidColor;\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "leak",
      "stateRenderFunction": "var leak = ctx.values.leak;\nif (leak) {\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "pipe-background",
      "stateRenderFunction": "var color = ctx.properties.pipeColor;\nelement.attr({fill: color});",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "openInSeparateDialog": false,
        "openInPopover": false
      }
    },
    {
      "id": "leak",
      "name": "{i18n:scada.symbol.leak}",
      "hint": "{i18n:scada.symbol.leak-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.leak-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    }
  ],
  "properties": [
    {
      "id": "fluidColor",
      "name": "{i18n:scada.symbol.fluid-color}",
      "type": "color",
      "default": "#518DA0",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "pipeColor",
      "name": "{i18n:scada.symbol.pipe-color}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata><g tb:tag="clickArea">
  <rect x="136" y="64" width="164" height="72" fill="#fff" tb:tag="pipe-background"/>
  <rect x="136" y="64" width="164" height="72" fill="url(#paint0_linear_1924_304906)"/>
  <rect x="137.5" y="65.5" width="161" height="69" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
  <path d="m64 64v-50h72v50h-72z" fill="#fff" tb:tag="pipe-background"/>
  <path d="m64 64v-50h72v50h-72z" fill="url(#paint1_linear_1924_304906)"/>
  <path d="m65.5 62.5v-47h69v47h-69z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
  <rect transform="rotate(-90 51.5 12.5)" x="51.5" y="12.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/>
  <path d="m136 136s-30.518-1.718-50.4-21.6-21.6-50.4-21.6-50.4h72v72z" fill="#fff" tb:tag="pipe-background"/>
  <path d="m136 136s-30.518-1.718-50.4-21.6-21.6-50.4-21.6-50.4h72v72z" fill="url(#paint2_linear_1924_304906)"/>
  <path d="m134.5 65.5v68.865c-0.833-0.087-1.972-0.222-3.358-0.428-3.12-0.464-7.489-1.287-12.471-2.723-9.983-2.876-22.316-8.18-32.01-17.875-9.6942-9.694-14.998-22.027-17.875-32.01-1.4355-4.9815-2.2588-9.3508-2.7228-12.47-0.2062-1.3867-0.3412-2.525-0.4273-3.3584h68.865z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
  <ellipse transform="rotate(-90 300 100)" cx="300" cy="100" rx="36" ry="4" fill="#5D5C5C"/>
  <path d="m323 94.5c-4-9.2-20-22.333-24-24.5 0 0-1.5 18.101-1.5 30.5s1.5 33 1.5 33c0 6.5-1.5 9-4 14.5-5.089 11.196-3.5 10.5-12.5 13.5s-24 2.5-21.5 11.5 32-8 34-4-16 17-10.5 21.5 17.5-10.5 21.5-10.5 4.5 10.5 10 10.5 7-11.5 16-10.5 12 13 18 17 14-2 14.5-11-20.5-7-20.5-13 39 1.5 45.5-4 0.5-6.5-6.5-10.5-38 5.5-39 0 16.5 0 15-5.5-16.5-2-23-10.5-8-36.5-13-48z" fill="#518DA0" tb:tag="fluid-background"/>
 </g><g style="display: none;" tb:tag="leak">
  <path d="m153.01 88.724c0.447 0.4587 0.927 1.092 1.445 1.447 1.445 1.447 3.278 2.5504 5.226 2.8189 0.906 0.0114 1.883-0.0808 2.756-0.244 2.374-0.3533 4.677-0.603 6.98-0.8527 2.058-0.1135 4.361-0.3632 5.704-1.8795-0.136-0.2456-0.169-0.4203-0.305-0.6659 3.324-1.0731 6.348-3.2652 8.751-5.8832 1.66-1.7562 3.047-4.0038 5.187-5.1267 0.737-0.4088 1.507-0.6431 2.173-0.9483 1.157-0.5778 2.21-1.2265 3.128-2.121 0.704-0.5835 1.272-1.4126 1.873-2.0672 1.731-1.8598 3.669-3.5777 5.951-4.9079-0.394 0.7965-0.787 1.593-1.18 2.3895 0 0 0.098 0.524 0.065 0.3493-0.278-0.0384-0.491 0.2726-0.53 0.5509 1.01 0.0824 2.019 0.1648 2.925 0.1763 4.841 0.2702 9.752 0.4367 14.669 0.1503-4.508 1.0234-9.218 1.4518-13.993 1.5309-1.081 0.0212-2.09-0.0613-3.209 0.2382-2.691 0.5933-4.602 2.9388-6.235 5.3226l-0.071 0.1036c-0.142 0.2073-0.355 0.5182-0.219 0.7639 0.066 0.3493 0.551 0.5296 0.933 0.639 1.981 0.4432 3.89 0.99 5.8 1.5368 2.395 0.7272 4.578 1.7653 7.044 2.3888 2.188 0.5852 4.414 0.8921 6.537 1.1279 1.009 0.0825 1.948 0.2686 2.892 0.0017 1.048-0.1959 1.96-0.6373 2.838-1.2535 1.125-0.7524 2.179-1.4012 3.2-2.2247 5.912-4.6296 21.148-3.4571 26.341-9.0368-1.846 2.6948-3.867 5.4222-5.713 8.1169 4.093-1.3073 8.487-1.4958 12.951-1.7879 1.255-0.0539 2.51-0.1077 3.803-0.4398 1.048-0.1959 2.135-0.67 3.046-1.1115 1.823-0.883 3.614-1.9407 4.646-3.67-0.502 1.1785-1.212 2.2149-1.955 3.0767 0.382 0.1094 0.867 0.2897 1.249 0.3991 3.263 1.0169 6.455 2.1374 9.614 3.0833-1.43 0.0865-2.925-0.1763-4.349-0.5427-1.91-0.5468-3.639-1.5792-5.625-1.5695-2.265-0.0285-4.269 1.34-6.474 2.1137-3.466 1.2804-7.368 1.1963-11.133 1.3578-3.766 0.1616-7.007-0.5296-13.148 0.2188-4.426 0.5394-11.568 1.5556-14.243 3.6823-0.879 0.6161-1.726 1.4069-2.468 2.2687-0.53 0.5509-0.956 1.1728-1.453 1.8983-0.568 0.8291-1.065 1.5546-1.737 2.3128-0.355 0.5182-0.813 0.9654-1.305 1.238-0.562 0.3762-1.364 0.4358-1.992 0.4627-2.232 0.1461-4.464 0.2921-6.696 0.4382 0.349-0.0653 0.873-0.1632 1.223-0.2285 2.794-0.5223 5.485-1.1156 7.734-2.6204 0.491-0.2726 0.95-0.7198 1.13-1.2054 0.071-0.1037 0.142-0.2073 0.039-0.2783 0.147-0.6602-0.469-1.5392-1.064-1.3376-0.654-0.6007-1.8-0.9287-2.564-1.1475-6.046-1.4005-12.162-2.6973-18.175-3.9232-0.661-0.1477-1.321-0.2954-2.123-0.2359-0.802 0.0596-1.397 0.2612-2.063 0.5664-4.973 1.9235-10.158 4.1579-13.969 7.9429-0.071 0.1037-0.142 0.2073-0.213 0.311 0 0 0.103 0.071 0.207 0.142-0.317 0.2399-0.666 0.3052-0.983 0.5451-2.145 1.5759-3.538 4.2764-4.615 6.7369-0.726 2.3722-0.904 3.5502-0.487 4.3342 0.474 0.891 2.078 3.92 4.351 4.252 1.01 0.082 2.483-0.847 2.927-0.711 0.157 0.048 1.591 1.801-0.027 0.649 0.784-0.417 9.773-1.923 15.178-4.922 0.491-0.272 1.053-0.649 1.512-1.096 0.317-0.24 0.53-0.551 0.639-0.933 0.071-0.103 0.038-0.278 6e-3 -0.453-0.033-0.174-0.208-0.142-0.311-0.213-4.643-2.1141-9.919-3.7488-13.142-7.9363 0.278 0.0384 0.382 0.1094 0.66 0.1477 0.104 0.0711 0.279 0.0384 0.486 0.1804 2.706 0.9402 5.297 2.7153 8.009 3.2025 1.217 0.2244 2.505 0.3452 3.612 0.9516 1.418 0.8194 2.547 2.5063 4.075 2.9441 0.764 0.218 1.67 0.23 2.368 0.099 1.921-0.3588 3.848-1.1708 5.802-1.3552 0.977-0.0923 2.09 0.0612 3.067-0.031 0.174-0.0327 0.278 0.0384 0.453 0.0057 2.232-0.146 10.307 0.6727 12.272-0.4176-1.719 0.954-9.342 0.141-10.925 1.3406 2.81 1.0115 5.516 1.9515 8.468 2.7555-2.576-0.242-5.113-0.762-7.612-1.56-1.249-0.399-2.499-0.798-3.786-0.919-2.472-0.17-5.071 1.4-5.242 3.872-0.038 0.278-0.076 0.556 0.06 0.802 0.19 1.501 1.079 2.871 1.864 4.17 2.497 3.691 5.033 7.103 7.776 10.657 2.023 2.604 4.117 5.105 6.883 6.847 0.207 0.142 0.485 0.181 0.627-0.027-0.082 1.01 0.676 1.681 1.576 2.146 0.829 0.568 1.8 0.929 2.7 1.393 0.797 0.393 1.593 0.787 2.39 1.18 3.775 1.825 7.622 3.546 11.577 4.885-2.123-0.236-4.31-0.821-6.318-1.891-1.489-0.716-2.837-1.639-4.294-2.18-1.56-0.612-3.197-0.668-4.757-1.28-0.693-0.322-1.49-0.716-2.254-0.934 0.643 1.506 1.357 2.909 2.001 4.415-0.72-0.949-1.44-1.899-2.16-2.849-0.856-1.196-1.783-2.288-2.71-3.38-2.476-2.61-5.902-4.5-7.99-7.453-0.137-0.246-0.273-0.492-0.409-0.737 0.278 0.038 0.485 0.18 0.764 0.218-0.033-0.174-0.24-0.316-0.273-0.491-1.428-2.806-3.451-5.41-5.513-7.736-1.75-2.113-3.708-4.368-6.278-5.062-0.764-0.219-1.599-0.334-2.362-0.553-0.764-0.218-1.523-0.89-1.757-1.66-3.161 1.947-6.328 4.695-10.049 4.125-2.887-0.454-5.249 1.484-6.683-0.268 1.201 0.367-0.552 1.802-2.537 1.851-1.986 0.049-6.614-6.224-6.651-8.838-4e-3 -2.439 0.898-4.8669 2.383-6.5905 0.071-0.1037 0.071-0.1037 0.142-0.2073-3.661 0.2325-7.498 0.4977-11.258 0.2063-1.113-0.1534-2.297-0.2032-3.301-0.7386-1.516-1.3433-2.71-3.3795-3.8-5.3447z" fill="#5C5A5A" style=""/>
  <path d="m176.07 110.09c0.95-0.72 1.344-1.294 2.567-1.522 2.445-0.457 3.381 0.075 5.892-0.033 2.756-0.244 3.944-0.825 6.537-1.942 1.331-0.61 2.56-1.292 3.684-2.044 0.3 1.119 3.052 1.505 4.198 1.833 1.145 0.328 2.433 0.449 3.295 1.192 0.966 0.813 1.369 2.003 2.017 3.057 0.753 1.124 1.822 2.009 2.82 2.997 1.925 2.081 3.042 4.673 3.985 7.299-0.104-0.071-0.104-0.071-0.207-0.142-1.428-2.806-3.452-5.41-5.513-7.736-1.75-2.113-3.708-4.368-6.278-5.062-0.764-0.219-1.599-0.334-2.363-0.553s-2.992-0.703-3.226-1.472c-3.161 1.946-5.097 3.841-8.819 3.271-2.886-0.454-3.492 0.085-6.39 0.536-0.556-0.077-1.429 0.086-2.199 0.321z" fill="#8B8B8B" style=""/>
  <path d="m181.61 92.781c0.279 0.0384 0.382 0.1094 0.66 0.1477 0.104 0.071 0.279 0.0384 0.486 0.1804l0.104 0.071c0.654 0.6007 1.309 1.2013 2.209 1.6657 0.622 0.426 1.418 0.8194 2.111 1.1418 2.979 1.4314 6.384 2.241 9.04 4.3651-0.497 0.726-0.994 1.451-1.627 1.931 0.316-0.24 0.529-0.551 0.639-0.933 0.071-0.103 0.038-0.278 5e-3 -0.453-0.032-0.174-0.207-0.142-0.31-0.213-4.889-1.9779-10.094-3.7162-13.317-7.9037z" fill="#8B8B8B" style=""/>
  <path d="m204 101.87c0.568-0.829 1.518-1.548 2.533-1.9189 1.015-0.3705 2.27-0.4244 3.389-0.7239 0.175-0.0326 0.278 0.0384 0.453 0.0057 2.232-0.146 9.367 0.6164 11.332-0.4739-1.719 0.954-8.401 0.1973-9.985 1.397 2.81 1.011 5.788 1.87 8.74 2.674-2.576-0.242-5.385-0.68-7.884-1.478-1.249-0.4-2.498-0.799-3.786-0.919-2.472-0.171-5.071 1.399-5.241 3.871-0.039 0.278-0.077 0.557 0.059 0.802-0.201-0.595-0.299-1.119-0.223-1.675 6e-3 -0.453 0.258-1.042 0.613-1.561z" fill="#8B8B8B" style=""/>
  <path d="m153.01 88.724c0.447 0.4586 0.927 1.0919 1.445 1.4469 0.72 0.95 1.407 1.7253 2.514 2.3317 1.315 0.7484 2.843 1.1858 4.234 1.3776 2.505 0.3452 4.982 0.0629 7.46-0.2194 1.152-0.1249 2.336-0.0751 3.269 0.5639-0.175 0.0327-0.175 0.0327-0.35 0.0653 0.071-0.1037 0.071-0.1036 0.142-0.2073-3.662 0.2326-7.498 0.4977-11.258 0.2063-1.113-0.1534-2.297-0.2032-3.301-0.7386-1.871-0.8251-3.065-2.8613-4.155-4.8264z" fill="#8B8B8B" style=""/>
  <path d="m178.26 89.88c0.775-0.6872 1.79-1.0577 2.566-1.7448 0.879-0.6162 1.55-1.3743 2.254-1.9578 1.442-0.9924 3.085-1.3898 4.455-2.2785 0.808-0.5125 1.687-1.1287 2.598-1.5702 1.681-0.6757 3.662-0.2325 5.365 0.1723 6.427 1.5098 12.751 2.9487 19.179 4.4586 1.042 0.2571 2.395 0.7271 2.52 1.8787-6e-3 0.4529-0.186 0.9385-0.399 1.2494 0.071-0.1036 0.142-0.2073 0.038-0.2783 0.148-0.6602-0.468-1.5391-1.063-1.3376-0.655-0.6006-1.801-0.9287-2.564-1.1475-6.046-1.4005-12.163-2.6973-18.176-3.9232-0.66-0.1477-1.32-0.2954-2.123-0.2358-0.802 0.0595-1.397 0.2611-2.063 0.5663-4.972 1.9235-10.158 4.1579-13.969 7.943-0.071 0.1036-0.142 0.2072-0.213 0.3109 0 0 0.104 0.071 0.207 0.142-0.316 0.2399-0.666 0.3052-0.982 0.5451 0.71-1.0364 1.42-2.0728 2.37-2.7926z" fill="#8B8B8B" style=""/>
  <path d="m197.91 73.644c1.305-1.238 3.226-1.597 5.044-2.0271 0.174-0.0326 0.174-0.0326 0.349-0.0653 4.84 0.2702 9.752 0.4367 14.669 0.1503-4.509 1.0234-9.218 1.4518-13.993 1.5309-1.081 0.0212-2.09-0.0612-3.209 0.2382-2.691 0.5933-4.602 2.9388-6.235 5.3226 0.18-0.4856 0.606-1.1074 0.748-1.3147 0.426-0.6219 0.781-1.1401 1.207-1.762 0.29-0.8675 0.716-1.4894 1.42-2.0729z" fill="#8B8B8B" style=""/>
  <path d="m207.85 94.682c0.349-0.0653 0.873-0.1632 1.222-0.2285 0.104 0.071 0.279 0.0383 0.279 0.0383 1.08-0.0212 2.161-0.0424 3.138-0.1346 1.921-0.359 3.262-0.8768 4.534-2.2895 0.672-0.7581 1.382-2.5157 1.95-3.3449 1.633-2.3838 4.364-3.5327 6.581-5.2122 1.971-1.5433 8.279-0.7511 11.049-1.2173 2.771-0.4661 3.371-0.2823 5.407-0.3364 3.263-0.0867 3.209-0.2383 4.814-0.3574 2.128-0.217 4.055-1.029 6.183-1.2461 1.43-0.0865 2.925 0.1763 4.251 0.0188 2.232-0.146 4.269-1.34 6.299-2.081 1.119-0.2995 2.238-0.599 3.318-0.6202 3.263 1.0169 6.455 2.1375 9.614 3.0834-1.43 0.0865-2.925-0.1763-4.349-0.5428-1.91-0.5468-3.639-1.5792-5.625-1.5694-2.265-0.0286-4.269 1.34-6.474 2.1136-3.466 1.2804-7.368 1.1963-11.133 1.3579-3.766 0.1615-6.58-0.174-9.741 1.7724-2.986 1.9137-13.062 0.1541-15.738 2.2808-0.879 0.6162-1.725 1.407-2.468 2.2688-0.529 0.5509-0.955 1.1727-1.452 1.8982-0.568 0.8292-1.065 1.5547-1.737 2.3128-0.355 0.5183-0.814 0.9655-1.305 1.238-0.562 0.3763-1.364 0.4358-1.992 0.4628-2.232 0.146-4.464 0.2921-6.625 0.3345z" fill="#8B8B8B" style=""/>
  <path d="m209.91 120.87c0.944-0.267 2.123 0.236 2.674 0.766 0.998 0.988 1.925 2.08 2.955 3.243 0.895 0.917 1.718 1.938 2.684 2.752 2.449 1.982 5.466 3.135 8.489 3.836 0 0 0.103 0.071 0.174-0.033 3.776 1.825 7.622 3.546 11.578 4.885-2.123-0.235-4.311-0.821-6.319-1.891-1.489-0.716-2.836-1.639-4.293-2.18-1.56-0.612-3.198-0.668-4.758-1.28-0.693-0.322-1.489-0.716-2.253-0.934 0.643 1.506 1.357 2.909 2 4.416-0.72-0.95-1.44-1.9-2.159-2.85-0.856-1.196-1.784-2.288-2.711-3.38-2.721-2.473-5.973-4.396-8.061-7.35z" fill="#8B8B8B" style=""/>
 </g><defs>
  <linearGradient id="paint0_linear_1924_304906" x1="178.64" x2="178.44" y1="64" y2="136" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint1_linear_1924_304906" x1="64" x2="136" y1="51" y2="51.654" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint2_linear_1924_304906" x1="86" x2="135.68" y1="111" y2="63.663" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".35637"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".4903"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".56651"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".63374"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".75781"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
 </defs>
</svg>