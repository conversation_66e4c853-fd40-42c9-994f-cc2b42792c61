{"fqn": "simple_soil_moisture_chart_card", "name": "Simple soil moisture chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_soil_moisture_chart_card_system_widget_image.png", "description": "Displays historical soil moisture values as a simplified chart. Optionally may display the corresponding latest soil moisture value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'soilMoisture', label: 'Soil Moisture', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'soilMoisture', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Soil Moisture\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#D81838\"},{\"from\":20,\"to\":40,\"color\":\"#F36900\"},{\"from\":40,\"to\":60,\"color\":\"#4B70DD\"},{\"from\":60,\"to\":100,\"color\":\"#234CC7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Soil Moisture\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"%\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "soil", "moisture"], "resources": [{"link": "/api/images/system/simple_soil_moisture_chart_card_system_widget_image.png", "title": "\"Simple soil moisture chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_soil_moisture_chart_card_system_widget_image.png", "publicResourceKey": "amhe8dpMal2Z1qsiuhK7OuwUGiizl5iK", "mediaType": "image/png", "data": "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", "public": true}]}