{"fqn": "temperature_chart_card", "name": "Temperature chart card", "deprecated": false, "image": "tb-image;/api/images/system/temperature_chart_card_system_widget_image.png", "description": "Displays a temperature data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'temperature', label: 'Temperature', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: '°C', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'temperature', '°C', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#234CC7\"},{\"from\":-20,\"to\":0,\"color\":\"#305AD7\"},{\"from\":0,\"to\":10,\"color\":\"#7191EF\"},{\"from\":10,\"to\":20,\"color\":\"#FFA600\"},{\"from\":20,\"to\":30,\"color\":\"#F36900\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Temperature\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"device_thermostat\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["temperature", "weather", "environment"], "resources": [{"link": "/api/images/system/temperature_chart_card_system_widget_image.png", "title": "\"Temperature chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "temperature_chart_card_system_widget_image.png", "publicResourceKey": "Z0R4RmTd5t4ESxWuK04aj0ERHdIurlmT", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAA+VBMVEXg4ODf39/g4ODg4OAAAAD////g4OAjTMfv7+/CwsLj4+PIyMjz8/POzs7n5+f5+vl0dHTb29vx8fEhISE9PT2QkJBYWFjk6fjt7e28vLw8PDwvLy+2trbV1dWRpeOCgoKsrKzU1NRKSkrj8OZ1j9w/Ys6tvOry9PswV8vI0vE+Ys7x9/NaedW5ubmdnZ2Mv5v9/f1aeNW6urpmZmbW3vTz+fJog9menp42kFG6x+7r9uuDmt9MbtLG38222L6p0LSwsLCeseeesOeax6h9t45vr4JFmV7f8N3U59pwsYNsbGxSoGqkpKRhp3YZgDjW3fWDmuCFvJRWVlZQKBd3AAAABXRSTlPvIL+vAC9A4IoAAAiBSURBVHja7NVBb5swGMbxdO2Dar+2AdtYBoJIfE9UtYdd+/0/1QyszWg6Me2AnIi/UPTaPv1kFHZPDz922a33+PC0e9g9v+DGe9GR8ahxBz3H1+rm72PoZbfLcBdlGySxNkhqbZDU2iCptUFS6zvIqw34yNlYj6X4K1ZvGVJUln/MnIeea2gOaB2H+IAPq2krBh6Hk42ncWs64xrLrQIJHJ+5Cji3zqGwzrwFQ9yExoHbYDm19lSF8MaD6ckiPs46uND8xGJrQA5fIW2HQEWPokDRc6N128Wx6qnVOHSd0YNiglQ4uGFYbA2IMXMIN845KioMT8ENYMk1zp3JAr0rDL9ADqha5wqs0BKEt8YUX26E0OEC0TzeSA/OyULH1RxydgDHCi1BziY2hxwa6/QFEtqoaKw9kQWKNhium0I3rRsh6FtbYakVIKfoaPAlfRm5mVZ6dqT/uAadyL9W34YOf49bJNhdf9lvsg2SWouQ3OOf8znmCYmFVoOIutznuOqI7/akwLzaY6HVIOThBUFlWe0Zy4/yKPZE/r0EFCNihGE7I5ll70qy2gOCmBdMqjgdymEQdYer1oaw6UeVpUINRWovarBxW5XDEJdelmrck14NQPLi6FED2CNXJVt4wdaBSI9M/IYweFJesCsIfUDEABFxLQVohGRSpAHJ95KhY/UEyWtZjhDZfUI6xsR4uFcTRBFlgpgcIYrJNCA30wZJrQ2SWv8BEfPRI4mWIEcJlIQhiSmGqfwXe3WsWlEIRFG02sLgGQe7K5jC///JvGsIBFKE2zwm4LaS07gqP+T76iToL4jPYBjLC477fvnyaKJHkb+GsYeFqjce9G6Ir6sbXYPJoApMbfWGyeXN+RpMI0rnQe+GtGoyRp9MDL8hqH5DesH2YJq03JBlWMyfkPVy1LohMcoeblavmSER3EdXIEQAoQvar0FtFR6X8B9RdR6UF/KsAzmQ/9KBZOtAsnUg2TqQbB1Itj7ZK5vVBoEoCq8OWXY5u2yu8ScjDEIS8qfSakyCJLTv/zKduY7TFgppKhGFfAgjZ3U+LnfmKTI0niI3ERt/E+E7C9+PAjjGIRLFE0PoCVheQ05iF4xCZDppmdviXhuECzSMQYQ9kt2KR+CS0Nsl5gjAjEBEtJMQc/0XucQKTdEwfJE33daHwbe9fZfEeiRgRiBiFv1rNh6AlanvJO/YkhcDLFICOFF/Iu9J4oEJdO0dgL1eGTAbns2fWRrAUH0l4FynQJr3/SBGtnboRMR9S7LcasBIWhOyg5mIKijvV8TcWwKAPrx/iWxJEQGglAAtUlaXtURRFtSfiNv1LiJ0LI9HAJlKWeSQmw+kFPUoIkJ96QadRD6yKjuDYZH8LKsUUArqYSLxrEGAsa+IQCcRWV11fctFAmVdmPiksoeJzCYNrUhgPHx0E4E8EH6nL5Fgz5VviAzxQRQWNHiu8Y/rdzF8kc/2zq23TRgMwzu9XoxPmFOAQAJRl27TOm2rtGlaL7rbatKk/f8fs5hSUUoYzdoUiHiUXGCihEevP9v4gtT5YS74dloX1czyHYZxiCzLzlTF8/a0tkSZn2yw+fp1OXAR43FxZxn57mYd9r5IZrk8vdga3nvdxSOmgVwIQIjtK1FPIPLzdTGBVJyaGWVenjm5btq6YA+U4gzgYADRMkfIDy/yrrgTfHPDzzKi9yfLD+bEHAV7iUihQkoAMFEc6ifpWpvXNZa1W92q1OcfgOX9u1ZYhIGoOBAgYT8iWL4vDj7/vlVJJz++4r4oIUIthUhhSEX6BImczmuc3nSlzWYzv/NB7AWZNujGwyQyNCaRoTGJDI1JZGhMIkPjmEVIRJUQOcbFDpGccFAwjIsdIlJHioJiXOwQCaElK0USqtEjX1K0wSjtrBFBkbIUhn5zsV13EazabDpE6hD0iB1bDvt4vi5srOa1jWX43XqgwNjEbpA545xHCo+K1I7d82yEIsbjDsyOFzY7hMiXb7gHjr/yv2WMOQ/0MJzZC89mjy3ieIvAQhfMi+0gWHvueg+T1dajhezc9Vbpo4r4sWUv/A6NYOE719qx3ZmwX2LyaMfKAndtPZ6I5WZAFnsWWnFWru2gxOqSthbndklX0FbmPJ6IfV58pe36rcWxCBgqmMvwL7xVL8t4y7VgqEJpFscZanxcWP+sC+dwIs6slfXVrCS/cleN01/+LFbkbuPV2pq18cvNZ3vBHyeRrKzHKpRmcTRw4lVrwJ7fzx1i9btVpTSLo1nPbVNPEKAXET9GnbMqFObFGSo6y6QcynsRsbysOdReh5IWM0crK8/ZOXKcoRcRe40mbBvKVqcxCXSXieP5OLQIC0oyqzn0NkNpKY6OMrFjHFwk9Utit1qtBXbrjIsG3WWSmZan61rMdtcZDJkJpJvuMqkyOoQIFwRSCBjC+i/6sWcq4EE9url89Gz8D7JLhIFCcL578yEL3ODBPdpa+FZF98j735sPCShlMFxS3bgKO87wQM7c26ToZv/tICGRcwCMY1TsSERoqSJ5BFumoZQK29e4GOwuyiQydiaRoXG8IgQVCjVkWGslHOFAHne2S4QCUNK8lYrUzVOQQg6EWiLkkAwwh6AJtK4+XbT2QrtIRCLz5gkBpEbCL0kCSiJJGJM64owYRcpyI8JZKnhEEuSE9mPSLpLnEQQLQVGKUDCeQEvOhOYCiaASoDwSGlBCU6lBEYFo9EC7CDcXpZGUIqp4oKyiEJJxUohAKSPCf2mAEE5DUYhogr5oijAhVMoEtEhBNKCoEPxyexAyEXLGJATnLAWgFbQ0iaQpUpEgZCn6w4h0wik6EDnDnvQhAoUuer97Od4JcaxMIkNjEhkak8jQmESGxhGJPDuOPwgmz17McAQ4L7f/Pu2MPpNPs63Gq+cvydh59uL5q7/+I/3Y5YHUpAAAAABJRU5ErkJggg==", "public": true}]}