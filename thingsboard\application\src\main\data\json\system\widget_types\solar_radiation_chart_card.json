{"fqn": "solar_radiation_chart_card", "name": "Solar radiation chart card", "deprecated": false, "image": "tb-image;/api/images/system/solar_radiation_chart_card_system_widget_image.png", "description": "Displays a solar radiation data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'radiation', label: 'Solar Radiation', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'W/m²', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'radiation', 'W/m²', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Solar Radiation\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 1100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"W/m²\",\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":48,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#5B7EE6\"},{\"from\":0,\"to\":250,\"color\":\"#80C32C\"},{\"from\":250,\"to\":500,\"color\":\"#FFA600\"},{\"from\":500,\"to\":1000,\"color\":\"#F36900\"},{\"from\":1000,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 1100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"W/m²\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < -120) {\\n\\tvalue = -120;\\n} else if (value > 120) {\\n\\tvalue = 120;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"W/m²\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Solar Radiation\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:radioactive\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "solar", "radiation", "sunlight"], "resources": [{"link": "/api/images/system/solar_radiation_chart_card_system_widget_image.png", "title": "\"Solar radiation chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "solar_radiation_chart_card_system_widget_image.png", "publicResourceKey": "t83N4YZ0IJIL59ftJup4kC082cxlzg3i", "mediaType": "image/png", "data": "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", "public": true}]}