{"fqn": "indoor_illuminance_progress_bar_with_background", "name": "Indoor illuminance progress bar with background", "deprecated": false, "image": "tb-image;/api/images/system/indoor_illuminance_progress_bar_with_background_system_widget_image.png", "description": "Displays indoor illuminance reading as a horizontal progress bar with background. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'illuminance', label: 'Illuminance', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Illuminance\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 400 - 200;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\\n\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":100,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":100,\"to\":300,\"color\":\"#F89E0D\"},{\"from\":300,\"to\":500,\"color\":\"#F77410\"},{\"from\":500,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":1000,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/indoor_illuminance_progress_bar_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":100,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":100,\"to\":300,\"color\":\"#F89E0D\"},{\"from\":300,\"to\":500,\"color\":\"#F77410\"},{\"from\":500,\"to\":null,\"color\":\"#DE2343\"}]},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Illuminance\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"lx\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"mdi:lightbulb-on\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["progress", "environment", "illuminance", "indoor", "brightness", "luminance", "luminosity", "light", "light level", "light intensity", "lux", "candela", "foot-candle"], "resources": [{"link": "/api/images/system/indoor_illuminance_progress_bar_with_background_system_widget_background.png", "title": "\"Indoor illuminance progress bar with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_illuminance_progress_bar_with_background_system_widget_background.png", "publicResourceKey": "1CAjNCSJ0Pxtp1tDD674sOqtkIhbc7qs", "mediaType": "image/png", "data": "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*************************************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", "public": true}, {"link": "/api/images/system/indoor_illuminance_progress_bar_with_background_system_widget_image.png", "title": "\"Indoor illuminance progress bar with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_illuminance_progress_bar_with_background_system_widget_image.png", "publicResourceKey": "699ezgd4MtNlxQ8lrSuK3InF2D0XLGaq", "mediaType": "image/png", "data": "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", "public": true}]}