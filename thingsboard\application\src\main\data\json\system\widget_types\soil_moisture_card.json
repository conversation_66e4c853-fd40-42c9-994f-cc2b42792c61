{"fqn": "soil_moisture_card", "name": "Soil moisture card", "deprecated": false, "image": "tb-image;/api/images/system/soil_moisture_card_system_widget_image.png", "description": "Displays the latest soil moisture telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'soilMoisture', label: 'Soil Moisture', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Soil Moisture\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:water-percent\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#D81838\"},{\"from\":20,\"to\":40,\"color\":\"#F36900\"},{\"from\":40,\"to\":60,\"color\":\"#4B70DD\"},{\"from\":60,\"to\":100,\"color\":\"#234CC7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":52,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#D81838\"},{\"from\":20,\"to\":40,\"color\":\"#F36900\"},{\"from\":40,\"to\":60,\"color\":\"#4B70DD\"},{\"from\":60,\"to\":100,\"color\":\"#234CC7\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Soil moisture card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"%\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "soil", "moisture"], "resources": [{"link": "/api/images/system/soil_moisture_card_system_widget_image.png", "title": "\"Soil moisture card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "soil_moisture_card_system_widget_image.png", "publicResourceKey": "V2W0NOQWCDV3LQmJmN3P58TpXWMbQcNc", "mediaType": "image/png", "data": "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", "public": true}]}