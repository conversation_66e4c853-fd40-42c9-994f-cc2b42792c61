<?xml version="1.0" encoding="UTF-8"?>
<!--
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-LwM2M_BinaryAppDataContainer-V1_0_1-20190221-A
   Type: xml

Public Reachable Information
   Path: http://www.openmobilealliance.org/tech/profiles
   Name: LwM2M_BinaryAppDataContainer-v1_0_1.xml

NORMATIVE INFORMATION

  Information about this file can be found in the latest revision of

  OMA-TS-LWM2M_BinaryAppDataContainer-V1_0_1

  This is available at http://www.openmobilealliance.org/

  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

CHANGE HISTORY

15062018 Status changed to Approved by DM, Doc Ref # OMA-DM&SE-2018-0061-INP_LWM2M_APPDATA_V1_0_ERP_for_final_Approval
21022019 Status changed to Approved by IPSO, Doc Ref # OMA-IPSO-2019-0025-INP_LwM2M_Object_App_Data_Container_1_0_1_for_Final_Approval

LEGAL DISCLAIMER

Copyright 2019 Open Mobile Alliance.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

The above license is used as a license under copyright only. Please
reference the OMA IPR Policy for patent licensing terms:
https://www.omaspecworks.org/about/intellectual-property-rights/

-->
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>BinaryAppDataContainer</Name>
		<Description1><![CDATA[This LwM2M Objects provides the application service data related to a LwM2M Server, eg. Water meter data. 
There are several methods to create instance to indicate the message direction based on the negotiation between Application and LwM2M. The Client and Server should negotiate the instance(s) used to exchange the data. For example:
 - Using a single instance for both directions communication, from Client to Server and from Server to Client.
 - Using an instance for communication from Client to Server and another one for communication from Server to Client
 - Using several instances
]]></Description1>
		<ObjectID>19</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:19</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0"><Name>Data</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration />
				<Units />
				<Description><![CDATA[Indicates the application data content.]]></Description>
			</Item>
			<Item ID="1"><Name>Data Priority</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>1 bytes</RangeEnumeration>
				<Units />
				<Description><![CDATA[Indicates the Application data priority:
0:Immediate
1:BestEffort
2:Latest
3-100: Reserved for future use.
101-254: Proprietary mode.]]></Description>
			</Item>
			<Item ID="2"><Name>Data Creation Time</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Time</Type>
				<RangeEnumeration />
				<Units />
				<Description><![CDATA[Indicates the Data instance creation timestamp.]]></Description>
			</Item>
			<Item ID="3"><Name>Data Description</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>32 bytes</RangeEnumeration>
				<Units />
				<Description><![CDATA[Indicates the data description.
e.g. "meter reading".]]></Description>
			</Item>
			<Item ID="4"><Name>Data Format</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>32 bytes</RangeEnumeration>
				<Units />
				<Description><![CDATA[Indicates the format of the Application Data.
e.g. YG-Meter-Water-Reading
UTF8-string
]]></Description>
			</Item>
			<Item ID="5"><Name>App ID</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>2 bytes</RangeEnumeration>
				<Units />
				<Description><![CDATA[Indicates the destination Application ID.]]></Description>
			</Item></Resources>
		<Description2><![CDATA[]]></Description2>
	</Object>
</LWM2M>
