{"fqn": "ozone_o3_chart_card_with_background", "name": "<PERSON><PERSON> (O3) chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/ozone-value-chart-card-with-background.svg", "description": "A beneficial layer in the upper atmosphere, but harmful when present near ground level. Results displays mainly from vehicle exhaust and industrial emissions by combining the latest and aggregated values and optional simplified chart. ", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'ozone', label: 'Ozone', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'µg/m³', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'ozone', 'µg/m³', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Ozone\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#3B911C\"},{\"from\":50,\"to\":100,\"color\":\"#7CC322\"},{\"from\":100,\"to\":130,\"color\":\"#F89E0D\"},{\"from\":130,\"to\":240,\"color\":\"#F77410\"},{\"from\":240,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 250) {\\n\\tvalue = 250;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nif (value < -10) {\\n\\tvalue = -10;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/ozone-value-and-chart-card-background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Ozone\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"public\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "particulate", "matter", "air", "coarse particulates", "coarse particles", "inhalable particles", "larger particulates", "dust", "airborne coarse particles", "O3", "ozone"], "resources": [{"link": "/api/images/system/ozone-value-and-chart-card-background.png", "title": "ozone-value-and-chart-card-background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "ozone-value-and-chart-card-background.png", "publicResourceKey": "EGL09d5VVzmW9pOtmK1l95DzB7bIOMNS", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/ozone-value-chart-card-with-background.svg", "title": "ozone-value-chart-card-with-background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "ozone-value-chart-card-with-background.svg", "publicResourceKey": "ypG2IkwizUaBpxkNZZx2FpFLW3lPWbeY", "mediaType": "image/svg+xml", "data": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "public": true}]}