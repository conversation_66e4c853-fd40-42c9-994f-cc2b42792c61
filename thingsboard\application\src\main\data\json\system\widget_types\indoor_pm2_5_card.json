{"fqn": "indoor_pm2_5_card", "name": "Indoor PM2.5 card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_pm2_5_card_system_widget_image.png", "description": "Displays the latest indoor fine particulate matter (PM2.5) telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pm2.5', label: 'PM2.5', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM2.5\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 150) {\\n\\tvalue = 150;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:broom\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":35,\"color\":\"#80C32C\"},{\"from\":35,\"to\":75,\"color\":\"#FFA600\"},{\"from\":75,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":32,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":35,\"color\":\"#80C32C\"},{\"from\":35,\"to\":75,\"color\":\"#FFA600\"},{\"from\":75,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Indoor PM2.5  card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"µg/m³\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["environment", "pm2.5", "particulate", "matter", "air", "fine particulates", "fine particles", "particulate matter 2.5", "airborne particles", "microscopic particles", "respirable particles", "aerosol particles", "dust particles"], "resources": [{"link": "/api/images/system/indoor_pm2_5_card_system_widget_image.png", "title": "\"Indoor PM2.5 card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_pm2_5_card_system_widget_image.png", "publicResourceKey": "4wdNXLd2l0Dl90Q0H1mhywi92aBNtKJJ", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAn1BMVEXg4ODf39/g4ODg4OAAAAD////g4OCAwyzv9+WQy0ag0mC/4ZWv2Xvz8/MhISGqqqrf8Mq2trbCwsL5+fnu7u73+/LQ6bCenp7Ozs6IxzlYWFjIyMiYzlPj4+Pa2trn5+fV1dW8vLywsLC43Yh0dHTH5aPX7L2srKykpKSQkJA9PT3P6LCn1m6CgoLn9NcvLy9LS0vn9NjY7L1mZmarq6sAoDYiAAAABXRSTlPvIL+vAC9A4IoAAAXySURBVHja7M+5EQAwCAMwE56Ojv1nzRqGszYQwh5qO7eAYXK9hsE7D5iHyhNwJVKKkFGEjSJsFGGjCBtF2CjCRhE2n/w6Wm0QBsMwfPQRa5tgS8U1aVZLHaIQItj7v7b9RmatYsZgZJa9J4kGIQ8hB/5TSITV9iPIbjOWcK01dyPHMHDFh2UKnv4OsmMsxqNCUFKjFA0AGmT/rhotK3gKDxkcm+gJorWqJcp+v43opgpGcHTJ1p2Yp2AQv4MgAIzojsIAuZASd5ooxyJYBX+BIH7HACksQSrLUchC9q9ruKwV1msJB/E7CCJlIyqC5PaK+i4dJBN7uEyVVyu6I2l8YuePaOZwkKrKFAiijbwK7iB3kj2iB0/hIGkEbBl1co4ZBJSDKCELdJB82Hpe8LVA0u1p8yXpHB4IaqE6iLZNWZY5sgzctsrQiqcwkCRm1CA5Y5ap4drXGllDsJamXRlaA+RS0MxTGIhjjCUJ1t0SZMMmkh3W3RLkxiaSGOtuCZKwZ8nLngjiieRV7wgSNpKs+D/kWwhuvyDhRyx2CQVJzyNJjHn7yZfvmPV2gOt4GQEO/XdZKAiiT3bsXcWRGIjCcHSEB6aoLVWxEjToFiwK5v2fbyX3eoe5OJigQYF+GlMuOfnA0KCXd8grvsYJI833DwUFmpPOh/5tJ8QMetSxNsLICadJoxNiBoBMda4ugHyW3J5BEldWYs92BA8g+vmE2hg2jjykcqTGjsa3jJkkoIlMyDwtiL32NFYcL4Lg9vh3veEZxMh6Ls2yIeAdYui5xjFqpsL3baVSH5DcoQHAecp57JyMlV4EOd/po19PIbHFnpGE6QOEIE7SHJv3d4gfQ/wPYZw/53kaaOyKBzpdBsHtdThe8F1ciLQ57TlF1IigAIrAD4hRp8OP0bqmAanjQMkeEO1EE2IdhSGHtpQ7GeM6yKD8fvuD7xJmdqmJOBIRgmcA2rgeCJULVOZ4sG9qbQ7thHgHRBbG6ODaYK1xQmycl7tpDIQf5MzYrXllKoofRCJl3/1uyKptyGptyGptyGptyGptyGptyGptyGptyGptyGptyGptyGptyGptyGptyGptyF/2zaDVcRCKwqtwvOrCjYLgTnTTUnj9/z9uJrm1xtcZp1OQJLycVao3eL/Ec5Vg96YTZG/6uSDi7oxom6wQteVt2f5N9mbsQBAXMEs2OWgA/0+iQZ1e5XmYQSAKRffamPERCPVOF0aAJEB2DIgDIJ2JHvB1CPoIJHdP4ErSy3C3MSAE6HJM8FLf0kcgV9Abz00MATGcMBOpYn5AVpC3vSsA/dopzO3ZprWHGuMRB3i+kkCsb6kCll5Osh4XFjMrtPAlsEwbBehHp51yAEDqSQq6jF5HiKcWZ6JEH6QexaMaKBFKhOdOqcAqJEIBZixIfiYkAJr6IMwNmRRhfd+lGMxHpwh8EWmpI9a5OQ5IQ0Esm54tC9EHeXhLtQtOAkQB+ZrB/HLBsXmaPMiICOShIPXvPW5JuQ/C8S/VQrYuCsC1zNo0gy4K4zxSnyLPfRL/BglAbEG+gEuNqPfUgmiuga5pGgmi8Jy6mvPpg3BpaEE0YPsg4zeNqdYVBwTzWw5ANre/guAFhDhgS5CMOlDAWvr9N2KAvDHIl1+NIzsgXY9o0LQtiKB1wsaxEoDkzAqkrUkR8A2Ih94WRNBrRfyz2WE4vFlHbOBAB5htQQhAkA/lLojXSns80rwCoLj8huCN77YgWOvSA2H5R5q22WsJIG4MQmvlCkJE30BCALzKRLxc20jzb/aIW2GnEqGJmE4Spb18RVmern35QsFm4Y3vot1/DlpAGt3LTmBmUIqtfkAQBXmb5xc7q+h4IIvXPX3fzh4PZBIarGhr4wFAnFL3qZVwWuvUYBwA5B2dICfIUXSC7E0nyN50gvxq706KAAZiIAaO9/Avv/DHGhqKS81ACERjCI0hNIbQGEJjCI0hNIbQGEJjCI0hNIbQGEJjCI0hNIbQGEJjCE0nd4TOGrCevvfZqQyYT7+pnNr9d1l1Ptzuw4oYi6aJAAAAAElFTkSuQmCC", "public": true}]}