{"fqn": "input_widgets.update_server_date_attribute", "name": "Update server date attribute", "deprecated": true, "image": "tb-image;/api/images/system/update_shared_date_attribute_system_widget_image.png", "description": "Simple form to input new date value for pre-defined server-side attribute key.\nThe widget is deprecated. Use \"Update Multiple Attributes\" widget. Attribute type and date value type can be selected in widgets data key configuration.", "descriptor": {"type": "latest", "sizeX": 7.5, "sizeY": 3.5, "resources": [], "templateHtml": "<div tb-toast toastTarget=\"{{ toastTargetId }}\" style=\"width: 100%; height: 100%;\">\n    <form *ngIf=\"attributeUpdateFormGroup\"\n          class=\"attribute-update-form\"\n          [formGroup]=\"attributeUpdateFormGroup\"\n          (ngSubmit)=\"updateAttribute()\">\n        <div style=\"padding: 0 8px; margin: auto 0;\">\n            <div class=\"attribute-update-form__grid\" [class.!hidden]=\"!entityDetected || !isValidParameter || !dataKeyDetected\">\n                <div class=\"grid__element\">\n                    <mat-form-field class=\"mat-block\" style=\"width: 100%;\">\n                        <mat-label *ngIf=\"settings.showLabel\">{{ labelValue }}</mat-label>\n                        <mat-datetimepicker-toggle [for]=\"datePicker\" matPrefix></mat-datetimepicker-toggle>\n                        <mat-datetimepicker #datePicker type=\"{{datePickerType}}\"\n                                            openOnFocus=\"true\"></mat-datetimepicker>\n                        <input matInput formControlName=\"currentValue\"\n                               [required]=\"settings.isRequired\"\n                               [matDatetimepicker]=\"datePicker\"\n                               (focus)=\"isFocused = true;\"\n                               (blur)=\"isFocused = false;\">\n                        <button *ngIf=\"!settings.isRequired && (attributeUpdateFormGroup.get('currentValue').value)\"\n                                type=\"button\"\n                                mat-icon-button matSuffix\n                                (click)=\"clear($event)\">\n                            <mat-icon>{{'clear'}}</mat-icon>\n                        </button>\n                        <mat-error *ngIf=\"attributeUpdateFormGroup.get('currentValue').hasError('required') && settings.isRequired\">\n                          {{requiredErrorMessage}}\n                        </mat-error>\n                    </mat-form-field>\n                </div>\n    \n                <div class=\"grid__element\">\n                    <button mat-icon-button class=\"applyChanges\"\n                               type=\"submit\"\n                               [disabled]=\"(originalValue === attributeUpdateFormGroup.get('currentValue').value || attributeUpdateFormGroup.invalid || !attributeUpdateFormGroup.dirty) && (originalValue === attributeUpdateFormGroup.get('currentValue').value || settings.isRequired)\"\n                               matTooltip=\"{{ 'widgets.input-widgets.update-attribute' | translate }}\"\n                               matTooltipPosition=\"above\">\n                        <mat-icon>check</mat-icon>\n                    </button>\n                    <button mat-icon-button class=\"discardChanges\"\n                               type=\"button\"\n                               [disabled]=\"originalValue === attributeUpdateFormGroup.get('currentValue').value || attributeUpdateFormGroup.invalid\"\n                               (click)=\"attributeUpdateFormGroup.get('currentValue').patchValue(originalValue)\"\n                               matTooltip=\"{{ 'widgets.input-widgets.discard-changes' | translate }}\"\n                               matTooltipPosition=\"above\">\n                        <mat-icon>close</mat-icon>\n                    </button>\n                </div>\n            </div>\n    \n            <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\" [class.!hidden]=\"entityDetected\" >\n                {{ 'widgets.input-widgets.no-entity-selected' | translate }}\n            </div>\n            <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\"\n                 [class.!hidden]=\"!entityDetected || dataKeyDetected\">\n                {{ 'widgets.input-widgets.no-attribute-selected' | translate }}\n            </div>\n            <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\"\n                 [class.!hidden]=\"!entityDetected || isValidParameter\">\n                {{ 'widgets.input-widgets.timeseries-not-allowed' | translate }}\n            </div>\n        </div>\n    </form>\n</div>", "templateCss": ".attribute-update-form {\n    overflow: hidden;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n}\n\n.attribute-update-form__grid {\n    display: flex;\n}\n.grid__element:first-child {\n    flex: 1;\n}\n.grid__element:last-child {\n    margin-top: 19px;\n    margin-left: 7px;\n}\n.grid__element {\n    display: flex;\n}\n\n.attribute-update-form .mat-button.mat-icon-button {\n    width: 32px;\n    min-width: 32px;\n    height: 32px;\n    min-height: 32px;\n    padding: 0 !important;\n    margin: 0 !important;\n    line-height: 20px;\n}\n\n.attribute-update-form .mat-icon-button mat-icon {\n    width: 20px;\n    min-width: 20px;\n    height: 20px;\n    min-height: 20px;\n    font-size: 20px;\n}\n\n.tb-toast {\n    font-size: 14px!important;\n}", "controllerScript": "let $scope;\r\nlet settings;\r\nlet attributeService;\r\nlet utils;\r\nlet translate;\r\n\r\nself.onInit = function() {\r\n    self.ctx.ngZone.run(function() {\r\n       init(); \r\n       self.ctx.detectChanges(true);\r\n    });\r\n};\r\n\r\nfunction init() {\r\n\r\n    $scope = self.ctx.$scope;\r\n    attributeService = $scope.$injector.get(self.ctx.servicesMap.get('attributeService'));\r\n    utils = $scope.$injector.get(self.ctx.servicesMap.get('utils'));\r\n    translate = $scope.$injector.get(self.ctx.servicesMap.get('translate'));\r\n    $scope.toastTargetId = 'input-widget' + utils.guid();\r\n    settings = utils.deepClone(self.ctx.settings) || {};\r\n    settings.showLabel = utils.defaultValue(settings.showLabel, true);\r\n    settings.showResultMessage = utils.defaultValue(settings.showResultMessage, true);\r\n    settings.isRequired = utils.defaultValue(settings.isRequired, true);\r\n    $scope.settings = settings;\r\n    $scope.isValidParameter = true;\r\n    $scope.dataKeyDetected = false;\r\n    $scope.entityDetected = false;\r\n\r\n    $scope.datePickerType = settings.showTimeInput ? 'datetime' : 'date'; \r\n    \r\n    $scope.requiredErrorMessage = utils.customTranslation(settings.requiredErrorMessage, settings.requiredErrorMessage) || translate.instant('widgets.input-widgets.entity-attribute-required');\r\n    $scope.labelValue = translate.instant('widgets.input-widgets.date');\r\n    \r\n    if (settings.showTimeInput) {\r\n        $scope.labelValue += \" & \" + translate.instant('widgets.input-widgets.time');\r\n    }\r\n    \r\n    var validators = [];\r\n    \r\n    if (settings.isRequired) {\r\n        validators.push($scope.validators.required);\r\n    }\r\n    \r\n    $scope.attributeUpdateFormGroup = $scope.fb.group(\r\n        {currentValue: [undefined, validators]}\r\n    );\r\n\r\n    if (self.ctx.datasources && self.ctx.datasources.length) {\r\n        var datasource = self.ctx.datasources[0];\r\n        \r\n        if (datasource.type === 'entity') {\r\n            if (datasource.entityType && datasource.entityId) {\r\n                $scope.entityName = datasource.entityName;\r\n                if (settings.widgetTitle && settings.widgetTitle.length) {\r\n                    $scope.titleTemplate = utils.customTranslation(settings.widgetTitle, settings.widgetTitle);\r\n                } else {\r\n                    $scope.titleTemplate = self.ctx.widgetConfig.title;\r\n                }\r\n\r\n                $scope.entityDetected = true;\r\n            }\r\n        }\r\n        if (datasource.dataKeys.length) {\r\n            if (datasource.dataKeys[0].type !== \"attribute\") {\r\n                $scope.isValidParameter = false;\r\n            } else {\r\n                $scope.currentKey = datasource.dataKeys[0].name;\r\n                $scope.dataKeyType = datasource.dataKeys[0].type;\r\n                $scope.dataKeyDetected = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    self.ctx.widgetTitle = utils.createLabelFromDatasource(self.ctx.datasources[0], $scope.titleTemplate);\r\n    \r\n    $scope.clear = function(event) {\r\n        event.stopPropagation();\r\n        $scope.attributeUpdateFormGroup.get('currentValue').patchValue(undefined);\r\n    }\r\n    \r\n    $scope.updateAttribute = function () {\r\n        if ($scope.entityDetected) {\r\n            var datasource = self.ctx.datasources[0];\r\n            var currentValueInMilliseconds;\r\n            \r\n            if (!$scope.attributeUpdateFormGroup.get('currentValue').value) {\r\n                currentValueInMilliseconds = undefined;\r\n            } else {\r\n                currentValueInMilliseconds = $scope.attributeUpdateFormGroup.get('currentValue').value.getTime();\r\n            }\r\n            \r\n            attributeService.saveEntityAttributes(\r\n                datasource.entity.id,\r\n                'SERVER_SCOPE',\r\n                [\r\n                    {\r\n                        key: $scope.currentKey,\r\n                        value: currentValueInMilliseconds\r\n                    }\r\n                ]\r\n            ).subscribe(\r\n                function success() {\r\n                    $scope.originalValue = $scope.attributeUpdateFormGroup.get('currentValue').value;\r\n                    if (settings.showResultMessage) {\r\n                        $scope.showSuccessToast(translate.instant('widgets.input-widgets.update-successful'), 1000, 'bottom', 'left', $scope.toastTargetId);\r\n                    }\r\n                },\r\n                function fail() {\r\n                    if (settings.showResultMessage) {\r\n                        $scope.showErrorToast(translate.instant('widgets.input-widgets.update-failed'), 'bottom', 'left', $scope.toastTargetId);\r\n                    }\r\n                }\r\n            );\r\n        }\r\n    };\r\n    \r\n    $scope.isValidDate = function(date) {\r\n      return date instanceof Date && !isNaN(date);\r\n    }\r\n}\r\n\r\nself.onDataUpdated = function() {\r\n    try {\r\n        if ($scope.dataKeyDetected) {\r\n            if (!$scope.isFocused) {\r\n                $scope.originalValue = moment(self.ctx.data[0].data[0][1]).toDate();\r\n\r\n                if (!$scope.isValidDate($scope.originalValue)) {\r\n                    $scope.originalValue = undefined;\r\n                }\r\n                \r\n                $scope.attributeUpdateFormGroup.get('currentValue').patchValue($scope.originalValue);\r\n                self.ctx.detectChanges();\r\n            }\r\n        }\r\n    } catch (e) {\r\n        console.log(e);\r\n    }\r\n};\r\n\r\nself.onResize = function() {\r\n};\r\n\r\nself.typeParameters = function() {\r\n    return {\r\n        maxDatasources: 1,\r\n        maxDataKeys: 1,\r\n        singleEntity: true\r\n    };\r\n};\r\n\r\nself.onDestroy = function() {\r\n}\r\n", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-update-date-attribute-widget-settings", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Sin\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.23592248334107624,\"funcBody\":\"return Math.round(1000*Math.sin(time/5000));\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{},\"title\":\"Update server date attribute\",\"dropShadow\":true,\"enableFullscreen\":false,\"enableDataExport\":false,\"widgetStyle\":{},\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{}}"}, "resources": [{"link": "/api/images/system/update_shared_date_attribute_system_widget_image.png", "title": "\"Update shared date attribute\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "update_shared_date_attribute_system_widget_image.png", "publicResourceKey": "QmWXDfjAvp9sotfJnDgrgv0DCt23Gwj6", "mediaType": "image/png", "data": "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", "public": true}]}