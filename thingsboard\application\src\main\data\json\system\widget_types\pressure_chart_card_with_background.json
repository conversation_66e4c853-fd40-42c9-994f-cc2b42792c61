{"fqn": "pressure_chart_card_with_background", "name": "Pressure chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/pressure_chart_card_with_background_system_widget_image.png", "description": "Displays a pressure data by combining the latest and aggregated values with the background image and optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pressure', label: 'Pressure', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'hPa', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'pressure', 'hPa', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < 980) {\\n\\tvalue = 980;\\n} else if (value > 1040) {\\n\\tvalue = 1040;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"hPa\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1000,\"color\":\"#DE2343\"},{\"from\":1000,\"to\":1020,\"color\":\"#7CC322\"},{\"from\":1020,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < 980) {\\n\\tvalue = 980;\\n} else if (value > 1040) {\\n\\tvalue = 1040;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"hPa\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"hPa\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/pressure_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Pressure\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"compress\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "barometry"], "resources": [{"link": "/api/images/system/pressure_chart_card_with_background_system_widget_background.png", "title": "\"Pressure chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "pressure_chart_card_with_background_system_widget_background.png", "publicResourceKey": "SxVntK2IJ0kDXgjnAUR7gU1LLJCOLvBN", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/pressure_chart_card_with_background_system_widget_image.png", "title": "\"Pressure chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "pressure_chart_card_with_background_system_widget_image.png", "publicResourceKey": "Gs6FO7m18BBcJXG2wcLKtzYDSJlFjTfB", "mediaType": "image/png", "data": "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", "public": true}]}