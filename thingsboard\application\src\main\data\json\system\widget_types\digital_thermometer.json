{"fqn": "digital_gauges.digital_thermometer", "name": "Digital thermometer", "deprecated": false, "image": "tb-image;/api/images/system/digital_thermometer_system_widget_image.png", "description": "Preconfigured gauge to display temperature. Allows to configure temperature range, gradient colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<canvas id=\"digitalGauge\"></canvas>", "templateCss": "#gauge {\n    text-align: center;\n   /* margin-left: -100px;\n    margin-right: -100px;*/\n    /*margin-top: -50px;*/\n    \n}\n", "controllerScript": "self.onInit = function() {\n    self.ctx.gauge = new TbCanvasDigitalGauge(self.ctx, 'digitalGauge');    \n}\n\nself.onDataUpdated = function() {\n    self.ctx.gauge.update();\n}\n\nself.onResize = function() {\n    self.ctx.gauge.resize();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        supportsUnitConversion: true\n    };\n}\n\nself.onMobileModeChanged = function() {\n    self.ctx.gauge.mobileModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.gauge.destroy();\n}\n\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-digital-gauge-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-digital-simple-gauge-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temp\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.7282710489093589,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nif (value < -60) {\\n\\tvalue = 60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#000000\",\"color\":\"rgba(255, 254, 254, 0.87)\",\"padding\":\"0px\",\"settings\":{\"maxValue\":60,\"donutStartAngle\":90,\"showValue\":true,\"showMinMax\":true,\"gaugeWidthScale\":1,\"levelColors\":[\"#304ffe\",\"#7e57c2\",\"#ff4081\",\"#d32f2f\"],\"titleFont\":{\"family\":\"Roboto\",\"size\":12,\"style\":\"normal\",\"weight\":\"500\"},\"labelFont\":{\"family\":\"Roboto\",\"size\":8,\"style\":\"normal\",\"weight\":\"500\"},\"valueFont\":{\"family\":\"Segment7Standard\",\"style\":\"normal\",\"weight\":\"500\",\"size\":18},\"minMaxFont\":{\"family\":\"Segment7Standard\",\"size\":12,\"style\":\"normal\",\"weight\":\"500\"},\"dashThickness\":1.5,\"minValue\":-60,\"gaugeColor\":\"#333333\",\"neonGlowBrightness\":35,\"gaugeType\":\"donut\",\"animation\":true,\"animationDuration\":500,\"animationRule\":\"linear\"},\"title\":\"Digital thermometer\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"widgetStyle\":{},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{},\"configMode\":\"basic\"}"}, "tags": ["pyrometer", "temp probe", "heat indicator", "mercury column", "clinical indicator"], "resources": [{"link": "/api/images/system/digital_thermometer_system_widget_image.png", "title": "\"Digital thermometer\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "digital_thermometer_system_widget_image.png", "publicResourceKey": "0NzwSPw39XTJxGX0yVN1LfrIwewHpvOJ", "mediaType": "image/png", "data": "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", "public": true}]}