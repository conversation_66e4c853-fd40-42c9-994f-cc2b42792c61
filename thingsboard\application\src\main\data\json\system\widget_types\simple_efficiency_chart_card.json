{"fqn": "simple_efficiency_chart_card", "name": "Simple efficiency chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_efficiency_chart_card.svg", "description": "Displays historical efficiency values as a simplified chart. Optionally may display the corresponding efficiency value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'efficiency', label: 'Efficiency', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'efficiency', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Efficiency\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 30;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 30;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":60,\"color\":\"#FFA600\"},{\"from\":60,\"to\":80,\"color\":\"#3FA71A\"},{\"from\":80,\"to\":null,\"color\":\"#305AD7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Efficiency\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"trending_up\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"units\":\"%\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true},\"margin\":\"0px\"}"}, "tags": ["productivity", "effectiveness", "performance", "capability"], "resources": [{"link": "/api/images/system/simple_efficiency_chart_card.svg", "title": "simple_efficiency_chart_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_efficiency_chart_card.svg", "publicResourceKey": "PuALTRP2xyDXXFg5qTVeTGaFanFmJc5a", "mediaType": "image/svg+xml", "data": "PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMTYwIiBmaWxsPSJub25lIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjExMiIgeT0iMjQiIGZpbGw9IiNmZmYiIHJ4PSI0Ii8+PHJlY3Qgd2lkdGg9IjE5OSIgaGVpZ2h0PSIxMTEiIHg9Ii41IiB5PSIyNC41IiBzdHJva2U9IiMwMDAiIHN0cm9rZS1vcGFjaXR5PSIuMSIgcng9IjMuNSIvPjxwYXRoIGZpbGw9IiMwMDAiIGZpbGwtb3BhY2l0eT0iLjkiIGQ9Ik0yMy41IDQ3LjZWNDloLTUuM3YtMS40aDUuM1pNMTguNyAzOXYxMEgxN1YzOWgxLjdabTQuMSA0LjJ2MS4zaC00LjZ2LTEuM2g0LjZabS43LTQuMnYxLjRoLTUuM1YzOWg1LjNabTMuNyAxMGgtMS42di04LjFjMC0uNi4xLTEgLjMtMS40LjItLjQuNS0uNy45LS44YTMuNCAzLjQgMCAwIDEgMi4zLS4ydjEuM2wtLjMtLjFhNCA0IDAgMCAwLS40IDBsLS42LjFhMSAxIDAgMCAwLS40LjRsLS4yLjdWNDlabTEuNi03LjR2MS4yaC00LjN2LTEuMmg0LjNabTIuNyA3LjRoLTEuN3YtNy45YzAtLjYuMi0xIC40LTEuNS4yLS40LjYtLjcgMS0xbDEuNi0uMmgxbDEuMS40LS4yIDEuMy0uOC0uMi0xLS4xYy0uNSAwLS44IDAtMSAuMy0uMy4yLS40LjYtLjQgMVY0OVptMS41LTcuNHYxLjJoLTQuM3YtMS4ySDMzWm0yLjcgMFY0OWgtMS42di03LjRoMS42Wm01LjEgNi4yLjctLjFjLjMtLjEuNC0uMy42LS41bC4yLS42aDEuNWMwIC40LS4xLjktLjQgMS4zYTMgMyAwIDAgMS0xIC45Yy0uNS4yLTEgLjMtMS42LjMtLjUgMC0xIDAtMS40LS4zYTMgMyAwIDAgMS0xLS44Yy0uNC0uMy0uNi0uNy0uNy0xLjEtLjItLjUtLjItMS0uMi0xLjV2LS4yYzAtLjUgMC0xIC4yLTEuNS4xLS40LjMtLjguNi0xLjEuMy0uNC42LS42IDEtLjhsMS41LS4zYy42IDAgMS4xIDAgMS42LjMuNC4zLjguNiAxIDEgLjMuNC40LjkuNCAxLjRoLTEuNWwtLjItLjctLjUtLjVjLS4yLS4yLS41LS4yLS44LS4yLS4zIDAtLjYgMC0uOC4ybC0uNS41Yy0uMi4zLS4zLjUtLjMuOHYyLjFjMCAuMi4xLjUuMy43IDAgLjIuMy40LjUuNS4yLjIuNS4yLjguMlptNi4zLTYuMlY0OWgtMS42di03LjRINDdabS0xLjctMmMwLS4yIDAtLjQuMi0uNmwuNy0uMmMuMyAwIC41IDAgLjcuMi4yLjIuMi40LjIuNyAwIC4yIDAgLjQtLjIuNmExIDEgMCAwIDEtLjcuMiAxIDEgMCAwIDEtLjctLjIuOC44IDAgMCAxLS4yLS42Wm03IDkuNWMtLjUgMC0xIDAtMS41LS4yYTMuMyAzLjMgMCAwIDEtMS44LTIgNCA0IDAgMCAxLS4yLTEuM3YtLjNjMC0uNiAwLTEuMS4yLTEuNnMuNC0uOS43LTEuMmEzIDMgMCAwIDEgMS4xLS44bDEuMy0uMmMuNiAwIDEgMCAxLjQuMmwxIC44Yy4zLjMuNS43LjYgMS4xbC4yIDEuNXYuN2gtNS43di0xLjJoNHYtLjFsLS4xLS45LS41LS42LS45LS4yYy0uMiAwLS41IDAtLjcuMi0uMiAwLS40LjItLjUuNWwtLjQuN3YyLjNsLjUuNy42LjUuOC4xYTIuMyAyLjMgMCAwIDAgMS45LS45bC45LjktLjcuNmMtLjIuMi0uNS40LS45LjUtLjQuMi0uOC4yLTEuMy4yWm02LjItNlY0OWgtMS43di03LjRoMS42djEuNlptLS4zIDJoLS42YzAtLjYuMS0xIC4zLTEuNS4xLS41LjMtLjguNi0xLjFhMi43IDIuNyAwIDAgMSAyLTFsMSAuMS44LjUuNS44LjEgMS4zVjQ5aC0xLjZ2LTQuOGMwLS4zIDAtLjYtLjItLjhhLjkuOSAwIDAgMC0uNC0uNWwtLjctLjFhMS42IDEuNiAwIDAgMC0xLjQuN2wtLjMuNy0uMS44Wm05LjcgMi43LjctLjEuNS0uNS4zLS42SDcxYzAgLjQtLjEuOS0uNCAxLjNhMyAzIDAgMCAxLTEgLjljLS41LjItMSAuMy0xLjYuMy0uNiAwLTEgMC0xLjUtLjNhMyAzIDAgMCAxLTEtLjhjLS4zLS4zLS41LS43LS42LTEuMS0uMi0uNS0uMi0xLS4yLTEuNXYtLjJjMC0uNSAwLTEgLjItMS41LjEtLjQuMy0uOC42LTEuMS4zLS40LjYtLjYgMS0uOGwxLjUtLjNjLjYgMCAxLjEgMCAxLjYuMy40LjMuOC42IDEgMSAuMy40LjQuOS40IDEuNGgtMS41bC0uMi0uNy0uNS0uNWMtLjMtLjItLjUtLjItLjgtLjItLjMgMC0uNiAwLS44LjItLjMuMS0uNC4zLS42LjVsLS4yLjgtLjEuOXYxLjJsLjMuN2MuMi4yLjMuNC42LjUuMi4yLjUuMi44LjJabTYuNy40IDItNi42aDEuOGwtMyA4LjUtLjMuNmMwIC4yLS4yLjQtLjQuNmEyIDIgMCAwIDEtLjcuNWwtLjkuMmgtLjRsLS40LS4xdi0xLjNhNi4zIDYuMyAwIDAgMCAuMyAwaC43bC40LS40Yy4yLS4xLjMtLjMuMy0uNmwuNi0xLjRabS0xLjItNi42IDEuOCA1LjUuMyAxLjgtMS4xLjMtMi43LTcuNmgxLjdaIi8+PHBhdGggZmlsbD0iIzNGQTcxQSIgZD0iTTI5LjcgOTZWOThMMjIuMSAxMTZoLTMuM2w3LjYtMTcuM2gtOS42di0yLjZoMTIuOVptMTUuNyA4LjR2My4yYTE1IDE1IDAgMCAxLS41IDQgNyA3IDAgMCAxLTEuMyAyLjdjLS41LjctMS4yIDEuMi0yIDEuNWE3LjcgNy43IDAgMCAxLTQuOS4yIDUuMyA1LjMgMCAwIDEtMy4yLTIuNmMtLjMtLjctLjYtMS41LS44LTIuNS0uMi0xLS4zLTItLjMtMy4zdi0zLjJjMC0xLjYuMS0zIC40LTQgLjMtMSAuOC0yIDEuMy0yLjZhNSA1IDAgMCAxIDItMS41Yy45LS4zIDEuOC0uNSAyLjgtLjUuOCAwIDEuNS4xIDIuMi4zYTUuMSA1LjEgMCAwIDEgMy4xIDIuNmMuNC42LjcgMS41IDEgMi40bC4yIDMuM1ptLTMuMyAzLjd2LTQuMmwtLjEtMi4xLS40LTEuNWEzIDMgMCAwIDAtLjctMWMtLjMtLjQtLjYtLjYtLjktLjdsLTEuMS0uMmEzIDMgMCAwIDAtMS40LjNjLS40LjItLjcuNS0xIDFhNSA1IDAgMCAwLS42IDEuN2MtLjIuNi0uMiAxLjUtLjIgMi41djQuMmwuMSAyLjFjMCAuNi4yIDEuMi40IDEuNi4yLjQuNC44LjcgMSAuMi4zLjUuNS45LjdsMS4xLjFjLjUgMCAxIDAgMS40LS4zLjQtLjIuNy0uNSAxLTEgLjMtLjQuNS0xIC42LTEuNmwuMi0yLjZaTTUyLjIgMTAzLjF2LS45YzAtLjYuMS0xLjIuNC0xLjguMy0uNS43LTEgMS4yLTEuMi42LS4zIDEuMi0uNSAyLS41czEuNC4yIDIgLjVjLjUuMy45LjcgMS4yIDEuMi4zLjYuNCAxLjIuNCAxLjh2MWMwIC42LS4xIDEuMS0uNCAxLjctLjMuNS0uNyAxLTEuMyAxLjItLjUuNC0xLjEuNS0yIC41LS43IDAtMS4zLS4xLTEuOS0uNS0uNS0uMy0xLS43LTEuMi0xLjItLjMtLjYtLjQtMS4xLS40LTEuOFptMi0uOXYxbC4xLjhjLjEuMy4zLjUuNi43LjIuMi41LjIuOS4ycy43IDAgMS0uMmwuNC0uN2MuMi0uMy4yLS42LjItLjl2LS45YzAtLjMgMC0uNi0uMi0uOSAwLS4zLS4zLS41LS41LS43bC0xLS4yYy0uMyAwLS42IDAtLjguMi0uMy4yLS41LjQtLjYuN2wtLjIgMVptNi4yIDEwLjV2LS45YTMuMyAzLjMgMCAwIDEgMS43LTNjLjUtLjMgMS4yLS41IDItLjUuNyAwIDEuMy4yIDEuOS41LjUuMyAxIC43IDEuMiAxLjMuMy41LjQgMSAuNCAxLjd2MWMwIC42LS4xIDEuMi0uNCAxLjctLjMuNS0uNyAxLTEuMiAxLjMtLjUuMy0xLjIuNC0yIC40LS43IDAtMS40LS4xLTItLjQtLjUtLjQtLjktLjgtMS4yLTEuMy0uMy0uNS0uNC0xLjEtLjQtMS44Wm0yLS45djFjMCAuMiAwIC41LjIuOCAwIC4zLjMuNS41LjcuMy4yLjYuMyAxIC4zLjMgMCAuNy0uMS45LS4zbC41LS43LjItLjl2LS45YzAtLjMgMC0uNi0uMi0uOS0uMS0uMy0uMy0uNS0uNi0uN2wtLjktLjJjLS40IDAtLjcgMC0xIC4ybC0uNS43LS4xIDFabTIuMy0xMC40LTguMyAxMy4zLTEuNS0uOCA4LjQtMTMuNCAxLjQuOVoiLz48cGF0aCBzdHJva2U9IiMwMDAiIHN0cm9rZS1vcGFjaXR5PSIuOSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0xODcgNTRjLTE1LjUgMC0xMy43IDU2LjEtMjkuOCA1Ni4xLTEzLjcgMC0xNS45LTIzLjgtMjYuNC0yMy44LTEwLjYgMC0xNS4yIDMyLjctMjguOSAzMi43LTExLjEgMC0xNy4zLTYuNi0yNy45LTYuNiIvPjwvc3ZnPg==", "public": true}]}