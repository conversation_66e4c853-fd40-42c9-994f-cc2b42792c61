{"fqn": "horizontal_solar_radiation_card", "name": "Horizontal solar radiation card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_solar_radiation_card_system_widget_image.png", "description": "Displays the latest solar radiation telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'radiation', label: 'Solar Radiation', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Solar Radiation\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 1100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:radioactive\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#5B7EE6\"},{\"from\":0,\"to\":250,\"color\":\"#80C32C\"},{\"from\":250,\"to\":500,\"color\":\"#FFA600\"},{\"from\":500,\"to\":1000,\"color\":\"#F36900\"},{\"from\":1000,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#5B7EE6\"},{\"from\":0,\"to\":250,\"color\":\"#80C32C\"},{\"from\":250,\"to\":500,\"color\":\"#FFA600\"},{\"from\":500,\"to\":1000,\"color\":\"#F36900\"},{\"from\":1000,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal solar radiation card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"W/m²\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "solar", "radiation", "sunlight"], "resources": [{"link": "/api/images/system/horizontal_solar_radiation_card_system_widget_image.png", "title": "\"Horizontal solar radiation card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_solar_radiation_card_system_widget_image.png", "publicResourceKey": "aoy0M1txXSC9dLLJRpdRThXR8bpKI8FI", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAk1BMVEUAAADf39/f39/g4ODg4ODf39/////k5OSAwyzg4OCQy0bf8Mrz8/Pv9+XQ6bCQkJD3+/LOzs6srKxYWFjIyMig0mGIxzm43Yj5+fmYzlPb29vV1dXCwsK2tra/4ZVKSkrY7L08PDzn5+fn9NjH5aO8vLyw2Xuenp6CgoJ0dHS6urqo1m5mZmbt7e2rq6svLy8hISGYzLGRAAAABnRSTlMAIEDfv1C6kOEmAAAEBElEQVR42u3aiW7aQBSFYZqk18N4X/AKGLNDSNL3f7reO9i1ukAbpCInOp/SaDLjkvnrcamUjgAAAAAAAAAAAAAAAAAAAAAAAAAAAADu6cuHNxIPj9ZHN378yh1WTh+eYz2Mnhz6BPKn0Zg+hTFCBgYhQ4OQoUHI0CBkaP4Q4gYk7H7G2fkWiehIHcv3IxJm+lidJ+mY0P1cD1mruclR2qVWvEtq2SXx3h2Hw3iY1Mnel5E0RVUlC07jyMDiKf7qCptaQTuy1/NFN+V5xBZeYD6vzGbki0532cK9GpIpVRDzVF8S+5ZF8SHmHR/j0876VnNITMnJqg8Hag5xxAv1aff69ur7Vn3a09uhSS5WZKn6MdIk5orpdrOpks1rJWWFMqVb/duxSVOt3Ssh5iVt80L9SzuvjV/FFO39yNrtYysmDnmLG8uJ/IYX/IgX/ENMDfn+/ki19U1u3h8FhWLEVql8CxOktL16VhkZW7XmRSVfBkrJFoJ0S7/ICv5d3uUQVyv2LK8t5mQciBo+XDvf55ZjG8LbtqKD1Vi1CdnvojYkoti5HOLquXcOsSc80uete7LptDvbW3MgCr6kOx02Bfzo2nKaXM/u/syv3BFPsdTmywvFCjL8Oj7Q/lTz0do1cd2FHOOkObxRfKojXqjjmhputWI+WhdDhGtC2ltBPw6Qbje2km/7rAoVcFNGbKLl0olsaT3vTspcra88I8+KeeRlbpAqFtCZI7/a0a/6hf6T+NeQLkFLjkhVQGmxVgvezsJcn5kjki0yjvAWWk5Kltp0JaQwu1/I6doqtqKb3R4yUfZKZbL9wix4/FnuSPdXgMe7W6tU6+xyiDD1E+rdOyRT6zWPUx20J6+gPsTmEB6uPGb/LUSOFvXuGyKz22cV8JFYqGdzufd7SOd6SCAfvf8eMlGL9tEw+EakE9mvVmtzg9xbQ7Ru34jYxafXefl1wrk1ZC5P84rH/ZM65572GdUTui2kf5/tzGSb8vFSkpHnlI9DmRhTTkTL1yXlZXle4AnnfSFBqrZz3b+/Zd17cmqu8W4McZWR/RRSVWGZJCGPpjQtp5t8Ng2rKgk3L+GGdx4unc2mJCc0C2GyeVcIrbRS6Zo6HhewzGx4mwY3hmh1tvopJJw5SbgMzyHLquQxzy3LcJqYkHFZckgyWyYO504deh/XvfRU6u3N/4zf8hlNJ5zeicJyGs6qZOrIHQk35XTmzJKQ5142yyQkCcnljiw3kSxU0yndm4S8W7mky6qkpH8xhJA8v7bo0L8YRMgQIWRoEDI0CBkahAzNePT0CX7MTuQ8jR6sT/CD9tx6GI2+Po6tj+7x4TP9pxoAAAAAAAAAAAAAAAAAAAAAAAAAAACAO/kOdioE5akwBJsAAAAASUVORK5CYII=", "public": true}]}