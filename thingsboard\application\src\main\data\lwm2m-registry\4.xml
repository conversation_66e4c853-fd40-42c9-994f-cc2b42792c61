<?xml version="1.0" encoding="UTF-8"?>

<!--
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-XML_4-V1_3_1-20221209-A.xml
   Path: http://www.openmobilealliance.org/release/ObjLwM2M_Conn_Mon/

OMNA LwM2M Registry
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 4.xml

NORMATIVE INFORMATION

  Information about this file can be found in the latest revision of

  OMA-TS-LightweightM2M_Core-V1_2_1

  This is available at http://www.openmobilealliance.org/release/LightweightM2M/

  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

LEGAL DISCLAIMER

  Copyright 2022 Open Mobile Alliance.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

  1. Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
  3. Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived
  from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.

  The above license is used as a license under copyright only.  Please
  reference the OMA IPR Policy for patent licensing terms:
  https://www.omaspecworks.org/about/intellectual-property-rights/

-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.openmobilealliance.org/tech/profiles/LWM2M-v1_1.xsd">
	<Object ObjectType="MODefinition">
		<Name>Connectivity Monitoring</Name>
		<Description1><![CDATA[This LwM2M Object enables monitoring of parameters related to network connectivity.
In this general connectivity Object, the Resources are limited to the most general cases common to most network bearers. It is recommended to read the description, which refers to relevant standard development organizations (e.g. 3GPP, IEEE).
The goal of the Connectivity Monitoring Object is to carry information reflecting the more up to date values of the current connection for monitoring purposes. Resources such as Link Quality, Radio Signal Strength, Cell ID are retrieved during connected mode at least for cellular networks.]]></Description1>
		<ObjectID>4</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:4:1.3</ObjectURN>
		<LWM2MVersion>1.1</LWM2MVersion>
		<ObjectVersion>1.3</ObjectVersion>
		<MultipleInstances>Single</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>Network Bearer</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..50</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates the network bearer used for the current LwM2M communication session from the network bearer list below. 
The number range is split into three categories:
0 - 20 are Cellular Bearers 
21 - 40 are Wireless Bearers 
41 - 50 are Wireline Bearers
More specifically: 
0: GSM cellular network
1: TD-SCDMA cellular network
2: WCDMA cellular network
3: CDMA2000 cellular network
4: WiMAX cellular network
5: LTE-TDD cellular network
6: LTE-FDD cellular network
7: NB-IoT
8: 5G-NR TDD
9: 5G-NR FDD
10 - 20: Reserved for other types of cellular network
21: WLAN network
22: Bluetooth network
23: IEEE 802.15.4 network
24 - 40: Reserved for other types of local wireless network
41: Ethernet
42: DSL
43: PLC
44 - 50: reserved for other types of wireline networks.]]></Description>
			</Item>
			<Item ID="1">
				<Name>Available Network Bearer</Name>
        <Operations>R</Operations>
        <MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..50</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates a list of current available network bearer. Each Resource Instance has a value from the network bearer list.]]></Description>
			</Item>
			<Item ID="2">
				<Name>Radio Signal Strength</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>dBm</Units>
				<Description><![CDATA[Indicates the average value of the received signal strength indication used in the
current network bearer (as indicated by Resource 0 of this Object). The value is expressed in dBm. For the following network bearers the signal strength parameters indicated below are represented by this resource:
GSM:    RSSI
UMTS:   RSCP
LTE:    RSRP
NB-IoT: NRSRP
For more details on Network Measurement Report, refer to the appropriate Cellular or Wireless Network standards, (e.g. for LTE Cellular Network
refer to 3GPP TS 36.133 specification).]]></Description>
			</Item>
			<Item ID="3">
				<Name>Link Quality</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This contains received link quality  e.g. LQI for IEEE 802.15.4 (range 0...255), RxQual Downlink for GSM (range 0...7, refer to [3GPP 44.018] for more details on Network Measurement Report encoding), RSRQ for LTE, (refer to [3GPP 36.214]), NRSRQ for NB-IoT (refer to [3GPP 36.214]).]]></Description>
			</Item>
			<Item ID="4">
				<Name>IP Addresses</Name>
        <Operations>R</Operations>
        <MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[The IP addresses assigned to the connectivity interface. (e.g. IPv4, IPv6, etc.)]]></Description>
			</Item>
			<Item ID="5">
				<Name>Router IP Addresses</Name>
        <Operations>R</Operations>
        <MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[The IP address of the next-hop IP router, on each of the interfaces specified in resource 4 (IP Addresses).
Note: This IP Address doesn't indicate the Server IP address.]]></Description>
			</Item>
			<Item ID="6">
				<Name>Link Utilization</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..100</RangeEnumeration>
				<Units>/100</Units>
				<Description><![CDATA[The percentage indicating the average utilization of the link to the next-hop IP router.]]></Description>
			</Item>
			<Item ID="7">
				<Name>APN</Name>
        <Operations>R</Operations>
        <MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Access Point Name in case Network Bearer Resource is a Cellular Network.]]></Description>
			</Item>
      <Item ID="8">
        <Name>Cell ID</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[Serving Cell ID in case Network Bearer Resource is a Cellular Network.
As specified in TS [3GPP 23.003] and in [3GPP. 24.008]. Range (0...65535) in GSM/EDGE
UTRAN Cell ID has a length of 28 bits.
Cell Identity in WCDMA/TD-SCDMA. Range: (0...268435455).
LTE Cell ID has a length of 28 bits.
Parameter definitions in [3GPP 25.331].]]></Description>
      </Item>
      <Item ID="9">
        <Name>SMNC</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>0..999</RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[Serving Mobile Network Code. This is applicable when the Network Bearer Resource value is referring to a cellular network.
As specified in TS [3GPP 23.003].]]></Description>
      </Item>
      <Item ID="10">
        <Name>SMCC</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration>0..999</RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[Serving Mobile Country Code. This is applicable when the Network Bearer Resource value is referring to a cellular network.
As specified in TS [3GPP 23.003].]]></Description>
      </Item>
      <Item ID="11"><Name>SignalSNR</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration></RangeEnumeration>
        <Units>dB</Units>
        <Description><![CDATA[SINR: Signal to Interference plus Noise Ratio SINR is the ratio of the strength of the received signal to the strength of the received interference signal (noise and interference).]]></Description>
      </Item>
      <Item ID="12">
        <Name>LAC</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[Location Area Code in case Network Bearer Resource is a Cellular Network. As specified in TS [3GPP 23.003] and in [3GPP. 24.008]]]></Description>
      </Item>
      <Item ID="13">
	<Name>Coverage Enhancement Level</Name>
	<Operations>R</Operations>
	<MultipleInstances>Single</MultipleInstances>
	<Mandatory>Optional</Mandatory>
	<Type>Integer</Type>
	<RangeEnumeration>0..4</RangeEnumeration>
	<Units />
	<Description><![CDATA[Indicates the Coverage Enhancement Level of the UE in the serving cell. The Coverage Enhancement levels are defined and specified in 3GPP TS 36.331 and 36.213.
		0: No Coverage Enhancement in the serving cell
		1: Coverage Enhancement level 0
		2: Coverage Enhancement level 1
		3: Coverage Enhancement level 2
		4: Coverage Enhancement level 3]]></Description>
      </Item>		
    </Resources> <Description2></Description2>
    </Object>
</LWM2M>
