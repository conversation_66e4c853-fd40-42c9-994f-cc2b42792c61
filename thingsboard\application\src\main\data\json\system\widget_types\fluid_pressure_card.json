{"fqn": "fluid_pressure_card", "name": "Pressure card", "deprecated": false, "image": "tb-image;/api/images/system/pressure_card.svg", "description": "Displays the latest fluid pressure telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pressure', label: 'Pressure', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 15 - 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"square\",\"autoScale\":true,\"showLabel\":true,\"labelFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"#000000DE\",\"rangeList\":null,\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"compress\",\"iconColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":0,\"to\":5,\"color\":\"#305AD7\"},{\"from\":5,\"to\":10,\"color\":\"#3FA71A\"},{\"from\":10,\"to\":15,\"color\":\"#F36900\"},{\"from\":15,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":28,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":0,\"to\":5,\"color\":\"#305AD7\"},{\"from\":5,\"to\":10,\"color\":\"#3FA71A\"},{\"from\":10,\"to\":15,\"color\":\"#F36900\"},{\"from\":15,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Pressure card\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"units\":\"bar\",\"decimals\":0,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"showTitleIcon\":false,\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\"}"}, "tags": ["fluid pressure", "liquid pressure", "pump pressure", "hydraulic", "pump", "compressibility", "compressive stress", "pipe", "pipeline"], "resources": [{"link": "/api/images/system/pressure_card.svg", "title": "pressure_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "pressure_card.svg", "publicResourceKey": "1Who1XlC7s7FAZUTUtN9jYYvCldC5J5u", "mediaType": "image/svg+xml", "data": "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", "public": true}]}