{"fqn": "simple_solar_radiation_chart_card", "name": "Simple solar radiation chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_solar_radiation_chart_card_system_widget_image.png", "description": "Displays historical solar radiation values as a simplified chart. Optionally may display the corresponding latest solar radiation value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'radiation', label: 'Solar Radiation', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'radiation', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Solar Radiation\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 1100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 1100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#5B7EE6\"},{\"from\":0,\"to\":250,\"color\":\"#80C32C\"},{\"from\":250,\"to\":500,\"color\":\"#FFA600\"},{\"from\":500,\"to\":1000,\"color\":\"#F36900\"},{\"from\":1000,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Solar Radiation\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:radioactive\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"W/m²\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "solar", "radiation", "sunlight"], "resources": [{"link": "/api/images/system/simple_solar_radiation_chart_card_system_widget_image.png", "title": "\"Simple solar radiation chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_solar_radiation_chart_card_system_widget_image.png", "publicResourceKey": "p3KtLufJ6Yg400dJOO2f3JYhzQo4wf4r", "mediaType": "image/png", "data": "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", "public": true}]}