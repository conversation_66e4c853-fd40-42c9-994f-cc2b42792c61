{"fqn": "simple_humidity_chart_card", "name": "Simple humidity chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_humidity_chart_card_system_widget_image.png", "description": "Displays historical humidity values as a simplified chart. Optionally may display the corresponding latest humidity value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'humidity', label: 'Humidity', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'humidity', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Humidity\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#D81838\"},{\"from\":20,\"to\":40,\"color\":\"#FFA600\"},{\"from\":40,\"to\":60,\"color\":\"#5B7EE6\"},{\"from\":60,\"to\":80,\"color\":\"#305AD7\"},{\"from\":80,\"to\":100,\"color\":\"#234CC7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Humidity\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"%\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "moisture", "dampness", "wetness", "humidness", "moistness", "dew", "water vapor", "condensation", "dew point", "steaminess"], "resources": [{"link": "/api/images/system/simple_humidity_chart_card_system_widget_image.png", "title": "\"Simple humidity chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_humidity_chart_card_system_widget_image.png", "publicResourceKey": "ejBqe11cESx7uaJRCgYH0XxVYZM0OrFE", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAhFBMVEUAAADg4ODf39/g4ODg4OD////g4OAhISEjTMfj4+PHx8c8PDysrKzx8fGQkJAvLy/k6fhYWFg+Ys50dHSsvOqenp6CgoLV1dXy9Pt1j9wwV8tohNlmZmZKSkqDmt9aedXI0vGRpeO5ublMbtK6urq6x+6eseeRpuNaeNXW3fXW3vTW3fS+UfxxAAAABXRSTlMA7yC/r1EOHTEAAAR+SURBVHja7M9JAcAgDAAwytEf/u0OGdAlDtIAAAAAAAB+p4/I181+HrHX83b0Ngs8zmS0XCVElUiKXEbkNh/79bIiNwxEYXh1OFSVLohG1ka9MX7/Z8zIYdJm3EFhIMJm9CPjonYfeGFNyNWakKs1IVdrQq7WD4Dc7JryV4g8Aw5pbrsgOCV1JyvF14LB9SFi5FESDIBScapU+YQkRU4YWR/y5Ef5DcQJvPMfxznxbgPEeXwMjuKdaI2uLdrTaQxk4Z6eIXQQqtCS2TMxQSlIMUWK0AVj8sxwFHQaAhFajrEyvSBsHSAZGwuW2CDKDdsO2cEptdNrCGThA6kUsvyBRFXNB4hrAgS2lyOgL0iObeo0BlJJKyWS6+nTeg/xR4iPlR6dxkAejKU8VlJPkPhA/grxDAgvCBIrug2BKG2LS6HhBMlk+gpBaEv5vSSgdOg2BILKPYdzXt4uj9MWMaY+BAtJW/GtFlvQbRQEEME3C8Hj35t/vxNy7Sbkak3I1ZqQqzUhV2tC/k+aQ8h6e0iO3LNwa4g8mZwAfq00uS9ELK6f82omt4VYlKPK/E0hv9gxtx1VYSgMX630Ly20tIgcRtFxO8f3f7/NdKYeCFqICcGE78YGuFhf1gmRTF5//kieVESkdIVV/ClFZPdDP1f2KUXEhjokLHoKkbX8F/V2iCdW6ROI/K4+IX3QQhD1pGQ+ItHK5iveE2Mq+VpuWB7/rcI1UU9K5iLCU8aUYszyrkfiDyKXMldKBgbAAyJ6V1ycdc+dIFypJG5/8s4bB2f2dHxhrWvOqZeN4I+KvFcAGn0+m8xrNADeBnkIH8f6qkhifz34aZarqydjHo8V2QLmA6j0+YwjOUpsdwYF0U4HRBJ2iuL1cijlviHCrJWfB3Fk1U/ybDRGRAMlka6wJcqAvTs35DCHn2tZezCh1WAviiS9qPxk1BuxsCu5ai2U+xVMyOEiR+CLiBociEqYvxxdi7xDj9jVryw5FxyNoZsJ2arkfLCIi7LGB1G2d92x9yJNpVuro0ZJ90nFdZ1Z7uJQIxq4vzci6+dgUKRwRaWBjDwGNTkymAMMNSaQEM5kp2NYaq1iKaeH4VZshjV7VsE0FWryvAHf/l5d7wvXJXexrBMxz1MhfIU8ShwPEylqtFSnYD9dx5/RpqRwq09AqLQMTLYrgczHjQNdUkJ/1k2tA60+If0iGaBduNXJo6ALdthrVLWpbpsEZ9MUIm5ceR/n0elsY2iLr9ZnSzfIgwmZQsTvjjcnUhw6Hm6FNCC6PYLz8NKbQMQZvP9mwq3FTgW5+EsU9H0jI9FmIo9wsx+AygA4/kh5Si+y1UQZ6mONrHfIM/FK0xAev6UB8LFzK70r4nDX9z0DPnHv7tMR/j+iC7pL0fdAJEav7jl+jecpExENY84iK6WGN/mcRV7siOaYs8gYFpFF5FlYRObGIjI3FpG5sYjMjUXkfzv3UQAACANBMLT88G8XAwigzDg4AbenMeQ0GWU8IaNd9mXdm/2dCFLUnrcrrQYAAAAAAMBvFtRAowTiuzzYAAAAAElFTkSuQmCC", "public": true}]}