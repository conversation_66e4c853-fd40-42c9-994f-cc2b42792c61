{"fqn": "horizontal_oval_tank", "name": "Horizontal oval tank", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_oval_tank_system_widget_image.png", "description": "Widget indicates the level of liquid in Horizontal oval tank.", "descriptor": {"type": "latest", "sizeX": 4, "sizeY": 4, "resources": [], "templateHtml": "<tb-liquid-level-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-liquid-level-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.liquidLevelWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.liquidLevelWidget.update();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true\n    };\n};\n\nself.onDestroy = function() {\n}\n\nself.actionSources = function() {    \n    return {        \n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false        \n        }    \n    };\n}", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-liquid-level-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-liquid-level-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"return Math.floor(Math.random() * 101);\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"tankSelectionType\":\"static\",\"selectedShape\":\"Horizontal Oval\",\"shapeAttributeName\":\"tankShape\",\"tankColor\":{\"type\":\"range\",\"color\":\"#242770\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E73535DE\"},{\"from\":20,\"to\":null,\"color\":\"#242770\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E73535DE';\\n  }\\n}\\nreturn '#242770';\"},\"datasourceUnits\":\"%\",\"layout\":\"percentage\",\"volumeSource\":\"static\",\"volumeConstant\":500,\"volumeAttributeName\":\"volume\",\"volumeUnits\":\"L\",\"volumeFont\":{\"family\":\"Roboto\",\"size\":14,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"volumeColor\":\"rgba(0, 0, 0, 0.18)\",\"units\":\"%\",\"widgetUnitsSource\":\"static\",\"widgetUnitsAttributeName\":\"units\",\"liquidColor\":{\"type\":\"range\",\"color\":\"#7A8BFF\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E27C7CDE\"},{\"from\":20,\"to\":null,\"color\":\"#7A8BFF\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FF0000DE\"},{\"from\":20,\"to\":null,\"color\":\"rgba(0,0,0,0.87)\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FF0000DE';\\n  }\\n}\\nreturn '#000000DE';\"},\"showBackgroundOverlay\":true,\"backgroundOverlayColor\":{\"type\":\"range\",\"color\":\"rgba(255, 255, 255, 0.76)\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FFEFEFDE\"},{\"from\":20,\"to\":null,\"color\":\"#FFFFFFC2\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FFEFEFDE';\\n  }\\n}\\nreturn '#FFFFFFC2';\"},\"showTooltip\":true,\"showTooltipLevel\":true,\"tooltipUnits\":\"%\",\"tooltipLevelDecimals\":0,\"tooltipLevelFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipLevelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.76)\",\"rangeList\":[],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"showTooltipDate\":true,\"tooltipDateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"tooltipDateFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipDateColor\":\"rgba(0, 0, 0, 0.76)\",\"tooltipBackgroundColor\":\"rgba(255, 255, 255, 0.76)\",\"tooltipBackgroundBlur\":3,\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Liquid level\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"titleFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"1.5\"},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"showTitleIcon\":false,\"titleIcon\":\"water_drop\",\"iconColor\":\"#5469FF\",\"decimals\":0,\"enableDataExport\":false,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\"}"}, "tags": ["reservoir", "container", "vessel", "storage unit", "cistern", "canister", "vat", "basin", "repository", "bin", "hopper"], "resources": [{"link": "/api/images/system/horizontal_oval_tank_system_widget_image.png", "title": "\"Horizontal oval tank\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_oval_tank_system_widget_image.png", "publicResourceKey": "6xKTUKZF6SZDHXexsvFCqQqxhmFqISxb", "mediaType": "image/png", "data": "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", "public": true}]}