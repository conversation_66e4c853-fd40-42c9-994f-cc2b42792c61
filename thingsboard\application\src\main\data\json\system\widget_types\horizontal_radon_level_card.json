{"fqn": "horizontal_radon_level_card", "name": "Horizontal radon level card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_radon_level_card_system_widget_image.png", "description": "Displays the latest radon level telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'radon', label: 'Radon level', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Radon level\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 75 - 37.5;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 300) {\\n\\tvalue = 300;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:radioactive\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":100,\"color\":\"#80C32C\"},{\"from\":100,\"to\":200,\"color\":\"#FFA600\"},{\"from\":200,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":100,\"color\":\"#80C32C\"},{\"from\":100,\"to\":200,\"color\":\"#FFA600\"},{\"from\":200,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal temperature card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"Bq/m³\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["environment", "indoor", "air", "radon"], "resources": [{"link": "/api/images/system/horizontal_radon_level_card_system_widget_image.png", "title": "\"Horizontal radon level card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_radon_level_card_system_widget_image.png", "publicResourceKey": "BZYuHPdDHSZ0oINi73K8StGBIHhO48bK", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAApVBMVEUAAADf39/f39/g4ODg4OD////k5OSAwyzg4ODv9+Xf8Mqv2XuQy0bP6bDOzs5YWFj3+/Lx8fH09PT5+fnA4ZWg0mDHx8eYzlOIxznb29u2trbQ6a/CwsJ0dHTV1dU8PDy43Yjn5+fIyMjn9NjX7L28vLytra1mZmbH5aPY7L1KSkqenp6CgoKo1m6Qykbt7e3U1NSQkJDH5aKdnZ2n1m4vLy+qqqrWFLAGAAAABXRSTlMAIETfv0i0+14AAAPtSURBVHja7dqLVtpAEAZgSzsZkmyuBEi4BAhXsdba2/s/WmeWUiJW5dDSA5z/O4rJBOP87G6EAzcAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/9O7i3ejGu+dS9d835AcTosuXuo0bj6kdAVaH26adBWaCHJmEOTcIMi5QZBzgyDn5g9B3JiUt6ssHh8d2kiWtJN8pLq7ko5wsiArzmwcNu62FN11Rgk5ibRejogklOyRbES6kySU2P1hhw7lrbLg9+n7/ZisuC9uPXpm9rzm9W9fDzJm7pIIeJck6tAo+Thcl4v1cETRMEqGUfSoQWRn7Uip/LiU4sFBMhZmun3k2CPLZcvc0lNx7tOeMXe5+1qQjIWe1+gZY7Ki9fdH6txFi6h0Rp0lfb0bduxoRJ3RQqujZLRYLg4OErDxphkbUq6pB5Eh8TmP9+/v0Z4skFbdF4PoWcU9kccq247I1yGty4+LZccZlbUgEqHTSxbRneTrOAcHafNMbvNNI9J3LQiJLgc6DDLHYpdU25BsSuVWEnq3UxJT32SvjEjAIveI3C6L7m5qOdFyveiMZGoNl2tnG4R0SiXfHfm5PHxqzfquDRLbv5iZvSA2Z5AzmzZ7tjzWvPdamWlfmRYzY+KXg9yzCCgYu3HOon7XRL+3G/Vq/efhAu7aKWCoHsR13TEbuzme+WyPBOzagVtpivYsY9n3A1rx9OUgXdv9jPmb/KaY0qm4xp7cl1uzv9g9Xau+bcfT26694xed8LkdsUC+u7l58x+iJ8HptGJjJ8hU+zVPFvvK18vlvfStLXtaDWyQQDc/bTdnctF+M4hOLTqpuCs5hMTp93Mee/U14vNYH/VtkDG7z4NYbwaJ9euktFfb+S/tehCdVv5uREybjg1ijE7OnZT+IH3YL6R0qLGsQRV7yvBq+vTyO6aVzaZteBwcGyRjUb+29bRN/XooyGq1qNUMtdCkluxPfkyoVRSbA1J4I9KK+d4Xtp+na8T35SqbuxTn7GdtliM+x0cGcdkaPwlSlmFRVaFsDWhQDOat3iAsyyqcP4Rz6TycpPN5QWloD4TV/I2JteE/C6Lyb7ompkYmnB4xPh0ZxPDGtB7kc9hLq3ASboJMPheyLbVJEQ4qG6RZFBKk6k2qVOIOUvprrqtrZMazo5/G+13mvF1/Gh8Wg7BXVoNURyScF4Ne2qtCqT3MJ1VIGqSlIzKZL/RAORjQv6BBTvrCqpjQyz5XupAuI0ir9drBlP6ZIHPxUvdSIci5QZBzgyDn5oqCfLiCt9mJ0g83DecK3mhvOQ39CEfTuXTvG9f0oRoAAAAAAAAAAAAAAAAAAAAAAAAAAACA/+QnXMPkgka4f5kAAAAASUVORK5CYII=", "public": true}]}