<?xml version="1.0" encoding="utf-8"?>

<!-- 
FILE INFORMATION
Public Reachable Information
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 504.xml
NORMATIVE INFORMATION
  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues
LEGAL DISCLAIMER
Copyright 2022 Open Mobile Alliance. 
Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
The above license is used as a license under copyright only.  Please
reference the OMA IPR Policy for patent licensing terms:
https://www.omaspecworks.org/about/intellectual-property-rights/
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
<Object ObjectType="MODefinition">
		<Name>Remote SIM Provisioning</Name>
		<Description1><![CDATA[This is a device management object that should be used for Remote SIM Provisioning according to GSMA standards]]></Description1>
		<ObjectID>504</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:504</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>Current SIM Type</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..3</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Provides the information about the currently being used SIM Type:
0: UICC (removable)
1: eUICC (removable)
2: eUICC (non-removable)
3: iUICC
4-24: Reserved for future use]]></Description>
			</Item>
			<Item ID="1">
				<Name>Supported SIM Type</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..3</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Provides the information about the currently supported SIM Types: 
0: UICC (removable)
1: eUICC (removable)
2: eUICC (non-removable)
3: iUICC
4-24: Reserved for future use]]></Description>
			</Item>
			<Item ID="2">
				<Name>Service Provider Name</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Provides the name of the Service Provider assocatiated with the downloaded profile or the profile currently being downloaded.]]></Description>
			</Item>
			<Item ID="3">
				<Name>Download URI/Information</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[URI, or formatted data containing the URI (e.g. GSMA SGP.22 Activation Code), from where the device can download the profile package by an alternative mechanism. 
The URI format is defined in RFC 3986.]]></Description>
			</Item>
			<Item ID="4">
				<Name>RSP Update</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Executes one or more RSP actions. The actions are given as arguments and in case providing multiple actions comma separation is used, e.g. 0,1,2. The following values are possible:
0: Download & install profile
1: Enable profile - for profile identified by resource 14
2: Disable profile - for profile identified by resource 14
3: Delete profile - for profile identified by resource 14
4: Reset memory of current SIM
5-24: Reserved for future use]]></Description>
			</Item>
			<Item ID="5">
				<Name>RSP Update State</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..10</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates current state with respect to the executed RSP action(s) in the RSP update resource. This value is set by the LwM2M Client.
0: Idle (before downloading a profile or after successful completetion of the action(s))
1: Downloading (The data sequence is on the way)
2: Downloaded profile ready for installation
3: Installing
5: Enabling
6: Disabling
7: Deleting
8: Resetting memory
9: Pending end-user consent
10: Pending confirmation code
11-24: Reserved for future use]]></Description>
			</Item>
			<Item ID="6">
				<Name>RSP Update Result</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..16</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Contains the result of the executed RSP action(s) in the RSP update resource.
0: Initial value. Once the updating process is initiated (Download /Update), this Resource MUST be reset to Initial value. 
1: RSP action completed successfully.
2: Not enough SIM memory for the new Profile package. 
3. Out of RAM during downloading process. 
4: Connection lost during downloading process.
5: Integrity check failure for new downloaded profile package. 
6: Unsupported profile package type. 
7: Invalid URI
8: Unsupported protocol.
9: Not authorized to download profile at the given URI.
10: Failed to authenticate server given by the URI.
11: Generic download and installation error.
12: Error in profile package format
13: Failed to enable profile.
14: Failed to disable profile.
15: Failed to delete profile.
16: Failed to reset memory.
17-24: Reserved for future use]]></Description>
			</Item>
			<Item ID="7">
				<Name>Profile Name</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Name of the downloaded profile / profile package or the profile / profile package currently being downloaded.]]></Description>
			</Item>
			<Item ID="8">
				<Name>Profile Package Version</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Version of the downloaded profile / profile package or the profile / profile package currently being downloaded.]]></Description>
			</Item>
			<Item ID="9">
				<Name>Free Memory on SIM</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>KiB</Units>
				<Description><![CDATA[Estimated current available amount of storage space on SIM which can store data and software in the LwM2M Device (expressed in kibibytes).]]></Description>
			</Item>
			<Item ID="10">
				<Name>Total Memory on SIM</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>KiB</Units>
				<Description><![CDATA[Total amount of storage space which can store data and software in the LwM2M Device]]></Description>
			</Item>
			<Item ID="11">
				<Name>Integrated Circuit Card Identifier (ICCID)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource provides the unique identification number for each installed profile of the current SIM in case of RSP support or
the unique identification number of the current SIM in case of current SIM being a UICC with no RSP support. Please refer ETSI TS 102.22.1. In case of multiple installed profiles,
the currently active profile is the first instance.]]></Description>
			</Item>
			<Item ID="12">
				<Name>eUICC ID</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[eUICC-ID (a.k.a. EID), see GSMA SGP.02 and GSMA SGP.29 for definitions]]></Description>
			</Item>
			<Item ID="13">
				<Name>RSP Type</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Provides the information about the Remote SIM Provisioning (RSP) type of the current SIM:
0: No RSP support
1: GSMA eSIM for M2M devices
2: GSMA eSIM for consumer devices
3-24: Reserved for future use]]></Description>
			</Item>
			<Item ID="14">
				<Name>Selected ICCID for RSP operation</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates ICCID of profile selected for profile specific RSP operations. This resource must be present if multiple installed profiles is supported
by the RSP implementation. In case of only support for a single profile at a time this resource is not required to be present for profile selection. After successful installation of
a profile the value of this resource (if present) is updated to the ICCID of the installed profile.]]></Description>
			</Item>
			<Item ID="15">
				<Name>RSP Consent Reason</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Provides information on the requested user consent]]></Description>
			</Item>
			<Item ID="16">
				<Name>RSP User Consent</Name>
				<Operations>W</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..1</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[User consent decision. The following values are possible:
0: Approved
1: Rejected
2-24: Reserved for future use]]></Description>
			</Item>
			<Item ID="17">
				<Name>RSP Confirmation Code</Name>
				<Operations>W</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Provides a confirmation code (see e.g. GSMA SGP.21/22)]]></Description>
			</Item>
			<Item ID="18">
				<Name>RSP Data Type</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..17</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[RSP data type for set and get RSP data:
0: SM-DP+ default address (see GSMA SGP.22)
1: eUICCInfo2 (see GSMA SGP.22)
2: DeviceInfo (see GSMA SGP.22)
3: eSIM Profile Metadata (GSMA SGP.22 ProfileInfo default data for all installed profiles)
5: ISD-P AID (see GSMA SGP.22) - for profile identified by resource 14
6: Profile state (see GSMA SGP.22) - for profile identified by resource 14
7: Profile Nickname (see GSMA SGP.22) - for profile identified by resource 14
8: Service provider name (see GSMA SGP.22) - for profile identified by resource 14
9: Profile name (see GSMA SGP.22) - for profile identified by resource 14
10: Icon type (see GSMA SGP.22) - for profile identified by resource 14
11: Icon (see GSMA SGP.22) - for profile identified by resource 14
12: Profile Class (see GSMA SGP.22) - for profile identified by resource 14
13: Notification Configuration Info (see GSMA SGP.22) - for profile identified by resource 14
14: Profile Owner (see GSMA SGP.22) - for profile identified by resource 14
15: SM-DP+ proprietary data (see GSMA SGP.22) - for profile identified by resource 14
16: Profile Policy Rules (see GSMA SGP.22) - for profile identified by resource 14
17: RSP formatted data request/response (RSP type specific)
18-24: Reserved for future use]]></Description>
			</Item>
			<Item ID="19">
				<Name>Set RSP Data</Name>
				<Operations>W</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource is used to update the (e/i)UICC data / profile specific data of type according to resource 30.
It is up to the RSP type whether update of the data is possible or not.]]></Description>
			</Item>
			<Item ID="20">
				<Name>Get RSP Data</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource is used to retrieve the (e/i)UICC data / profile specific data of type according to resource 30.
It is up to the RSP type whether retrieval of the data is possible or not.]]></Description>
			</Item>
		</Resources>
		<Description2 />
	</Object>
</LWM2M>
