<?xml version="1.0" encoding="UTF-8"?>
<!--
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-XML_11-V1_1-20201110-A.xml
   Path: http://www.openmobilealliance.org/release/ObjLwM2M_APN_Conn/

OMNA LwM2M Registry
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 11.xml

NORMATIVE INFORMATION
  This file is not part of a Technical Specification document.

  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

LEGAL DISCLAIMER

  Copyright 2020 Open Mobile Alliance.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

  1. Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
  3. Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived
  from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.

  The above license is used as a license under copyright only.  Please
  reference the OMA IPR Policy for patent licensing terms:
  https://www.omaspecworks.org/about/intellectual-property-rights/
  
-->
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M-v1_1.xsd">
	<Object ObjectType="MODefinition">
		<Name>LWM2M APN Connection Profile</Name>
		<Description1><![CDATA[This object specifies resources to enable a device to connect to an APN.]]></Description1>
		<ObjectID>11</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:11:1.1</ObjectURN>
		<LWM2MVersion>1.1</LWM2MVersion>
		<ObjectVersion>1.1</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0"><Name>Profile name</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Human-readable identifier. Multiple connection profiles can share the same APN value but e.g. have different credentials.]]></Description>
			</Item>
			<Item ID="1"><Name>APN</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Presented to network during connection to PDN e.g. ‘internet.15.234’.
				This resource is not included in case ‘Auto select APN by device’ resource has the value TRUE.
				If the APN resource is present but contains an empty string, then the device shall not provide an APN in the connection request (invoking default APN procedures in the network).]]></Description>
			</Item>
			<Item ID="2"><Name>Auto select APN by device</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[If this resource is present for a connection profile, it enables the device to choose an APN according to a device specific algorithm. It provides a fall-back mechanism e.g. for some MVNO SIMs the configured APN may not work.  Resource not included or set to False in case the ‘APN’ resource is specified.]]></Description>
			</Item>
			<Item ID="3"><Name>Enable status</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Allows the profile to be remotely enabled or disabled. True: APN profile is enabled. False: APN profile is disabled. If the status is set to disabled (False) while a data connection for that profile is active, that data connection will be released.]]></Description>
			</Item>
			<Item ID="4"><Name>Authentication Type</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Enumerated type:
				0: PAP
				1: CHAP
				2: PAP or CHAP
				3: None]]></Description>
			</Item>
			<Item ID="5"><Name>User Name</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Used with e.g. PAP.]]></Description>
			</Item>
			<Item ID="6"><Name>Secret</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Used with e.g. PAP.]]></Description>
			</Item>
			<Item ID="7"><Name>Reconnect Schedule</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Comma separated list of retry delay values in seconds to be used in case of unsuccessful connection establishment attempts. e.g. "10,60,600,3600,86400"]]></Description>
			</Item>
			<Item ID="8"><Name>Validity (MCC, MNC)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Coma separated mobile country code, then mobile network code - for which this APN is valid.]]></Description>
			</Item>
			<Item ID="9"><Name>Connection establishment time (1)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Time</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[UTC time of connection request. See note (1)]]></Description>
			</Item>
			<Item ID="10"><Name>Connection establishment result (1)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[0 = accepted
				1 = rejected]]>
				</Description>
			</Item>
			<Item ID="11"><Name>Connection establishment reject cause (1)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..111</RangeEnumeration>
				<Units/>
				<Description><![CDATA[Reject cause, see [3GPP-TS_24.008, 3GPP-TS_24.301]]]></Description>
			</Item>
			<Item ID="12"><Name>Connection end time (1)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Time</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[UTC time of connection end.]]></Description>
			</Item>
			<Item ID="13"><Name>TotalBytesSent</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Rolling counter for total number of bytes sent via this interface since last device reset.]]></Description>
			</Item>
			<Item ID="14"><Name>TotalBytesReceived</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Rolling counter for total number of bytes received via this interface since last device reset.]]></Description>
			</Item>
			<Item ID="15"><Name>IP address (2)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[May be IPv4 or IPv6 address.]]></Description>
			</Item>
			<Item ID="16"><Name>Prefix length(2)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Associated with IPv6 address.]]></Description>
			</Item>
			<Item ID="17"><Name>Subnet mask (2)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Subnet mask.]]></Description>
			</Item>
			<Item ID="18"><Name>Gateway (2)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Gateway.]]></Description>
			</Item>
			<Item ID="19"><Name>Primary DNS address (2)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Primary DNS address.]]></Description>
			</Item>
			<Item ID="20"><Name>Secondary DNS address (2)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Secondary DNS address.]]></Description>
			</Item>
			<Item ID="21"><Name>QCI (3)</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>1..9</RangeEnumeration>
				<Units/>
				<Description><![CDATA[Quality of service Class Identifier QCI), applicable to LTE and NB-IoT only
				This resource enables the LWM2M server to signal the LWM2M client which QCI it shall request from the network.
				See [3GPP-TS_23.203, and 3GPP-TS_24.301] for a description of QCI values. See note (3).]]></Description>
			</Item>
			<Item ID="22"><Name>Vendor specific extensions</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Objlnk</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Links to a vendor specific object.]]></Description>
			</Item>
			<Item ID="23"><Name>TotalPacketsSent</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Rolling counter for total number of packets sent via this interface since last device reset.]]></Description>
			</Item>
			<Item ID="24"><Name>PDN Type</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[0=Non-IP
				1=IPv4
				2=IPv6
				3=IPv4v6]]>
				</Description>
			</Item>
			<Item ID="25"><Name>APN Rate Control</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Determines the number of allowed uplink PDU transmissions per time interval per APN.
				Rate Control information is part of the Protocol Configuration Options (PCO) according to  [3GPP-TS_24.301], [3GPP-TS_24.008 and [3GPP-TS_27.060]]]>
				</Description>
			</Item>
			<Item ID="26"><Name>Serving PLMN Rate control</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Unsigned Integer</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[For data sent over NAS, this indicates the maximum number of messages the UE can send per time unit (Resource 27) per PDN connection. See [3GPP-TS_23.401] clause ******* and [3GPP-TS_24.008] clause ********.2.]]></Description>
			</Item>
			<Item ID="27"><Name>Uplink Time Unit</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Unsigned Integer</Type>
				<RangeEnumeration>0..4</RangeEnumeration>
				<Units/>
				<Description><![CDATA[In association with APN Rate Control (Resource 28), this indicates the units of the period over which the maximum number of PDU transmissions are measured. [3GPP-TS_24.008] clause ********.2 specifies that 0=unrestricted, 1=minute, 2=hour, 3=day and 4=week.]]></Description>
			</Item>
			<Item ID="28"><Name>APN Rate Control for Exception Data</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Determines the number of allowed uplink PDU transmissions for exception data per time interval for this APN. This
				Rate Control information is part of the Protocol Configuration Options (PCO) according to  [3GPP-TS_24.301], [3GPP-TS_24.008 and [3GPP-TS_27.060]]]>
				</Description>
			</Item>
			<Item ID="29"><Name>APN Exception Data Uplink Time Unit</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Unsigned Integer</Type>
				<RangeEnumeration>0..4</RangeEnumeration>
				<Units/>
				<Description><![CDATA[In association with APN Rate Control for Exception Data (Resource 28), this indicates the units of the period over which the maximum number of messages is measured. [3GPP-TS_24.008] clause ********.2 specifies that 0=unrestricted, 1=minute, 2=hour, 3=day and 4=week.]]></Description>
			</Item>
			<Item ID="30"><Name>Supported RAT Types</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Unsigned Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units/>
				<Description><![CDATA[This resource specifies the supported RAT Types that the LWM2M Client can use for connecting to the specified APN.
				Each instance is one of these values:
				1: 3GPP PS
				2: 3GPP PS GSM (GPRS)
				3: 3GPP PS UMTS
				4: 3GPP PS LTE
				5: 1xEV-DO
				6: 3GPP CS
				12: 3GPP PS LTE with CIoT EPS optimisations, User Plane
				13: 3GPP PS LTE with CIoT EPS optimisations, Control Plane
				14: 3GPP PS NB-IoT Control Plane optimisations
				15: 3GPP PS NB-IoT User Plane optimisations
				16-100: Reserved for future use
				]]></Description>
			</Item>
			<Item ID="31"><Name>RDS Application ID</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource provides the Application ID to correlate to the appropriate instance Ids of Resources 32 and 33.]]></Description>
			</Item>
			<Item ID="32"><Name>RDS Destination Port</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..15</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource provides the correlated RDS Destination Port Number (as defined in 3GPP TS 24.250) to use for contacting the LwM2M Server identifed in Resource 29 when communicating through the SCEF across the Non-IP binding (Resource 24 must equal 0).]]></Description>
			</Item>
			<Item ID="33"><Name>RDS Source Port</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..15</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource provides the correlated RDS Source Port Number (as defined in 3GPP TS 24.250) to use for contacting the LwM2M Server identifed in Resource 29 when communicating through the SCEF across the Non-IP binding (Resource 24 must equal 0).]]></Description>
			</Item>
		</Resources>
		<Description2><![CDATA[Notes:
(1)	For each activated PDP context request, the device may store at least one value of ‘Connection establishment time’, ‘connection establishment result’, ‘Connection end time’ and if activation is unsuccessful then a ‘connection establishment reject cause’.   It is a device decision how many instances to keep.
(2)	These resources are used in case IP related parameters are defined statically, can also be used to reflect IP related parameters in case of dynamic IP address assignment.  The normal use case would be to have one IPv4 and one IPv6 address which have each associated a prefix length (IPv6 only), a subnet mask, a gateway, and a primary and secondary DNS address.
(3)	For LTE a higher QoS may be established by setting up an additional bearer ("dedicated bearer") in addition to the default bearer which is established to the default APN. A dedicated bearer which is set up by the network on request by the device containing a requested QCI value can either be established to the same APN as the default bearer or to another APN. The QoS of a dedicated bearer may be modified on request by the device. The association of QoS values and APNs for a subscriber is stored in the network and checked during the establishment of a bearer.
]]></Description2>
	</Object>
</LWM2M>
