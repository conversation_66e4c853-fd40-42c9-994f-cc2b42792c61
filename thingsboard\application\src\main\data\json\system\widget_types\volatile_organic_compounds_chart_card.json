{"fqn": "volatile_organic_compounds_chart_card", "name": "Volatile organic compounds chart card", "deprecated": false, "image": "tb-image;/api/images/system/volatile_organic_compounds_chart_card_system_widget_image.png", "description": "Displays a volatile organic compounds (VOCs) data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'voc', label: 'VOCs', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'ppb', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'voc', 'ppb', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"VOCs\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 2000) {\\n\\tvalue = 2000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"ppb\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":500,\"color\":\"#80C32C\"},{\"from\":500,\"to\":1000,\"color\":\"#FFA600\"},{\"from\":1000,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 2000) {\\n\\tvalue = 2000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"ppb\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 50 - 25;\\nif (value < -100) {\\n\\tvalue = -100;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"ppb\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"VOCs\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:molecule\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "indoor", "air", "vocs", "voc", "organic solvents", "hydrocarbons", "emissions", "fumes", "gaseous organics", "contaminants", "air pollutants"], "resources": [{"link": "/api/images/system/volatile_organic_compounds_chart_card_system_widget_image.png", "title": "\"Volatile organic compounds chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "volatile_organic_compounds_chart_card_system_widget_image.png", "publicResourceKey": "okI1C4SmXYb83lfj7t9WluDtBDGFMit0", "mediaType": "image/png", "data": "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", "public": true}]}