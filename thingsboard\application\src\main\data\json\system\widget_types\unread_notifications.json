{"fqn": "unread_notifications", "name": "Unread notifications", "deprecated": false, "image": "tb-image;/api/images/system/unread_notification_system_widget_image.png", "descriptor": {"type": "static", "sizeX": 5.5, "sizeY": 5, "resources": [], "templateHtml": "<tb-unread-notification-widget\n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-unread-notification-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.unreadNotificationWidget.onInit();\n}\n\nself.typeParameters = function() {\n    return {\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true\n    };\n}\n\nself.onDestroy = function() {\n}\n", "settingsSchema": "", "dataKeySettingsSchema": "", "settingsDirective": "tb-unread-notification-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-unread-notification-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"static\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgb(255, 255, 255)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0\",\"settings\":{\"cardHtml\":\"<div class='card'>HTML code here</div>\",\"cardCss\":\".card {\\n    font-weight: bold;\\n    font-size: 32px;\\n    color: #999;\\n    width: 100%;\\n    height: 100%;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n}\",\"maxNotificationDisplay\":6,\"showCounter\":true,\"counterValueFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"600\",\"lineHeight\":\"\"},\"counterValueColor\":\"#fff\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"enableViewAll\":true,\"enableFilter\":true,\"enableMarkAsRead\":true},\"title\":\"Unread notification\",\"dropShadow\":true,\"configMode\":\"basic\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleColor\":\"#000000\",\"showTitleIcon\":true,\"iconSize\":\"22px\",\"titleIcon\":\"notifications\",\"iconColor\":\"#000000\",\"actions\":{},\"enableFullscreen\":false,\"borderRadius\":\"4px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\"}"}, "resources": [{"link": "/api/images/system/unread_notification_system_widget_image.png", "title": "\"Unread notification\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "unread_notification_system_widget_image.png", "publicResourceKey": "37tqc7H1Tk3IXqBpsj2hWpOPlfWkZRqu", "mediaType": "image/png", "data": "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", "public": true}]}