<?xml version="1.0" encoding="UTF-8"?>

<!-- BSD-3 Clause License

Copyright 2020 uCIFI Alliance

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>Global Navigation Satellite System</Name>
		<Description1>More than a positioning object, the uCIFI global navigation satellite system object provides all the information required to calculate a position/location.</Description1>
		<ObjectID>3430</ObjectID>
		<ObjectURN>urn:oma:lwm2m:ext:3430</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Single</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>Fix timestamp</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Time</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description>Timestamp of when the location measurement was performed.</Description>
			</Item>
			<Item ID="1">
				<Name>Latitude</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>lat</Units>
				<Description>Decimal notation of latitude, e.g. -43.5723 [World Geodetic System 1984]. This value ranges from [-90, 90].</Description>
			</Item>
			<Item ID="2">
				<Name>Longitude</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>lon</Units>
				<Description>Decimal notation of longitude, e.g. 153.21760 [World Geodetic System 1984]. This value ranges from [-180, 180].</Description>
			</Item>
			<Item ID="3">
				<Name>Altitude</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>m</Units>
				<Description>Altitude above mean sea level in meters.</Description>
			</Item>
			<Item ID="4">
				<Name>Speed</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>m/s</Units>
				<Description>Horizontal speed calculated by the device.</Description>
			</Item>
			<Item ID="5">
				<Name>Heading</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>deg</Units>
				<Description>Direction that the device is following</Description>
			</Item>
			<Item ID="6">
				<Name>Radius</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>m</Units>
				<Description>Radius of a circular area corresponding to the location’s uncertainty (GPS data precision). Negative values indicate that the radius is not available.</Description>
			</Item>
			<Item ID="7">
				<Name>HDOP</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Horizontal dilution of precision.</Description>
			</Item>
			<Item ID="8">
				<Name>VDOP</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Vertical dilution of precision.</Description>
			</Item>
			<Item ID="9">
				<Name>Estimated horizontal accuracy</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>m</Units>
				<Description>Estimated horizontal accuracy.</Description>
			</Item>
			<Item ID="10">
				<Name>Estimated vertical accuracy</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>m</Units>
				<Description>Estimated vertical accuracy.</Description>
			</Item>
			<Item ID="11">
				<Name>Estimated speed accuracy</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>m/s</Units>
				<Description>Estimated speed accuracy.</Description>
			</Item>
			<Item ID="12">
				<Name>Estimated heading accuracy</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>deg</Units>
				<Description>Estimated heading accuracy.</Description>
			</Item>
			<Item ID="13">
				<Name>Fix type</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Possible values are : 
					0 = No fix
					1 = Autonomous GNSS fix
					2 = Differential GNSS fix
					3 = GPS PPS
					4 = RTK-fixed
					5 = RTK-floated
					6 = Estimated fix
					7 = Dead reckoning fix
					8 = Manual input, surveyed
					9 = Simulated mode
					.]]>
				</Description>
			</Item>
			<Item ID="14">
				<Name>Fix dimension</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Possible values are: 
					0,1 = no fix
					2 = 2D fix
					3 = 3D fix 
					As per NMEA-0183 GSA.]]>
				</Description>
			</Item>
			<Item ID="15">
				<Name>Used satellites</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Number of satellites used for the fix.</Description>
			</Item>
			<Item ID="16">
				<Name>Visible satellites</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Number of satellites viewed. Represent all the satellites seen but some of them cannot be in use (e.g. bad signal).</Description>
			</Item>
			<Item ID="17">
				<Name>Satellite identifiers</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Identifier of the satellite.</Description>
			</Item>
			<Item ID="18">
				<Name>Satellite identifiers</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>deg</Units>
				<Description>Elevation of the satellite.</Description>
			</Item>
			<Item ID="19">
				<Name>Satellite azimuth</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>deg</Units>
				<Description>Azimuth of the satellite.</Description>
			</Item>
			<Item ID="20">
				<Name>Almanac</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Validity of the almanac. Possible values are: 
					0 = invalid
					1 = valid
					.]]>
				</Description>
			</Item>
			<Item ID="21">
				<Name>Ephemeris</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Validity of the ephemeris of each satellite. Possible values are : 
					0 = invalid
					1 = valid
					.]]>
				</Description>
			</Item>
			<Item ID="22">
				<Name>Signal-to-noise ratio</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>dB</Units>
				<Description>Strength of the signal for each satellite, also called carrier-to-noise.</Description>
			</Item>
			<Item ID="23">
				<Name>GNSS</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[GNSS used for the fix. A mask is used with the following bit definition:
					0 = GPS (USA)
					1= GALILEO (EU)
					2 = GLONASS (Russia)
					3 = BeiDou (China)
					4 = QZSS (Japan)
					5 = IRNSS/NAVIC (India)
					A 0 indicates that the GNSS is not used a 1 indicates it is used.]]>
				</Description>
			</Item>
			<Item ID="24">
				<Name>Hardware RTC</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Time</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description>Time of the internal clock of the GNSS hardware.</Description>
			</Item>
			<Item ID="25">
				<Name>Assisted GPS</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Set to True if the almanac is obtained via a cellular connection. Set to False otherwise.</Description>
			</Item>
			<Item ID="26">
				<Name>Power command</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Command to switch the hardware ON or OFF and status of the device.</Description>
			</Item>
			<Item ID="27">
				<Name>PDOP</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Dilution of precision (NMEA-0183 GSA).</Description>
			</Item>
			<Item ID="28">
				<Name>Status</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description>Status A=active or V=Void (NMEA-0183 RMC).</Description>
			</Item>
		</Resources>
		<Description2></Description2>
	</Object>
</LWM2M>
