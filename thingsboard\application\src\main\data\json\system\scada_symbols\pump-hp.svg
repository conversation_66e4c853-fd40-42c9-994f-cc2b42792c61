<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="400" height="400" fill="none" version="1.1" viewBox="0 0 400 400"><tb:metadata xmlns=""><![CDATA[{
  "title": "HP Centrifugal pump",
  "description": "Centrifugal pump with configurable connectors and various states.",
  "searchTags": [
    "pump",
    "centrifugal",
    "high performance"
  ],
  "widgetSizeX": 2,
  "widgetSizeY": 2,
  "stateRenderFunction": "\n",
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.runningColor;\n}\nelement.attr({fill: color});",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "circle-background",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = '#cccccc';\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "critical",
      "stateRenderFunction": "element.attr({fill: ctx.properties.criticalColor});\nif (ctx.values.critical) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = ctx.values.critical && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'criticalClick');"
        }
      }
    },
    {
      "tag": "warning",
      "stateRenderFunction": "element.attr({fill: ctx.properties.warningColor});\nvar warning = ctx.values.warning && !(ctx.values.warning && ctx.values.critical)\nif (warning) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = warning && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'warningClick');"
        }
      }
    }
  ],
  "behavior": [
    {
      "id": "running",
      "name": "{i18n:scada.symbol.running}",
      "hint": "{i18n:scada.symbol.running-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.running}",
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "running"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warning",
      "name": "{i18n:scada.symbol.warning}",
      "hint": "{i18n:scada.symbol.warning-state-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.warning}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "MAJOR",
            "MINOR",
            "WARNING",
            "INDETERMINATE"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warningClick",
      "name": "{i18n:scada.symbol.warning-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "critical",
      "name": "{i18n:scada.symbol.critical}",
      "hint": "{i18n:scada.symbol.critical-state-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.critical}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "CRITICAL"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "criticalClick",
      "name": "{i18n:scada.symbol.critical-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "criticalAnimation",
      "name": "{i18n:scada.symbol.warning-critical-state-animation}",
      "hint": "{i18n:scada.symbol.warning-critical-state-animation-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.animation}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    }
  ],
  "properties": [
    {
      "id": "runningColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": "{i18n:scada.symbol.running}",
      "divider": true,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "stoppedColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#666666",
      "required": null,
      "subLabel": "{i18n:scada.symbol.stopped}",
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "warningColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#FAA405",
      "required": null,
      "subLabel": "{i18n:scada.symbol.warning}",
      "divider": true,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "criticalColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#D12730",
      "required": null,
      "subLabel": "Critical",
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata>
<g fill="#fff" tb:tag="background">
  <path d="m114.5 309h144.33l51.173 66h-247l51.497-66z"/>
  <path d="m65.041 374 49.942-64.008h143.36l49.628 64.008h-242.93z" stroke="#000" stroke-opacity=".87" stroke-width="1.9925"/>
  <rect x="23.77" y="373.35" width="325.82" height="26.648" rx="4.6492"/>
  <rect x="24.766" y="374.35" width="323.82" height="24.656" rx="3.6529" stroke="#000" stroke-opacity=".87" stroke-width="1.9925"/>
  <rect x="175" width="225" height="133.67"/>
  <rect x="176.34" y="1.3357" width="222.33" height="130.99" stroke="#000" stroke-opacity=".87" stroke-width="2.6715"/>
  <circle cx="186.5" cy="186.5" r="186.5"/>
  <circle cx="186.5" cy="186.5" r="185.16" stroke="#000" stroke-opacity=".87" stroke-width="2.6715"/>
  <ellipse cx="186.69" cy="186.82" rx="120.18" ry="119.92"/>
  <path d="m305.86 186.82c0 65.672-53.356 118.91-119.18 118.91-65.821 0-119.18-53.242-119.18-118.91 0-65.673 53.356-118.92 119.18-118.92 65.822 0 119.18 53.242 119.18 118.92z" stroke="#000" stroke-opacity=".12" stroke-width="2.0026"/>
  <g clip-path="url(#clip0_3214_60796)">
   <path d="m182.79 111.96c-9.073-23.303-20.123-33.549-26.376-41.692l-34.461 0.9063-24.336 29.24c1.9967-2.1678 12.577-0.11 38.925 25.46 26.347 25.571 35.77 48.871 37.187 57.324l18.509-12.372c0-18.168-0.375-35.563-9.448-58.866z" stroke="#727171" stroke-width="2.1802"/>
   <path d="m254.2 148.49c16.446-18.838 20.404-33.379 24.722-42.693l-16.773-30.117-37.188-8.0152c2.847 0.765 5.926 11.094-4.523 46.292-10.449 35.197-26.73 54.344-33.564 59.518l19.54 10.668c16.101-8.42 31.341-16.813 47.786-35.652z" stroke="#727171" stroke-width="2.1802"/>
   <path d="m265.64 225.84c24.877 2.544 39.033-2.623 49.131-4.478l14.898-31.087-14.935-34.987c1.019 2.765-5.634 11.245-40.402 23.044-34.769 11.799-59.802 9.567-67.973 6.976l-4.25 20.522c16.172 8.28 38.654 17.466 63.531 20.01z" stroke="#727171" stroke-width="2.1802"/>
   <path d="m122.4 225.38c-17.502 17.861-28.126 32.344-32.973 41.394l15.011 31.033 36.665 10.143c-2.797-0.928-5.278-11.417 7.18-45.955 12.459-34.537 29.814-52.715 36.935-57.487l-15.061-11.313c-16.558 7.479-30.255 14.324-47.757 32.185z" stroke="#727171" stroke-width="2.1802"/>
   <path d="m190.19 260.06c8.234 23.613 17.831 35.594 23.789 43.955l34.471 0.327 25.366-28.349c-2.073 2.094-16.862-0.212-42.278-26.709-25.415-26.497-32.543-40.913-33.657-49.412l-14.467 4.29c-0.65 18.157-1.457 32.286 6.776 55.898z" stroke="#727171" stroke-width="2.1802"/>
   <path d="m120.32 154.41c-24.432-5.331-39.08-1.792-49.323-1.087l-18.306 29.211 10.898 36.447c-0.7013-2.863 6.8652-10.539 42.741-18.345 35.877-7.805 65.052-9.653 72.879-6.158l-2.121-15.449c-15.136-10.05-32.336-19.288-56.768-24.619z" stroke="#727171" stroke-width="2.1802"/>
   <circle cx="186.72" cy="187.12" r="24.872" fill="#ccc" stroke="#000" stroke-opacity=".87" stroke-width="2.1802" tb:tag="circle-background"/>
   <circle cx="186.72" cy="187.12" r="12.436" fill="#727171"/>
  </g>
 </g><mask id="path-20-inside-1_3214_60796" fill="white">
  <path d="m306.86 186.82c0 66.227-53.806 119.92-120.18 119.92-66.372 0-120.18-53.689-120.18-119.92 0-66.228 53.806-119.92 120.18-119.92 66.373 0 120.18 53.689 120.18 119.92zm-221.64 0c0 55.911 45.424 101.24 101.46 101.24 56.034 0 101.46-45.325 101.46-101.24 0-55.912-45.424-101.24-101.46-101.24-56.033 0-101.46 45.325-101.46 101.24z"/>
 </mask><path d="m306.86 186.82c0 66.227-53.806 119.92-120.18 119.92-66.372 0-120.18-53.689-120.18-119.92 0-66.228 53.806-119.92 120.18-119.92 66.373 0 120.18 53.689 120.18 119.92zm-221.64 0c0 55.911 45.424 101.24 101.46 101.24 56.034 0 101.46-45.325 101.46-101.24 0-55.912-45.424-101.24-101.46-101.24-56.033 0-101.46 45.325-101.46 101.24z" fill="#ccc" mask="url(#path-20-inside-1_3214_60796)" stroke="#000" stroke-width="5.3402" tb:tag="circle-background"/><g fill="#D12730" style="display: none;" tb:tag="critical">
  <rect width="84" height="84" rx="4" fill="#fff" style=""/>
  <rect width="84" height="84" rx="4" style=""/>
  <rect x="2" y="2" width="80" height="80" rx="2" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 27.562-0.4688 20.059h-4.0234l-0.4883-20.059h4.9805zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g><g fill="#faa405" style="display: none;" tb:tag="warning">
  <path d="m38.422 7.1554c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" fill="#fff" style=""/>
  <path d="m38.422 7.1554c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" style=""/>
  <path d="m40.211 8.0498c0.7371-1.4741 2.8407-1.4741 3.5778-1e-5l35.528 71.056c0.6649 1.3298-0.3021 2.8944-1.7888 2.8944h-71.056c-1.4868 0-2.4538-1.5646-1.7889-2.8944z" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 37.562-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g><defs>
  <clipPath id="clip0_3214_60796">
   <rect x="66.508" y="66.9" width="240.36" height="239.83" rx="119.92" fill="#fff"/>
  </clipPath>
 </defs>
</svg>