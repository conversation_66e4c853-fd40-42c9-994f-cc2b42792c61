{"fqn": "horizontal_pressure_card_with_background", "name": "Horizontal pressure card with background", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_pressure_card_with_background_system_widget_image.png", "description": "Displays the latest pressure telemetry in a scalable horizontal layout with the background image.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pressure', label: 'Pressure', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < 980) {\\n\\tvalue = 980;\\n} else if (value > 1040) {\\n\\tvalue = 1040;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"compress\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1000,\"color\":\"#DE2343\"},{\"from\":1000,\"to\":1020,\"color\":\"#7CC322\"},{\"from\":1020,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":1000,\"color\":\"#DE2343\"},{\"from\":1000,\"to\":1020,\"color\":\"#7CC322\"},{\"from\":1020,\"to\":null,\"color\":\"#DE2343\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/horizontal_pressure_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal pressure card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"hPa\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "barometry"], "resources": [{"link": "/api/images/system/horizontal_pressure_card_with_background_system_widget_background.png", "title": "\"Horizontal pressure card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_pressure_card_with_background_system_widget_background.png", "publicResourceKey": "BSzEY6MLuGyPsYR5MsrvVBSJ3mMj577o", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/horizontal_pressure_card_with_background_system_widget_image.png", "title": "\"Horizontal pressure card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_pressure_card_with_background_system_widget_image.png", "publicResourceKey": "DSYfFXs5kntWudG9UmlBaZ4O6RtyvsOm", "mediaType": "image/png", "data": "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", "public": true}]}