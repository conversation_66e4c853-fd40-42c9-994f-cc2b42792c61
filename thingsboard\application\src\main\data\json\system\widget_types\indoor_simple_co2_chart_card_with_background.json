{"fqn": "indoor_simple_co2_chart_card_with_background", "name": "Indoor simple CO2 chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/indoor_simple_co2_chart_card_with_background_system_widget_image.png", "description": "Displays historical indoor CO2 level values as a simplified chart with background. Optionally may display the corresponding latest indoor CO2 level value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'co2', label: 'CO2 level', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'co2', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"CO2 level\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 160 - 80;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 400) {\\n\\tvalue = 400;\\n} else if (value > 1600) {\\n\\tvalue = 1600;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 160 - 80;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 400) {\\n\\tvalue = 400;\\n} else if (value > 1600) {\\n\\tvalue = 1600;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":600,\"color\":\"#3B911C\"},{\"from\":600,\"to\":800,\"color\":\"#F77410\"},{\"from\":800,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/indoor_simple_co2_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"CO2 level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"co2\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"ppm\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["environment", "co2", "indoor", "carbon dioxide", "carbon emission", "carbon footprint", "carbon output", "carbon pollution", "carbon capture", "carbon offset", "carbon reduction", "carbon neutral", "climate gas", "fossil fuel emission", "carbon cycle"], "resources": [{"link": "/api/images/system/indoor_simple_co2_chart_card_with_background_system_widget_background.png", "title": "\"Indoor simple CO2 chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_simple_co2_chart_card_with_background_system_widget_background.png", "publicResourceKey": "WJhEeAOizGizEGYepKDOo3lxrKKGHdoG", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/indoor_simple_co2_chart_card_with_background_system_widget_image.png", "title": "\"Indoor simple CO2 chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_simple_co2_chart_card_with_background_system_widget_image.png", "publicResourceKey": "VdeIsCnYC7WSveC750idGdZyPxg6RTUV", "mediaType": "image/png", "data": "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", "public": true}]}