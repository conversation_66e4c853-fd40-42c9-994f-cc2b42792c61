{"fqn": "gpio_widgets.basic_gpio_control", "name": "Basic GPIO Control", "deprecated": false, "image": "tb-image;/api/images/system/basic_gpio_control_system_widget_image.png", "description": "Allows to change state of the GPIO for target device using RPC commands. Requires handling of the RPC commands in the device firmware. Uses 'getGpioStatus' and 'setGpioStatus' RPC calls", "descriptor": {"type": "rpc", "sizeX": 4, "sizeY": 2, "resources": [], "templateHtml": "<fieldset class=\"gpio-panel\" style=\"height: 100%;\">\n  <section class=\"gpio-row flex flex-row\" *ngFor=\"let row of rows\"\n           [style.height.px]=\"prefferedRowHeight\">\n    <section class=\"flex flex-1 flex-row\" *ngFor=\"let cell of row; let $index = index\">\n      <section class=\"flex flex-1 flex-row items-center\" [class.justify-end]=\"$index===0\" [class.justify-start]=\"$index!==0\" *ngIf=\"cell\">\n        <span class=\"gpio-left-label\" [class.!hidden]=\"$index!==0\">{{ cell.label }}</span>\n        <section class=\"switch-panel flex flex-row items-center justify-start\" [class.col-0]=\"$index===0\" [class.col-1]=\"$index!==0\"\n                 [style.height.px]=\"prefferedRowHeight\"\n                 [style.background-color]=\"switchPanelBackgroundColor\">\n          <span class=\"pin\" [class.!hidden]=\"$index!==0\">{{cell.pin}}</span>\n          <span class=\"flex-1\" [class.!hidden]=\"$index!==1\"></span>\n          <mat-slide-toggle\n              [disabled]=\"!rpcEnabled || executingRpcRequest\"\n              [checked]=\"cell.enabled\"\n              (change)=\"gpioToggleChange($event, cell)\"\n              (click)=\"gpioClick($event, cell)\">\n          </mat-slide-toggle>\n          <span class=\"flex-1\" [class.!hidden]=\"$index!==0\"></span>\n          <span class=\"pin\" [class.!hidden]=\"$index!==1\">{{cell.pin}}</span>\n        </section>\n        <span class=\"gpio-right-label\" [class.!hidden]=\"$index!==1\">{{ cell.label }}</span>\n      </section>\n      <section class=\"flex flex-1 flex-row\" *ngIf=\"!cell\">\n        <span class=\"flex-1\" [class.!hidden]=\"$index!==0\"></span>\n        <span class=\"switch-panel\"\n              [style.height.px]=\"prefferedRowHeight\"\n              [style.background-color]=\"switchPanelBackgroundColor\"></span>\n        <span class=\"flex-1\" [class.!hidden]=\"$index!==1\"></span>\n      </section>\n    </section>\n  </section>\n  <span class=\"error\" style=\"position: absolute; bottom: 5px;\" [class.!hidden]=\"!rpcErrorText\">{{rpcErrorText}}</span>\n  <mat-progress-bar [class.!hidden]=\"!executingRpcRequest\" style=\"position: absolute; bottom: 0;\" mode=\"indeterminate\"></mat-progress-bar>\n</fieldset>", "templateCss": ".error {\n    font-size: 14px !important;\n    color: maroon;/*rgb(250,250,250);*/\n    background-color: transparent;\n    padding: 6px;\n}\n\n.error span {\n    margin: auto;\n}\n\n.gpio-panel {\n    padding-top: 10px;\n    white-space: nowrap;\n}\n\n.gpio-panel section.flex-1 {\n    min-width: 0px;\n}\n\n\n.switch-panel {\n    margin: 0;\n    height: 32px;\n    width: 66px;\n    min-width: 66px;\n}\n\n.switch-panel mat-slide-toggle {\n    margin: 0;\n    width: 36px;\n    min-width: 36px;\n}\n\n.switch-panel.col-0 mat-slide-toggle {\n    margin-left: 8px;\n    margin-right: 4px;\n}\n\n.switch-panel.col-1 mat-slide-toggle {\n    margin-left: 4px;\n    margin-right: 8px;\n}\n\n.gpio-row {\n    height: 32px;\n}\n\n.pin {\n    margin-top: auto;\n    margin-bottom: auto;\n    color: white;\n    font-size: 12px;\n    width: 16px;\n    min-width: 16px;\n}\n\n.switch-panel.col-0 .pin {\n    margin-left: auto;\n    padding-left: 2px;\n    text-align: right;\n}\n\n.switch-panel.col-1 .pin {\n    margin-right: auto;\n\n    text-align: left;\n}\n\n.gpio-left-label {\n    margin-right: 8px;\n}\n\n.gpio-right-label {\n    margin-left: 8px;\n}", "controllerScript": "var namespace;\nvar cssParser = new cssjs();\n\nself.onInit = function() {\n    var utils = self.ctx.$injector.get(self.ctx.servicesMap.get('utils'));\n    namespace = 'gpio-control-' + utils.guid();\n    cssParser.testMode = false;\n    cssParser.cssPreviewNamespace = namespace;\n    self.ctx.$container.addClass(namespace);\n    self.ctx.ngZone.run(function() {\n       init(); \n    });\n}\n\nfunction init() {\n        \n    var i, gpio;\n    var scope = self.ctx.$scope;\n    var settings = self.ctx.settings;\n    scope.gpioList = [];\n    for (var g = 0; g < settings.gpioList.length; g++) {\n        gpio = settings.gpioList[g];\n        scope.gpioList.push(\n            {\n                row: gpio.row,\n                col: gpio.col,\n                pin: gpio.pin,\n                label: gpio.label,\n                enabled: false\n            }\n        );\n    }\n\n    scope.requestTimeout = settings.requestTimeout || 1000;\n\n    scope.switchPanelBackgroundColor = settings.switchPanelBackgroundColor || tinycolor('green').lighten(2).toRgbString();\n\n    scope.gpioStatusRequest = {\n        method: \"getGpioStatus\",\n        paramsBody: \"{}\"\n    };\n    \n    if (settings.gpioStatusRequest) {\n        scope.gpioStatusRequest.method = settings.gpioStatusRequest.method || scope.gpioStatusRequest.method;\n        scope.gpioStatusRequest.paramsBody = settings.gpioStatusRequest.paramsBody || scope.gpioStatusRequest.paramsBody;\n    }\n    \n    scope.gpioStatusChangeRequest = {\n        method: \"setGpioStatus\",\n        paramsBody: \"{\\n   \\\"pin\\\": \\\"{$pin}\\\",\\n   \\\"enabled\\\": \\\"{$enabled}\\\"\\n}\"\n    };\n    \n    if (settings.gpioStatusChangeRequest) {\n        scope.gpioStatusChangeRequest.method = settings.gpioStatusChangeRequest.method || scope.gpioStatusChangeRequest.method;\n        scope.gpioStatusChangeRequest.paramsBody = settings.gpioStatusChangeRequest.paramsBody || scope.gpioStatusChangeRequest.paramsBody;\n    }\n    \n    scope.parseGpioStatusFunction = \"return body[pin] === true;\";\n    \n    if (settings.parseGpioStatusFunction && settings.parseGpioStatusFunction.length > 0) {\n        scope.parseGpioStatusFunction = settings.parseGpioStatusFunction;\n    }\n    \n    scope.parseGpioStatusFunction = new Function(\"body, pin\", scope.parseGpioStatusFunction);\n    \n    function requestGpioStatus() {\n        self.ctx.controlApi.sendTwoWayCommand(scope.gpioStatusRequest.method, \n                            scope.gpioStatusRequest.paramsBody, \n                            scope.requestTimeout)\n            .subscribe(\n                function success(responseBody) {\n                    for (var g = 0; g < scope.gpioList.length; g++) {\n                        var gpio = scope.gpioList[g];\n                        var enabled = scope.parseGpioStatusFunction.apply(this, [responseBody, gpio.pin]);\n                        gpio.enabled = enabled;   \n                        self.ctx.detectChanges();\n                    }\n                }\n            );\n    }\n    \n    function changeGpioStatus(gpio) {\n        var pin = gpio.pin + '';\n        var enabled = !gpio.enabled;\n        enabled = enabled === true ? 'true' : 'false';\n        var paramsBody = scope.gpioStatusChangeRequest.paramsBody;\n        var requestBody = JSON.parse(paramsBody.replace(\"\\\"{$pin}\\\"\", pin).replace(\"\\\"{$enabled}\\\"\", enabled));\n        self.ctx.controlApi.sendTwoWayCommand(scope.gpioStatusChangeRequest.method, \n                                    requestBody, scope.requestTimeout)\n                    .subscribe(\n                        function success(responseBody) {\n                            var enabled = scope.parseGpioStatusFunction.apply(this, [responseBody, gpio.pin]);\n                            gpio.enabled = enabled;\n                            self.ctx.detectChanges();\n                        }\n                    );\n    }\n    \n    scope.gpioCells = {};\n    var rowCount = 0;\n    for (i = 0; i < scope.gpioList.length; i++) {\n        gpio = scope.gpioList[i];\n        scope.gpioCells[gpio.row+'_'+gpio.col] = gpio;\n        rowCount = Math.max(rowCount, gpio.row+1);\n    }\n    \n    scope.prefferedRowHeight = 32;\n    scope.rows = [];\n    for (i = 0; i < rowCount; i++) {\n        var row = [];\n        for (var c =0; c<2;c++) {\n            if (scope.gpioCells[i+'_'+c]) {\n                row[c] = scope.gpioCells[i+'_'+c];\n            } else {\n                row[c] = null;\n            }\n        }\n        scope.rows.push(row);\n    }\n\n    scope.gpioClick = function($event, gpio) {\n        if (scope.rpcEnabled && !scope.executingRpcRequest) {\n            changeGpioStatus(gpio);\n        }\n    };\n    \n    scope.gpioToggleChange = function($event, gpio) {\n        gpio.enabled = !$event.checked;\n        $event.source.toggle();\n        self.ctx.detectChanges();\n    }\n    \n    if (scope.rpcEnabled) {\n        requestGpioStatus();   \n    }\n    \n    self.onResize();\n}\n\nself.onResize = function() {\n    var scope = self.ctx.$scope;\n    var rowCount = scope.rows.length;\n    var prefferedRowHeight = (self.ctx.height - 35)/rowCount;\n    prefferedRowHeight = Math.min(32, prefferedRowHeight);\n    prefferedRowHeight = Math.max(12, prefferedRowHeight);\n    scope.prefferedRowHeight = prefferedRowHeight;\n    var ratio = prefferedRowHeight/32;\n    \n    var css = '.mat-slide-toggle .mat-slide-toggle-bar {\\n' +\n        '   height: ' + 14*ratio+'px;\\n'+\n        '   width: ' + 36*ratio+'px;\\n'+\n    '}\\n';\n    css += '.mat-slide-toggle .mat-slide-toggle-thumb-container {\\n' +\n        '   height: ' + 20*ratio+'px;\\n'+\n        '   width: ' + 20*ratio+'px;\\n'+\n    '}\\n';\n    css += '.mat-slide-toggle .mat-slide-toggle-thumb {\\n' +\n        '   height: ' + 20*ratio+'px;\\n'+\n        '   width: ' + 20*ratio+'px;\\n'+\n    '}\\n';\n    css += '.mat-slide-toggle .mat-slide-toggle-ripple {\\n' +\n        '   height: ' + 40*ratio+'px;\\n'+\n        '   width: ' + 40*ratio+'px;\\n'+\n        '   top: calc(50% - '+20*ratio+'px);\\n'+\n        '   left: calc(50% - '+20*ratio+'px);\\n'+\n    '}\\n';\n    css += '.gpio-left-label, .gpio-right-label {\\n' +\n        '   font-size: ' + 16*ratio+'px;\\n'+\n    '}\\n';\n    var pinsFontSize = Math.max(9, 12*ratio);\n    css += '.pin {\\n' +\n        '   font-size: ' + pinsFontSize+'px;\\n'+\n    '}\\n';\n\n    cssParser.createStyleElement(namespace, css);\n    \n    self.ctx.detectChanges();\n}\n\nself.onDestroy = function() {\n}\n", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-gpio-control-widget-settings", "defaultConfig": "{\"targetDeviceAliases\":[],\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"parseGpioStatusFunction\":\"return body[pin] === true;\",\"gpioStatusChangeRequest\":{\"method\":\"setGpioStatus\",\"paramsBody\":\"{\\n   \\\"pin\\\": \\\"{$pin}\\\",\\n   \\\"enabled\\\": \\\"{$enabled}\\\"\\n}\"},\"requestTimeout\":500,\"switchPanelBackgroundColor\":\"#b71c1c\",\"gpioStatusRequest\":{\"method\":\"getGpioStatus\",\"paramsBody\":\"{}\"},\"gpioList\":[{\"pin\":1,\"label\":\"GPIO 1\",\"row\":0,\"col\":0,\"_uniqueKey\":0},{\"pin\":2,\"label\":\"GPIO 2\",\"row\":0,\"col\":1,\"_uniqueKey\":1},{\"pin\":3,\"label\":\"GPIO 3\",\"row\":1,\"col\":0,\"_uniqueKey\":2}]},\"title\":\"Basic GPIO Control\"}"}, "tags": ["pin", "pins", "board", "circuit", "digital read", "digital write", "analog read", "analog write", "microcontroller", "i/o", "input/output", "hardware"], "resources": [{"link": "/api/images/system/basic_gpio_control_system_widget_image.png", "title": "\"Basic GPIO Control\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "basic_gpio_control_system_widget_image.png", "publicResourceKey": "xFx05SPWDYVDk5IBiveTJLLEbYhz4zBd", "mediaType": "image/png", "data": "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", "public": true}]}