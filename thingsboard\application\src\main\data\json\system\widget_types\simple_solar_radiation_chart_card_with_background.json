{"fqn": "simple_solar_radiation_chart_card_with_background", "name": "Simple solar radiation chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_solar_radiation_chart_card_with_background_system_widget_image.png", "description": "Displays historical solar radiation values as a simplified chart with background. Optionally may display the corresponding latest solar radiation value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'radiation', label: 'Solar Radiation', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'radiation', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Solar Radiation\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 1100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 1100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#5579E5\"},{\"from\":0,\"to\":250,\"color\":\"#7CC322\"},{\"from\":250,\"to\":500,\"color\":\"#F89E0D\"},{\"from\":500,\"to\":1000,\"color\":\"#F77410\"},{\"from\":1000,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_solar_radiation_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Solar Radiation\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:radioactive\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"W/m²\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "solar", "radiation", "sunlight"], "resources": [{"link": "/api/images/system/simple_solar_radiation_chart_card_with_background_system_widget_background.png", "title": "\"Simple solar radiation chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_solar_radiation_chart_card_with_background_system_widget_background.png", "publicResourceKey": "qBh60PPvSykx8M1UZaFiF90SSn96dZlK", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_solar_radiation_chart_card_with_background_system_widget_image.png", "title": "\"Simple solar radiation chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_solar_radiation_chart_card_with_background_system_widget_image.png", "publicResourceKey": "Qab57HVgh4eRFaLYQAScROVeGFNyoDlj", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAC/VBMVEUAAACQTQyVUABvMQWZUgFvMATLfwCgVwTDcAOUTQFrLQHRiAevXAf////04MPy3sP57M714sP679D35sfs2sL25cTx3cPw3MP46Mnu2sL78tP46Mvu3MP568z67dB8wyL46sr89dX78dL148X++dn89NT999b56s3///rv18T36Mf78dC+awL++96yYgGqXwHv38T57s7///SmWwKjWAKoXAH35cWwXwK6aAG0ZQH++9v35MmsYQLUggW2ZgKvYgLDcQPRfwK7awL///Dz48Xx2Mb14cj//eHy28e3aAL//uzGdQGyZALAbwLx4cTKeQLz3ceMyTjp18OFPgLXiQ/OfAK2YgKtXQI7ODHgmSf//un//ubVhgegWAKdVAHotDelWAL468jhnCqrWwLptjvrvUDiny7ckSDUew7y28QhHxuLQgLTeQigUgLZihjPcwTS15r//ePquT3grQ7jpSogHhqcTwN/OgL578nioSjelSXajxkfHRnalwvXkQnlqS2iVQKZUQLboAfLbwSTSQLIaQTCYgL68snWgA/CWgmYRwPe0bHswEQuLCaaTAO5ZALjozHmrTDdnhHSdgSRRAL79Mrl1MLrzSmMhnbnsTPnrjPjshrepgmWTgLYx63mvB/GYwjNhAeqWALtxEjhpxfo1ri+sprg07huZ1vXgxVkKwR6NwLDu6Ss1WWYzEnlpzQhIR6+VAqrTATOeQKJf27dlx9fKAP038VVUEWhSAPoxCPTiwdqLgODxSzitQ6TjXvmrTe0TwdiXFDz5TLHfAdxMgPe56mozl9/eGghISGsrKLw89Tq3r+nnIeiloJ1c2jvyU3v2S7jrSEhISDkvBdcJgXZ3KKg0Fau2XBYV0/j4syyrJaXl5BIRDvj4+HGxrfe7rTL4I+30XLovSm3VgPn4bOqpY/j49fr6brB2IDB45Wnn4rH1Yj470Xx8fDLy8jp88ewo473++/w9eTd2L7O6adKSUK7cxvcu5LVrHr2+NPL0pD8+2nBnHv9+lmIXlQDAAAADXRSTlMA9iDvryAgv7+/v7CvW+81JQAAGw5JREFUeNrs1LFu4kAUhWESRVrtuyAosAWWYgjuVtiisRUhmRAh1nQUURpsKUCXYgtc2hLVVqkoIqpteIQ80p57Z4Zhin2CnT8pEyWfzlwaNpvNZrPZbDabzWaz2Ww2m81ms9ls/1233292u6enqdNx3fZjd9BqeV6/1+sFQbPZjKIHNBqNxuMkSYbI9/33948VtaTyfMLNJ/M194o2m01Zn8/n2Wy24GZcVVV1WZdludm8IvxwlmVzhN/P83yZL5fFCsVxnKapj/AHk+TzczweIfwnUYR/Kgh+oH4Ytgb37Xan40zvbuG4OewAmTqAwNFthV5fOxgCBmKHT46PGH+sEArFMBRglHVdXRgvWgNLeZFklKLk1BIUgpiSZKwkkZD0eoB4HiD3bdd1nOnzbePucIBj60gI9ugDwo4IyT14ELVHHJuOOaUdzCCHMLyowJGrsASWPVKWCTIlgLCER7mi0CQBIH0vJEjbxSbOtwY7aJBn9xEOhsg9TMeQgiONASkKw5GtOZOhDD9FkoJAwQuDZc8Zo7CkWAnIPyQRb6IkAtIRkC0cHZcflj4QchgPiweRjoIcpGBGxgjFgAIthOEXvvAtAoUsFVPKI7qMIiV6k5TyzecFiNqkhzPxIOFJ8J4aPMh2CgdfOjsU5MF8WP5QOhhymUNMsZenUVXiNlgBgk5QxK3wJkeOJJoCC01SqDNJwaAYoiUBS2iSAU+iIPSw3HaXL8QcZKQhCBB2YI9Tfs0QCjg0QyJ+y6RFj1KfkUHZZ/t5RpKCJX94FPW4lIQhahOGkARPS14IPntxIWHosQMQ89KRcKTiYZ1OJ3kdao1jjWiOhWJoxdeXwshj4VHOnJCwZY2wSn4iydvb1esCZCjPREv0JJBoCH9kwXE5dc0wHfFfyuoeJ4EoiuI4K+B7+JBhEhxxCmMIiVDoLMMWK9fhBmjYBLULsGFXQOe5d+6dw8vLxPgvtdCf513RAxEIGPjp1WWchBGuoYTbSPkEBVWSk49y+D7o68JfSSTvXz+cxC/e7p0Shawcoo7z5ThEUylNx2g0GnVQ2+r1ejkaSJPJrKoout56nfW1e3SnLdECLSX9Cr7VR5nXRYU10yaTAdpukyTJe1Lb60j4pfCrpeh4PdeTbFqVA5DzfqiOf0EKdOMgxBkLzSwOkQiJJQ7JAaGEEJNMp3tI5EoIwalf1cFBEBxS6HhTCAeZOyQzB+dQBqs3CSCg0KIOJBJbpHkSUC7+qbhqfdjLKo8RZBRDJB8kdKwzPqwbxqNllIbHhbgIIXlikMZJjgJRiS+yWg0DSNoAMQdfVtdyxlPlgMIYzEahhBBKAkiTZCwQlci5lwqBY4cT2ZRD69YRn0iSi4OQuTOygLF0xYPlEh+Fd0IJFxlICSLE4iSpBAgmqSA7QP5eBAxCRGKOCpKt3UGGG5iNEl48IZQAwkkazz2GvAQQcxASvKxku60nKQAJ/u/aHGS81hmFkr4WQ7gIJ0EOEYo6bBL7dI8glMAx5iBchBBIMIg6OAcZoniucwwokKBaQsgckkaIFS1ikLKGlDEkXEQhv4TXsWviUBwH8L9AOClW2qOQtjZIS7mrcB26Kgj+BQ/l4OZsDtnyH8jbM2XrS4YMb8jgFnAyUZMhUzToZgehIifO93vvXvq0In2DLiJ8/P6+7xfhmwVE4Y6rkw6uaMrDMXuSm4eHIwhIlO8KfPm9hEjJUUdEInyT5JAfhxDLB7EV7TnI0IksgHBHT58ARBGBMIdrmma05e0QjBmdTp1tt8NPt9ttNp3F45GEU56EQ0AUWfZjyOFoXQAESiIg4OAQKbGQYZfLkTE4zyEbo+8h9Mogzx8Q4WD9MDVngPAWGNwBaQBklE7b4qCsM0rdJoRyMF3HEgVOnsizkBy3XS4SmchvlsivA4iNDMMrl+E1ziGDfrFo6ZPbEpkQDlEUdzW7+mav7NVPDrm8bODhteouCcQxmrkZQMK/rdYidFv1kDqLdjjtNqczMq6oxHKXWyYRmTwJiYTc70OOJHK1i0QE5M8nSGQYxqBsIXizBcTDURFOycF9POSQIXZQ/5urYWRDPxjkDg9VH8cofWzucErp2xtd1E34WFxPKcpaNGy7dIfQuILQDuGGnK4nkIhIJAQcpxKRkAvxKC8T2b+1+oaByt6GACQSkFfHQI5VJLr7THTCIKZ7tdLGrrZik3Vjastlireq71YInS7oth0CJFnUsnUtS2q1JKvVadhCcXeEvQqK1Ya2FJGchnw1WqLspyGR5202Bjr3PI983L5WBB3x9FLpTJ+wjljQCc12NZtdWABJdxpRK70YITr1KUxTApB1de0gHFSrSQaYECJqd+L0Bfmqqi15T4REzJaEMIl0nI5EJpIv9k+PKHbs71++HmEd8X2AvHLIGA/Hqw/IpandNXCswm89WtBpRusAmc+D9XuQvYcACbJqFSBJ2Go76SPyKwDhjefDJSORkOOOiCMjOYTkt28OEQ+/hGxskOSQPrKKkb4hunfm6b0es8yuhjnkBiDX177mKtRr+jBaEIAZMMg6WLzHQaGATcCE1TStv9GwCZAKQMQtvB/JIeT5i0gEBCQA4X9zP0N40+O9RKyBoevD25Kn69j/X3ZdG3AIczCIeo/SF5/SFCqeJcmAQwpmEOyCeSEO4gJA5ihJ4nYH+S8AUeFZWBReRqKwIyByk5zc7QABCYeAg+11BpGOsmPAIft7/R9ldozaRhSEAdgXSBELWYaoCigssbMmCkGuJNAWArG1JaQmsJVLFTqFCbrBFgHFPoAL12ljcoCAdQRByAXyv/n3aXaZPFY7jUrz8c/sm/e8zbfuPNzebVut9+f4a8/Psi6eCoQHIU7zn78X06Tf3++iV6z9Xn528hNFu79JMl1OPvA80bULFPaWc5jeCuyN3U53XDzUjQrI1Wigu2+nc+8gX39VNy1uKC1XDsL1BJALgcCBc3CyEkcEhilKon5/unASPeIVUj8kSumgABlLb6XpiXtiRCKD+OCQDf4+z/3GqJI2ShyyoHhIAwcKkqQiQSSl5iKkrrd0T8HzFiWEZHhjBIOlC3xJooEQIg5ApLN6B8jSOAKSlTSXzrvpLYWYOwkoB8il7y2BXM1GcQyHgahDINwZNRCMCC8hmBA6FkmijgBEJNVIdE/RSARCSSARQsaApDEg+PiiseJUHWd0WAgchJzLDg8HIRIIG8s4bHFMVhONhFd4f5TY3nodkhCC3orTk+IVPr7xjABE8mirA4kIBA4HYSB01FU5EkIosRAGYh+FVHIpveVm5BMhNwqhoxbCWW8YCKuIhJCehWhvOQckpCiEtxIPuRbIl8/ZzAWyke+VdxgIHLazOCJNA9FI2FvFWwSvilwcFYISSCCRMzxSO0iMRLJs4CFgmECqibQU4hxliAmkdkrQW7UQOigxEEjkMXs4BASBOMiGkFAgTCQ8IvxkTdVRH8liqRBUGfIGEjg0kSDEJTKW1iIEgWy64ri9tRBKCEEFITaQ+t4yicjFvXpvdw4nUQjLQOAARBxMhJLwmxb/meAhvV6TEWFVIZBYSHlGUHAEIMWwZw0htrVwrjvIqhEkkiH5L8S8pNBxNKTD1gpD2gZSOQ6PnHU6qpC3BmKG/ShIrIlQUgvxb4yE9BSijvohEci7EuTwSmeH3Y6IQmRrBGTGRK6Nw7SWPUYsJGoG4dFOByH+tbHFOhISEzIgRI8Rrep5aL9ap4Rw2JtD9GSHw3y0to9P85eHXCF0mNYiJCOEn18TiTlHuPwiEUIuFNJ42CcBiE8kn69fHp7W68dgIIQMBfLNt1ZXEwmsKAbykYnIyQ7IogEER/uSEH60/GO2dwByN59/R2PlP9Z/ArOO4tKYpiMm4iD/KLd/16aiKA7g/gPW2kRMNXrUKorFGEWtWsGhQaFI4U4+FEFw8oEQh4hDhu7h7U4dhDQZkjcJfcujkCENWDs2gRJICpE2dggtXXTxe+7N7e3zpZVc0Ne0te3Hc879XVPswdQKD4hqoatX7OfMFGW4AREju6l16QhCipmcmjR6DiDh1DKz30eJBEPecUT+N9dCGwhBbiEiw+aWqXV2hEpEHSxYxYiqdOxtHjVnBORhH/KVIQmGoB0zIqqQMOSmlGiIrvarQ0JmVa0biDnrUQFZzGz0O9+8UxsMQUTULF5DphTESI6H3AxBDg2JQ0zjNUQ70Mz8hDcC9frQcwIOE5HDkLdcJE8h0Y4jl7rsMBBV7YBMcpEMFZIbJiCXQ5mF/TKVWZ53sGJfyy4eBZEOLNkB+YqRJMEQLTmy/9UOQKQEEEgmJzm3wmPi8AFhRwHdbTMHx2qmdrBjms9+GxlUImg6IE9OvJUhgWT8uJAoBwQx1eDQIQFkuCphhw4IIHAYSMFyvixbTi4SWc4smp0HzzptGPgTgCAgiamngHxF/zslIQPrXUPgkD++7FRMRKRkmI4LDtll6b43uVAqHOyg/MrkLsVyjlWrZIoPDGQ5WxoYkDggss8CZB572HKxC0RAcuDo9B2d9SoHARKTWQqi936PXO7u7QYcOrEY8tOxbbtYUI5LlseVnvMyzre8ziyTW9IRhMgtlMTU1EtAcAuQcyuqIdzSZT+tKGlXAJI6s+4SkWiPSkqbqMoQlxo8kmwTbcl6r1MP611IApTvAv9yTzuCa6oNu7lRWnOa6uuWMj+C51Va4ll5IzHb8eNxhiQAeXJifl7dy8QmtqbkN/mHTiMo8BAJOCY6PvmuwHvld9wi2gYED8H9b51cGZIeCbXZGJQI6naprhnsMBP4ppVEiWzYG3KxXrG0I2Ic3BazlQUo1uQk0gQkPs4TLWTWk9uAYNNUQTQFCgVJ4+EDkpo4UyfRmYi45GMAwfcUVMaTP74FCF5dY0mb2re0xFD2Gt2TJ30KOB6rrvdc0l7jbd9Rp8KQXKYWCweE28i3rFUpOjjIrGSzyxqibgcl0PneBkSGJADxV9JpBRGb6RUSSCwOQSqVwrvXz3OhlEmgTMqAtMeufMbf8s6fS73Ht3rfb8329lu7N27stvZ7fUyXGlBwWhkHAjK5YZdkn/XFXr5wYbXp5GIsYce/RwoLxabl8YkNzpyKCyqx0NiBiNzHXZR5dWYFiKbkx8cZokoeEAjW8XpuZGSVaCfCNx5QFanYeZdcKl8cwwdbDGmRi4IX1KgDKHpdHw93Vzrw7Ifj1WHHZM1W29ernm01HQcVcjMUkND0BNFZkPFAe4RS56u/7+R9LZlcUSNRENV9HUDWufiJNidwDTBWxetIB59EYnR0m/xrlwEpUxsVL4h8pgiqN/B2FwDEYxcKhAPjB9e5dox5nj5P+FWp1HLcs0sHWshhut4Fq6khCEj/TuMneX0ZIYnqZiC8RFGQHaLOWUgASckbWz5tT++QG/Npa9Sl+jWWCNl3CRK/n79uEDVwVIJu4O7dBrn7+/t7Myochx1Ju6bPRcwOYygg4dlJocAObv2AKMgbeRE7apqC8OdJyNzcHCA5jg8gI0spTK1dWplYoU2Uf/s6UDyQVOHBA5WPkbGFdJudnWmQmJkh2VrM0FvwyjFWskt3gmduSnJsQEylo8lKB0Te+30zDwlDTNOQuIIkGQIVQ04tjSwtLW2SmBC0Pr1NK0iz6hVIyqrkAcEyiyGvX70CZHb2u2y/wTDhgENeYUlqR/DwMAwxZ1XM0I7oC3S9fBEbEEg+cHKFIVICSDIZTwPCAWIItyXuv8ifnq6SwJujF7Fz6gv+j2YIhsYqIM+ffwREX3SSN7aMY4yb1wQjADGpZSCDAhKPS0eUAwLIW/zaBUM4uUIQpJiKSDIeR7H/wcs80c779/zVVonqVH82Pe3j6V6HpE1leSkDEFQKQ/CTl0moa2esUAyVVhJSsGshSITbMZllxkI4AGEHIPf6vwiD5Ir+5eNOXpsIwzCAx3/AKiYaSSTickgUjWipMooLY1BKx7ZRclFJyEFrDBUiSS4psY2IFgoGt0OjRoxbUFBR0daCtupZcbkrBT30rOKCz/steTtOyEOttaEwP595ZyaTL/UjNggk/rCAuGvPA5/xT+qhr48o3T0BoCKRyJsAWvJB8inwWmwiILiEpP0NexggIADRdPXcrSM3tYMWATKEHQyxX/aCISBbO6SDIBU5Jn5/OBwmCkNgsyTEjXP6sLuGI1KfTPfdAFCAfKOLANzo+hrooT4EBDNPEOxhgKzQgcK+CPDSkUvsWMorfgFpcchiB0EwIeQApAQJKkkXwWAJQcjRZtKwQzJMF13482uRkmD8e548iUSmAbEMnw971jINwaQQBM+2AKHnXLbly3q1GRyKwasymyz5bVUIGlGQooRkD6bT4bCFkIQhYYsgppCIq99jizUEQ/KmO4L0BO4ZhuG7J666lgkIrusJgk0XEF4az8v/HA4HZH6rY690ILFYezs5DrhKpUqpks0KiGkqCSJ3LDOUSCRCpgmJuzY8TCfLoITs2tWNREQAmQ7cw8DL/+1VCEqhaANiW9+/bp5yLGGHlDT2rOUsaeZgCAopAnKuBMoIJBoCiaKgEE8ikc+DIiS97qB7cTCoHCwhyvTHaUDgYAllJXnwmd86AgYiHMxgh4QsdzoYYm8kuT4OSLqoICOAqH1LS3oBMRck8qdOncoLCb7jDgIS3LlzJ0skxcCU4OYQQbREv/tCGxB+eg6HYmiHbdabvCStLNrBEJqQdDrtKulGcNTSAUU1QpDz5yFZYIlvgUESBA5ktkTcHLJLOMwQeXkJDAQKjnQgywnCDMRWiK2RdlFI+qBrbEf2zBlqxN8IIEpieQgiJFQJQ1iiKQbOJUTgoB9G2BfCKwVig/gMgxxG4wVpZthPhyxJXlsfJwcg1WoxO5Jtj8ttx+4DBUnUsANCEjUmQSVhymwIKnGGF8EzgxW2PsihIbYBccyIXL0sIHFADmazrmhlc6FzzUCmStMs425Uw5B8wmP5MSBuhjgkS2Ql68RdIg4rpMM2GbY+fIYXDMOwO6SCJQggMgQpohFAXr2qTxXKmdGCdFy4cAESSVnUZnoYEjLb/GA074SnRG4sXu5lAJehGE4H2vB5DSiaQJjCGEBkrl3bQA5AxsfHo11jg4Up9CQgmoKDGAoRM6KmxFqESlpLKPS2EiWxMdjRtA18yFFnSBOFNdey2jDEDIkLyAnXnTvj4/XKpo0xMSGIktB+ZXpmQxaYbSxx7F1PhERv5Dw1+I46bA5vYzhI4uWjlsf5oqG+XahOdW4K/mLIY0he1eupuHhAtkJzgkLIAYjetxIecyEgLSXYNEysAQndFWaMZgjJUj790Wf8BJm8+uqX34PYcDDEJIgeZ2xyNdkRg2NkxPWYJKBEa2p81MDLQtCIkOCkKCrxQ8BxSEARkYcwetahLQyhZx6KgdBo0JRDwe+4QCBxQshhohB3r05ttDyY6UrPhqR++P0Lw9waFeLREoTGXU5JL0P+k4AiLWiGIDpEYQc/PZcU9WqeeCCB+0HeEAKJZ76zkPmAoBA5ysifDdVMZqK6HpAPkACSO/F9OBxuXKKIL02mMKRVJYim0LGYrrjU80FctUPSaEU7GAJHHknohELUiYJwAMH5rFdDLs/JpZKxqUIhTRBdyfqfNYskMhZCP6YkDOFCuBJA+gARERIscRaQlYiUAILQDXwh0RQoFCSvICxpDQHjxpx6vZ7LpToGdux2PQZETUkqvWZj11S1um8PvQN8W+fU6lgqGs1FK2u7xgqDnR3JZPrp0wcPHrxA7oucplylnDx5cmhoqL+//+jRw/h1I7fxy0YeHT/77svkzMzk25mZv79/4wt8XHx2/d3bycnJZ/jq4sVnX768e3ToEXIFD5T37y1fvz5QLg+OZjKjE+/fT2x/SOl/+HCoc2h/dd/Alr37x0YnJgpdyRI2LJery+TaxwpoRHeS+9fO2bUsDYZx3Igiqo8yPKmYQbO5szDxwDkJPBA8eWCJBzcYEffNw1wRO9hgz8CDLcaGJ3UWUtAWnjQ/gGKfQD9I/3v5QvgRun+4TXTC9fN/XZviizmpGvwrNr009SS12S4kGfsD0548dgMzCBzHOaocRS5Nrq6uSpN3UNnMfq13O175DJds/SubWz4E1usZ1lji2a/dt02cZdksnM834WY+n8OGhEsQcZF2ezxuTqc5Zb5FE/yyXmGRlb+0TZgc6ARGUvnN4SYr89MjOQ1QduCkkpQkhSoZrmnijZdtmqWPeWFyVoEITKBy8/oUClJ5hVg2u9n61xpK63g249WGZDnbZhlSiEsd3BlvsziOZ3EW+z6lfhRmW2pZ/T7jiUynzZypkipJqp631YZFoqT1HC9NHIcfeV3bTtsHEZisTDuwbYh0gh+eKhWJIbV6rok4TIAOKxVPJuDUW1zkbDK+uXnNQ4HK4Y9gvr3dxLtst92h1DAMd1jieBnHWWiROARlQnG4Jro+9xWF9fsk3nKWW2JZyXhMqSp5/GsN0sLv54S2Xmr1p1rRfFHTwFPZ6Fa+g78iGBvUiqd9ZNRaNTVRDW/glFHw9DhorgAiR5P9fn9I5MO/JkiFq8CllCl9MAf+N99H50QEnRPOfQwCjwe3hDHYhtFm4zeUECK+wtBYGBWKhZCckQVLal7RZYkSkch6wc+BzuRFMRjhtH7d8xZ65fZqtfoCMCToNdsZDpEED7EoJFWtD4+9WIqUJj+OJvuvgGuAUgRwkbMJ+Hzm45F3fZ1Spvd1nfFGmiMgBEMZ9TcMImxOdZ2iXsuKAAFLK2JJTqylpSY0qV9DA93jtgrNkF+M3FRhlfs/V6gQo+xep72qYbS0nucNU9mopoOhVqjak4AfHI5xQeSUyf4ggdVZ5RBJOSmcQ+1XR3D7zWug9BtYYz9Fpz4iIhYX8XFhJMoVxGIBWERkuYy3S7QY2SIm1u226xMUMXIDe9AsrntFU20kDyp3bpuG0Zt0zNQz5LpkaJorjzodx5AdM7AfFVqrOnw2wJw4LmzPzQX2eyi8V9vTDycuVcBZgnMDeP0fy2vj1x91XjxlfYCNz6IoJDklFsysEGYsZzwZi4MdcG7QJrYzSBL1hVZ73HnjSM1GcrdSufPwU+fNCqeRSco/ppZTpz7C02+XHRU8leue1mq2JFmWeykidV3nxyjdexgQpPB+Ou4m7feXJmV7gUsH8PdObLC9ajQYVXANj1l0lUUj7y8alBCLMMWnudqqGdWq1OzmOY6jGhpmiKlwbTd9/uh6FNgm2qVz697dikAgEAgEAoFAIBAIBAKBQCAQCAQCwf/GH72WNe3P//9sAAAAAElFTkSuQmCC", "public": true}]}