{"fqn": "horizontal_rainfall_card", "name": "Horizontal rainfall card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_rainfall_card_system_widget_image.png", "description": "Displays the latest rainfall telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'rainfall', label: 'Rainfall', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Rainfall\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 8) {\\n\\tvalue = 8;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:weather-pouring\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#7191EF\"},{\"from\":0,\"to\":2.5,\"color\":\"#4B70DD\"},{\"from\":2.5,\"to\":7.6,\"color\":\"#305AD7\"},{\"from\":7.6,\"to\":null,\"color\":\"#234CC7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#7191EF\"},{\"from\":0,\"to\":2.5,\"color\":\"#4B70DD\"},{\"from\":2.5,\"to\":7.6,\"color\":\"#305AD7\"},{\"from\":7.6,\"to\":null,\"color\":\"#234CC7\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal rainfall card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"mm\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "rain", "precipitation", "downpour", "rain shower", "drizzle", "raindrop", "cloudburst", "rainwater"], "resources": [{"link": "/api/images/system/horizontal_rainfall_card_system_widget_image.png", "title": "\"Horizontal rainfall card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_rainfall_card_system_widget_image.png", "publicResourceKey": "PebapEIjm2P6uqWqZ35XI8GvHAAut5uO", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAqFBMVEUAAADf39/f39/g4ODg4ODf39/////k5OQjTMfg4OB1j9yRpePk6fjOzs7I0vGtvOry9Pvk6Pj09PT5+flYWFjIyMg+Ys7b29vV1dUwV8vCwsK2trbw8PC6x+6fseetra3n5+eDmuBohNi8vLyQkJCsvOo9PT1aeNVMbtE/Ys7W3fXW3vRohNkxV8q6urp0dHRmZmaenp6CgoJKSkovLy+su+pLS0shISHWoRcHAAAABnRSTlMAIEDfv1C6kOEmAAADUUlEQVR42u3aDW+aQBzHcWe7P3/BA+QZBETQ4aytfdr2/t/ZDgStjS7baBtqfp+m5Q5Mel8P0xg7AAAAAAAAAAAAAAAAAAAAAAAAAAAAAPhIXz69OmN4pXx2o6uvskOx6dOzlOHg2qILYF8PRnQRRgjpGYT0DUL6BiF9g5C+ORWilyF7Gh3MFguFKnfB7vBUT7f0qFBfnAjROFTVkj2TWs6D+y0mJaYgiGOFYueO5IG+kdMxRJffptFY789qWvIGITrPq4RlWB5CXPoWP26fgsVs4dw77vO94mydriH6PGR5GHNDo9p4xZJndgxZ5uGKailP9iFPzwtyH5zZQn7t1n+3+NUtxKzXWw30Wtj+ug2rk/WK591CNA7LdltDbbLfkYct/QweDyF3W6VjyPi7ZsiQls55E8i3RJSw10wnppkkciE3yU0z1eupof8pROeNSa0Vc27uby3Fuf95CAmc++e4U4g0fhGSs96s3EjqhZRUU1negHyrl8zVJhm88eQoVeWPlXk+ZM6TF1WGyjkdxESnZ91D5NCjl1RetoNQMzZy1UYayla5iVoiKzxj6XF6PkRlOpLf0juSIUcLP0j3XSpr9QMn1fAHGfVTy3xTPenz8yFpVTkOE/JS2uQy5CjsHUJOb8g6DCevQpqhwaocfa+6NFbPh5hyv2SNrssD568f+34h6tGzOw55Td1CaJwn1f2k8Y0uN1srTXo/h5Cx17w229mSuoS0wpxK7+h1b9EJVvz6hPW/Ict2TZNdh0ZvESK3wpSPKVe0N93FWBT7VLNtskeiOjEiW86zWUa27+8uyBPWv4W0G5KyWk1u1YreeUcSM5F7m6yPQoJA+K4r5CiiyI8KexqJIHBFEYvCJhKZVRQ+WaK+INziX0KWXFITMifixo+OIYc/J8chYmq5IhO7kCzw5Viey3wRuXXIyPdliDvNXEvmRhb9D/ON34+kczoyE34kpoEbWdWOiMKPptbUFfJcXGSuoCrErnYkK2bVhSCKqIOPe2PlZ3Re4Pr0N/oQYtt/umjR3+hFSB8hpG8Q0jcI6RuE9M1ocH0BH7MTWdeDoXIBH7TbynAw+Ho1Uj67q+El/VMNAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAf5Damp/4Ir985nAAAAAElFTkSuQmCC", "public": true}]}