{"fqn": "indoor_illuminance_chart_card_with_background", "name": "Indoor illuminance chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/indoor_illuminance_chart_card_with_background_system_widget_image.png", "description": "Displays a indoor illuminance data by combining the latest and aggregated values with the background image and optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'illuminance', label: 'Illuminance', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'lx', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'illuminance', 'lx', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Illuminance\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 400 - 200;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"lx\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":100,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":100,\"to\":300,\"color\":\"#F89E0D\"},{\"from\":300,\"to\":500,\"color\":\"#F77410\"},{\"from\":500,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 400 - 200;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"lx\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 12 - 6;\\nif (value < -20) {\\n\\tvalue = -20;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"lx\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/indoor_illuminance_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Illuminance\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:lightbulb-on\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "illuminance", "indoor", "brightness", "luminance", "luminosity", "light", "light level", "light intensity", "lux", "candela", "foot-candle"], "resources": [{"link": "/api/images/system/indoor_illuminance_chart_card_with_background_system_widget_background.png", "title": "\"Indoor illuminance chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_illuminance_chart_card_with_background_system_widget_background.png", "publicResourceKey": "AKragAyJaPrlEeZJdwb6lMkC2BB13a6w", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/indoor_illuminance_chart_card_with_background_system_widget_image.png", "title": "\"Indoor illuminance chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_illuminance_chart_card_with_background_system_widget_image.png", "publicResourceKey": "NnHGFHd0nFzTAD3Yd7K379mOxs75CufJ", "mediaType": "image/png", "data": "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", "public": true}]}