{"fqn": "maps_v2.here_map", "name": "HERE Map", "deprecated": true, "image": "tb-image;/api/images/system/here_map_system_widget_image.png", "description": "Displays the location of the entities on HERE Maps. Requires the HERE map key to work properly. Highly customizable via custom markers, marker tooltips, and widget actions.", "descriptor": {"type": "latest", "sizeX": 9.5, "sizeY": 6, "resources": [], "templateHtml": "", "templateCss": ".leaflet-zoom-box {\n\tz-index: 9;\n}\n\n.leaflet-pane         { z-index: 4; }\n\n.leaflet-tile-pane    { z-index: 2; }\n.leaflet-overlay-pane { z-index: 4; }\n.leaflet-shadow-pane  { z-index: 5; }\n.leaflet-marker-pane  { z-index: 6; }\n.leaflet-tooltip-pane   { z-index: 7; }\n.leaflet-popup-pane   { z-index: 8; }\n\n.leaflet-map-pane canvas { z-index: 1; }\n.leaflet-map-pane svg    { z-index: 2; }\n\n.leaflet-control {\n\tz-index: 9;\n}\n.leaflet-top,\n.leaflet-bottom {\n\tz-index: 11;\n}\n\n.tb-marker-label {\n    border: none;\n    background: none;\n    box-shadow: none;\n}\n\n.tb-marker-label:before {\n    border: none;\n    background: none;\n}\n", "controllerScript": "self.onInit = function() {\n    self.ctx.map = new TbMapWidgetV2('here', false, self.ctx);\n}\n\nself.onDataUpdated = function() {\n    self.ctx.map.update();\n}\n\nself.onResize = function() {\n    self.ctx.map.resize();\n}\n\nself.actionSources = function() {\n    return TbMapWidgetV2.actionSources();\n}\n\nself.onDestroy = function() {\n    self.ctx.map.destroy();\n}\n\nself.typeParameters = function() {\n    return {\n        hasDataPageLink: true\n    };\n}", "settingsSchema": "", "dataKeySettingsSchema": "", "settingsDirective": "tb-map-widget-settings-legacy", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"First point\",\"entityAliasId\":null,\"filterId\":null,\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"latitude\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.05427416942713381,\"funcBody\":\"var value = prevValue || 15.833293;\\nif (time % 5000 < 500) {\\n    value += Math.random() * 0.05 - 0.025;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"longitude\",\"color\":\"#4caf50\",\"settings\":{},\"_hash\":0.680594833308841,\"funcBody\":\"var value = prevValue || -90.454350;\\nif (time % 5000 < 500) {\\n    value += Math.random() * 0.05 - 0.025;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"temperature\",\"color\":\"#9c27b0\",\"settings\":{},\"_hash\":0.9430343126300238,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Type\",\"color\":\"#8bc34a\",\"settings\":{},\"_hash\":0.1784452363910778,\"funcBody\":\"return \\\"colorpin\\\";\",\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]},{\"type\":\"function\",\"name\":\"Second point\",\"entityAliasId\":null,\"filterId\":null,\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"latitude\",\"color\":\"#f44336\",\"settings\":{},\"_hash\":0.05012157428742059,\"funcBody\":\"var value = prevValue || 14.450463;\\nif (time % 4000 < 500) {\\n    value += Math.random() * 0.05 - 0.025;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"longitude\",\"color\":\"#ffc107\",\"settings\":{},\"_hash\":0.6742359401617628,\"funcBody\":\"var value = prevValue || -84.845334;\\nif (time % 4000 < 500) {\\n    value += Math.random() * 0.05 - 0.025;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"temperature\",\"color\":\"#8bc34a\",\"settings\":{},\"_hash\":0.773875863339494,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Type\",\"color\":\"#3f51b5\",\"settings\":{},\"_hash\":0.405822538899673,\"funcBody\":\"return \\\"thermometer\\\";\",\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"provider\":\"here\",\"gmApiKey\":\"AIzaSyDoEx2kaGz3PxwbI9T7ccTSg5xjdw8Nw8Q\",\"gmDefaultMapType\":\"roadmap\",\"mapProvider\":\"HERE.normalDay\",\"useCustomProvider\":false,\"customProviderTileUrl\":\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",\"mapProviderHere\":\"HERE.normalDay\",\"credentials\":{\"useV3\":true,\"apiKey\":\"kVXykxAfZ6LS4EbCTO02soFVfjA7HoBzNVVH9u7nzoE\"},\"mapImageUrl\":\"tb-image;/api/images/system/here_map_system_widget_map_image.svg\",\"tmApiKey\":\"84d6d83e0e51e481e50454ccbe8986b\",\"tmDefaultMapType\":\"roadmap\",\"latKeyName\":\"latitude\",\"lngKeyName\":\"longitude\",\"xPosKeyName\":\"xPos\",\"yPosKeyName\":\"yPos\",\"defaultCenterPosition\":\"0,0\",\"disableScrollZooming\":false,\"disableDoubleClickZooming\":false,\"disableZoomControl\":false,\"fitMapBounds\":true,\"useDefaultCenterPosition\":false,\"mapPageSize\":16384,\"markerOffsetX\":0.5,\"markerOffsetY\":1,\"posFunction\":\"return {x: origXPos, y: origYPos};\",\"draggableMarker\":false,\"showLabel\":true,\"useLabelFunction\":false,\"label\":\"${entityName}\",\"showTooltip\":true,\"showTooltipAction\":\"click\",\"autocloseTooltip\":true,\"useTooltipFunction\":false,\"tooltipPattern\":\"<b>${entityName}</b><br/><br/><b>Latitude:</b> ${latitude:7}<br/><b>Longitude:</b> ${longitude:7}<br/><b>Temperature:</b> ${temperature} °C<br/><small>See advanced settings for details</small>\",\"tooltipOffsetX\":0,\"tooltipOffsetY\":-1,\"color\":\"#fe7569\",\"useColorFunction\":true,\"colorFunction\":\"var type = dsData[dsIndex]['Type'];\\nif (type == 'colorpin') {\\n\\tvar temperature = dsData[dsIndex]['temperature'];\\n\\tif (typeof temperature !== undefined) {\\n\\t    var percent = (temperature + 60)/120 * 100;\\n\\t    return tinycolor.mix('blue', 'red', percent).toHexString();\\n\\t}\\n\\treturn 'blue';\\n}\\n\",\"useMarkerImageFunction\":true,\"markerImageSize\":34,\"markerImageFunction\":\"var type = dsData[dsIndex]['Type'];\\nif (type == 'thermometer') {\\n\\tvar res = {\\n\\t    url: images[0],\\n\\t    size: 40\\n\\t}\\n\\tvar temperature = dsData[dsIndex]['temperature'];\\n\\tif (typeof temperature !== undefined) {\\n\\t    var percent = (temperature + 60)/120;\\n\\t    var index = Math.min(3, Math.floor(4 * percent));\\n\\t    res.url = images[index];\\n\\t}\\n\\treturn res;\\n}\",\"markerImages\":[\"tb-image;/api/images/system/map_marker_image_0.png\",\"tb-image;/api/images/system/map_marker_image_1.png\",\"tb-image;/api/images/system/map_marker_image_2.png\",\"tb-image;/api/images/system/map_marker_image_3.png\"],\"showPolygon\":false,\"polygonKeyName\":\"coordinates\",\"editablePolygon\":false,\"showPolygonLabel\":false,\"usePolygonLabelFunction\":false,\"polygonLabel\":\"${entityName}\",\"showPolygonTooltip\":false,\"showPolygonTooltipAction\":\"click\",\"autoClosePolygonTooltip\":true,\"usePolygonTooltipFunction\":false,\"polygonTooltipPattern\":\"<b>${entityName}</b><br/><br/><b>TimeStamp:</b> ${ts:7}\",\"polygonColor\":\"#3388ff\",\"polygonOpacity\":0.5,\"usePolygonColorFunction\":false,\"polygonStrokeColor\":\"#3388ff\",\"polygonStrokeOpacity\":1,\"polygonStrokeWeight\":1,\"usePolygonStrokeColorFunction\":false,\"showCircle\":false,\"circleKeyName\":\"perimeter\",\"editableCircle\":false,\"showCircleLabel\":false,\"useCircleLabelFunction\":false,\"circleLabel\":\"${entityName}\",\"showCircleTooltip\":false,\"showCircleTooltipAction\":\"click\",\"autoCloseCircleTooltip\":true,\"useCircleTooltipFunction\":false,\"circleTooltipPattern\":\"<b>${entityName}</b><br/><br/><b>TimeStamp:</b> ${ts:7}\",\"circleFillColor\":\"#3388ff\",\"circleFillColorOpacity\":0.2,\"useCircleFillColorFunction\":false,\"circleStrokeColor\":\"#3388ff\",\"circleStrokeOpacity\":1,\"circleStrokeWeight\":3,\"useCircleStrokeColorFunction\":false,\"useClusterMarkers\":false,\"zoomOnClick\":true,\"maxClusterRadius\":80,\"animate\":true,\"spiderfyOnMaxZoom\":false,\"showCoverageOnHover\":true,\"chunkedLoading\":false,\"removeOutsideVisibleBounds\":true,\"useIconCreateFunction\":false},\"title\":\"HERE Map\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{}}"}, "tags": ["mapping", "gps", "navigation", "geolocation", "satellite", "directions"], "resources": [{"link": "/api/images/system/here_map_system_widget_image.png", "title": "\"HERE Map\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "here_map_system_widget_image.png", "publicResourceKey": "ENvnSqztiD3Q2KCf2ZLXNtcPCLI9jTeM", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/here_map_system_widget_map_image.svg", "title": "\"HERE Map\" system widget map image", "type": "IMAGE", "subType": "IMAGE", "fileName": "here_map_system_widget_map_image.svg", "publicResourceKey": "LSV7W1Y0urq16uzORoe85xrjPse4Nckl", "mediaType": "image/svg+xml", "data": "PHN2ZyBpZD0ic3ZnMiIgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMTAwIiB3aWR0aD0iMTAwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zOmNjPSJodHRwOi8vY3JlYXRpdmVjb21tb25zLm9yZy9ucyMiIHhtbG5zOmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIgdmlld0JveD0iMCAwIDEwMCAxMDAiPgogPGcgaWQ9ImxheWVyMSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCAtOTUyLjM2KSI+CiAgPHJlY3QgaWQ9InJlY3Q0Njg0IiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBoZWlnaHQ9Ijk5LjAxIiB3aWR0aD0iOTkuMDEiIHN0cm9rZT0iIzAwMCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiB5PSI5NTIuODYiIHg9Ii40OTUwNSIgc3Ryb2tlLXdpZHRoPSIuOTkwMTAiIGZpbGw9IiNlZWUiLz4KICA8dGV4dCBpZD0idGV4dDQ2ODYiIHN0eWxlPSJ3b3JkLXNwYWNpbmc6MHB4O2xldHRlci1zcGFjaW5nOjBweDt0ZXh0LWFuY2hvcjptaWRkbGU7dGV4dC1hbGlnbjpjZW50ZXIiIGZvbnQtd2VpZ2h0PSJib2xkIiB4bWw6c3BhY2U9InByZXNlcnZlIiBmb250LXNpemU9IjEwcHgiIGxpbmUtaGVpZ2h0PSIxMjUlIiB5PSI5NzAuNzI4MDkiIHg9IjQ5LjM5NjQ3NyIgZm9udC1mYW1pbHk9IlJvYm90byIgZmlsbD0iIzY2NjY2NiI+PHRzcGFuIGlkPSJ0c3BhbjQ2OTAiIHg9IjUwLjY0NjQ3NyIgeT0iOTcwLjcyODA5Ij5JbWFnZSBiYWNrZ3JvdW5kIDwvdHNwYW4+PHRzcGFuIGlkPSJ0c3BhbjQ2OTIiIHg9IjQ5LjM5NjQ3NyIgeT0iOTgzLjIyODA5Ij5pcyBub3QgY29uZmlndXJlZDwvdHNwYW4+PC90ZXh0PgogIDxyZWN0IGlkPSJyZWN0NDY5NCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgaGVpZ2h0PSIxOS4zNiIgd2lkdGg9IjY5LjM2IiBzdHJva2U9IiMwMDAiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgeT0iOTkyLjY4IiB4PSIxNS4zMiIgc3Ryb2tlLXdpZHRoPSIuNjM5ODYiIGZpbGw9Im5vbmUiLz4KIDwvZz4KPC9zdmc+Cg==", "public": true}, {"link": "/api/images/system/map_marker_image_0.png", "title": "Map marker image 0", "type": "IMAGE", "subType": "IMAGE", "fileName": "map_marker_image_0.png", "publicResourceKey": "CdCrVxsjA4EAiFaXK4a7K2MZFMeEuGeD", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAB4AAAB/CAYAAAD4mHJdAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAACWAAAAlgB7MGOJQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAwgSURBVGiB7Zt5cBT3lce/v18fc89oRoPEIRBCHIUxp2ywCAgIxLExvoidZIFNxXE2VXHirIO3aqtSseM43qpNeZfYKecox3bhpJykYgdjDkU2mBAB5vCamMNYAgQyURBCoxnNPd39O/aP7hGSEUR24L/uqqf+zfR77/Pe69/Rv6kWwcgPLRIJfZUAa7xez2xd90QBwDSNZKlkHJHAK+l09mUA7BP4vPpRUVExMVoRef+L998njxx9X57vPi/PnTsnO850yPaT7XLXrrflqjtWymhF+HA0Gp0wEp/kHymEQqG4ptJDGzf+um5RUxMSiV7Z3Lyt88L5nozgHJWj4pGmpqZav99PWve04onHHuswmViQzWb7ruZX+Udgv8/z3A+f/NGye1evxssvb+wo5PMfTZs6bfqcuXNHL7hlweh58+ZVAOTUpk2b0p9dvjyqqmrs/b8ejpUMc+unzjgUCsXjsYruE+2n1JY/NedM0zCi0VjA7/d7/f4AAgE//H4/vF4fOjvP9h5695C/oaEhcN/q1SyTzVdnMpnklXzTq4EplUsXfmaRCgC7du3cOn78+KfGj59Add3z1Md1vV7vqPa2D1sA4MYbZ6qUiqVX9X21i4TQcfX19QCA6urquN/vn0kAPRQKpYbTnzRpUhgAampqAEFrPjVYSql7fD4AgK5r2tV0AcDj8WkAoOk6JJGeTw2+nocLdsEu2AW7YBfsgl2wC3bBLtgFu2AX7IJdsAt2wS7YBbtgF+yCXbALdsEu2AW7YBfsgl2wC76mh/ppjIQgXVloPxVSBRV0rBe455P6+kTKBYF3tonxY/IWarry7DvI298Tgp0PR9RzACaN1NeIS100+EdvKXW3cMZvF8wCK10Sq2it2NAzakmukP/wmoP/KuId3BRUMg5uCfCSNVSKVn1rNto7Un8jLrUVqJ4Fi2eEQiEYBzOsy3SYL37TNQdzi8Q5FxkqJIQBsNLlYMGF/zqAJWBxSEogDAY+DJibYqTuRg4WFgO3OKhCYTExbKk5G/mbkSPP2DQhLA5IO/NhSz1MMP882BDgnAFQwdiVSs2vPVhYDIJLUMkBgw1favM6lJoZDDAYhKbAYsOX+rqAhcXAuQSIAKzhSy2vS8YmB7NYH4WCfM7kw5VaWtdpOO3bfWZJZVXgPxMX898bVsm6RhkTIseX29yyIErm/J5z5vwr6pvmsLYjBgeDwSpVJS/OmT1n1de+9qANZgLc4q9Dyj2qQhUhSSUAUCL7GBcchCymTEYBYNWqVXj30MGHT586PZEJ+WAul7ts8bjspd9QKDRNU2nz4z94YtI3H3oI+XwB//3j/9m77eRUUJ9/0eh4APGoDz6vCi4ksgUTmYyBC4k8RLGwtzF+EGu+tHqRqqrYtm0rXnzhhQ7G5cpsNnvyiuBIJFKnqvSd55772eilS5fhwIH9ye+/dPaEf1T9otW3T8GtiyYgGNBBymYEgLSbvakidu8/h01vnkYhcab1gcVs5tx5c6PHjh7DU0/9qFsINPb3939UZg28X11dXR0Qwtr9g8efqGtc+Bn89re/O7FhR9BXNaFm+n98uxHTZ1SDKQqKAihweZlITUVtXQwNs8fg+Bmzdk+bnmPdf/7bwsbGeO2ECaED+9/5XCxWuTGbzVpDwJpGNtx+28o77rr7bmzZsu3k7z+cMlHzeiPrvnoTwtVhFAVQHAZY4HBEoiAAeDXUjI/gyJGeQEd6TFj2tHYuXNgYy2azVe0fngiWDLNloHNFo4FZkXDsoTVr1+KD4x8U/3Ci1qP5PV7N74FeFUbClKDEriy57A5JANL5a68hnqoINL8OAPqbXbNp7clTxTVr1/oOHjr0MFXxq2Qy9wEFACnoY//6la9QAHj+9Q/eUL2RWkVXoWgqkhZBypRImkDKBFIWkLIk+h1JWdL+zrmeNCWSDFB0DYquQvWG637TcnozAKxbt45yTr8PAGowGBwVDAbvmT9/Pvbu3dddijV9WdUUUE0BUQm6kwaCYe+ljK/w8ruUdsYCBLlMEUQhoJoCygWM+LIvHTx4sGfevIbqYMD3BSFkJVUUrG5oaFABoPXwhd1UVUBVahtpKtoOnEV/gSHHgBwDso5c6XO6yNF24CNQTbV9qBRUUenuwz1/BoCZM2dplOJeSggWL1myFEII9IeXziIKBVUUW1QKo2Ci41Anei9kkWcY6Ex5R8qfc0wi0ZPF6QNnYeQNB2j7IQpFOtg0WwiBxoWNIBKLVQI6Z8rUqTh69FiWaFNmEIWgLFShoM5TZbIzgVxvFp6ID5rfA6JQgBAIxsGLJkrpAsycAcH4gN1gX0QPTW9vP5Grr58cJJTOpbqmjgWAnp6ei4QSEEJAKAGh1BbHCS2DLAFmMAgmICwObjDnyYMMAtJL9oN89vRc7KWUQtOUsSqhSggA8sWivSEh9qBxTiCEAGRwQARUVaB67Hf5pZAQlA0Ayrq2LTCogVyhlLURNEw55yYABP2+4ED3vHSClBKQ9jiFdHqvEBCMQzAOKYSt6/RqSGnbDPJRbgT93hAAcM4NyhjrBYDKylhswEEZJgYJFxDchnGTwSqasIomuMnsIDiH5GKIzUAQTsCVlZUxB9xLIUVbKpVEff3kiLTMfimEA7HP5bZgHMJ07mnJAiuaYEXT3jcZDMLkTgBD7exgBKRp9NfVTQwnk0kIKduoJGRH8/ZmhMNh4skc3DnEkDlAi4GbtjDDguVAmZM1M6yB68JyKsCGBqD373s7GAySnTt3gBDyFhWCvPHee/8HAJhTU5g0BMg4uMXBTT4AZSUTrGjBKpiwCnablQbDbZuyfTmAuRPMegA4euQopCRbaCaTOd2XSLzX3d2Nu+64bR7PnP3LJSCDMBm4YW9FWcmyQYMytsW+Zpfdsm1MdimAdMc7K29bMedCdzeSyeS76XT6jLNI4PGf/+w5aLqOu25IjOOWKcSg0jJjcLZ2ecsZD5TdybqsOxC0ZYpbJ58frek6nn/+eVBJHgecjXkqk2nu7Ozcdfz4cdx556rJN5C3m8v3jBt2xpdnazjysawNy5lUbKkrbmtZsWL5pGNHj6Or62+7k5lMy5CFNRQKTfN6tAMvvvhSRe3EOqx/4oXXLvia7qO6CsVZrey5154KB5YpKSG5tHs+5/ZsZnEIk6Ei1fLH73373i/09fXi0fWPpgyTLchkMqeGgAEgHA5/vjJWsf2PmzYr1dXV+K8fP7vjLxduWkY8ilpetQZPg+UJxh63lzqlNDi7gTa3fuPraz6bzxXw79/5FutP51am0+kdZdaQ/2kzDKNDUci51179w8pbP3er8sAD6+pnVCWy+/fs21LAqBnlMT50qJXFLq2a2L/5gaVy7N133j69u7sb67/7iFHIFf4tlU6/Ppg1kLGU8hYAywBMeOWV33gfXb9+1Q+ffDL+4Ne/AcYY/tS8PbV5++4Dhy+MopY2ZrLiidQDgDBSp5TS+Y7psS65ZOHsW26++eYosxje2PwGNm586eKzz/x027+sXWsBOAfgbULIQQAgUspaAA8BGAfnsamrq4u0tZ0Q333kkdGmZS3f8JNnlBXLV0AOilRKCS7sWYlxjlKxgHw+j5Y3W/C/Tz/NQ6Hgjp9seKZ31py5ajwe4wAtz9zdAH5OpJTPAqgEgL5USkpu4eLFHloqFXniYh9t3bunauuWrStisSi5//4vYnHTEkyZOhWqokBICcuy0N7ehr2trXjt1VeRzqTl3ffc81bjgsZELF4pQ6EAqa4eI6UEicfj5dhTKoCikynx6Bop5C14dJ2XcjmouipvvGFGoSJaWfr738/7tmzdjl/88pfIZjKwnH2SpmkIhSMYW1ODhvmNGFcztjhudFXR69Wgck58Hg+XEorH5ylDJYA8kVKOckpdB0ADIBOJhOzv70OhUFILuTzPZLNcSE6SfSlvJp0O5A1DN0qGDxLS4/OUAh6PGQqHC5XxeJEQgkgoRH1+L/wBP6LRuIjH4+Uf8gSAUwB+MbhzzQSwCMA0p/QUQADgNJ/PJ/v7+wnnnFiWkJZhKCYzKADoqiZUXeW67iGcSxKPx2QoFAo7AybnuE8COAZgHyHkxGXjeFAQEQCzANQCqAIQBeAH4AXgcex052w45TMcyQHIAOgBcBbAUUJI5uOM/wcaHmf3g9UM7QAAAABJRU5ErkJggg==", "public": true}, {"link": "/api/images/system/map_marker_image_1.png", "title": "Map marker image 1", "type": "IMAGE", "subType": "IMAGE", "fileName": "map_marker_image_1.png", "publicResourceKey": "DF3fuPXua9Vi3o3d9Nz2I1LXDTwEs2Tv", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/map_marker_image_2.png", "title": "Map marker image 2", "type": "IMAGE", "subType": "IMAGE", "fileName": "map_marker_image_2.png", "publicResourceKey": "rz5SFAw2Sg5T2EyXNdwLycoDwf4QbMiZ", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/map_marker_image_3.png", "title": "Map marker image 3", "type": "IMAGE", "subType": "IMAGE", "fileName": "map_marker_image_3.png", "publicResourceKey": "KfPfTuvKCeAnmTcKcrvZQHfdU0TPArWY", "mediaType": "image/png", "data": "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", "public": true}]}