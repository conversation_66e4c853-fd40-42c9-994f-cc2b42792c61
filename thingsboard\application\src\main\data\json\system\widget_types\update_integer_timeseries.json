{"fqn": "input_widgets.update_integer_timeseries", "name": "Update integer timeseries", "deprecated": true, "image": "tb-image;/api/images/system/update_shared_integer_attribute_system_widget_image.png", "description": "Simple form to input new integer value for pre-defined timeseries key.\nThe widget is deprecated. Use \"Update Multiple Attributes\" widget. Timeseries key type and integer value type can be selected in widgets data key configuration.", "descriptor": {"type": "latest", "sizeX": 7.5, "sizeY": 3, "resources": [], "templateHtml": "<div tb-toast toastTarget=\"{{ toastTargetId }}\" style=\"width: 100%; height: 100%;\">\n<form *ngIf=\"attributeUpdateFormGroup\"\n      class=\"attribute-update-form\"\n      [formGroup]=\"attributeUpdateFormGroup\"\n      (ngSubmit)=\"updateAttribute()\">\n    <div style=\"padding: 0 8px; margin: auto 0;\">\n        <div class=\"attribute-update-form__grid\" [class.!hidden]=\"!entityDetected || !isValidParameter || !dataKeyDetected\">\n            <div class=\"grid__element\">\n                <mat-form-field class=\"mat-block\" style=\"width: 100%;\"\n                                floatLabel=\"{{settings.showLabel ? 'auto' : 'always'}}\"\n                                [hideRequiredMarker]=\"!settings.showLabel\">\n                    <mat-label>{{ settings.showLabel ? labelValue : '' }}</mat-label>\n                    <input matInput\n                           formControlName=\"currentValue\"\n                           required\n                           type=\"number\"\n                           step=\"1\"\n                           pattern=\"^-?[0-9]+$\"\n                           (focus)=\"isFocused = true\"\n                           (blur)=\"changeFocus()\"\n                           max=\"{{settings.maxValue}}\"\n                           min=\"{{settings.minValue}}\"/>\n                    <mat-error *ngIf=\"attributeUpdateFormGroup.get('currentValue').hasError('required')\">\n                        {{requiredErrorMessage}}\n                    </mat-error>\n                </mat-form-field>    \n            </div>\n\n            <div class=\"grid__element\">\n                <button mat-icon-button class=\"applyChanges\"\n                           type=\"submit\"\n                           [disabled]=\"originalValue === attributeUpdateFormGroup.get('currentValue').value || attributeUpdateFormGroup.invalid || !attributeUpdateFormGroup.dirty\"\n                           matTooltip=\"{{ 'widgets.input-widgets.update-timeseries' | translate }}\"\n                           matTooltipPosition=\"above\">\n                    <mat-icon>check</mat-icon>\n                </button>\n                <button mat-icon-button class=\"discardChanges\"\n                           type=\"button\"\n                           [disabled]=\"originalValue === attributeUpdateFormGroup.get('currentValue').value\"\n                           (click)=\"attributeUpdateFormGroup.get('currentValue').patchValue(originalValue); isFocused = false\"\n                           matTooltip=\"{{ 'widgets.input-widgets.discard-changes' | translate }}\"\n                           matTooltipPosition=\"above\">\n                    <mat-icon>close</mat-icon>\n                </button>\n            </div>\n        </div>\n\n        <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\" [class.!hidden]=\"entityDetected\">\n            {{ 'widgets.input-widgets.no-entity-selected' | translate }}\n        </div>\n        <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\" [class.!hidden]=\"!entityDetected || dataKeyDetected\">\n            {{ 'widgets.input-widgets.no-timeseries-selected' | translate }}\n        </div>\n        <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\" [class.!hidden]=\"!entityDetected || isValidParameter\">\n            {{ 'widgets.input-widgets.attribute-not-allowed' | translate }}\n        </div>\n    </div>\n</form>\n</div>", "templateCss": ".attribute-update-form {\n    overflow: hidden;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n}\n\n.attribute-update-form__grid {\n    display: flex;\n}\n.grid__element:first-child {\n    flex: 1;\n}\n.grid__element:last-child {\n    margin-top: 19px;\n    margin-left: 7px;\n}\n.grid__element {\n    display: flex;\n}\n\n.attribute-update-form .mat-button.mat-icon-button {\n    width: 32px;\n    min-width: 32px;\n    height: 32px;\n    min-height: 32px;\n    padding: 0 !important;\n    margin: 0 !important;\n    line-height: 20px;\n}\n\n.attribute-update-form .mat-icon-button mat-icon {\n    width: 20px;\n    min-width: 20px;\n    height: 20px;\n    min-height: 20px;\n    font-size: 20px;\n}\n\n.tb-toast {\n    font-size: 14px!important;\n}\n", "controllerScript": "let $scope;\nlet settings;\nlet utils;\nlet translate;\nlet http;\n\nself.onInit = function() {\n    self.ctx.ngZone.run(function() {\n       init(); \n       self.ctx.detectChanges(true);\n    });\n};\n\n\nfunction init() {\n\n    $scope = self.ctx.$scope;\n    utils = $scope.$injector.get(self.ctx.servicesMap.get('utils'));\n    translate = $scope.$injector.get(self.ctx.servicesMap.get('translate'));\n    http = $scope.$injector.get(self.ctx.servicesMap.get('http'));\n    $scope.toastTargetId = 'input-widget' + utils.guid();\n    settings = utils.deepClone(self.ctx.settings) || {};\n    settings.showLabel = utils.defaultValue(settings.showLabel, true);\n    settings.showResultMessage = utils.defaultValue(settings.showResultMessage, true);\n    $scope.settings = settings;\n    $scope.isValidParameter = true;\n    $scope.dataKeyDetected = false;\n    $scope.requiredErrorMessage = utils.customTranslation(settings.requiredErrorMessage, settings.requiredErrorMessage) || translate.instant('widgets.input-widgets.entity-timeseries-required');\n    $scope.labelValue = utils.customTranslation(settings.labelValue, settings.labelValue) || translate.instant('widgets.input-widgets.value');\n\n    $scope.attributeUpdateFormGroup = $scope.fb.group(\n        {currentValue: [undefined, [$scope.validators.required,\n                                    $scope.validators.min(settings.minValue),\n                                    $scope.validators.max(settings.maxValue),\n                                    $scope.validators.pattern(/^-?[0-9]+$/)]]}\n    );\n\n    if (self.ctx.datasources && self.ctx.datasources.length) {\n        var datasource = self.ctx.datasources[0];\n        if (datasource.type === 'entity') {\n            if (datasource.entityType && datasource.entityId) {\n                $scope.entityName = datasource.entityName;\n                if (settings.widgetTitle && settings.widgetTitle.length) {\n                    $scope.titleTemplate = utils.customTranslation(settings.widgetTitle, settings.widgetTitle);\n                } else {\n                    $scope.titleTemplate = self.ctx.widgetConfig.title;\n                }\n\n                $scope.entityDetected = true;\n            }\n        }\n        if (datasource.dataKeys.length) {\n            if (datasource.dataKeys[0].type !== \"timeseries\") {\n                $scope.isValidParameter = false;\n            } else {\n                $scope.currentKey = datasource.dataKeys[0].name;\n                $scope.dataKeyType = datasource.dataKeys[0].type;\n                $scope.dataKeyDetected = true;\n            }\n        }\n    }\n\n    self.ctx.widgetTitle = utils.createLabelFromDatasource(self.ctx.datasources[0], $scope.titleTemplate);\n\n    $scope.updateAttribute = function () {\n        $scope.isFocused = false;\n        if ($scope.entityDetected) {\n            var datasource = self.ctx.datasources[0];\n\n            let observable = saveEntityTimeseries(\n                datasource.entityType,\n                datasource.entityId,\n                [\n                    {\n                        key: $scope.currentKey,\n                        value: $scope.attributeUpdateFormGroup.get('currentValue').value\n                    }\n                ]\n            );\n            if (observable) {\n                observable.subscribe(\n                    function success() {\n                        $scope.originalValue = $scope.attributeUpdateFormGroup.get('currentValue').value;\n                        if (settings.showResultMessage) {\n                            $scope.showSuccessToast(translate.instant('widgets.input-widgets.update-successful'), 1000, 'bottom', 'left', $scope.toastTargetId);\n                        }\n                    },\n                    function fail() {\n                        if (settings.showResultMessage) {\n                            $scope.showErrorToast(translate.instant('widgets.input-widgets.update-failed'), 'bottom', 'left', $scope.toastTargetId);\n                        }\n                    }\n                );\n            }\n        }\n    };\n\n    $scope.changeFocus = function () {\n        if ($scope.attributeUpdateFormGroup.get('currentValue').value === $scope.originalValue) {\n            $scope.isFocused = false;\n        }\n    }\n\n    function saveEntityTimeseries(entityType, entityId, telemetries) {\n        var telemetriesData = {};\n        for (var a = 0; a < telemetries.length; a++) {\n            if (typeof telemetries[a].value !== 'undefined' && telemetries[a].value !== null) {\n                telemetriesData[telemetries[a].key] = telemetries[a].value;\n            }\n        }\n        if (Object.keys(telemetriesData).length) {\n            var url = '/api/plugins/telemetry/' + entityType + '/' + entityId + '/timeseries/scope';\n            return http.post(url, telemetriesData);\n        }\n        return null;\n    }\n}\n\nself.onDataUpdated = function() {\n\n    try {\n        if ($scope.dataKeyDetected) {\n            if (!$scope.isFocused) {\n                $scope.originalValue = self.ctx.data[0].data[0][1];\n                $scope.attributeUpdateFormGroup.get('currentValue').patchValue(correctValue($scope.originalValue));\n                self.ctx.detectChanges();\n            }\n        }\n    } catch (e) {\n        console.log(e);\n    }\n}\n\nfunction correctValue(value) {\n    if (typeof value !== \"number\") {\n        return 0;\n    }\n    return value;\n}\n\nself.onResize = function() {\n\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        dataKeyOptional: true\n    }\n}\n\nself.onDestroy = function() {\n\n}\n", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-update-integer-attribute-widget-settings", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{},\"title\":\"Update integer timeseries\",\"dropShadow\":true,\"enableFullscreen\":false,\"widgetStyle\":{},\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{}}"}, "resources": [{"link": "/api/images/system/update_shared_integer_attribute_system_widget_image.png", "title": "\"Update shared integer attribute\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "update_shared_integer_attribute_system_widget_image.png", "publicResourceKey": "7USFIzmXmXBqaopZXmkaEjOlHWMo3coq", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAAAAABslHx1AAAAAmJLR0QA/4ePzL8AAANBSURBVHja7d3dS1NhHAfw/Sum1IUQBZYJIdSF4VVQIOSNXZiJg5nmWi18CXG9WJgSqd1IBUWu10PY1EBcIFYqyoTCLOZWA8/0uGmbbjs73y7mS+Z2YdjYs76/i3Oe88C5+PB73jhwnkeHiOxyCh4uOQxdxO1XIXiofndEJ/uRBuGXdS41HSCqS+dEWoSTEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYSQfwl59xkA+teqDfLWl9wOESC9twAo5QsJIdHI6EstkvoQX4UP6LsNzNo/hgGDrI2oCI0B0XG7DADuSzcttaMC9JG2t0DTMEZqezuvAwY5UhbEvB5a213J9A0Awg0mnwid/cM1zBpCmPQgWrGwARlv1GDvBABbd88TESDhc3Ov7wM/H99oKp/dgEgXWlqaGgFAWYl6hBh+H9rqp4COV1EYYpAA5vWwdXi9XkWoeeRLjVkDGofxqdwDg4zqrxjTY+b8HDwOoSDaZQnAeNXFVuMEDDIGKs1temCwpsE8JeLMHg2u95qVGHBR4xKFEEIIIeT/gyxvKYgB0d5sXhAtN0uxgtS8LBJEsxrtm2sko/TbTRSI9tzY92ddj1FavYgD0axbHYBklOLnI2Uh8fIRy0kCR8pBgvdcALRn8R3bhMjdrdYlAFh62mpd/Uk81J8ciGKp/56gXW27aQ3uPVqac9AJTOUePpOfMw1gom5/VpJSsnC1zpU4H9vp7KEDlSqUQ3qg6GQI4cKzwIvMU6eTBYFiMSXOx3aG38mM9wCMBUDDAIAr+YDzB9qTBoFiie/4uwmxuGJ1+CgqAYBkQhDcwSVKX+YIANi6SvKmkw7ZwUXjzL5GAEDlsT2l8wJD5o+UrO1o4S0sFhcSOH4isP7wYFdAVEikpEABgGBWF4D27KigEK06u9/hcDgUlOUNLQ7lmkRtWksZsXiEhaqsjN3mgMij1lqEPcJsY8OPD4QQQgghhOws5E5SgxkhhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCElNSNocEJweRzb7ZF04PQ7RVnXpcay5il+KVmW7YpZ2rQAAAABJRU5ErkJggg==", "public": true}]}