{"fqn": "soil_moisture_progress_bar", "name": "Soil moisture progress bar", "deprecated": false, "image": "tb-image;/api/images/system/soil_moisture_progress_bar_system_widget_image.png", "description": "Displays soil moisture reading as a horizontal progress bar. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'soilMoisture', label: 'Soil Moisture', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Soil Moisture\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#D81838\"},{\"from\":20,\"to\":40,\"color\":\"#F36900\"},{\"from\":40,\"to\":60,\"color\":\"#4B70DD\"},{\"from\":60,\"to\":100,\"color\":\"#234CC7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":100,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#D81838\"},{\"from\":20,\"to\":40,\"color\":\"#F36900\"},{\"from\":40,\"to\":60,\"color\":\"#4B70DD\"},{\"from\":60,\"to\":100,\"color\":\"#234CC7\"}]},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Soil Moisture\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"%\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["progress", "weather", "environment", "soil", "moisture"], "resources": [{"link": "/api/images/system/soil_moisture_progress_bar_system_widget_image.png", "title": "\"Soil moisture progress bar\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "soil_moisture_progress_bar_system_widget_image.png", "publicResourceKey": "jOcrXVLphJhPWNVNY9vhLfXzNAf7Ruax", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAq1BMVEXg4ODf39/g4OAAAADg4ODf39/////g4OAjTMf19fUhISHk5OQ9PT1YWFh0dHTx8fGsrKzk6fgvLy/Hx8e6urqQkJDI0vF1j9xaeNXv7++enp7y9PutvOrV1dU+Ys6GhoaDmt+Xl5cwV8vW3fX39/efsee5ubmpqamCgoLLy8tmZmZKSkq6x+2RpeNLbdGRpuPCwsKOjo7c3NxohNl+fn51dXXU1NSxsbHb4O/m3dkHAAAABnRSTlPvIL8Ar7DvmsykAAAEw0lEQVR42uzPwREAQAQDwDiGp7f+G702wmQ7WLg91HZhDsPkeg1DdB4wD5Un4EqkFCGjCBtF2CjCRhE2irBRhI0in9062JUVhMEAvOvib6GkyIaErTHj+7/fpeKNnmPOLMks5l8wENH0i3Tip+UL+bR8IZ+WdxBNpSx0T4pMCXEs1nRezCnRFY6JZuc9hAU9wnTlhfWCBCCTRwC60rDTlVLpTeZABEW1INxtjX5ANv/NuEF8rXQFQm8yBwLzcRftgBIL+2kqfIfIsSNCDsgaNz9rWpJrtpiYWoGVNl5LLUqptK3PtY7H0RyIodLIYhBY85rzHbL1JSleDtEdZn6JEYg6YIfoKoCs47UImAIMgXg8bhokAVK5T1SMqZnpb0iz4NtWhxQU76F0QNRL31BpGG6QV24kyNQQpkGII4CotHjpjnhAXlAKQg7ZweTlDYhZJmV9Qpr31It8v86CeJIgUEEl6mP6DckZlfsSOIv10SF+n4VED4hrF4zwJEgruY9q0IRyQNYHhCxs4ANi5ruxDwjpGgXLH5CQPDoJkhFGy+uYCfgJ8aYmAsbKrx2QHBcvOJ4Gwx2isG5gpUkQ78ya+uCzmCIiPSEKpAHJkFoNfPaIpCUiuaG28/7/ENoQUpV5za7RAIvqs9H1T8goDRjtBMnn328TwPxAVqAQCxDkhBwSIPAsiIdZT9SYvA8zPW9VHqs/t36/fr+Qf+zPMQrAIBTA0O3zVxcpFErRA3j/21Wws64Z8qasYXOExhEaR2gcoXGExhEaR2gcoXGExhEaR2gcoXGExhEaR2gcoXGExhGa80gp8btrrUF1GOlvyywrr5yeEUz7kZHTGuktlyuQ5sj242O/jlkbhoEwDG/Hl/EWSaBDCLvabGXwkv//yyo7anBbEBhjuBA9WzLlRb5TPMSJaRWA6IkDoPPxaoUwEJiexnoUE2BJo1aIA5gqL2K2uDcM2X4zG+NpxwCRNGqETEByKO67uICB6xmRKo2QGT8SbaILwDDSxmY3kiKNEAsMJSEB8HVmioVpZRyZhVjPLm6HSD2ZRCsjyQJhqzKWRkd2Ii3aIUyF2S8qqcPuwxSjKFpg7ZDx/8YdEGjFc2LHpEYjJAHy2rjGWlNv+OFVKmZW82w1QjzgXrMi9Vi4fllw5iBZSIdGCC1AZH8HQqkagDuTCWvVU2CZadIyJq0QH1DUq0NQOXpKM0kk0XLPt0KILYp68bHbouZfpUvQMu+NkPpS6Pcf+G+qmj8qH/TO/iZ6iDY9RJseok0P0aaHaNNDtPmAEHa4Rn7cTjgeknEVdybk63AIrnM743CIw1XymY7jJ8IZ13CPMx0fvLXeTA/Rpod8t3fHLAzCQBiGY20/EYcuWc7lOBDELYrV///PWkvmQlxypvdC9nvIcONpyyDa+gEJs3hkbggjABIRigOlQ4gJsiBrLe8MYAyYBAsPtFI6ZOsBn/1L6IAwoV0RdmCc0iHTqAby+j7vDaIFwgSsCCchtLboN2QtQmTBJlgE4C4dAs+zIG8R0rEcgn2e/amFOAzQEsWB/nuzXyuDaMsg2jKItgyiLYNoyyDaMoi2DKItg2jLINoqCOJQRI27FXB6Gng+XOUKOD5NH0Zd3Zur525V/QaCIDJe0ogfNAAAAABJRU5ErkJggg==", "public": true}]}