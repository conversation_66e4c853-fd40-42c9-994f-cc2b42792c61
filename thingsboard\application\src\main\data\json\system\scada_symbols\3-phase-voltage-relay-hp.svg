<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="400" height="400" fill="none" version="1.1" viewBox="0 0 400 400"><tb:metadata xmlns=""><![CDATA[{
  "title": "HP 3 phase voltage relay",
  "description": "Three phase voltage relay with various states and indications.",
  "searchTags": [
    "energy",
    "power",
    "protection"
  ],
  "widgetSizeX": 2,
  "widgetSizeY": 2,
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.runningColor;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "critical",
      "stateRenderFunction": "element.attr({fill: ctx.properties.criticalColor});\nif (ctx.values.critical) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = ctx.values.critical && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'criticalClick');"
        }
      }
    },
    {
      "tag": "firstPhaseIndicator",
      "stateRenderFunction": "if (ctx.values.running) {\n    element.stroke(ctx.properties.phaseIndicatorColor);\n    if (!ctx.values.firstPhaseVoltage) {\n        element.fill('#fff');\n    } else {\n        element.fill(ctx.properties.phaseIndicatorColor);\n    }\n} else {\n    element.stroke('#000');\n    element.fill(ctx.properties.stoppedColor);\n}",
      "actions": null
    },
    {
      "tag": "firstPhaseValue",
      "stateRenderFunction": "if (ctx.values.running) {\n    element.show();\n    ctx.api.font(element, ctx.properties.currentVoltageFont, ctx.properties.currentVoltageColor);\n    ctx.api.text(element, ctx.api.formatValue(ctx.values.firstPhaseVoltage, {units: ctx.properties.units, decimals: 0, ignoreUnitSymbol: true}));\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "secondPhaseIndicator",
      "stateRenderFunction": "if (ctx.values.running) {\n    element.stroke(ctx.properties.phaseIndicatorColor);\n    if (!ctx.values.secondPhaseVoltage) {\n        element.fill('#fff');\n    } else {\n        element.fill(ctx.properties.phaseIndicatorColor);\n    }\n} else {\n    element.stroke('#000');\n    element.fill(ctx.properties.stoppedColor);\n}",
      "actions": null
    },
    {
      "tag": "secondPhaseValue",
      "stateRenderFunction": "if (ctx.values.running) {\n    element.show();\n    ctx.api.font(element, ctx.properties.currentVoltageFont, ctx.properties.currentVoltageColor);\n    ctx.api.text(element, ctx.api.formatValue(ctx.values.secondPhaseVoltage, {units: ctx.properties.units, decimals: 0, ignoreUnitSymbol: true}));\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "thirdPhaseIndicator",
      "stateRenderFunction": "if (ctx.values.running) {\n    element.stroke(ctx.properties.phaseIndicatorColor);\n    if (!ctx.values.thirdPhaseVoltage) {\n        element.fill('#fff');\n    } else {\n        element.fill(ctx.properties.phaseIndicatorColor);\n    }\n} else {\n    element.stroke('#000');\n    element.fill(ctx.properties.stoppedColor);\n}",
      "actions": null
    },
    {
      "tag": "thirdPhaseValue",
      "stateRenderFunction": "if (ctx.values.running) {\n    element.show();\n    ctx.api.font(element, ctx.properties.currentVoltageFont, ctx.properties.currentVoltageColor);\n    ctx.api.text(element, ctx.api.formatValue(ctx.values.thirdPhaseVoltage, {units: ctx.properties.units, decimals: 0, ignoreUnitSymbol: true}));\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "units",
      "stateRenderFunction": "if (ctx.properties.showUnits) {\n    element.show();\n    ctx.api.font(element, ctx.properties.unitsFont, ctx.properties.unitsColor);\n    ctx.api.text(element, ctx.api.unitSymbol(ctx.properties.units));\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "value-box",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.valueBoxBackground;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "warning",
      "stateRenderFunction": "element.attr({fill: ctx.properties.warningColor});\nvar warning = ctx.values.warning && !(ctx.values.warning && ctx.values.critical)\nif (warning) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = warning && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'warningClick');"
        }
      }
    }
  ],
  "behavior": [
    {
      "id": "running",
      "name": "{i18n:scada.symbol.running}",
      "hint": "{i18n:scada.symbol.running-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.running}",
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "running"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "firstPhaseVoltage",
      "name": "{i18n:scada.symbol.first-phase-voltage}",
      "hint": "{i18n:scada.symbol.phase-voltage-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_TIME_SERIES",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "firstPhaseVoltage"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "secondPhaseVoltage",
      "name": "{i18n:scada.symbol.second-phase-voltage}",
      "hint": "{i18n:scada.symbol.phase-voltage-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_TIME_SERIES",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "secondPhaseVoltage"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "thirdPhaseVoltage",
      "name": "{i18n:scada.symbol.third-phase-voltage}",
      "hint": "{i18n:scada.symbol.phase-voltage-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_TIME_SERIES",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "thirdPhaseVoltage"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warning",
      "name": "{i18n:scada.symbol.warning}",
      "hint": "{i18n:scada.symbol.warning-state-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.warning}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "MAJOR",
            "MINOR",
            "WARNING",
            "INDETERMINATE"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warningClick",
      "name": "{i18n:scada.symbol.warning-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "critical",
      "name": "{i18n:scada.symbol.critical}",
      "hint": "{i18n:scada.symbol.critical-state-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.critical}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "CRITICAL"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "criticalClick",
      "name": "{i18n:scada.symbol.critical-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "criticalAnimation",
      "name": "{i18n:scada.symbol.warning-critical-state-animation}",
      "hint": "{i18n:scada.symbol.warning-critical-state-animation-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.animation}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    }
  ],
  "properties": [
    {
      "id": "runningColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#FFFFFF",
      "subLabel": "{i18n:scada.symbol.running}",
      "divider": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "stoppedColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#666666",
      "subLabel": "{i18n:scada.symbol.stopped}",
      "disabled": false,
      "visible": true
    },
    {
      "id": "valueBoxBackground",
      "name": "{i18n:scada.symbol.value-box-background}",
      "type": "color",
      "default": "#DEDEDE",
      "disabled": false,
      "visible": true
    },
    {
      "id": "phaseIndicatorColor",
      "name": "{i18n:scada.symbol.phase-indicator-color}",
      "type": "color",
      "default": "#198038",
      "disabled": false,
      "visible": true
    },
    {
      "id": "currentVoltageFont",
      "name": "{i18n:scada.symbol.current-voltage-color}",
      "type": "font",
      "default": {
        "size": 32,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "400",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "currentVoltageColor",
      "name": "{i18n:scada.symbol.current-voltage-color}",
      "type": "color",
      "default": "#002878",
      "disabled": false,
      "visible": true
    },
    {
      "id": "showUnits",
      "name": "{i18n:scada.symbol.units}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "units",
      "name": "{i18n:scada.symbol.units}",
      "type": "units",
      "default": "V",
      "supportsUnitConversion": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "unitsFont",
      "name": "{i18n:scada.symbol.units}",
      "type": "font",
      "default": {
        "size": 24,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "500",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "unitsColor",
      "name": "{i18n:scada.symbol.units}",
      "type": "color",
      "default": "#000",
      "disabled": false,
      "visible": true
    },
    {
      "id": "warningColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#FAA405",
      "subLabel": "{i18n:scada.symbol.warning}",
      "divider": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "criticalColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#D12730",
      "subLabel": "{i18n:scada.symbol.critical}",
      "disabled": false,
      "visible": true
    }
  ]
}]]></tb:metadata>
<rect width="400" height="400" fill="#fff" tb:tag="background"/><rect x="1" y="1" width="398" height="398" stroke="#000" stroke-opacity=".87" stroke-width="2"/><circle cx="200" cy="50" r="19" stroke="#000" stroke-opacity=".87" stroke-width="2"/><circle cx="112" cy="50" r="19" stroke="#000" stroke-opacity=".87" stroke-width="2"/><circle cx="288" cy="50" r="19" stroke="#000" stroke-opacity=".87" stroke-width="2"/><circle cx="200" cy="350" r="19" stroke="#000" stroke-opacity=".87" stroke-width="2"/><circle cx="112" cy="350" r="19" stroke="#000" stroke-opacity=".87" stroke-width="2"/><circle cx="288" cy="350" r="19" stroke="#000" stroke-opacity=".87" stroke-width="2"/><rect x="150" y="180" width="100" height="80" rx="2" fill="#DEDEDE" tb:tag="value-box"/><rect x="151" y="181" width="98" height="78" rx="1" stroke="#000" stroke-opacity=".87" stroke-width="2"/><rect x="270" y="180" width="100" height="80" rx="2" fill="#DEDEDE" tb:tag="value-box"/><rect x="271" y="181" width="98" height="78" rx="1" stroke="#000" stroke-opacity=".87" stroke-width="2"/><rect x="30" y="180" width="100" height="80" rx="2" fill="#DEDEDE" tb:tag="value-box"/><rect x="31" y="181" width="98" height="78" rx="1" stroke="#000" stroke-opacity=".87" stroke-width="2"/><text x="201.6431" y="233.64844" fill="#002878" font-family="Roboto, sans-serif" font-size="32px" font-weight="400" text-anchor="middle" tb:tag="secondPhaseValue" xml:space="preserve"><tspan dominant-baseline="start">220</tspan></text><text x="321.46201" y="233.29332" fill="#002878" font-family="Roboto, sans-serif" font-size="32px" font-weight="400" text-anchor="middle" tb:tag="thirdPhaseValue" xml:space="preserve"><tspan dominant-baseline="start">220</tspan></text><text x="81.372673" y="233.21404" fill="#002878" font-family="Roboto, sans-serif" font-size="32px" font-weight="400" text-anchor="middle" tb:tag="firstPhaseValue" xml:space="preserve"><tspan dominant-baseline="start">220</tspan></text><circle cx="80" cy="146" r="10" fill="#198038" stroke="#198038" stroke-width=".5" tb:tag="firstPhaseIndicator"/><path d="m79.446 125.67v2.332h-8.5664v-2.332h8.5664zm-7.7461-14.73v17.062h-2.9414v-17.062h2.9414zm17.635-0.059v17.121h-2.8242v-13.77l-4.1836 1.418v-2.332l6.668-2.437h0.3398z" fill="#000" fill-opacity=".87"/><circle cx="199" cy="146" r="10" fill="#198038" stroke="#198038" stroke-width=".5" tb:tag="secondPhaseIndicator"/><path d="m198.45 125.67v2.332h-8.566v-2.332h8.566zm-7.746-14.73v17.062h-2.941v-17.062h2.941zm21.362 14.812v2.25h-11.438v-1.934l5.555-6.058c0.609-0.688 1.09-1.281 1.441-1.781 0.352-0.5 0.598-0.95 0.739-1.348 0.148-0.406 0.222-0.801 0.222-1.184 0-0.539-0.101-1.011-0.304-1.418-0.196-0.414-0.485-0.738-0.868-0.972-0.382-0.243-0.847-0.364-1.394-0.364-0.633 0-1.164 0.137-1.594 0.411-0.43 0.273-0.754 0.652-0.972 1.136-0.219 0.477-0.329 1.024-0.329 1.641h-2.824c0-0.992 0.227-1.899 0.68-2.719 0.453-0.828 1.109-1.484 1.969-1.969 0.859-0.492 1.894-0.738 3.105-0.738 1.141 0 2.109 0.192 2.906 0.574 0.797 0.383 1.403 0.926 1.817 1.629 0.422 0.703 0.633 1.535 0.633 2.496 0 0.532-0.086 1.059-0.258 1.582-0.172 0.524-0.418 1.047-0.739 1.571-0.312 0.515-0.683 1.035-1.113 1.558-0.43 0.516-0.902 1.039-1.418 1.571l-3.691 4.066h7.875z" fill="#000" fill-opacity=".87"/><circle cx="320" cy="146" r="10" fill="#198038" stroke="#198038" stroke-width=".5" tb:tag="thirdPhaseIndicator"/><path d="m319.45 125.67v2.332h-8.566v-2.332h8.566zm-7.746-14.73v17.062h-2.941v-17.062h2.941zm13.241 7.253h1.687c0.656 0 1.199-0.113 1.629-0.339 0.438-0.227 0.762-0.54 0.973-0.938s0.316-0.855 0.316-1.371c0-0.539-0.097-1-0.293-1.383-0.187-0.39-0.476-0.691-0.867-0.902-0.383-0.211-0.871-0.317-1.465-0.317-0.5 0-0.953 0.102-1.359 0.305-0.399 0.195-0.715 0.477-0.949 0.844-0.235 0.359-0.352 0.789-0.352 1.289h-2.836c0-0.906 0.238-1.711 0.715-2.414 0.476-0.703 1.125-1.254 1.945-1.653 0.828-0.406 1.758-0.609 2.789-0.609 1.102 0 2.063 0.184 2.883 0.551 0.828 0.359 1.473 0.898 1.934 1.617s0.691 1.609 0.691 2.672c0 0.484-0.113 0.977-0.34 1.477-0.226 0.5-0.562 0.957-1.008 1.371-0.445 0.406-1 0.738-1.664 0.996-0.664 0.25-1.433 0.375-2.308 0.375h-2.121v-1.571zm0 2.204v-1.547h2.121c1 0 1.851 0.117 2.554 0.351 0.711 0.235 1.29 0.559 1.735 0.973 0.445 0.406 0.769 0.871 0.973 1.394 0.21 0.524 0.316 1.079 0.316 1.664 0 0.797-0.145 1.508-0.434 2.133-0.281 0.617-0.683 1.141-1.207 1.571-0.523 0.429-1.136 0.754-1.84 0.972-0.695 0.219-1.453 0.328-2.273 0.328-0.734 0-1.437-0.101-2.109-0.304s-1.274-0.504-1.805-0.903c-0.531-0.406-0.953-0.91-1.266-1.511-0.304-0.61-0.457-1.313-0.457-2.11h2.825c0 0.508 0.117 0.957 0.351 1.348 0.242 0.383 0.578 0.684 1.008 0.902 0.437 0.219 0.937 0.328 1.5 0.328 0.594 0 1.105-0.105 1.535-0.316s0.758-0.523 0.984-0.938c0.235-0.414 0.352-0.914 0.352-1.5 0-0.664-0.129-1.203-0.387-1.617s-0.625-0.718-1.101-0.914c-0.477-0.203-1.039-0.304-1.688-0.304h-1.687z" fill="#000" fill-opacity=".87"/><text x="199.75247" y="299.65625" fill="#000000" font-family="Roboto, sans-serif" font-size="28px" font-weight="400" text-anchor="middle" tb:tag="units" xml:space="preserve"><tspan dominant-baseline="start">v</tspan></text><path d="m134.53 0s-134.53 0-134.53 67v328.36c0 2.6512 3.5818 4.6404 8 4.6404h384c4.418 0 8-1.9892 8-4.6404v-328.36c0-67-132.14-67-132.14-67h-67.858zm134.14 81.2c-2.5774 0-4.6666 1.2536-4.6666 2.8v300.4c0 1.5464 2.0894 2.8 4.6666 2.8h29.332c2.5774 0 4.6666-1.2536 4.6666-2.8v-300.4c0-1.5464-2.0894-2.8-4.6666-2.8z" fill="#000" fill-opacity="0" tb:tag="clickArea"/><g fill="#d12730" style="display: none;" tb:tag="critical">
  <rect width="84" height="84" rx="4" fill="#fff" style=""/>
  <rect width="84" height="84" rx="4" style=""/>
  <rect x="2" y="2" width="80" height="80" rx="2" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 27.562-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g><g fill="#faa405" style="display: none;" tb:tag="warning">
  <path d="m35.955 2.2112c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" fill="#fff" style=""/>
  <path d="m35.955 2.2112c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" style=""/>
  <path d="m37.72 3.1061c0.7371-1.4741 2.8407-1.4741 3.5778-1e-5l35.528 71.056c0.6649 1.3298-0.3021 2.8944-1.7888 2.8944h-71.056c-1.4868 0-2.4538-1.5646-1.7889-2.8944z" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m42.092 32.618-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g>
</svg>