<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="800" height="200" fill="none" version="1.1" viewBox="0 0 800 200"><tb:metadata xmlns=""><![CDATA[{
  "title": "Extra long horizontal pipe",
  "description": "Extra long horizontal pipe with fluid and leak visualizations.",
  "searchTags": [
    "pipe",
    "horizontal pipe",
    "long pipe"
  ],
  "widgetSizeX": 4,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "fluid",
      "stateRenderFunction": "var fluid = ctx.values.fluid && !ctx.values.leak;\nvar flow = ctx.values.flow;\nvar flowDirection = ctx.values.flowDirection;\n\nvar elementFluid = element.remember('fluid');\nvar elementFlow = null;\nvar elementFlowDirection = null;\n\nif (fluid !== elementFluid) {\n    element.remember('fluid', fluid);\n    elementFlow = null;\n    elementFlowDirection = null;\n} else {\n    elementFlow = element.remember('flow');\n    elementFlowDirection = element.remember('flowDirection');\n}\n\nvar liquidPattern = element.reference('fill').first();\n\nvar fluidAnimation = ctx.api.cssAnimation(liquidPattern);\n\n\nif (fluid) {\n    element.show();\n    if (flow !== elementFlow) {\n        element.remember('flow', flow);\n        if (flow) {\n            if (elementFlowDirection !== flowDirection || !fluidAnimation) {\n                element.remember('flowDirection', flowDirection);\n                fluidAnimation = animateFlow(liquidPattern, flowDirection);\n            } else {\n                fluidAnimation.play();\n            }\n        } else {\n            if (fluidAnimation) {\n                fluidAnimation.pause();\n            }\n        }\n    } else if (flow && elementFlowDirection !== flowDirection) {\n        element.remember('flowDirection', flowDirection);\n        fluidAnimation = animateFlow(liquidPattern, flowDirection);\n    }\n    if (flow) {\n        if (fluidAnimation) {\n            fluidAnimation.speed(ctx.values.flowAnimationSpeed);\n        }\n    }\n} else {\n    if (fluidAnimation) {\n        fluidAnimation.pause();\n    }\n    element.hide();\n}\n\nfunction animateFlow(liquidPattern, forwardElseReverse) {\n    ctx.api.resetCssAnimation(liquidPattern);\n    var deltaX = forwardElseReverse ? 172 : -172;\n    return ctx.api.cssAnimate(liquidPattern, 1000).relative(deltaX, 0).loop();\n}",
      "actions": null
    },
    {
      "tag": "fluid-background",
      "stateRenderFunction": "var fluid = ctx.values.fluid && !ctx.values.leak;\nif (fluid) {\n    var color = ctx.properties.fluidColor;\n    element.attr({fill: color});\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "leak",
      "stateRenderFunction": "var leak = ctx.values.leak;\nif (leak) {\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "pipe-background",
      "stateRenderFunction": "var color = ctx.properties.pipeColor;\nelement.attr({fill: color});",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "fluid",
      "name": "{i18n:scada.symbol.fluid-presence}",
      "hint": "{i18n:scada.symbol.fluid-presence-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.present}",
      "falseLabel": "{i18n:scada.symbol.absent}",
      "stateLabel": "{i18n:scada.symbol.fluid-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "flow",
      "name": "{i18n:scada.symbol.flow-presence}",
      "hint": "{i18n:scada.symbol.flow-presence-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.present}",
      "falseLabel": "{i18n:scada.symbol.absent}",
      "stateLabel": "{i18n:scada.symbol.flow-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "flowDirection",
      "name": "{i18n:scada.symbol.flow-direction}",
      "hint": "{i18n:scada.symbol.flow-direction-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.forward}",
      "falseLabel": "{i18n:scada.symbol.reverse}",
      "stateLabel": "{i18n:scada.symbol.forward}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": true,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "flowAnimationSpeed",
      "name": "{i18n:scada.symbol.flow-animation-speed}",
      "hint": "{i18n:scada.symbol.flow-animation-speed-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": 1,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "leak",
      "name": "{i18n:scada.symbol.leak}",
      "hint": "{i18n:scada.symbol.leak-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.leak-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    }
  ],
  "properties": [
    {
      "id": "fluidColor",
      "name": "{i18n:scada.symbol.fluid-color}",
      "type": "color",
      "default": "#1EC1F480",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "pipeColor",
      "name": "{i18n:scada.symbol.pipe-color}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata>
<path d="m14 64h772v72h-772v-72z" fill="#fff" tb:tag="pipe-background"/><path d="m14 64h772v72h-772v-72z" fill="url(#paint0_linear_2910_72033)"/><path d="m15.5 65.5h769v69h-769v-69z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><rect x="1.5" y="51.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/><rect x="787.5" y="51.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/><rect x="14" y="64" width="772" height="72" fill="#1ec1f4" stroke-width="0" style="display: none;" tb:tag="fluid-background"/><rect x="14" y="64" width="772" height="72" fill="url(#liquid)" stroke-width="0" style="display: none;" tb:tag="fluid"/><g style="display: none;" tb:tag="leak">
  <path d="m470.53 93.395c0-0.3006 7.47-0.7515 7.47-0.7515l-7.975 0.1503s-6.461-1.9539-7.874-2.48c-1.212-0.526-11.319 3.3214-12.328 3.4717h-0.202l0.101-0.0751 3.533-3.0812c-0.505 0.3006-8.694 6.1772-9.905 6.6281-1.312 0.3757-11.29 1.1272-12.098 1.503-0.202 0.0752-2.782 0.0752-3.085 0.0752-1.11-0.1503-2.624-0.9019-3.331-1.1273-0.909-0.3006-2.827-1.1273-2.423-1.503 0.404-0.3758 2.928-5.4861 3.836-5.8618 0.808-0.3758 10.455-2.1794 10.858-2.48 0.404-0.3006 1.313-2.48 1.414-2.7054l-1.515 2.4048s-0.807 0.3757-1.716 0.4509c-0.908 0.0752-9.243 1.4279-9.243 1.4279l0.606-2.7055c-0.101 0.1503-1.009 1.8788-1.817 3.1564-0.303 0.526-0.606 0.9769-0.808 1.2775-0.807 1.0522-2.322 3.1564-3.23 3.5321-0.808 0.3758-2.625 0.1503-4.038-0.4509s-5.034-1.7756-5.942-2.0011c-0.909-0.3006-2.538-2.057-3.043-2.7334v-0.0751c-0.504-0.6764-0.605-1.954-0.303-2.5552 0.404-0.6763 0.303-1.6533 0.606-2.7054s5.653-4.96 5.653-4.96-0.101 0-0.201 0.0752c-0.606 0.3006-2.928 1.503-4.039 2.1794-1.211 0.7515-2.119 1.1272-2.523 1.1272-0.101 0-0.202-0.0751-0.404-0.1503-0.606-0.526-1.817-2.0291-2.726-3.3066-1.11-1.5782-4.442-4.0582-4.946-4.6594-0.505-0.6012 0.706-3.6824 0.908-3.983-0.202 0.3006-1.918 3.0812-2.322 3.4569l-0.101 0.0752c-0.504 0.1503-1.817-0.3758-3.028-0.9018-1.413-0.6012-5.451-0.6764-6.36-0.977s-6.966-2.7054-7.369-2.8557c0.303 0.1503 2.523 1.4279 4.441 2.5551 0.707 0.3758 1.313 0.7515 1.717 1.0521 1.918 1.2025 0.908 0.6012 2.322 0.8267 1.312 0.2254 4.542 1.0521 6.359 0.977 1.717-0.0752 2.726 0.526 3.635 1.1272 0.908 0.6012 1.615 2.5552 4.038 4.3588 2.422 1.8036 2.725 4.8097 3.23 5.4109 0.101 0.0751 0.101 0.1503 0.101 0.2254 0.101 0.6764-1.312 1.9539-1.918 3.2315-0.707 1.3527-3.029 1.2024-3.836 1.2024-0.909 0.0752-1.313 0.0752-1.716 0.4509-0.404 0.3758-2.726-0.1503-3.534-0.1503-0.908 0.0752-4.34 1.3528-5.249 0.7515-0.909-0.6012 1.413-4.1333 1.312-4.8096v-0.3006c-0.101-0.3758-0.101-0.5261-0.605-1.3527-0.505-0.9019 2.624-2.7055 2.826-2.7806-0.202 0.0751-2.12 0.8266-2.524 1.1272-0.403 0.3006-0.706 0.9018-0.807 0.977 0 0-1.413-0.6012-2.322-0.8267-0.909-0.3006-4.139-1.0521-5.048-1.3527-0.908-0.3006-2.523-1.3728-3.028-2.0492-0.505-0.6012-8.189-3.6274-8.694-4.3037-0.504-0.6012-3.723-0.5593-3.723-0.5593s1.366 1.1071 2.228 1.1071c0.569 0 0.688-0.0218 0.991 0.354 0.302 0.3757 2.102 0.9387 3.564 1.6077 1.625 0.7444 3.878 1.3075 4.322 1.8694 1.009 1.2775 2.12 1.7486 3.028 2.3498 0.909 0.6012 1.514 1.2024 2.827 1.503 1.312 0.2254 3.129 0.4509 4.543 0.7515 1.413 0.2255 0.504 0.977 0.605 1.6533 0.101 0.6764 0.505 0.3006 0.505 0.977 0.101 0.6763-0.303 0.977-0.707 1.3527-0.101 0.0752-0.101 0.1503-0.202 0.2255-0.302 0.6012-0.605 1.8787-1.312 2.4799-0.808 0.6764-7.167 3.7576-8.48 4.1334-0.101 0-0.202 0.0751-0.303 0.0751-0.504 0.0752-1.312 0.1503-2.221 0.0752-1.918-0.0752-4.239-0.3006-5.047-0.6764-1.413-0.6012-4.24-1.7285-4.745-2.7054-0.504-0.977-4.744-2.7055-5.653-3.3067s-10.87-5.6109-11.678-5.2351c-0.807 0.3758-4.442-0.3758-8.076-0.5261-1.817-0.0751-3.23-0.4509-4.744-1.1272-1.515-0.6012-3.13-1.4279-5.452-2.2546-4.442-1.6533-9.287-6.6132-9.59-6.989 0.202 0.3006 1.615 2.5551 2.524 3.1563 0.908 0.6013 2.423 1.8037 2.423 1.8037l-2.726-0.5261s2.322 0.8267 3.634 1.1273c1.413 0.2254 1.514 1.2024 4.24 2.4048 2.827 1.1273 6.562 2.9309 7.47 3.2315 0.909 0.3006 1.817 0.5261 2.726 0.8267s7.067-0.0752 8.48 0.1503c1.413 0.2254 3.23 0.8266 3.23 1.1272s0 0.3006-1.211 1.0522c-1.212 0.7515-7.47 0.7515-7.874 0.8266-0.404 0-3.13-0.4509-4.947-0.0751-1.716 0.4509-4.442-0.3758-4.946-0.3758-0.404 0 2.725 0.8267 3.23 0.8267 0.404 0 3.533 0.1503 4.038 0.0751 0.404 0-0.404 0.3758-0.808 0.3758-0.403 0-0.807 1.0521-1.211 1.4278-0.404 0.3758-1.514 2.1043-2.322 3.1564-0.807 1.0521 0.404 3.3066 0.404 3.3066s-0.202-1.6533 0.202-2.3296c0.404-0.6764 1.11-1.7285 1.615-1.7285 0.404 0 0.303-1.3527 1.918-2.4049 1.615-1.1272 3.029-1.2024 4.745-1.2775 1.817-0.0752 13.764 3.6824 16.159 4.4339 1.708 0.5359 3.292 2.0036 4.604 2.2291 1.413 0.2254 5.956 1.2775 9.086 2.1042 3.23 0.8267 8.479 4.4341 8.479 4.4341h-0.201c-0.404 0-1.313 0-2.019-0.226-0.808-0.225-3.837-1.653-4.543-1.9536l-0.101-0.0751 0.101 0.0751c0.202 0.0756 0.505 0.3006 0.908 0.5256 0.303 0.226 1.212 0.827 1.817 1.203 0.303 0.15 0.505 0.301 0.505 0.301-0.101 0-6.106-1.122-8.563-0.561s-10.073 6.873-10.073 6.873 7.971-5.035 10.817-5.536c2.494-0.439 5.148-0.207 7.648 0.089 2.537 0.302 5.037 0.788 5.219 0.788 0.403 0 1.413 0.902 2.322 1.202 0.908 0.301 4.34 2.706 4.845 3.382 0.505 0.601-1.716 5.035-1.312 4.735 0.404-0.376 1.817-4.059 2.221-4.134 0.404 0 0.504 0.301 0.908 0.601 0.505 0.301 2.524 3.157 3.029 3.157 0.404 0-0.808 0.676-1.212 1.052-0.403 0.376-0.706 1.728-0.605 2.33 0.101 0.601-1.01 1.277-1.212 1.352 0.202-0.075 1.716-0.751 1.615-1.052 0-0.3 1.01-2.705 1.919-3.081 0.807-0.376 1.817 0.225 2.725 0.451 0.909 0.301 2.12 2.33 2.625 2.63 0.505 0.301 2.423 1.321 2.927 1.998 0.505 0.601 3.13 1.459 3.635 2.136 0.504 0.601-1.817 3.456-2.12 4.734-0.303 1.353 0.908 4.584 0.908 4.584s-0.303-2.63-0.404-3.983c-0.1-1.277 1.919-3.081 2.322-3.156 0.404 0 0.909 0.3 1.414 0.902 0.504 0.601 1.211 2.555 1.716 3.231 0.505 0.601 0.606 1.654 1.615 2.856 0.606 0.827 0.909 1.202 1.01 1.428-0.202-0.451-0.606-1.503-0.606-1.804 0-0.3-1.615-2.254-1.615-2.856-0.101-0.676-2.221-4.509-2.524-6.463-0.202-1.953 2.322-7.74 2.12-8.717-0.101-0.977-1.514-1.578-1.615-2.856-0.101-1.353 1.817-3.757 4.845-4.283 2.625-0.451 7.471 0.751 8.783 1.052 0.101 0 0.101 0 0.202 0.075h0.101c-0.202-0.075-3.735-1.428-5.553-1.954-1.817-0.526-4.34-1.052-4.542-1.353 0-0.3 1.615-1.728 2.423-2.104 0.807-0.3758 2.523-1.1273 3.836-1.2024 1.312-0.0752 1.817 0.2254 2.725 0.8266 0.909 0.6012 3.186 0.6012 5.609 1.8038 2.221 1.127 5.35 2.555 5.653 2.63-0.303-0.225-5.249-3.307-4.845-3.682 0.403-0.376 6.949-0.5263 9.977-0.7518 3.13-0.1503 16.496-3.0812 17.808-3.1563 1.313-0.0752 2.847 0 6.263 0.2545 3.412 0 7.854-1.4865 7.854-1.4865s-3.836-1.2776-3.937-1.5782zm-68.711 17.389c-0.404 0.075-1.514 0.301-1.918 0-0.505-0.376-2.12-0.751-2.625-1.954-0.403-0.977-2.927-2.405-3.836-2.931-0.202-0.15-0.302-0.15-0.302-0.15 1.009-0.376 6.258 0.752 6.763 1.127 0.505 0.376 0 1.578 0.505 1.954s1.514 1.954 1.514 1.954h-0.101zm1.515-0.902c-0.101-0.15-0.202-0.3-0.303-0.375-0.505-0.602-1.414-0.602-1.515-1.203-0.101-0.676 0.808-1.052 1.212-1.428 0.404-0.375 3.634 0.451 3.634 0.451s6.461 2.555 6.663 3.908c0 0.376 0 0.752-0.101 1.052-0.202 0.752-0.606 1.278-0.505 1.954 0.101 0.977 0.202 2.33-1.11 2.029-1.313-0.225-2.019-2.179-4.442-3.682-1.918-1.278-2.928-2.104-3.533-2.706zm10.498-5.786c-0.302 0.375-0.706 0.902-1.009 1.428-0.707 1.352-0.606 2.329-1.514 2.404-0.909 0.076-1.818-0.225-3.231-0.826-0.706-0.301-1.413-0.526-2.12-0.752-0.706-0.225-1.11-0.375-1.11-0.375s1.615-1.128 2.624-0.827c0.909 0.3 1.414 0.225 1.818-0.075 0.403-0.376 5.148-1.954 5.552-2.029-0.101 0.075-0.505 0.451-1.01 1.052zm3.231-4.058c-0.404 0.375-1.615 1.728-1.615 1.728h-0.101c-0.404 0.075-2.726 0.526-4.24 1.202-0.909 0.376-3.231 1.278-5.654 1.879-0.201 0.075-0.504 0.151-0.706 0.226-1.918 0.451-3.735 0.751-4.846 0.526-2.725-0.526-5.047-1.654-6.864-1.879-1.818-0.225-2.221 0.15-3.635-0.451-1.413-0.601-1.918-1.202-3.331-2.104s-2.019-1.8789-2.928-2.4801c-0.605-0.3758 0.346-0.451 0.346-0.977 0.569-0.4761 1.139-0.4761 1.139-0.4761s0.837-0.3506 2.15-0.8015c1.312-0.3757 5.425-1.962 9.237-1.4128 3.345 0.4822 5.382-0.0192 8.732 0.4359 3.611 0.4906 4.744-0.4219 8.076 1.006 3.23 1.4279 7.571 0.6473 7.571 0.6473s-2.927 2.5551-3.331 2.9313z" fill="#5C5A5A" style=""/>
  <path d="m379.31 95.377c-0.505 0.3006-1.11 0.6012-1.716 0.6764-1.11 0.1503-2.221 0-3.23-0.1503-4.139-0.6764-8.379-2.0291-11.004-4.5091-0.505-0.4509-1.009-0.9769-1.716-1.3527-1.615-0.9018-4.031-3.9565-6.05-3.8813-6.832-3.3215-14.318-2.0714-17.649-3.875 1.514 0.6012 9 1.0319 10.817 1.1071 3.533 0.1503 5.694 1.6607 7.741 2.3921 1.695 0.6056 4.131 2.4535 5.04 3.0547s5.148 2.3297 5.653 3.3066c0.505 0.977 3.332 2.1043 4.745 2.7055 1.413 0.4509 5.754 0.8266 7.369 0.526z" fill="#8B8B8B" style=""/>
  <path d="m406.87 86.118c-0.202 1.3528-0.908 2.7055-2.019 3.7576-0.202 0.2255-0.505 0.4509-0.807 0.5261-0.909 0.3757-2.019 0.1503-3.029 0.2254-0.908 0.0752-1.716 0.3758-2.625 0.4509-0.504 0.0752-1.11 0.0752-1.615 0-1.11-0.0751-1.896-0.4889-3.245 0-0.843 0.6934-2.067 0.6769-3.206 0.6769-0.419 0-0.845 0.1423-0.845-0.3964 0-0.4081 0.216-0.3556 0.276-0.7107 0.202-0.6764 0.937-2.0915 1.139-2.7679 0.101-0.3757 0.531-0.7097 0.632-1.0855 0-0.1503 0-0.5261 0.202-0.6764v0.3006c0.101 0.6764-1.174 3.5693-0.265 4.1705 0.908 0.6012 3.394-0.0371 4.202-0.1123 0.908-0.0751 3.129 0.4509 3.533 0.1503 0.404-0.3757 0.808-0.3757 1.716-0.4509 0.909-0.0751 3.13 0.1503 3.836-1.2024 0.707-1.2024 2.019-2.48 1.918-3.2315 0.202 0 0.202 0.2254 0.202 0.3757z" fill="#8B8B8B" style=""/>
  <path d="m413.73 79.07c-0.808 0.7515-1.918 1.3527-2.827 1.9539-1.211 0.6764-2.322 1.4279-3.533 2.1043-0.101-0.3006-0.404-0.6012-0.505-0.977 0.101 0.1503 0.303 0.1503 0.404 0.1503 0.404 0 1.312-0.3757 2.524-1.1273 1.009-0.6012 3.331-1.7284 3.937-2.1042z" fill="#8B8B8B" style=""/>
  <path d="m397.58 73.734c-0.1 0.0752-0.1 0.1503-0.201 0.2255-0.101 0.0751-0.202 0.0751-0.303 0.0751-0.404 0.0752-0.808 0.0752-1.313 0-1.514-0.1503-2.826-0.8266-4.34-1.1272-0.707-0.1503-1.515-0.1503-2.221-0.3006-0.606-0.0752-1.212-0.2255-1.818-0.451-0.706-0.2254-1.514-0.3757-2.22-0.6012-2.019-1.2024-4.442-2.5551-4.442-2.5551s6.562 2.5551 7.47 2.8557c0.909 0.3006 4.947 0.3758 6.36 0.977 1.312 0.6012 2.524 1.1273 3.028 0.9018z" fill="#8B8B8B" style=""/>
  <path d="m390.31 84.707c-1.414-0.9018-3.231-1.2024-5.048-1.503-0.707-0.0752-1.413-0.2255-1.918-0.5261-0.404-0.2254-0.707-0.526-1.01-0.8266-0.706-0.6764-0.987-0.5494-1.693-1.2258-0.303-0.3006-0.57-0.5536-1.139-1.1071-0.943-0.3589-1.139 0-2.277-0.5536-1.139-1.1072-3.372-1.5178-4.886-2.4948-0.303-0.3757-1.175-0.6763-1.377-0.8266-0.505-0.3006-3.416-0.5536-3.416-0.5536s4.793-0.198 5.298 0.4784c0.504 0.6012 5.685 2.742 7.169 3.2019 0.764 0.2368 2.12 2.8557 3.028 3.1563 0.909 0.3006 4.139 1.0521 5.048 1.3527 0.908 0.3006 2.322 0.8267 2.322 0.8267 0.1 0.1503 0 0.3757-0.101 0.6012z" fill="#8B8B8B" style=""/>
  <path d="m389.4 88.69c-0.101 0.3757-0.202 0.8266-0.303 0.9769 0 0.6012-0.101 1.2776-0.606 1.7285-0.202 0.2254-0.504 0.3757-0.807 0.526-0.909 0.5261-1.817 0.977-2.827 1.5031-0.606 0.3006-1.211 0.6763-1.918 0.9018-0.505 0.1503-1.009 0.3006-1.514 0.526-0.505 0.2255-1.01 0.4509-1.514 0.6764-0.909 0.3006-1.818 0.5261-2.827 0.5261h-0.202c-0.202-0.0752-0.202-0.2255-0.101-0.3758 0.101-0.0751 0.202-0.1503 0.303-0.1503 0.909 0 1.716 0 2.221-0.0752 0.101 0 0.202-0.0751 0.303-0.0751 1.312-0.3758 7.672-3.457 8.48-4.1333 0.706-0.6764 1.009-1.9539 1.312-2.5551z" fill="#8B8B8B" style=""/>
  <path d="m405.56 104.92c-1.01 0.451-2.12 0.827-3.231 1.127-0.807 0.151-1.615 0.301-2.423 0.151-0.605-0.076-1.11-0.376-1.615-0.602-0.908-0.375-1.918-0.526-2.927-0.751-1.01-0.226-2.019-0.376-3.13-0.526-0.404 0-0.807-0.075-1.211-0.15-0.505-0.076-0.909-0.301-1.414-0.451-0.908-0.376-1.716-0.752-2.523-1.203-0.707-0.451-1.414-0.902-2.019-1.428-1.01-0.901-2.928-2.0287-3.029-3.3063 0-0.3006 0.101-0.6763 0.303-0.9018 0 0.5261 0.101 1.3527 0.707 1.7285 0.908 0.6012 1.514 1.5786 2.927 2.4796 1.414 0.902 1.918 1.503 3.332 2.105 1.413 0.601 1.817 0.225 3.634 0.451 1.817 0.225 4.139 1.427 6.864 1.878 1.414 0.376 3.534 0 5.755-0.601z" fill="#8B8B8B" style=""/>
  <path d="m385.46 102.07v0.376c-1.514-0.075-2.928-0.451-4.341-0.977-0.606-0.376-1.514-0.977-1.817-1.203-0.303-0.225-0.707-0.4505-0.909-0.5257 0.707 0.3007 3.735 1.7287 4.543 1.9537 0.707 0.226 1.615 0.226 2.019 0.226 0.202 0.075 0.303 0.075 0.505 0.15z" fill="#8B8B8B" style=""/>
  <path d="m415.45 102.44c-0.808 0-1.616 0.225-2.322 0.451-2.827 0.827-5.553 1.578-8.379 2.254v-0.075c0.202-0.075 0.505-0.15 0.707-0.225 2.422-0.677 4.744-1.503 5.653-1.879 1.514-0.676 3.836-1.127 4.24-1.202 0.101 0.3 0.101 0.676 0.101 0.676z" fill="#8B8B8B" style=""/>
  <path d="m413.83 104.1c-0.101 0.15-0.303 0.375-0.404 0.526-0.101 0.3-0.303 0.526-0.404 0.826-0.303 0.827-0.404 1.654-0.707 2.405-0.1 0.151-0.1 0.301-0.201 0.451-0.303 0.226-0.707 0.301-1.111 0.226-0.404-0.076-0.807-0.301-1.11-0.451-0.909-0.451-1.918-0.902-2.928-1.278-0.303-0.075-0.606-0.225-0.908-0.451 0.605 0.226 1.413 0.451 2.12 0.752 1.413 0.601 2.321 0.826 3.23 0.826 0.908-0.075 0.808-1.052 1.514-2.404 0.202-0.526 0.505-0.977 0.909-1.428z" fill="#8B8B8B" style=""/>
  <path d="m427.36 105c-0.202-0.075-0.202-0.075-0.303-0.075-2.019-0.601-4.139-1.203-6.259-1.729-1.01-0.3-2.12-0.526-3.23-0.676-0.202 0-0.404-0.075-0.505-0.15-0.202-0.151-0.202-0.301-0.101-0.526 0.101-0.075 0.101-0.151 0.202-0.151 0.202 0.301 2.826 0.827 4.543 1.353 1.918 0.526 5.451 1.879 5.653 1.954z" fill="#8B8B8B" style=""/>
  <path d="m424.94 89.742c-0.101 0.3006-0.202 0.6012-0.404 0.8267-0.807 1.6533-2.019 3.1563-3.331 4.5842-0.101 0.1503-0.303 0.3006-0.505 0.3757-0.404 0.1503-0.807 0-1.11-0.0751-2.221-0.7515-1.501-0.2255-3.722-0.977-0.404-0.1503-0.807-0.2254-1.11-0.4509-0.404-0.2255-0.47-0.4122-1.091-0.6675-0.569-0.2343-0.735-0.253-1.138-0.5536-0.404-0.3006-0.57 0-0.57 0-0.201-0.2255-0.569-0.5536 0-0.5536h0.57c0.569 0 0.311 0.5723 1.219 0.8729 0.909 0.3006 1.804 0.526 3.217 1.0521 1.413 0.6012 3.23 0.8267 4.038 0.4509 0.808-0.3757 2.423-2.48 3.23-3.5321 0.101-0.3757 0.404-0.8267 0.707-1.3527z" fill="#8B8B8B" style=""/>
  <path d="m434.43 87.486c-0.101 0.0752-0.202 0.1503-0.404 0.2255-1.312 0.3006-2.524 0.6763-3.836 0.9769-0.808 0.2255-1.615 0.451-2.524 0.6013-0.404 0.0751-1.817 0.4509-1.615-0.0752 0-0.0751 0-0.0751 0.101-0.0751l8.278-1.6534z" fill="#8B8B8B" style=""/>
  <path d="m443.84 97.232-3.416 0.5536c-1.708 0-2.689 0.2731-3.9 0.4234-1.515 0.2254-3.227 0.0994-4.64 0.4752-0.706 0.1503-1.494 0.3006-2.201 0.3006-0.404 0-1.211-0.0751-1.514-0.3006 0.303 0.0752 0.606 0.0752 0.808-0.0751 0.807-0.3758 7.849-0.851 9.061-1.2268 1.211-0.3757 5.399 0.1503 5.802-0.1503z" fill="#8B8B8B" style=""/>
  <path d="m471.04 92.73c-0.101 0.0751-0.101 0.1503-0.202 0.1503-0.101 0.0751-0.303 0-0.403 0-3.534-0.4509-5.982-2.2904-9.516-2.2904-1.716 0-10.817 3.3214-11.387 3.3214 0-0.5536 1.139-1.1072 2.278-1.6607 1.11-0.0752 10.145-2.5267 11.356-2.0006 1.413 0.5261 7.773 2.48 7.874 2.48z" fill="#8B8B8B" style=""/>
  <path d="m402.02 111.54c-1.212-0.151-2.625 0.15-3.533-0.451-0.808-0.451-1.01-1.278-1.515-1.879-0.706-0.977-2.019-1.578-2.927-2.48-0.202-0.15-0.505-0.451-0.505-0.676v-0.075c0.909 0.526 3.432 1.954 3.836 2.93 0.505 1.128 2.12 1.579 2.625 1.954 0.404 0.301 1.514 0.151 1.918 0 0 0.151 0.101 0.376 0.101 0.677z" fill="#8B8B8B" style=""/>
  <path d="m412.92 115.29c-0.101 0.526-0.303 1.052-0.605 1.503-0.101 0.3-0.404 0.601-0.707 0.601s-0.404-0.15-0.606-0.301c-1.11-0.977-2.221-1.878-3.129-2.931-0.202-0.225-0.404-0.526-0.707-0.751-0.404-0.451-0.908-0.752-1.413-1.203-0.909-0.676-1.716-1.503-2.423-2.329 0.606 0.601 1.615 1.428 3.533 2.705 2.423 1.503 3.029 3.457 4.442 3.683 1.413 0.225 1.211-1.052 1.111-2.029-0.101-0.677 0.302-1.278 0.504-1.954v0.3c0 0.526 0.101 0.977 0.101 1.503-0.101 0.301 0 0.752-0.101 1.203z" fill="#8B8B8B" style=""/>
 </g><defs>
  <linearGradient id="paint0_linear_2910_72033" x1="214.72" x2="214.68" y1="64" y2="136.01" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <pattern id="liquid" width="172" height="72" patternUnits="userSpaceOnUse"><rect x="-172" width="688" height="72" fill="url(#baseLiquid)" stroke-width="0"/></pattern>
  <pattern id="baseLiquid" width="172" height="72" patternTransform="translate(14,-8)" patternUnits="userSpaceOnUse">
   <circle transform="rotate(-90)" cx="-15" cy="21" r="8" fill="url(#paint28_linear_1182_32781-5)"/>
   <circle transform="rotate(-90)" cx="-15" cy="150" r="8" fill="url(#paint29_linear_1182_32781-1)"/>
   <circle transform="rotate(-90)" cx="-16" cy="113" r="8" fill="url(#paint30_linear_1182_32781-7)"/>
   <circle transform="rotate(-90)" cx="-58" cy="34" r="8" fill="url(#paint31_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-58" cy="155" r="8" fill="url(#paint32_linear_1182_32781-3)"/>
   <circle transform="rotate(-90)" cx="-26" cy="33" r="5" fill="url(#paint33_linear_1182_32781-1)"/>
   <circle transform="rotate(-90)" cx="-26" cy="162" r="5" fill="url(#paint34_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-37" cy="5" r="5" fill="url(#paint35_linear_1182_32781-6)"/>
   <circle transform="rotate(-90)" cx="-8" cy="94" r="4" fill="url(#paint36_linear_1182_32781-9)"/>
   <circle transform="rotate(-90)" cx="-60" cy="72" r="4" fill="url(#paint37_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-37" cy="112" r="5" fill="url(#paint38_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-39" cy="59" r="5" fill="url(#paint39_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-62" cy="115" r="5" fill="url(#paint40_linear_1182_32781-6)"/>
   <circle transform="rotate(-90)" cx="-42" cy="139" r="5" fill="url(#paint41_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-21" cy="76" r="5" fill="url(#paint42_linear_1182_32781-1)"/>
   <circle transform="rotate(-90)" cx="-50.5" cy="126.5" r="2.5" fill="url(#paint43_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-46.5" cy="169.5" r="2.5" fill="url(#paint44_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-9.5" cy="57.5" r="2.5" fill="url(#paint45_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-35.5" cy="96.5" r="2.5" fill="url(#paint46_linear_1182_32781-9)"/>
   <circle transform="rotate(-90)" cx="-23.5" cy="91.5" r="2.5" fill="url(#paint47_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-40.5" cy="22.5" r="2.5" fill="url(#paint48_linear_1182_32781-88)"/>
   <circle transform="rotate(-90)" cx="-23.5" cy="124.5" r="2.5" fill="url(#paint49_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-47.5" cy="86.5" r="2.5" fill="url(#paint50_linear_1182_32781-6)"/>
   <circle transform="rotate(-90)" cx="-21.5" cy="51.5" r="2.5" fill="url(#paint51_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-51.5" cy="48.5" r="2.5" fill="url(#paint52_linear_1182_32781-3)"/>
   <circle transform="rotate(-90)" cx="-64" cy="14" r="4" fill="url(#paint53_linear_1182_32781-83)"/>
   <circle transform="rotate(-90)" cx="-64" cy="135" r="4" fill="url(#paint54_linear_1182_32781-33)"/>
   <circle transform="rotate(-90)" cx="-58.5" cy="95.5" r="9.5" fill="url(#paint55_linear_1182_32781-8)"/>
   <path d="m0 0h172v72h-172z" fill="url(#paint84_linear_1182_32781-8)" stroke-width=".57735"/>
  </pattern>
  <linearGradient id="paint28_linear_1182_32781-5" x1="19.316" x2="21" y1="8.2632" y2="23" gradientTransform="translate(-36,6)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint29_linear_1182_32781-1" x1="148.32" x2="150" y1="8.2632" y2="23" gradientTransform="translate(-165,135)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint30_linear_1182_32781-7" x1="111.32" x2="113" y1="9.2632" y2="24" gradientTransform="translate(-129,97)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint31_linear_1182_32781-4" x1="32.316" x2="34" y1="51.263" y2="66" gradientTransform="translate(-92,-24)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint32_linear_1182_32781-3" x1="153.32" x2="155" y1="51.263" y2="66" gradientTransform="translate(-213,97)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint33_linear_1182_32781-1" x1="31.947" x2="33" y1="21.789" y2="31" gradientTransform="translate(-59,7)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint34_linear_1182_32781-4" x1="160.95" x2="162" y1="21.789" y2="31" gradientTransform="translate(-188,136)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint35_linear_1182_32781-6" x1="3.9474" x2="5" y1="32.79" y2="42" gradientTransform="translate(-42,-32)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint36_linear_1182_32781-9" x1="93.158" x2="94" y1="4.6316" y2="12" gradientTransform="translate(-102,86)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint37_linear_1182_32781-4" x1="71.158" x2="72" y1="56.632" y2="64" gradientTransform="translate(-132,12)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint38_linear_1182_32781-2" x1="110.95" x2="112" y1="32.79" y2="42" gradientTransform="translate(-149,75)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint39_linear_1182_32781-2" x1="57.947" x2="59" y1="34.79" y2="44" gradientTransform="translate(-98,20)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint40_linear_1182_32781-6" x1="113.95" x2="115" y1="57.79" y2="67" gradientTransform="translate(-177,53)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint41_linear_1182_32781-4" x1="137.95" x2="139" y1="37.79" y2="47" gradientTransform="translate(-181,97)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint42_linear_1182_32781-1" x1="74.947" x2="76" y1="16.789" y2="26" gradientTransform="translate(-97,55)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint43_linear_1182_32781-2" x1="125.97" x2="126.5" y1="48.395" y2="53" gradientTransform="translate(-177,76)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint44_linear_1182_32781-8" x1="168.97" x2="169.5" y1="44.395" y2="49" gradientTransform="translate(-216,123)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint45_linear_1182_32781-8" x1="56.974" x2="57.5" y1="7.3947" y2="12" gradientTransform="translate(-67,48)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint46_linear_1182_32781-9" x1="95.974" x2="96.5" y1="33.395" y2="38" gradientTransform="translate(-132,61)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint47_linear_1182_32781-2" x1="90.974" x2="91.5" y1="21.395" y2="26" gradientTransform="translate(-115,68)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint48_linear_1182_32781-88" x1="21.974" x2="22.5" y1="38.395" y2="43" gradientTransform="translate(-63,-18)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint49_linear_1182_32781-8" x1="123.97" x2="124.5" y1="21.395" y2="26" gradientTransform="translate(-148,101)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint50_linear_1182_32781-6" x1="85.974" x2="86.5" y1="45.395" y2="50" gradientTransform="translate(-134,39)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint51_linear_1182_32781-8" x1="50.974" x2="51.5" y1="19.395" y2="24" gradientTransform="translate(-73,30)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint52_linear_1182_32781-3" x1="47.974" x2="48.5" y1="49.395" y2="54" gradientTransform="translate(-100,-3)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint53_linear_1182_32781-83" x1="13.158" x2="14" y1="60.632" y2="68" gradientTransform="translate(-78,-50)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint54_linear_1182_32781-33" x1="134.16" x2="135" y1="60.632" y2="68" gradientTransform="translate(-199,71)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint55_linear_1182_32781-8" x1="93.5" x2="95.5" y1="50.5" y2="68" gradientTransform="translate(-154,37)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint84_linear_1182_32781-8" x1="248" x2="248" y1="1.8513e-7" y2="72" gradientTransform="scale(.33333 1)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" offset=".10895"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".11331"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".88736"/>
   <stop stop-color="#727171" offset=".89138"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
 </defs>
</svg>