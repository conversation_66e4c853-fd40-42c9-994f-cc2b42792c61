<?xml version="1.0" encoding="utf-8"?>
<!--BSD 3-Clause License

Copyright (c) 2019, Cloudminds
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>EC<PERSON>L, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON>
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>Tripod Head</Name>
    <Description1><![CDATA[This LWM2M Object includes Tripod Head information.]]></Description1>
    <ObjectID>10339</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10339</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Single</MultipleInstances>
    <Mandatory>Optional</Mandatory>
    <Resources>
		<Item ID="5907">
			<Name>Host Device Unique ID</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>String</Type>
			<RangeEnumeration/>
			<Units/>
			<Description>
				<![CDATA[ The host device unique ID as assigned by an OEM, MNO, 
				or other as the Device ID in the onboarding or manufacturing process. ]]>
			</Description>
		</Item>
		<Item ID="5905">
			<Name>Host Device Manufacturer</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>String</Type>
			<RangeEnumeration/>
			<Units/>
			<Description>
			<![CDATA[Human readable host device manufacturer name.]]>
			</Description>
		</Item>
		<Item ID="5906">
			<Name>Host Device Model Number</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>String</Type>
			<RangeEnumeration/>
			<Units/>
			<Description>
				<![CDATA[A host device model identifier (manufacturer specified string).]]>
			</Description>
		</Item>
		<Item ID="100">
			<Name>Tripod Direction Control</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Control the direction of the Tripod Head: Parameter: 0:Up, 1:Down, 2:Left, 3:Right.]]></Description>
		</Item>
		<Item ID="101">
			<Name>Tripod Automatic Control</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[If the Tripod Head is automatically controlled: Parameter: 0:False, 1:True.]]></Description>
		</Item>
		<Item ID="102">
			<Name>Tripod Reset</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type/>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Reset the Tripod Head.]]></Description>
		</Item>
    </Resources>
    <Description2 />
  </Object>
</LWM2M>
