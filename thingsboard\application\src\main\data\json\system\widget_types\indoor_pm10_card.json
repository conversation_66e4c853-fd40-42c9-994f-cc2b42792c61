{"fqn": "indoor_pm10_card", "name": "Indoor PM10 card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_pm10_card_system_widget_image.png", "description": "Displays the latest indoor fine and coarse particulate matter (PM10) telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pm10', label: 'PM10', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM10\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 50 - 25;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 200) {\\n\\tvalue = 200;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:broom\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":150,\"color\":\"#FFA600\"},{\"from\":150,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":32,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":150,\"color\":\"#FFA600\"},{\"from\":150,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Indoor PM10 card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"µg/m³\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["environment", "particulate", "matter", "air", "pm10", "coarse particulates", "coarse particles", "particulate matter 10", "inhalable particles", "larger particulates", "dust", "airborne coarse particles"], "resources": [{"link": "/api/images/system/indoor_pm10_card_system_widget_image.png", "title": "\"Indoor PM10 card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_pm10_card_system_widget_image.png", "publicResourceKey": "bZvUodvOJ9l7ws3OGCWJrSUWd363u9fq", "mediaType": "image/png", "data": "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", "public": true}]}