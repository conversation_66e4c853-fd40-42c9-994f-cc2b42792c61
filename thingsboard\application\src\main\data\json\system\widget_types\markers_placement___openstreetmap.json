{"fqn": "input_widgets.markers_placement_openstreetmap", "name": "Markers Placement - OpenStreetMap", "deprecated": true, "image": "tb-image;/api/images/system/markers_placement_openstreetmap_system_widget_image.png", "description": "Allows configuring the location of the selected entities on OpenStreetMap. By default, store the location using 'latitude' and 'longitude' server-side attributes.", "descriptor": {"type": "latest", "sizeX": 8.5, "sizeY": 6.5, "resources": [], "templateHtml": "", "templateCss": ".leaflet-zoom-box {\n\tz-index: 9;\n}\n\n.leaflet-pane         { z-index: 4; }\n\n.leaflet-tile-pane    { z-index: 2; }\n.leaflet-overlay-pane { z-index: 4; }\n.leaflet-shadow-pane  { z-index: 5; }\n.leaflet-marker-pane  { z-index: 6; }\n.leaflet-tooltip-pane   { z-index: 7; }\n.leaflet-popup-pane   { z-index: 8; }\n\n.leaflet-map-pane canvas { z-index: 1; }\n.leaflet-map-pane svg    { z-index: 2; }\n\n.leaflet-control {\n\tz-index: 9;\n}\n.leaflet-top,\n.leaflet-bottom {\n\tz-index: 11;\n}\n\n.tb-marker-label {\n    border: none;\n    background: none;\n    box-shadow: none;\n}\n\n.tb-marker-label:before {\n    border: none;\n    background: none;\n}\n", "controllerScript": "self.onInit = function() {\n    self.ctx.map = new TbMapWidgetV2('openstreet-map', false, self.ctx, null, true);\n}\n\nself.onDataUpdated = function() {\n    self.ctx.map.update();\n}\n\nself.onResize = function() {\n    self.ctx.map.resize();\n}\n\nself.actionSources = function() {\n    return TbMapWidgetV2.actionSources();\n}\n\nself.onDestroy = function() {\n    self.ctx.map.destroy();\n}\n\nself.typeParameters = function() {\n    return {\n        hasDataPageLink: true\n    };\n}", "settingsSchema": "", "dataKeySettingsSchema": "", "settingsDirective": "tb-map-widget-settings-legacy", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"First point\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"latitude\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.05427416942713381,\"funcBody\":\"var value = prevValue || 15.833293;\\nif (time % 5000 < 500) {\\n    value += Math.random() * 0.05 - 0.025;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"longitude\",\"color\":\"#4caf50\",\"settings\":{},\"_hash\":0.680594833308841,\"funcBody\":\"var value = prevValue || -90.454350;\\nif (time % 5000 < 500) {\\n    value += Math.random() * 0.05 - 0.025;\\n}\\nreturn value;\"}]},{\"type\":\"function\",\"name\":\"Second point\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"latitude\",\"color\":\"#607d8b\",\"settings\":{},\"_hash\":0.7867521952070078,\"funcBody\":\"var value = prevValue || 14.450463;\\nif (time % 4000 < 500) {\\n    value += Math.random() * 0.05 - 0.025;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"longitude\",\"color\":\"#9c27b0\",\"settings\":{},\"_hash\":0.7040053227577256,\"funcBody\":\"var value = prevValue || -84.845334;\\nif (time % 4000 < 500) {\\n    value += Math.random() * 0.05 - 0.025;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"fitMapBounds\":true,\"latKeyName\":\"latitude\",\"lngKeyName\":\"longitude\",\"showLabel\":true,\"label\":\"${entityName}\",\"tooltipPattern\":\"<b>${entityName}</b><br/><br/><b>Latitude:</b> ${latitude:7}<br/><b>Longitude:</b> ${longitude:7}<br/><br/><link-act name='delete'>Delete</link-act>\",\"markerImageSize\":34,\"useColorFunction\":false,\"markerImages\":[],\"useMarkerImageFunction\":false,\"color\":\"#fe7569\",\"mapProvider\":\"OpenStreetMap.Mapnik\",\"showTooltip\":true,\"autocloseTooltip\":true,\"defaultCenterPosition\":\"0,0\",\"customProviderTileUrl\":\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",\"showTooltipAction\":\"click\",\"polygonKeyName\":\"coordinates\",\"polygonOpacity\":0.5,\"polygonStrokeOpacity\":1,\"polygonStrokeWeight\":1,\"zoomOnClick\":true,\"showCoverageOnHover\":true,\"animate\":true,\"maxClusterRadius\":80,\"removeOutsideVisibleBounds\":true,\"defaultZoomLevel\":5,\"provider\":\"openstreet-map\",\"draggableMarker\":true,\"editablePolygon\":true,\"mapPageSize\":16384,\"showPolygon\":false,\"polygonTooltipPattern\":\"<b>${entityName}</b><br/><br/><b>TimeStamp:</b> ${coordinates|ts:7}<br/><br/><link-act name='delete'>Delete</link-act>\"},\"title\":\"Markers Placement - OpenStreetMap\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{\"tooltipAction\":[{\"name\":\"delete\",\"icon\":\"more_horiz\",\"type\":\"custom\",\"customFunction\":\"var entityDatasource = widgetContext.map.map.datasources.filter(\\n    function(entity) {\\n        return entity.entityId === entityId.id;\\n    });\\n\\nwidgetContext.map.setMarkerLocation(entityDatasource[0], null, null).subscribe(() => widgetContext.updateAliases());\",\"id\":\"54c293c4-9ca6-e34f-dc6a-0271944c1c66\"},{\"name\":\"delete_polygon\",\"icon\":\"more_horiz\",\"type\":\"custom\",\"customFunction\":\"var entityDatasource = widgetContext.map.map.datasources.filter(\\n    function(entity) {\\n        return entity.entityId === entityId.id\\n    });\\n\\nwidgetContext.map.savePolygonLocation(entityDatasource[0], null).subscribe(() => widgetContext.updateAliases());\",\"id\":\"6beb7bed-dfd8-388d-b60c-82988ab52f06\"}]},\"showTitleIcon\":false,\"titleIcon\":\"more_horiz\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"displayTimewindow\":true}"}, "tags": ["mapping", "gps", "navigation", "geolocation", "satellite", "directions"], "resources": [{"link": "/api/images/system/markers_placement_openstreetmap_system_widget_image.png", "title": "\"Markers Placement - OpenStreetMap\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "markers_placement_openstreetmap_system_widget_image.png", "publicResourceKey": "mNinG4oIEu5i7bUCVmdWbnsL2eOjbR38", "mediaType": "image/png", "data": "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", "public": true}]}