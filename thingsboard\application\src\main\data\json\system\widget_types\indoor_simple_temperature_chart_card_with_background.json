{"fqn": "indoor_simple_temperature_chart_card_with_background", "name": "Indoor simple temperature chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/indoor_simple_temperature_chart_card_with_background_system_widget_image.png", "description": "Displays historical indoor temperature values as a simplified chart with background. Optionally may display the corresponding latest indoor temperature value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'temperature', label: 'Temperature', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'temperature', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nif (value < 15) {\\n\\tvalue = 15;\\n} else if (value > 30) {\\n\\tvalue = 30;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nif (value < 15) {\\n\\tvalue = 15;\\n} else if (value > 30) {\\n\\tvalue = 30;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":18,\"color\":\"#224AC2\"},{\"from\":18,\"to\":24,\"color\":\"#3B911C\"},{\"from\":24,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/indoor_simple_temperature_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Temperature\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"thermostat\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"°C\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["temperature", "environment", "indoor"], "resources": [{"link": "/api/images/system/indoor_simple_temperature_chart_card_with_background_system_widget_background.png", "title": "\"Indoor simple temperature chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_simple_temperature_chart_card_with_background_system_widget_background.png", "publicResourceKey": "VpjSApFlRCcm9dDS5BmVd5KEt57xuoVz", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/indoor_simple_temperature_chart_card_with_background_system_widget_image.png", "title": "\"Indoor simple temperature chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_simple_temperature_chart_card_with_background_system_widget_image.png", "publicResourceKey": "SKv9wLeczWWX7pO21HZoprJL0jTuAnbi", "mediaType": "image/png", "data": "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", "public": true}]}