{"fqn": "air_quality_chart_card", "name": "Air quality index chart card", "deprecated": false, "image": "tb-image;/api/images/system/air_quality_index_chart_card_system_widget_image.png", "description": "Displays air quality index data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'air', label: 'Air Quality Index', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'AQI', decimals: 1 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'air', 'AQI', 1);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Air Quality Index\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 320) {\\n\\tvalue = 320;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"AQI\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":100,\"color\":\"#FFA600\"},{\"from\":100,\"to\":150,\"color\":\"#F36900\"},{\"from\":150,\"to\":200,\"color\":\"#D81838\"},{\"from\":200,\"to\":300,\"color\":\"#8D268C\"},{\"from\":300,\"to\":null,\"color\":\"#6F113A\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 320) {\\n\\tvalue = 320;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"AQI\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < -20) {\\n\\tvalue = -20;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"AQI\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Air Quality Index\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:weather-windy\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "air", "aqi", "pollution", "emission", "smog"], "resources": [{"link": "/api/images/system/air_quality_index_chart_card_system_widget_image.png", "title": "\"Air quality index chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "air_quality_index_chart_card_system_widget_image.png", "publicResourceKey": "0qZBCktbUoRj0CrGVPTDU9sKO2JByIT0", "mediaType": "image/png", "data": "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", "public": true}]}