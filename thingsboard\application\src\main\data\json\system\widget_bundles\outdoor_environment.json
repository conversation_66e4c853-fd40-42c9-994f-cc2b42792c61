{"widgetsBundle": {"alias": "outdoor_environment", "title": "Outdoor Environment", "image": "data:image/png;base64,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", "description": "Contains widgets displaying outdoor environment telemetry.", "order": 11000, "externalId": null, "name": "Outdoor Environment"}, "widgetTypeFqns": ["temperature_card", "temperature_card_with_background", "horizontal_temperature_card", "horizontal_temperature_card_with_background", "temperature_chart_card", "temperature_chart_card_with_background", "simple_temperature_chart_card", "simple_temperature_chart_card_with_background", "temperature_range_chart", "temperature_range_chart_with_background", "temperature_gauge", "humidity_card", "humidity_card_with_background", "horizontal_humidity_card", "horizontal_humidity_card_with_background", "humidity_chart_card", "humidity_chart_card_with_background", "simple_humidity_chart_card", "simple_humidity_chart_card_with_background", "humidity_progress_bar", "humidity_progress_bar_with_background", "pressure_card", "pressure_card_with_background", "horizontal_pressure_card", "horizontal_pressure_card_with_background", "pressure_chart_card", "pressure_chart_card_with_background", "simple_pressure_chart_card", "simple_pressure_chart_card_with_background", "pressure_progress_bar", "pressure_progress_bar_with_background", "wind_speed_card", "wind_speed_card_with_background", "horizontal_wind_speed_card", "horizontal_wind_speed_card_with_background", "wind_speed_chart_card", "wind_speed_chart_card_with_background", "simple_wind_speed_chart_card", "simple_wind_speed_chart_card_with_background", "wind_speed_and_direction", "wind_speed_and_direction_with_background", "rainfall_card", "rainfall_card_with_background", "horizontal_rainfall_card", "horizontal_rainfall_card_with_background", "rainfall_chart_card", "rainfall_chart_card_with_background", "simple_rainfall_chart_card", "simple_rainfall_chart_card_with_background", "solar_radiation_card", "solar_radiation_card_with_background", "horizontal_solar_radiation_card", "horizontal_solar_radiation_card_with_background", "solar_radiation_chart_card", "solar_radiation_chart_card_with_background", "simple_solar_radiation_chart_card", "simple_solar_radiation_chart_card_with_background", "uv_index_card", "uv_index_card_with_background", "horizontal_uv_index_card", "horizontal_uv_index_card_with_background", "uv_index_chart_card", "uv_index_chart_card_with_background", "simple_uv_index_chart_card", "simple_uv_index_chart_card_with_background", "air_quality_card", "air_quality_card_with_background", "horizontal_air_quality_card", "horizontal_air_quality_card_with_background", "air_quality_chart_card", "air_quality_chart_card_with_background", "simple_air_quality_chart_card", "simple_air_quality_chart_card_with_background", "visibility_card", "visibility_card_with_background", "horizontal_visibility_card", "horizontal_visibility_card_with_background", "visibility_chart_card", "visibility_chart_card_with_background", "simple_visibility_chart_card", "simple_visibility_chart_card_with_background", "ground_temperature_card", "ground_temperature_card_with_background", "horizontal_ground_temperature_card", "horizontal_ground_temperature_card_with_background", "ground_temperature_chart_card", "ground_temperature_chart_card_with_background", "simple_ground_temperature_chart_card", "simple_ground_temperature_chart_card_with_background", "soil_moisture_card", "soil_moisture_card_with_background", "horizontal_soil_moisture_card", "horizontal_soil_moisture_card_with_background", "soil_moisture_chart_card", "soil_moisture_chart_card_with_background", "simple_soil_moisture_chart_card", "simple_soil_moisture_chart_card_with_background", "soil_moisture_progress_bar", "soil_moisture_progress_bar_with_background", "noise_level_card", "noise_level_card_with_background", "horizontal_noise_level_card", "horizontal_noise_level_card_with_background", "noise_level_chart_card", "noise_level_chart_card_with_background", "simple_noise_level_chart_card", "simple_noise_level_chart_card_with_background", "vibration_card", "vibration_card_with_background", "horizontal_vibration_card", "horizontal_vibration_card_with_background", "vibration_chart_card", "vibration_chart_card_with_background", "simple_vibration_chart_card", "simple_vibration_chart_card_with_background", "leaf_wetness_card", "leaf_wetness_card_with_background", "horizontal_leaf_wetness_card", "horizontal_leaf_wetness_card_with_background", "leaf_wetness_chart_card", "leaf_wetness_chart_card_with_background", "simple_leaf_wetness_chart_card", "simple_leaf_wetness_chart_card_with_background", "leaf_wetness_progress_bar", "leaf_wetness_progress_bar_with_background", "snow_depth_card", "snow_depth_card_with_background", "horizontal_snow_depth_card", "horizontal_snow_depth_card_with_background", "snow_depth_chart_card", "snow_depth_chart_card_with_background", "simple_snow_depth_chart_card", "simple_snow_depth_chart_card_with_background", "co2_card", "co2_card_with_background", "horizontal_co2_card", "horizontal_co2_card_with_background", "co2_chart_card", "co2_chart_card_with_background", "simple_co2_chart_card", "simple_co2_chart_card_with_background", "flooding_level_card", "flooding_level_card_with_background", "horizontal_flooding_level_card", "horizontal_flooding_level_card_with_background", "flooding_level_chart_card", "flooding_level_chart_card_with_background", "simple_flooding_level_chart_card", "simple_flooding_level_chart_card_with_background", "flooding_level_progress_bar", "flooding_level_progress_bar_with_background", "illuminance_card", "illuminance_card_with_background", "horizontal_illuminance_card", "horizontal_illuminance_card_with_background", "illuminance_chart_card", "illuminance_chart_card_with_background", "simple_illuminance_chart_card", "simple_illuminance_chart_card_with_background", "illuminance_progress_bar", "illuminance_progress_bar_with_background", "pm2_5_card", "pm2_5_card_with_background", "horizontal_pm2_5_card", "horizontal_pm2_5_card_with_background", "pm2_5_chart_card", "pm2_5_chart_card_with_background", "simple_pm2_5_chart_card", "simple_pm2_5_chart_card_with_background", "pm10_card", "pm10_card_with_background", "horizontal_pm10_card", "horizontal_pm10_card_with_background", "pm10_chart_card", "pm10_chart_card_with_background", "simple_pm10_chart_card", "simple_pm10_chart_card_with_background"]}