{"fqn": "indoor_pm10_chart_card", "name": "Indoor PM10 chart card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_pm10_chart_card_system_widget_image.png", "description": "Displays a indoor fine and coarse particulate matter (PM10) data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pm10', label: 'PM10', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'µg/m³', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'pm10', 'µg/m³', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM10\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 50 - 25;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 200) {\\n\\tvalue = 200;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":150,\"color\":\"#FFA600\"},{\"from\":150,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 50 - 25;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 200) {\\n\\tvalue = 200;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nif (value < -10) {\\n\\tvalue = -10;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"PM10\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:broom\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "particulate", "matter", "air", "pm10", "coarse particulates", "coarse particles", "particulate matter 10", "inhalable particles", "larger particulates", "dust", "airborne coarse particles"], "resources": [{"link": "/api/images/system/indoor_pm10_chart_card_system_widget_image.png", "title": "\"Indoor PM10 chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_pm10_chart_card_system_widget_image.png", "publicResourceKey": "AGIgBAMhVWIKzKN5GUCowsyEmhod2NAR", "mediaType": "image/png", "data": "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", "public": true}]}