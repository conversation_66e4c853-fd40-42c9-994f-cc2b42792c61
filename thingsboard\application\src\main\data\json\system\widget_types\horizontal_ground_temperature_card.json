{"fqn": "horizontal_ground_temperature_card", "name": "Horizontal ground temperature card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_ground_temperature_card_system_widget_image.png", "description": "Displays the latest ground temperature telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'temperature', label: 'Ground temperature', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Ground temperature\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"thermostat\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#234CC7\"},{\"from\":-20,\"to\":0,\"color\":\"#305AD7\"},{\"from\":0,\"to\":10,\"color\":\"#7191EF\"},{\"from\":10,\"to\":20,\"color\":\"#FFA600\"},{\"from\":20,\"to\":30,\"color\":\"#F36900\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#234CC7\"},{\"from\":-20,\"to\":0,\"color\":\"#305AD7\"},{\"from\":0,\"to\":10,\"color\":\"#7191EF\"},{\"from\":10,\"to\":20,\"color\":\"#FFA600\"},{\"from\":20,\"to\":30,\"color\":\"#F36900\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal temperature card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"°C\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "soil temperature", "terrestrial temperature", "subsurface temperature", "earth temperature", "below surface temperature", "surface temp", "soil warmth", "land temperature", "geothermal reading", "ground warmth"], "resources": [{"link": "/api/images/system/horizontal_ground_temperature_card_system_widget_image.png", "title": "\"Horizontal ground temperature card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_ground_temperature_card_system_widget_image.png", "publicResourceKey": "3Y6UZLIaUNBWug31Y3vfz1uIe77DBi1R", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAqFBMVEUAAADf39/f39/g4ODg4ODf39/////k5OQjTMfg4ODy8vLIyMjI0vFYWFh1j9yQkJDOzs7k6fg+Ys50dHT5+fmsrKxKSkpmZmaRpePb29vy9PvCwsK2trasvOrV1dXn5+efsedaedU9PT28vLy6x+6enp4/Ys66urowV8taeNVMbtI8PDzt7e2dnZ2CgoIhISExV8ovLy/W3fXW3fSRpuODmuBog9nW3vRsY+KEAAAABnRSTlMAIEDfv1C6kOEmAAADiUlEQVR42u3ai1LaQBiGYar232WXzflACIRzOSnVVtv7v7P+2bQijoVYHRqZ7xkIm92M7jtmBmegBQAAAAAAAAAAAAAAAAAAAAAAAAAAAHBKnz48m3FxKT669uVn7hARfXhKXLSuFJ2B6KrVprPQRkjDIKRpENI0CGkahDTNiyGDpXygp+ZuTC8I+anogAOrpwjpyeCHHNGOu9WJVkpR9RAk+MijCR9SThT8sPOkqgX7KhTpRIlybI9UX2fUnfmPo93GRoO99WMhHTnoSod2koKEcJPtjTFzHZIRJt3EIkm/csAmpSRM6C7cpKkRX0MjFomZx8nWDTc63CyM0KGehHz1nOpyJAu+8+jejnyy/GAVjMvXsZ0d1Anp7YVMVJzGrkuJUJMqRMShO6cJr4V6kcSm+EpuLCbCkDs37pyvJjFPXT63ISml23hCNQ1k0PnmyDXvpBo5ZM3W5MseUSBXs05Xyu81Qr7vhRhNLuMgkbwQYrRWv0MS4oobrTnELG4eQ0IKXa2pprXslPc379mRo3J026v2ddu5D2wnMb5paoSM9q4qTDpZcEg8MQuVpHc2RJj0jtdcwzPJn5C7NBELY+YccpMmW7VZ3CSmDClM6FJNs0G58TJkXCbRF+mT1V0u+XzMdayzqhNC+7kkqKKqJ3txIIzanav9RUWvMpArTihDbM6OnawcD1mPOeQfKJfexGe9ahRI/x1CmEP/wRf7i6uOEb01hH9IaUanN+p27dtGL5Bd+nvIrF7ISFq39P+sqw76Yfe8lD494VSLM7k+EjKWlZ+H/9FQ188nFL2Tx3eObjnwZfDszr/1beTDq0P6VYyi64ysKKKo7ZUTbYr4fBgPKcqyaoEn3pZ0L6VTGlBvKZ1uIEfPOwPnYSwD/0hIV1oBPQ0pCi/T2uNRTnmWT6N+7hWF9qbX3jQi8oZqOs1IeXbB01N6g7GsOETfAimXI3qG26Rc+XQkpBfI0mA/xOsr7Q29KmRYZDzmuWHm5dqGtLOMQ3R/qBXn5oreie/3DsweDCHfWcpVh3ZiL8u9fqFzxUm5N83yvuprj+eup0PtURkSlX+R4TQuF4o8p9OqQl4tG9LfFTqjOpoQEkWHFhXV0YiQJkJI0yCkaRDSNAhpmnbr6gw+ZidSV60LcQYftEfiotX6fNkWH93lxTl9qQYAAAAAAAAAAAAAAAAAAAAAAAAAAADgRH4BUVMBmbp0nFsAAAAASUVORK5CYII=", "public": true}]}