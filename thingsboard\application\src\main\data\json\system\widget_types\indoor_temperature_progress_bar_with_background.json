{"fqn": "indoor_temperature_progress_bar_with_background", "name": "Indoor temperature progress bar with background", "deprecated": false, "image": "tb-image;/api/images/system/indoor_temperature_progress_bar_with_background_system_widget_image.png", "description": "Displays indoor temperature reading as a horizontal progress bar with background. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'temperature', label: 'Temperature', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nif (value < 15) {\\n\\tvalue = 15;\\n} else if (value > 30) {\\n\\tvalue = 30;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":18,\"color\":\"#224AC2\"},{\"from\":18,\"to\":24,\"color\":\"#3B911C\"},{\"from\":24,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":40,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/indoor_temperature_progress_bar_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":18,\"color\":\"#224AC2\"},{\"from\":18,\"to\":24,\"color\":\"#3B911C\"},{\"from\":24,\"to\":null,\"color\":\"#DE2343\"}]},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Temperature\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"°C\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"device_thermostat\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["progress", "environment", "indoor", "temperature"], "resources": [{"link": "/api/images/system/indoor_temperature_progress_bar_with_background_system_widget_background.png", "title": "\"Indoor temperature progress bar with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_temperature_progress_bar_with_background_system_widget_background.png", "publicResourceKey": "bwbN6NuC6Ezd3c17WYhJMWGGPpUNzwbV", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/indoor_temperature_progress_bar_with_background_system_widget_image.png", "title": "\"Indoor temperature progress bar with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_temperature_progress_bar_with_background_system_widget_image.png", "publicResourceKey": "XtBiH4yZ6cFZbqNvep20d8JzeQ9U8ouf", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAC/VBMVEUNCgqzoZeulH7CubeVe18HCQmdjIITEw8HCgnt7O3r6uzv7e73+PrGx8bq6Oj09Pft6ujs6urs6Ofw7/Hv7Ovq6eoiSsHu7vD8/Pzi397k4eD5+fvc2tnHyMjt6ur18/T08fDf3Nrp5uXU1NTx7/DQ0M/g3dzn5OMKDQzp5ODy8/Xn5ufz8fLa2dnl4uLT0tLW1tba2NcbHBvR0dHz7+3d3d2voJvb29zk5OXi3tuunJTY1tTOzs729vevnpfe3t/c2dbg4OHk39zn4t3s5+TJysri4uPm5eXKycjX19jMy8rm4uDp493n6Ora19Tq5uLw6+jf29fx8fSsmpHOzcwHCwrl3tjW09GmlI3l5ujT0c6nk4qxo57MzMykkov09vjx7eoNERDh3dgSFRPv8PPS09UYGRirl43n4NrNz9CfjYWplYysnZcgHxwwMC+ypaLa3uPr5N7W2t7KzM23tLbJzdlRSEHt5+Clkoje4edXTkbv6eShkIe2tb4mKinMz9vDvL+xrrEeJCMnJiLPyca0srNFPzjm6vDMw8a8usHRzMbIv8M7OTSysru6t7ujl5IyNjXSzMuyqqyxoJmKeWqurre9u7vBuLri5evAvsWQgnrr7fJ/fHyCcmdFR0eUh4FiYmLHyte3ra2ZlZaeko2YjIdWWFk5MSiSj5GOiYqGhIXT1trCwL+9tLa5sLKNc2HO0dSJfXRjWFBNTEvCx9XHxMJ8bWI8QULP0t2tqaujn59ub3FdVExNRDyci39mX1x+dnKTeWhpaGhNUlOEd24wLCakmphgW1mqqrKPfm9hTj6cmZtyaWFvXUt3dndtYlm7wdG1qaeWhXV5Y1BKOSzL1eMmMDOspaaloqR/gYh4aVs7XcV4cGu7m32Da1hWRjebgGt3fITU3OZcb4GZnaaxusvDo4OIl6dWZXWjs8eylHpsVEEZKS+ojXk4S1lMa8bApY1EWm94iJXHs6ZodYS6ydyXpdSWp7ytu9ajh3BthMxeecgqO0WBlMzGrZqlhjDTAAAACXRSTlMNIL+477+vr68JvroxAAA2WElEQVR42pTVwWvTUBwH8CooHhvxsjGFDdR4UFFBsJNQ5gRxsDnqWFlmIAZWSLPtNsoMghY8DaZWPARGYbqDF2EVPUiXedgUoVDmRZD+MX5/v/de3hKq4jfJe0lF9vv0916ayx056quEsR/WwlpNXKFtY7J1qkiFQqO6qdp2/ONHHPt2pcQpVEuFAs5C1S8V8Iw7DJh45qiPKJYVcCwx0CMG6/qhzFOWl5eDL93WzuLMzIxpmjMyJudEDg4fRajEPiEQIoiZANJRAYQtYlCYmv9qY2M7xp8vcOUVWW7FFyVrRyGbQDoUhp5wNZCE0Ugk88F2p7362DUpkoET04yXyx1nR0gXdyQERIYQNWoEWSQgMWiIHX7f24vCwBLfv4JwRxDukmiMlVJYOCkk0Aw0wwIjG9GT9aizu1L3TNGTxIPxWE71gwgMqelwL8pVSKh+BkkLDyp2HMdhiXtBqYrSNUSFMTwoCocNvJpwe50gunwcCiIlve5O3UtDELREOxBMDEAjHBsnIcq2SIVXl6AokO6MVFT4ErFjtRk0RKcUKIp0UBdYwyNrmJGWzL/tdlf2TQ1RyUkDD07sO07ZwYX6y9UybmTAyaZKLrbwwBohk1UTRLaBN4p6wl7CgfqbDqaGFZQgkUUHmeXEA810twzKwcF8r9ta8RjCDFNDECFx/BC1p0KPBLIxUtiQfpvBQc0pAcIcP+mIboG+s3T8dTjQiaAEB8VKK7RBqgBB1rHldxZUL0yZXFNINERLpEEQMHIYQQwdNEw3pVTeLldsP25G2xvNivj6uQPyTgWAdd7f1xtB2WICj3p7pCDzGkJbvrW26CqI67ozppvrfNmOmltsAcShpCFiUqk5tlMT0RAsxQqtRNpkftSNYPjU6bbanbDQaKi61V1DPqETgPAFiJWF6FWVDHBwDoImJE9ZAoKELDx+9q0XUQVqaTnJijoMQZnkAIQKDhXF4Q1EceJoe+/TLyxgzg6utg8IhysXBC3jhmDPB04hOPQ7GGQhEkEHQvvkYAs/jXXhMJmykDPpxnu6Bo0TAqJzqCs4hUP8A01YXzT5zWhvg779VVH/ahKChAxIYVgiMLT1+XpbKgWHI9eSFqRDkuDLbmvNNV2PuuFBAggtM2+h/nhpbXW3F235eqOorpCEBxk/jvDtb3zqdlutVrvd1tW3KS0ObG1fFk9p4NDBtheQAC+0EDfUlOQnBMVz/ZqQlSx/jbrtpauua6YgoFBf6h+efHxw89rs6MWhcwODgxMig4NjAwMDN26cGx6eLhYnJydHk0xSisXi9PTw8I0bY2O3bk1cmJqaGh9/lM/nT46PjIyf/N/k87gwGkbe4BGDOjn86RnDeLHVWeWNguIBAETn6evTd+5Ccnn0ytAwUYCQBmSoCAaSIUyDQIgxIKAYh+LRPQokf3JwRcmkIxF5ugVE1K1yhi8QjPwZGi+97bTXFtVrWEG4Le9Onz595/aD+2fRFKJQheck4cqVySRFFsg2MIENjMjfU0nXyBVmoyXpDwyc3BPVClyoHgyaqCE0DuPlteIxI9ORO5y7kMxdBqU4hBSRLOGhbAEWHwRMQLga7dCQvMq/Iaw18GwYRNCGfhnZvB7tthddMw3BHrmLgHH/5tm5udnZ0VT5TIBBECRgXCRVLAR09I+q6ZQuR0jyKobBEIwU/oRa0DebB5+3dlf2PQnB6C3W6/X9/fs3b55F5uYuX4ZDbAQl4JUExKDYCgKRWvV/Wz4onY5UEol2ICwAhIOnM0Q3+lNebm7+fNdZWVpQEHdx7RllDoRZRBk0IpEMjE1MnGeIakf/HZ1CgNE3sIGDDhi8lVW4IzLMSJdvaAgkm81ee8kjCOLVd3YIMprpgkAMTfOzWGR4BQBDe+P8+ampERVgRjIQrejv0H0hCCb1PwwkgaBqvbTkJjeU54Ag79/0Vp+75m9GzOZFiTiM415bUCjq4OIedFEPobM+IRYGbYGlRUEkRNEcIgr2MHNp6FTCJHayTTZaKMwGLNNh6dBSoRKF2wtk2C33EPSv9H2eGRttI/qsM+rym5nvZ57fi+q7cuUKRO7cefvzyxdvKAA3PMudB8pFRlEwdFIhLk1wHrUBkQkzLq6F8A8HBrG56aQmu/+fcXcNRXkx7ry8o/lkWX/+8vWjm+fuuyIy0Y7bQqezGo0eLhaLKuaBPKYCzAQHlMWlVAK1gUwy6cl4GjJKxeEfyLrAHSkQ2MNDYcrCPU0AlfkXXUdkbdx81/BhxGNJv/Ht9eMPH5y1jm+/ctEiFzvPYIXxM7zQnFQdGf4AIDKiIiIIxRpS/Z4ddBPvLINMTYFIBA8Gx0yVQs6CHfNvEXhI71rrDCDCs1a5+vz584/oO1HcfynARqUyJN00zXX/NJ5M0alMKA2ZeZZBrIATwencW5rihGbwJHCLiBCPJx1wbCSAY9zbwC089gEcxvudrHXhsWaabCKzFnMZnAS5fB6PBcYkXYKv11uWied+3ey3bL9ptWzI2KONkdU5sAiZbr/fRGHGdtFu7wkYo0E7GgjbVW3UDXTti4GAYW9EsLVHkXiiPRhVJP580IHvA0y8+LMekPiLxtzuOTzWIOFslRe+6e+Lfg/uSq6IqZNOVPf7qVzGixbv+n5/g/QyUQNLj0WlElnp2EAraVsRWys1tGqxWNW06ig+0CrxZF0bYqtpNJ99QyWijhgcEmIgCJGp8K4CP7OE8PeKMKZQmRa57J/FFWkhtlkur/qJhn4TSvyORfoLlSr11A5t+Q+0qJuw6I25ntVq2eRIq8/Pb2r5YHBAZjBYpya22vY63taD+VrpfHAaVETiux7/RwCYgqPiuywKgCvyyo8HdjMiVMbOom2/vNVJdixSyedtGhQb1I1GmzTKWtSNBcF5/zZZh2Kb9CoWG9DaIUdEQx2O1TR8pK5rzVMYIHGP/VIAwUu6k/2BwOn9zIkTJw4CroTZ3e4Nh822j65dgYUrwnguEJG9g71TRC32yIrqJFgsEg7Hem/kXTgMkXDYojVUBMnRvZBYcxhG9swO678ln0SWzMJx5ihz5MiRdDrdAXZ91B8MPrd8Vf32NXx+FHhguDzE30REtxlzIoLxo9PCQoOMqLKN/DWy22CcgsjZ8CuqdcNdskKhTdoIQcQMsgSGzhDhtFKPUWQSc4GDl1yyAze3FxpkmEQiUQBLzG1CdBff4N27z59bm40qhq6qnszlMGPNdq0ymfzGv0D6gjjkczqaNmj9PIrQXsaWSi0by8sQWV7uUX95GSKpMxBJ4X+94PwIErbWwxy8pa3u2rU3uGvXnCPgGfzOPh0fsd3Qiw4KOMBEwQp/rWUT3nzbvSaXZ9Da2tzkVtGVw6qayy2ACunsVCe9bjf0hTzpuRwcsIToFI02qNauU+lV2CiV6m2LDFRknM2OqWbbNbIUxSJrqNjSioYnUBHMmF2t2u5tlQ6wA6f34nN24N76UAh3PlVYWkRsjryychjw0zR0u/T9e61W+14q6T7DMLtj2LShw4XDwTial0XV4ORw6utE5ebJk6SrqqoTPqTopCgNsohK3XAoZNTwws4uYkLAkSM0HlCjWOzqmJxfoVHNouaROg2R/PQQs3KpPYnvhc8wIZAQUgw0cFuLqosjshL1gMObN5ugsdXyVSqGYawCwxzzqaWi0BEfrg8KpBqGWgSHHbCko8roOcWNEMBV8+t5aS7+G8aG+KOkeKFurKJ5IZFJHzl6nMnnpeeAGAtI9r8DEwTgIkh8XBYsCktCC3x+NxgMvv54LyKswjun3Aehwz4Z+HB9rh5APA8W4TPxEMDV2CMLbYi4hVSvq9ehIUCkKOJQCcHFuf1eCXYS9gglsjip4GQvFHBFtMlk0sKTJ0+e8QaeiIi43DWMXXNz+5jTpy/89slkCksKAs6KLGULLOIiA5KVr3JVuI5QycEip0r9FI6RSrgBMn/kjXlgnQ96xKCCcyN+6nd8aXAUayjTeebhg8HdW6u3mLu7wJwAGdhcOMg26cSiskOkkKqMU0LCoSAsATyhiiuHr6tSC0hzIw6PGFN4C/sMp5KnksypeahAGyA/cJsmBUwT8fjTe+DTp0/806QPGiKBx925iYXrwSYYNRkuSdTzYBFx8DQyicwE7kA8yK5evQpflEFu+Uxm72VyhlPy5yLtPGdX4gQT2c9EIp8+weCS8xuqDwaiwiJILumBNykiFU9j0ygs4lmEnPwADjyk2T7tOjnXj8dxfWzYgaQn4eEJYJsIzxYNp4FA4DfjMUsAfCdBRVwePHhwVDjigihCApPHjAj6S3Yi4Q6+jFiwxtHjB/ejmnIfoMTZ94MAtt8rN4LuQBx2MlM7eEx/qNk9HosFePHiF6V29zJDFMcBfG5nRt7GGO+b93pmrfesltbeWC+ti01p3bjhVvJy4UJeIiSKkhWJlBDulJc7Skq585Yr8i8okfL9/n5n5uzs2V18d3d48uB8nt85Z845u97tu5++fHn7/Oadly9vSF93wruSSCwEXcYY+LIMcXRasY4xAbhJm5VGQ1pX6ZcQo5cicrEUUh09a3puD6lAHnp7cVtETiArMFbzSVoRQqOEKUJm5ekpB/sVHJw0dLBlw4423VsAspGQSjHsfTaLTdObeplHwbx5hhEog+daRJDx8KEHxHvcIJkbaLltv/YX9jD2LY06ONatQwtCBR0sSOAniZ8l6E88vlmFpNIsREbvgOql8ociUEYQ6P7QR3Dw8FAYhLx8eefmi+dvvzBrNDpmEXTyCuffzSiJJocUHQbCQUFIIvHdqCTF+KmwdW5ittXESMQhVVAE42vgeCiMKw+veNfv37t316TBaHsY/Tk1K/Uly3DXtsGktUQNeCJFR4cFiZI8DiZuoSTVdDy7mwa4gC/JJMZ0TAPB9ypA42dhMZRx5Yp32+ZutVpRAQHZwEqbjVkYJZBYCG8PRmELIvyO9KyIUYerYUmqaRoHfZlEQ++XASHS7eLcAIWNVkPj8RAO4eV+Sj8VIDCmJ2xcs9nuBBCMdUEUHQ04mnCgZ0UOxHAShXSamKIdiJusIuhZCrCMkKFCIMcAefPM5OPHZ2aPA0VPFYN0IwYJJRo5zcJMhYc4bEFYyRYLspuhpY8hYUVSgP8FEhOSWkjRQQhCyLFj3qXjpx4dRB5eufBQuyUuitAErY2rl9sNGsIhIu23jroUhI4YBdndbhuLoxEJVj9py2m27TxWAgiCBjkIB3L86dPXr4/j8fr1U7PO6vXzB1itYwLOAgcga5TBDlYsSMyCtDXUMBalLkJYEHYV8yPDyzqyryYZCb/XZRACAiOQ15rLvBBScCT4bt9CJHDUCDH7UbkfEkLHeDqi9lh/qMp1LAkrJ01Wjqb4O0oIkX+VAjd0MOfOnTvjEYG85svPJkFBmBAyi9sNC9nMITKLG1Ld6sjur6r1COGYMCpj7YgSQkbGQMYDgm91FSiuMlTiqUPj2yFOQQZp1mfZkwyzR4KDG0PduKFAjY22IKUJ65lxg7NrQjsJ4r86tHNhzGpBQpsk0ixYoAgwcsh5A1GJMMIwH5vNBnaJeQjharHGXT3DEq2po2vpCJGKkDFzYCAZ2+1nZR8d3FhQEkJyhGUgZGTxRIBVfBGSKTg4CVleq1kKKoJ6mKMa3FZ4g1xd38i7NSCZBJBpbkiZ4ECGSnIIADRYBRKd65F4osghvuOIwiBtrMYBg6XUBFKbDwYjSxaOeEjYCygpgTIOEDcKGekIiyWJCVGEZfCXKFOcOXPGQhBANJaBJITwqAQZCuGaBRKFqMRC3IokFuISwtBi9C0efGkMRY0g+EA8AGSfyyTWEeVJfIEwNQYOjhFAlqpDIZDUNzZlDBNCCSFuHIirocJCpCCOgwEjj0eHBhCV0NEHQQWUshkUFISQHdjJ02EguNtnkN2oyJgLcSsyOrYiYegwGDhcCCXG0Q8Zv3ERbxoa1EMgGwwEDoFsxoBvNGUFJRVpl9Y7o11nrfUKUUrg/k6Trej1Bh2C0ovphZzk66SFIIUhAk2SQeq8iRuLQHDiu62WQeYrZPOaRrUlPQGSNqYuZwKGgo524me74Gw/4qZncyWDZFDOnNly8eRFjWcMZw0k0OGW2GDWqiwyR7TGwXNC7Vt0EIIjCkxd9SqXtbLVhQXD3QYARm7tvLNnSfszno/x8pqHXbpKdCVTrBlWYm9ead68un3bu2zzOl9oGQKfoU8IKiCSAkQ3wHowO3/P0j2bMQEjnZb8dxFGiY0iSlx2ocYEdPieQjGVagUHJlWcPdjMs9sKJ/d64n3rSavFvyN1xLKXDISQ6SgJJUixIsYBEjrZ5tV6ltWJKUFJrME6uGrkHpFBu/H9MzbOGJwKAsn4SW49dBDdvS15w3hfPmW5fz+lflLfZMdV4/RFaLlQGJziLrIQPYZfh08QLjMQ2WqwIqZL4WmSLRqxf1ZHBY4RqWyFYw7bk2hsq5iryIPHszfh1P+ARxBDGiGT/KRczpbecsUaZcYiSgyFBcGXG2q1pYiWY/uWLdsJqeoOm/8lp63+0CWLxlbalMDSm6028nt2rYkhmuOmjXz/2u0cRlrh4X0e9rh5ZFyF5ZL88Bhex9r++ApLklFYEMAwbe0wEDgAWbocg12ObqBAJvSvtvJFI0uSZd68Jp6L3WQfcJmyoLR+3C4naNgYIN39Rw89OVz++s778OHD0ywxDjL8qKyzDXqErrrHQkJEUoAsL0C2X9wxq9GUcenL1goFGbzU4lrLeS963sDw81JTyqVxTlT24123++7Q90MCefr6qXwEghd27TAqceHaM22OJfFiQERShMi+Fxd+DuriztrcShpzf8puBcjwOzvm35iRN3ZtJjP6q4aOOavKPe2ZqTEYVmTfu0PvugsAIYLvWTE+MgASTWrOsBI4LAQSQhauu7hzx/JFFUwVkwK/AHEdu3T1OwnPoZmoT2TKAoHMtA6LOdwNWn7Q7fgLul3v/Knz5/FO6AUmpGMQZDEhKhHI2l6I3E527qhtWFSZF3OqzJcoLsTdWAX+HM2Qz5L2Q/Sf0fCQolQqlcvlMPTMG28KQaLyYIiUhIEDH2u0EATXHYRwGZ9DxhTiOgzEz4OPo/l4GtEUG/6eY50A4yAFD02MJUKJkgWAGMRoyDxCkIGQPXu4c69tWzK30YzNohEZBhnnQBha3BQgSKFnsWu1Osnua4F0LTjOmvRCZlpIKZqUVnokOYQnwnsYOLiOXy2LRjSxAHEcnH4h0S1UiPh4SLKDKxUoLcwhlqJDfdyud18PH752unu4y1kL7YdBw0XJIIg/DxArwVhXCKqhgQNHdQqJAYEkwn1kGGS3cYwMORYyZPo9fPj06a9fu+V3gBw5ayHIQMj4xVKStUMh3LMLBBI9jQdk5mhI+Lc4EMsQyASFHP56ej0hkqtnWRn2LBcyoTyFEFsSXM0YsRC+tVVvVFOWRCDDKzLBgQxLDtEUNKTwPtIe43709GlPFFevUkKIU5FrgPjxvidHjx49dOjQLYQFMRVRyUpA6gwq4kAchwMZFULKBlJctTFfu61Wm8up0liJEHFQwhu7hVDx7tdUQML451SbH7YiiEA2L8HpLyGdFigCSQDp71v/C2EAwQ6NDjctJCppCFEJkrIRdvp99/sz2k3IpCKEIWSZgXBzCAchqYEkDmRYRZIIjXUe1jECEgStOCwzUfSHlHOPabuK4nj/ZThkPEpaebOuVsazI6yhBmh5l1IwFUJj4wBpwGRkMHVOHZqK7xcYtlBR3IIPBougODqdLjqMbrplE1/bMOo2CC7GEdGxOVzi95x729829B/9bjKGg/4+Peeee+6591wVzvyxtg2PP4oVXnRIcGYnigDIpn3St9Tq6STiUCwCodLIuwz5JfX1VDdlkLUKCL2oSE9k+JUgtHKTVQV8FMInq0kKSAKXkZclWzyP9G19ou8Mci0M9pGRXQwyOJ7dhBJuKYOw1OqlxWkJEpasT6fxvqhWL14NAskzJ7L6wDsfiH2KRdgScGT5+jJqcfRlDlCzGGI16iNSkkMBWR5/L3yzte+JJ775ZiuF300dIwyy29/UYEzJLYmOuUVWDRb7briVQW4TIDaAzMMgQZBMySEW7VQCzgGJtIgCAgxKh+j9USwiSxwSRAoUCggcjDnoG6WuX5FcAgjPIwkEsqkDp8R8vhfu25jt1mXmp2KNemtADJIgLKIHyCW1+soyEFq3G+lwmi4lDc4FEHpAdi1hDywE8NgijEqLBDgUCmmM5VJAAgBSEqS/r68/YetWFU7/NI+M+Xx3fPX11xVmMkkIldNvSSDdAhCkZCEAYZMsqdX3rhFZ/K4sBtm1q9kImel8qhPlBwYJYTehpS4bJAFvBK30YGkJEiNBIhXBZopFFJus+weQG/iXBImk9SxFrUxj3FPb3ht8bWr0F4BklKMSLScCqnkDBD8vnkBgkofV6stgIINcnp8ey0s5Po/Bf8WIg6l09o8Mup63ongCkHEcBgmNxmIvnk1yZwAEGALgttvW0n/0ESjLYBhEmc4lkxAdKotB8kvxQVVhrvA9tX3wvbt/+OXzCqcuhfZsuDLFGSlAoqLAIUAuwyACZGQ+9vJiXsr00q7mRfViKzjQF1ChK8+v56yWmlyiQhMTGIVynFUR8Qwmt6wi4XoEQmUOtvxaCB+kk10PEvi+G8FxUzB+CZCQSLJutB07Vtt8vu1DM5/2fvVLRQUO7zVz8JEHlACCzg76DCD71Op5k7TI/JXpdzMXp5uhy/NmCWJMK7Hzpl08tXhWR61L5Ie4ldYLeCJ8rpAQCGwACglCNpFexrqK5EbpU7cwiTK719vr++z9faiiYLAPDR3y+Q4NnTzx6qkfqS10s9k4ksunGJIhgMhOHYDAIJdMMtnatXgpL2VpyQgdnzVvJpAyZ0ru+hX9d0XGRIWLbjigwAoctDhsQTIPFSTyoZW9eP4a6PgbElevo2xrw2ouT1FxD1nVNXXYrVAfRa3QCxdUg19sA8jJ00P7/R5uGyl2j5XnbsJ+czrEIMlhDLJvWj2tkSCiZDq/pGuFzG4CKWPPYpD4iKIt3G8ZHkU2ESAJkMx+bqX6rxSVq5VSttjuIjFIVDgpCuZMTFxNyxVYkbZaqToGXfhmk0zjAfL99yt9z82c3D/gbRDdCnGbW5s7+BwAnAggNpSXuB+KJkPKgIMgGfNLbpzyhYrRBMQhz97fj9AbX+RobzcApaga3oWHwvhlZxHjnwvy0XZSPR1CkwfvIDtXYOgZ6ZtWR1VHsGXD4abUMZucHE5reGxRwjp3xcRcOLO1Pua2/idiEs6cUQ0PrZw9d3Hu7MLP3R4BUtzaPDIiTr5mAUSr1wsQGEQLiwCCKqYEsrRUDAv6Zqnjyumqi9WkrgBHCAxiqCQZHOEbEhMTyUNCOX8iEnatmOh6Vkk+bxhzhGESKhkHQeCiDtlK+2BRkcOBT5MRNbBsQ26Af33o+bieuipsPGk6clDEfvfcuUvnFub2+7MlyOYxDOExBFWdDiAm0QFFBrlMIEkkgOTVGa+offiGpXlEO1ddVY1FH4ayRjSYDVaWTV8Uv24dHAQFEng7lUTI02CQEDs9df36wtxMIx0kzswtrJcmgWsJkEgCMRgqDczSDhvjU7ypdGgnxl6Co31vHNz9e0CqubnZi+cu7Nt+erLNQxhUEG4dGxtrJYepAEiSxkb7FDTuH0aSQiCyGO9aOX3Zt/Ki+orTlVdQY7IaIuJDVoTRxGnRWLjJXauPiN8QFU6NcPHUcgi/IA7EYzvtPtjX55cb3cVlxc6MNFrL2AmESjAgQSBjELasoZ058AlssioUG7Trc3GwuMn7wK+sP//8U3X2t7mFueObRvweHiPgAAgEDkDh6bPWkEnCyCDpbBEFxDlLGfIVtyuvprGlcsuD1VFYS2o13AXPou4p+DZ6d2AUfKDJBO82Rgi9+XCP/JRWvAy8ORM5QTRAuHJBIDzPhTsMgkS6KqRnEHthOeqbnqa2Y68ITajOn585vzA7tt+DfhIGUTjEJJcSq9HLxsHrQVDzxQBx6/Jqaq3t4NiwobrIZqFGthyh/ML0VIQ8inssgHBAiolOrUf+EJKak+Isi8OLuo3lZBL2rbuESTC5hLJrSQYphwDJh0u6NzY9+sFPP/30MUn1129zZ+e2be9ukCDoyxccxXE8YMwZKIXqmQNpI0ctCYKyFvcx6OoKTMTBIA4rnEpTKM6cUjACCJejGWcVtgjgM/Cs0lIGyWcQvCqci/e7wMcmIRIJwhjLQXRu96Oe7DYB0jsKkIWFuUO7m25WQMxm4pAgbmNmkjYAQiQmmfzGFohTNRl1VY2WyvYtRFL94BarVasV+z0kcAAk0GYdhr0OKBIEBEIekmkuWynfsLRcOo9eL0aJAoKgYbgeJKRegjR5R9keo6Oq82f/ODs3s/NmkgRxupkjDjZnEOFbkgQoQoSTQxvwNM4VEIMtnTcSpezRIk9zEAxcizlCV2DrjUCiS8p1xXFMgo2ilPJylJQEiQRxGGxWG98oAYlQHBGvgGQ3eI4yB0AWzv58dm5hQIJgfnY6gyDU/6HLkCCMokgeAKcoZrJIEMhh0Acw7CxwOPQQQMJQWqeEJBQnp7BBF8LBx+gGCL+D3KVBcXiFApJMPqBFGIeYBT6KbI5BzE5vdlPTh6MSZOaQU7f9tJ9BiKOnx9VjJhJM2GZxYCZHi+gNsXdA3EEo0Eo5SLUwCJM49NgdFAdnUyH6AH4DSPAMq0JXU16F5Y0AiVxRmpvhhG9JlGJKv9MKU3lFlCBANPC3dH55McuTQe4CCLWCbNzoacj+ARgEMjcT13Po9CSDlIEDt1YgHFFXk1GewtQwR+AiE1EvRx93hNhnsmFw17YEQLYAhJouKFWjs+604QkOG0jgWZhH4FdRDFKSuiIGFslJkSCSRVnbEQhtAyD06WFLeclF9CqaDxG3y7G2PvjyzqaGo4Txww+qp3wZxudmTtNgv73CCQ405aOhXXRFiQZv+ARyHG6cBwMyDUkSARYCabwKxKDn1pmcNXyfQnohOQZLTz8mHvv8UFhyeiGqkmhpKJEWUVCKdciho0MjKTcjEKoylUbjVcW6iAqyMf0rMJMizpx4bLff+/lE7ysvjX6lGtsct+3QzEkvhV8XzFFVwNckYA86R6MBBHkTJxhINAgklCRAIAlS2wIQkGyhsZ6uwY0wNfiixWpBQACJFDb/Ie7GT9cgQMEHczKNFTxGpOTyDE/OS93oUsooYbxQ+hssmSqO2drFUeQvTgxPdj/+zsRHn/YeVQ2fPI3mkfHObI/Hg/sc5PUt8uYAyrLw/MyBxRLa5wUJYcHUAqSxsREggqO90mbDV2pqAAKZsJtCIHxxT7BVmtb/GpAU4vhUhpmjlsLBU4pGj7XMhqgoBsFMSSCIEdQewJEEORo1UHjbur/t3tg5Obxn4qjqgQ83NuDyivveOfL927G894FESQuxN/DgAAcUHpZM3hFwLYBg2DNITcC32tsrW6zWltpapsAfjTUMojWBqKAAM09WFtxVTK35mDbTUnQVZUECIQz4vCStA8uy6vAwYZH6VXhV4iiRXSrImY2tbnfnwLi/25vdOT74Qbfqpbcn3nz2g09w7dmB1zUaYgg4AiRAwoVBsEmiLQ2LEsKOH6MwSI0AAUZtQC0tzMEgWk1SQVUeBh4pr8Bk0zvEjLQmDSNERl8iENIhdbNYDVsQo5JpjGDGT0aahiP1JdSFxp0t5c06BFav34/mUK+3e3KgW3X3gakPHjhygC4xe4hmnms27B0KSXiEXgt/T4ZzyQEDkUVMNQKknTjgZkxRKf6SBJkwd8ZWUSSE0GiGtxvTGr2QNinL6JYGAQZGqBBS6VorfiCmDYpaCFvk5KXr4Ym8eIFGmls3x8V5Oru/bfN3eolGdWDq8KljE3ffTzJcLXmDSxFIWGF6TRLcIkw6GkRTi55BGltaBEcNQJiKzIPADFfF6EiqojMFfOEF3u5YC5aODkiblKcLDPUytwtxhkXD1IKpqRKLTJsNIDlwFIwpxkgBSEfHSPOYGTN2U7bX29mN353ZTaqpqcOvTPTeff9D0DU5jVBy4I4jPDPmPkrLBYcEcQRci0EYQ44Xa4vFAhQTOArqdO4yDIIKAkGKCRDIYImtg2dJECdWZo1StZYWyEqyoKmS34tYYKQQBjyLQCg7x40u2fgFHK+HQCYm3oZr4XJWw3ISRmGWInJsmx5MQRLGQ7rLDw8Sawu/kcxRCRCS1mKqyXPhgSGAwH+qsARzwCYGU5WLPUuCFDSaggNMCj9C9O9ZktIymSOLupcxRLDwI5AmoGS3TWY/200gpyZ6D0DXguybV/9XTftcPT1PC/X0dHXt3bMDPc3UFPz8jh079ux9sgtfdrl6yvbswFeF7sHXu67R00+LPxEDzD2IbXv37Nmzd2UWtUbkd4wApDiOOKBvJwcGPlMdPtz71aneKWBMTdmCMhjA8Z81j6dU1PWkIJEcBNL1byDErkhwmF34twEfdK7R0PzT0WxEMa0JwvzX6Z+c3AmQ0aMAYVkV2dT/R0jXFOHtxLu5g/UIOPDA0iLMp4CwFfDrOhCnjt4XuroIqqBzddQInYnu9KYGgGR7vN0gUfX2EshhCaLof1lEXEEkRSjkGnv3EgU4CIQAhc9BCsh1Er7F6ior6+oqq3Cl83k1nlHKybMA0jnQ6fcHQEBy+LDlKj08/9853s26SszkwvreWVH2JMQORAZhkB3PE4riWstAyoAe1EpnkgZVAKRc66mw9/4Xg18M7R5/9o2B7G8B8gqBsDQcJJbLRNIEby8UN+ahKktaR0oMKoG1lpQotTaRMj6kBhThaiGKbYZKq6mmoC6D+aAKHUUtC/4XS1ROrFaEPHG+jSdKKCsWUxlkpw49bLQ943t/HBbxeFXHjh194J1jR44cOXXkSLBVWaOIF+g5dLjJEiCJUEgYRCFZm/DPYi6UqRzWFgaBgIEndIEjCFIDEsEBUBKBxOYZzU5I3I/H69VgU+l3J05899aQv9M/7m9T7Ty4c+fBg/tZtLITLkgqlBK3P+CaM7IJmyQCIBBAFBIpFKsT/40mcUOEASQQ5QDIWoQ5+AYy1MYIpIWtgd9BkII8JAVEynI7jSjqUPMQFL17eHj34PBA98D+N9pUs8dnfcePH5+dPXfuoh3idJ9ULwUqLtBmxZokiGKSKAZhKY707yTrqskkLHDw88XJR3S6qsBRyyCKBIibLp3Bb6gMu2LoJZAtn19++eKLJwb3YxrxNv3NyN28NhGEcRzPtQi+oQdDU2yrleJLkfoW6qGkvqS+UhCLYhCCgoqHkhJEL6uHoqcqIkGjogEVbXNoJdTYgkgVRSoxeFGLJ8FD/wu/zzOz2d1mU/zV0iII+fjM7OzM7E7k799qgZRI9SE5R+Qna/gPMQnrFNMYnjdRCBIgKiEhElE0oqxfs+9IDSKjvd72omDIZ1EpTovDEYCkB3q69h5CotGpsG1bPHV4Ztfl6dGZD2/uMJhEqqXSQjXrZEnh3DnWxkjwDUJm1kMZhUQthDktEEl9N6EmUpRQy4YAxN4vCoRBj75O51lckQvpge2DtC0kPghtS1dBbpGNozfeTo+9eBrh8y8s/C05YsFAWEAOhDULF+KWRCHhJSFmV6cesyUMIm2LgjBPSaNgWmYN5uaNivRSklTShcjKZ6t7hh+vgfN2Eu8ljc69jfBsRnWBmsiDP0ZR997gw6NXM+OLIUg0HgRJoCj+S1YN0ncyHDKoEA9hotfoY5SkVpHkidPtva1xc32dzD9/9mn6x4+7I7nrQMrarkpEHS5k9NXLkUUQc4qsvyTUJExCT+HqtUW+iaUAaQmFJF3IIobMBYD0MNjMVZKxXKVSydHbu1hYYMLG0Pb627dvX+fnv079fj8ZKWT5KmQdfmT9hdjza8+Kl+dVcs6FJEIgtpPUU7bUZwMDySJITCEp07SEoBIQmuNHEnKZPpGbneX0vrnYdIUBp0su1GRgQPcT5iXfrlGRarVAVShLADI8t2LFiJSETnLzKk+s9Dd3JOyBrAZSJ8ERlgCEEcSDaMyCA50diDunwVGDSBcZmwYym4sVn9LbPcj81NTUY05ExzMPpERXh8FjTH7Ig58+yH2FxBWCJFCRYEnqFH7HstWdsuZiISl7TJId2BVyhEULBhMpiWA6+3RAnB7LAfkc49tUJG0yT6Y+Pn58j/usiENnL5UYR7JluWrxucMgVzOZrQJBYiDEgyDBEuoIQpYfjsuM3EL2m8jJogrRpST+o+M6z9RmppBiJZebTSaBJD/TCgdl6NRMqQQIEUiWkV0HRBjyJZ6zFsLvChkKh+DQLFERskECZE1btJUTapHogLh/mCCRgeS0znRhyOKDnevSyuJpIGMzY4+evjo0mzu0/ynXBQ9y7zEBoZCyU5JWVfYgJnsqTSvmhk1nf6iQVoH0KaRO4kHCFRog65pZzLQVYc3fSNTBxyPHuOcXqc0AF9/27lixODJTvPhoZO/bSioAsWGWKxC5YhWcEuOIIVjLyK9XL/UXKhKAdIZD6iVBBqEizdu3sy90wUAukhqkx0xc/IdYC8u87VicaW8vzjyaGd2793QAAoFo03J0HAFSprP709RkXdrZaVpBSLBp1SiNHcxJlkc7mps7aJ5cVbs4Um5CIUg2AtDJV0qXGn2pvURLYoLqAsLON38SphhY+KEQbVpOudzgSAAGRA9yxKuIH+Il4Nghn1+ikJblvAjYEW3bd7wvzXgNZEIgMeklGGRmYq7GJvyl2ac5z6MRhPPHBNJDTROaLyT/xSRSBjIx4YwXfJCzAYiO7BltWhcM5MoSkGA9dtgHw6EohPXe6MFVK/cdTvcOdm/EAcQdTLwDImMmSa4GAjnf388+KXMJcXQDabWQ/Pfv+XzefHPTKBInU1iyIn/qIWTpsR2HUbQQLOxvbI6y58Ki68rOeG97t+kifPqwoMExTNXO92d2swTPwWv8CyDtHoST9Di+7ZnEQEgh2wBCRe67kD4XYsO4SMQSAqEcIDi625ykzIsuaw8C2bR2Dbu163p75Jg6qUcjxn4Y4hjP7B7iwSzuwC/qfulgDfJxcnKSMwGfgbktEE1pKchQXdOym6x2Dq9VWSzZsUM2h+y2qU6EWAfvWLdtbdOylrZEM5CUOJYuhzgy4jgw1D+RGkbig3Bz8jGff2IhGjo7oLP/DbnC0i3lMZspnW37VpqpiV+Cg2rUNrXF0hZlZ56NtCZbkXCHeVDBZYgDCBUZZ+E6ldrY3QDiOCoBUnYanGOit1rjWwfSLoS7IByJuG5/dMRlI7xNl+wNxjpkTwXGNqjMg6Ac3LZu51ZesV7bsnpTAog8Chkk2AhEHeOeA8hFPfiRAVHmIwK5N5l/d+nJcxxACkxF/vF1PiFRRVEYb9smSNoU0qLJKQjTUYYKB5KhsKlFm5bNIopioKZFJCFBi4FJmKFcpaCNSaWrCrUsIikwFSKJMoaI6A8umqIGpwI3Iv2+e66+YKQPx4FZvR/nnHfPvee888h/z98fuPRfkAQgBAkgJHQC0VYB2YB/WKhDuWLKBvMwbxB1ojvBsnNTuL6RjoAd6wWi7RIXXQXCHspA4ADDOHoUIjG1uZHxYxE4ENZg1ORFs4i2IvdZ2fVteRb/jeBfEK0jgMStbIBvHWYbCocJlrBG4jvTuJoj5eyVLttgXGG0hdJAdPc6smBAkqtw7EcHDCTwKyQQd6c+GAMkbBbp5y1JPtan1mAJdofYRSCryVItQEie4zgXvuWCHd9Sirf8Ogwd4YlGdfkV+eHq9oSASnCNzc20NtBnQ5GHahWqAkkexCQeRBwBSMpZJNYg14pDsqkfc5hnAQKDzoIAcXv2QHqU0KYW9SrX2lWvXA6TsO1R+utJAPGKGIwMgwUolbkp6s46aq13zXdUcVPNib1bakiD/XmVFHAAkDyiQq9AzCBGQgu/gh1Ocy0Uj2OQi/1mkCkF+6WBAWjI5XtPVIsjrsuWNEaMROnWPmyiDiTSbjI75XsepRWz+EcAIvXRjW3rN1iStZ4WAX6NhOpiqVC0raZm255IQ53rqHc0xoE0xjWpXwIQk0BSrj8mJBAJ1wLFYRx9zO3XDueknt5qwWEgCUAU73Fl8rr/csjMCS4cCBRAvFE04JbHMEKJcNvmrbalWssSIrpQ6lwspCDZciisqYm0+AhGJHIrPkzVxSSAmGsFJJRAldKkmldAurUY3j49BQrBruNS9I0T04WFHl11tXrkWglCQSQCIdjZv7GQuLMBV1f2JLWmCNfIg/rbaCtF1h4DSKIhho/XkqXUYKJobcSxgGLhYbdX4GALQPjzIJAYSFglgtbh58O6b/lg9xwCQY9W53AWaXE7HjVnaSXBHgGIbScAiQQgsbrG8A46sT1I20YPkhTIhs10o1KwrW9xJJY4ehB+EMi5wCJVINxUWh8Mj425BfG2gSwsfPX65fXlD5pDpVKlNFcpVSp8ysVMdrz9xcjM++tvLszPL82fujf7Yub799nxrjQDRCUmbHfls+OjHSPTT1+97Lt1tjtHrfBIMnftCsXD4ZFXH57dKKQpMRWudnI+yIdhfnfdUNWzg03bt5/MpIcmM5l8R7rQ0Z7Ntk8wbnx6cLBUmkPukirlxcViIT/Cizl0dDKGDETBXi7/qFJxWYuFwULhIaOzJ6jJN00OZdufzH4+Nb+09Pv30s+Zt58+fpzpyHalTUV4mgSUzmdHp1/23Tz6upNaaO4OVVAD6QOE4fij73LUrTqFkutm9uXrqZujtFlRRzszPnQmk2+aTE+ePHa8WC5XKhOAIJHMlZZBxgCRnrO0E+8GwoX+bepsWpyGojA8foA/R5iFCxf+CTcibqSQ3a2bMkiR0MllgtJYaxFFibFUbVS0xRYm2A86bY3Ry2QEEzoqbZIBGaS1ouhGEPE9Sfx4pw2BIel9cs97W7iHczRNpR49fUMz2m28oF6d5OA9phM8m3xoMsbtvusGw/mvb1AeIAtclqcWdapWTpQAKUyRjenbi1cvFnJPgFG4rrcGU91xumNl1XpUS0jwKhZquy+mMhWyl1DOnkkS1xTkAHAuyT9BYllfiQH5ih8Tjoma7SGiCAU/fmERMgmFlqbhYdo2vXGEMDBVs2Vbhngi2XzfHEbDZuB5ritC4b5vvlkubVN4AEHEkfLEY6iWpaUyRp3CxRu0KU0gpa0eQLCZhNLslt6oNIpXrxLLjcruw7qcyaCZgIQURSDIWRkkEliy5c+fZXk8+r5H+g6Qzz8nnwgEDEB5SiDgSEBsbpq+b3IODs4Z4/kg8AQ6LvwVNYEbLufz2WyO3O0IOC6pZ4YLgMAVfWBAae+6cb4NrbVHD59tNGq13JPrNwCSm+YHox20QagqiqU/qTQKtANfvFHpdPQydplpRhSFxq/ICQg4ypahMLW6E9du2fve/VjHXoiKAM+34MJpPCG37qUgph+GridCPxRCo5kVePauwhiTOF+VuGkzifk/ZqT9WLNl1HzvLZgv/oEQCqnVGkCO4/QcHRVXOru1pERnITey6lWAdLuKYujr67ni0dOwz27nxRldTkEYW82AhHEGI7GsZRjqWpZn6909kJD0LhoOIF7gBcA429Pp1tYUMPfJI815FM0xPDq8ZxmJCZD5pgBasPDDZjQUvgBHivHlj5Y8I/kJCHGkKNRD0BmQHLQkvNd51ngS5wkUNuoTo9rdefROVmRr9OBt6eLRo4UGOHZrugIQkGRWKQ4kQiEOow17qn01i8v0GCPWzvZrqNUDT+wINT/S77x40VnZ32/GQ5wFrR5HFDEzvxC+GYrXb5ZBtIQxIkTVX0XRcgaM/dkPJNPb/4PkVTwoInEcYOCwdefusUoDcUVlIdfrcrneHbVpXbP2Ll+6WSzmKqjJgmrQOqfRJ/kxMDxkmybdFiuMhoAdTHcgYtiKtQnB6bHV0Zl0uu20NctYQeTPomDhuWuqaftC+Az38H1/sfnqtQeLky0os3Y4jOYQwIIANgmCswTi0uhTkLV+v5+C0IRspyCgSEHGo3FZAUf3JYoBXM9V0L2pVgMIQ94VFPdpY6QUBHOM2/dfTxFC/2mTIG6RzS+QPfC+//DBvZWDs/nQFbA7fRmZYWhKHPeBc1otrKwt18NStRDCI5IfyzmZPYDdPU86G4PgOvPv567FIIMU5NGVzj+QsTIxRuOJUq7vfbhculTaWKdysI11gCArEVIyq7KpmiQ8HIjuqHGuDZAE8B9JulqB4tRJast5jr5SG4dWDhwOQkyARMLMMtiEjG5jeJwlTD4O7hBzgQmK3uDkGqbI8znzPde3OZNwCQdL/NEJSHXgbG/BIwCBQR4XCs/GctnABqxV/fASBUxy4EDaU2ODQGRlMolXKq6JMBV6tvUhO5PhPWxLbU6fQ2CAaDbQyRgYx6hjcK1BRWYPoB/4EbIa/pK2JHRMz8mDEkaJ/7E1z21tuhQ+wvWax7/Mo6a3cAOAYKlJ+9tmmI046yWhBZA7945VcuBASlDxWX1i1XeqoweXgVEqoWFTqQSQxkalopfpNw45fTWbF0IsRKqwr6JhttRrAYRscTudDHS6Po9+oycIAxzAOHRg5Telrlh5fBImOAAAAABJRU5ErkJggg==", "public": true}]}