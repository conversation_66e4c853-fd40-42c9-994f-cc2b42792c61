<?xml version="1.0" encoding="UTF-8"?>
<!--
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-XML_13-V1_1-20201110-A.xml
   Path: http://www.openmobilealliance.org/release/ObjLwM2M_Bearer_Conn/

OMNA LwM2M Registry
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 13.xml

NORMATIVE INFORMATION
  This file is not part of a Technical Specification document.

  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

LEGAL DISCLAIMER

  Copyright 2020 Open Mobile Alliance.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

  1. Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
  3. Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived
  from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.

  The above license is used as a license under copyright only.  Please
  reference the OMA IPR Policy for patent licensing terms:
  https://www.omaspecworks.org/about/intellectual-property-rights/
  
-->
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M-v1_1.xsd">
	<Object ObjectType="MODefinition">
		<Name>LWM2M Bearer Selection</Name>
		<Description1><![CDATA[This object specifies resources to enable a device to choose a PLMN/network on which to attach/register and what type of bearer to then connect.
		This object allows via remote bearer and network configuration to overwrite automatic network and bearer selection e.g. as supported by the UICC. An equivalent example for overwriting automatic selection is a user doing manual network and bearer selection on a smart phone.]]></Description1>
		<ObjectID>13</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:13:1.1</ObjectURN>
		<LWM2MVersion>1.1</LWM2MVersion>
		<ObjectVersion>1.1</ObjectVersion>
		<MultipleInstances>Single</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0"><Name>Preferred Communications Bearer</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..100</RangeEnumeration>
				<Units/>
				<Description><![CDATA[Used in network selection and, if applicable, in subsequent mobility management procedures:
				0: auto connect
				1: 3GPP PS preferred
				2: 3GPP PS GSM (GPRS) preferred (including EC-GSM-IoT)
				3: 3GPP PS UMTS preferred
				4: 3GPP PS LTE preferred
				5: 1xEV-DO preferred (1)
				6: 3GPP CS preferred (1)
				7: WLAN preferred
				8: Ethernet preferred (1)
				9: DSL preferred (1)
				10: Bluetooth preferred (1)
				11: WIMAX preferred (1)
				12: 3GPP PS LTE with CIoT EPS optimisations, User Plane preferred (2)
				13: 3GPP PS LTE with CIoT EPS optimisations, Control Plane preferred (2)14: 3GPP PS NB-IoT Control Plane optimisations preferred (2)
				15: 3GPP PS NB-IoT User Plane optimisations preferred (2) 
				16: 3GPP 5G-NR preferred
				17-100: Reserved for future use
				The Preferred Communications Bearer resource specifies the preferred communications bearer that the LWM2M Client is requested to use for connecting to the LWM2M Server. If multiple preferred communications bearers are specified, the priority order is reflected by the resource instance order. E.g. the bearer which appears first in the list of resource instances is to have higher priority over the rest of available bearers. The LWM2M Client SHOULD use the preferred bearers with higher priority first if they are available. If none of indicated preferred bearers is available, the LWM2M Client SHOULD wait until one of them becomes available. ]]></Description>
			</Item>
			<Item ID="1"><Name>Acceptable RSSI (GSM)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>-110..-48</RangeEnumeration>
				<Units/>
				<Description><![CDATA[Provides guide to the application when performing manual network selection.]]></Description>
			</Item>
			<Item ID="2"><Name>Acceptable RSCP (UMTS)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>-120..-25</RangeEnumeration>
				<Units/>
				<Description><![CDATA[Provides guide to the application when performing manual network selection.]]></Description>
			</Item>
			<Item ID="3"><Name>Acceptable RSRP (LTE)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>-140..-44</RangeEnumeration>
				<Units/>
				<Description><![CDATA[Provides guide to the application when performing manual network selection.]]></Description>
			</Item>
			<Item ID="4"><Name>Acceptable RSSI (1xEV-DO)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Provides guide to the application when performing manual network selection.]]></Description>
			</Item>
			<Item ID="5"><Name>Cell lock list</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Comma separated list of allowed Global Cell Identities.]]></Description>
			</Item>
			<Item ID="6"><Name>Operator list</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Comma separated list of MCC+MNC of operators, in priority order.
				Resource "operator list mode" indicates how to process this list.]]></Description>
			</Item>
			<Item ID="7"><Name>Operator list mode</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Indicates whether resource "operator list" represents the allowed operator list (white list), or, the preferred operator list.
				0=preferred
				1=allowed]]></Description>
			</Item>
			<Item ID="8"><Name>List of available PLMNs</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Allows server to see results of network scan (e.g. result of AT+COPS=? as per 3GPP-TS_27.007)]]></Description>
			</Item>
			<Item ID="9"><Name>Vendor specific extensions</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Objlnk</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Link to a vendor specific object.]]></Description>
			</Item>
			<Item ID="10"><Name>Acceptable RSRP (NB-IoT)</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>-158..-44</RangeEnumeration>
				<Units/>
				<Description><![CDATA[Provides guide to the application when performing manual network selection.]]></Description>
			</Item>
			<Item ID="11"><Name>Higher Priority PLMN Search Timer</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[Interval between periodic searches for higher priority PLMNs of the same country when camped on a visited PLMN, i.e. roaming scenario; based on SIM configuration, EFHPPLMN [3GPP-TS_31.102, section 4.2.6]]]></Description>
			</Item>
			<Item ID="12"><Name>Attach without PDN connection</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration/>
				<Units/>
				<Description><![CDATA[False: attach with PDN connection
				True: attach without PDN connection]]></Description>
			</Item>
			</Resources>
		<Description2><![CDATA[Notes:
		(1)	Remote management of this communications bearer via LwM2M is currently not supported.
		(2)	See [3GPP-TS_23.401, section ********] for the Preferred Network Behaviour indication used by the UE to inform the network about its capabilities and preferences.]]></Description2>
	</Object>
</LWM2M>
