<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="400" height="200" fill="none" version="1.1" viewBox="0 0 400 200"><tb:metadata xmlns=""><![CDATA[{
  "title": "Horizontal inline flow meter",
  "description": "Horizontal inline flow meter component used to display flow related value and render various states. Includes pipe fluid and leak visualizations.",
  "searchTags": [
    "meter",
    "flow meter"
  ],
  "widgetSizeX": 2,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "var value = ctx.values.value;\nvar colorProcessor = ctx.properties.backgroundColor;\ncolorProcessor.update(value);\nvar fill = colorProcessor.color;\nelement.attr({fill: fill});",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'displayClick');"
        }
      }
    },
    {
      "tag": "border",
      "stateRenderFunction": "var value = ctx.values.value;\nvar colorProcessor = ctx.properties.defaultBorderColor;\nif (ctx.values.critical) {\n    colorProcessor = ctx.properties.criticalBorderColor;\n} else if (ctx.values.warning) {\n    colorProcessor = ctx.properties.warningBorderColor;\n} else if (value) {\n    colorProcessor = ctx.properties.activeBorderColor;\n}\ncolorProcessor.update(value);\nvar fill = colorProcessor.color;\nelement.attr({fill: fill});\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = ctx.values.critical && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'displayClick');"
        }
      }
    },
    {
      "tag": "broken",
      "stateRenderFunction": "var broken = ctx.values.broken;\nif (broken) {\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'displayClick');"
        }
      }
    },
    {
      "tag": "fluid",
      "stateRenderFunction": "var fluid = ctx.values.fluid && !ctx.values.leak;\nvar flow = ctx.values.flow;\nvar flowDirection = ctx.values.flowDirection;\n\nvar elementFluid = element.remember('fluid');\nvar elementFlow = null;\nvar elementFlowDirection = null;\n\nif (fluid !== elementFluid) {\n    element.remember('fluid', fluid);\n    elementFlow = null;\n    elementFlowDirection = null;\n} else {\n    elementFlow = element.remember('flow');\n    elementFlowDirection = element.remember('flowDirection');\n}\n\nvar liquidPattern = element.reference('fill').first();\n\nvar fluidAnimation = ctx.api.cssAnimation(liquidPattern);\n\n\nif (fluid) {\n    element.show();\n    if (flow !== elementFlow) {\n        element.remember('flow', flow);\n        if (flow) {\n            if (elementFlowDirection !== flowDirection || !fluidAnimation) {\n                element.remember('flowDirection', flowDirection);\n                fluidAnimation = animateFlow(liquidPattern, flowDirection);\n            } else {\n                fluidAnimation.play();\n            }\n        } else {\n            if (fluidAnimation) {\n                fluidAnimation.pause();\n            }\n        }\n    } else if (flow && elementFlowDirection !== flowDirection) {\n        element.remember('flowDirection', flowDirection);\n        fluidAnimation = animateFlow(liquidPattern, flowDirection);\n    }\n    if (flow) {\n        if (fluidAnimation) {\n            fluidAnimation.speed(ctx.values.flowAnimationSpeed);\n        }\n    }\n} else {\n    if (fluidAnimation) {\n        fluidAnimation.pause();\n    }\n    element.hide();\n}\n\nfunction animateFlow(liquidPattern, forwardElseReverse) {\n    ctx.api.resetCssAnimation(liquidPattern);\n    var deltaX = forwardElseReverse ? 172 : -172;\n    return ctx.api.cssAnimate(liquidPattern, 1000).relative(deltaX, 0).loop();\n}",
      "actions": null
    },
    {
      "tag": "fluid-background",
      "stateRenderFunction": "var fluid = ctx.values.fluid && !ctx.values.leak;\nif (fluid) {\n    var color = ctx.properties.fluidColor;\n    element.attr({fill: color});\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "leak",
      "stateRenderFunction": "var leak = ctx.values.leak;\nif (leak) {\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "pipe-background",
      "stateRenderFunction": "var color = ctx.properties.pipeColor;\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "value",
      "stateRenderFunction": "var value = ctx.api.formatValue(ctx.values.value, {units: ctx.properties.valueUnits, decimals: ctx.properties.valueDecimals, ignoreUnitSymbol: true});\nctx.api.text(element, value);\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'displayClick');"
        }
      }
    },
    {
      "tag": "valueUnits",
      "stateRenderFunction": "var units = ctx.api.unitSymbol(ctx.properties.valueUnits);\nctx.api.text(element, units || '');\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'displayClick');"
        }
      }
    }
  ],
  "behavior": [
    {
      "id": "value",
      "name": "{i18n:scada.symbol.value}",
      "hint": "{i18n:scada.symbol.flow-meter-value-hint}",
      "group": "{i18n:scada.symbol.display}",
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_TIME_SERIES",
        "defaultValue": 0,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "flowRate"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warning",
      "name": "{i18n:scada.symbol.warning-state}",
      "hint": "{i18n:scada.symbol.warning-state-hint}",
      "group": "{i18n:scada.symbol.display}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.warning}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "MAJOR",
            "MINOR",
            "WARNING",
            "INDETERMINATE"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "critical",
      "name": "{i18n:scada.symbol.critical-state}",
      "hint": "{i18n:scada.symbol.critical-state-hint}",
      "group": "{i18n:scada.symbol.display}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.critical}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "CRITICAL"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "criticalAnimation",
      "name": "{i18n:scada.symbol.critical-state-animation}",
      "hint": "{i18n:scada.symbol.critical-state-animation-hint}",
      "group": "{i18n:scada.symbol.display}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.animation}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "broken",
      "name": "{i18n:scada.symbol.broken}",
      "hint": "{i18n:scada.symbol.broken-hint}",
      "group": "{i18n:scada.symbol.display}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.broken}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "displayClick",
      "name": "{i18n:scada.symbol.on-display-click}",
      "hint": "{i18n:scada.symbol.on-display-click-hint}",
      "group": "{i18n:scada.symbol.display}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "openInSeparateDialog": false,
        "openInPopover": false
      }
    },
    {
      "id": "fluid",
      "name": "{i18n:scada.symbol.fluid-presence}",
      "hint": "{i18n:scada.symbol.fluid-presence-hint}",
      "group": "{i18n:scada.symbol.pipe}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.present}",
      "falseLabel": "{i18n:scada.symbol.absent}",
      "stateLabel": "{i18n:scada.symbol.fluid-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "flow",
      "name": "{i18n:scada.symbol.flow-presence}",
      "hint": "{i18n:scada.symbol.flow-presence-hint}",
      "group": "{i18n:scada.symbol.pipe}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.present}",
      "falseLabel": "{i18n:scada.symbol.absent}",
      "stateLabel": "{i18n:scada.symbol.flow-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "flowDirection",
      "name": "{i18n:scada.symbol.flow-direction}",
      "hint": "{i18n:scada.symbol.flow-direction-hint}",
      "group": "{i18n:scada.symbol.pipe}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.forward}",
      "falseLabel": "{i18n:scada.symbol.reverse}",
      "stateLabel": "{i18n:scada.symbol.forward}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": true,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "flowAnimationSpeed",
      "name": "{i18n:scada.symbol.flow-animation-speed}",
      "hint": "{i18n:scada.symbol.flow-animation-speed-hint}",
      "group": "{i18n:scada.symbol.pipe}",
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": 1,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "leak",
      "name": "{i18n:scada.symbol.leak}",
      "hint": "{i18n:scada.symbol.leak-hint}",
      "group": "{i18n:scada.symbol.pipe}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.leak-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    }
  ],
  "properties": [
    {
      "id": "valueUnits",
      "name": "{i18n:scada.symbol.units}",
      "type": "units",
      "default": "m³/hr",
      "fieldClass": "medium-width",
      "supportsUnitConversion": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "valueDecimals",
      "name": "{i18n:scada.symbol.decimals}",
      "type": "number",
      "default": 0,
      "fieldClass": "medium-width",
      "min": 0,
      "step": 1,
      "disabled": false,
      "visible": true
    },
    {
      "id": "defaultBorderColor",
      "name": "{i18n:scada.symbol.default-border-color}",
      "type": "color_settings",
      "default": {
        "type": "constant",
        "color": "#4A4848",
        "gradient": {
          "advancedMode": false,
          "gradient": [
            "rgba(0, 255, 0, 1)",
            "rgba(255, 0, 0, 1)"
          ],
          "gradientAdvanced": [
            {
              "source": {
                "type": "constant"
              },
              "color": "rgba(0, 255, 0, 1)"
            },
            {
              "source": {
                "type": "constant"
              },
              "color": "rgba(255, 0, 0, 1)"
            }
          ],
          "minValue": 0,
          "maxValue": 100
        },
        "rangeList": {
          "advancedMode": false,
          "range": [],
          "rangeAdvanced": []
        },
        "colorFunction": "var temperature = value;\nif (typeof temperature !== undefined) {\n  var percent = (temperature + 60)/120 * 100;\n  return tinycolor.mix('blue', 'red', percent).toHexString();\n}\nreturn 'blue';"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "activeBorderColor",
      "name": "{i18n:scada.symbol.active-border-color}",
      "type": "color_settings",
      "default": {
        "type": "constant",
        "color": "#1C943E",
        "gradient": {
          "advancedMode": false,
          "gradient": [
            "rgba(0, 255, 0, 1)",
            "rgba(255, 0, 0, 1)"
          ],
          "gradientAdvanced": [
            {
              "source": {
                "type": "constant"
              },
              "color": "rgba(0, 255, 0, 1)"
            },
            {
              "source": {
                "type": "constant"
              },
              "color": "rgba(255, 0, 0, 1)"
            }
          ],
          "minValue": 0,
          "maxValue": 100
        },
        "rangeList": {
          "advancedMode": false,
          "range": [],
          "rangeAdvanced": []
        },
        "colorFunction": "var temperature = value;\nif (typeof temperature !== undefined) {\n  var percent = (temperature + 60)/120 * 100;\n  return tinycolor.mix('blue', 'red', percent).toHexString();\n}\nreturn 'blue';"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "warningBorderColor",
      "name": "{i18n:scada.symbol.warning-border-color}",
      "type": "color_settings",
      "default": {
        "type": "constant",
        "color": "#FAA405",
        "gradient": {
          "advancedMode": false,
          "gradient": [
            "rgba(0, 255, 0, 1)",
            "rgba(255, 0, 0, 1)"
          ],
          "gradientAdvanced": [
            {
              "source": {
                "type": "constant"
              },
              "color": "rgba(0, 255, 0, 1)"
            },
            {
              "source": {
                "type": "constant"
              },
              "color": "rgba(255, 0, 0, 1)"
            }
          ],
          "minValue": 0,
          "maxValue": 100
        },
        "rangeList": {
          "advancedMode": false,
          "range": [],
          "rangeAdvanced": []
        },
        "colorFunction": "var temperature = value;\nif (typeof temperature !== undefined) {\n  var percent = (temperature + 60)/120 * 100;\n  return tinycolor.mix('blue', 'red', percent).toHexString();\n}\nreturn 'blue';"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "criticalBorderColor",
      "name": "{i18n:scada.symbol.critical-border-color}",
      "type": "color_settings",
      "default": {
        "type": "constant",
        "color": "#D12730",
        "gradient": {
          "advancedMode": false,
          "gradient": [
            "rgba(0, 255, 0, 1)",
            "rgba(255, 0, 0, 1)"
          ],
          "gradientAdvanced": [
            {
              "source": {
                "type": "constant"
              },
              "color": "rgba(0, 255, 0, 1)"
            },
            {
              "source": {
                "type": "constant"
              },
              "color": "rgba(255, 0, 0, 1)"
            }
          ],
          "minValue": 0,
          "maxValue": 100
        },
        "rangeList": {
          "advancedMode": false,
          "range": [],
          "rangeAdvanced": []
        },
        "colorFunction": "var temperature = value;\nif (typeof temperature !== undefined) {\n  var percent = (temperature + 60)/120 * 100;\n  return tinycolor.mix('blue', 'red', percent).toHexString();\n}\nreturn 'blue';"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "backgroundColor",
      "name": "{i18n:scada.symbol.background-color}",
      "type": "color_settings",
      "default": {
        "type": "constant",
        "color": "#FFFFFF",
        "gradient": {
          "advancedMode": false,
          "gradient": [
            "rgba(0, 255, 0, 1)",
            "rgba(255, 0, 0, 1)"
          ],
          "gradientAdvanced": [
            {
              "source": {
                "type": "constant"
              },
              "color": "rgba(0, 255, 0, 1)"
            },
            {
              "source": {
                "type": "constant"
              },
              "color": "rgba(255, 0, 0, 1)"
            }
          ],
          "minValue": 0,
          "maxValue": 100
        },
        "rangeList": {
          "advancedMode": false,
          "range": [],
          "rangeAdvanced": []
        },
        "colorFunction": "var temperature = value;\nif (typeof temperature !== undefined) {\n  var percent = (temperature + 60)/120 * 100;\n  return tinycolor.mix('blue', 'red', percent).toHexString();\n}\nreturn 'blue';"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "fluidColor",
      "name": "{i18n:scada.symbol.fluid-color}",
      "type": "color",
      "default": "#1EC1F480",
      "disabled": false,
      "visible": true
    },
    {
      "id": "pipeColor",
      "name": "{i18n:scada.symbol.pipe-color}",
      "type": "color",
      "default": "#FFFFFF",
      "disabled": false,
      "visible": true
    }
  ]
}]]></tb:metadata>
<defs>
  <pattern id="baseLiquid" width="172" height="72" patternTransform="translate(14,-8)" patternUnits="userSpaceOnUse">
   <circle transform="rotate(-90)" cx="-15" cy="21" r="8" fill="url(#paint28_linear_1182_32781-5)"/>
   <circle transform="rotate(-90)" cx="-15" cy="150" r="8" fill="url(#paint29_linear_1182_32781-1)"/>
   <circle transform="rotate(-90)" cx="-16" cy="113" r="8" fill="url(#paint30_linear_1182_32781-7)"/>
   <circle transform="rotate(-90)" cx="-58" cy="34" r="8" fill="url(#paint31_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-58" cy="155" r="8" fill="url(#paint32_linear_1182_32781-3)"/>
   <circle transform="rotate(-90)" cx="-26" cy="33" r="5" fill="url(#paint33_linear_1182_32781-1)"/>
   <circle transform="rotate(-90)" cx="-26" cy="162" r="5" fill="url(#paint34_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-37" cy="5" r="5" fill="url(#paint35_linear_1182_32781-6)"/>
   <circle transform="rotate(-90)" cx="-8" cy="94" r="4" fill="url(#paint36_linear_1182_32781-9)"/>
   <circle transform="rotate(-90)" cx="-60" cy="72" r="4" fill="url(#paint37_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-37" cy="112" r="5" fill="url(#paint38_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-39" cy="59" r="5" fill="url(#paint39_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-62" cy="115" r="5" fill="url(#paint40_linear_1182_32781-6)"/>
   <circle transform="rotate(-90)" cx="-42" cy="139" r="5" fill="url(#paint41_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-21" cy="76" r="5" fill="url(#paint42_linear_1182_32781-1)"/>
   <circle transform="rotate(-90)" cx="-50.5" cy="126.5" r="2.5" fill="url(#paint43_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-46.5" cy="169.5" r="2.5" fill="url(#paint44_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-9.5" cy="57.5" r="2.5" fill="url(#paint45_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-35.5" cy="96.5" r="2.5" fill="url(#paint46_linear_1182_32781-9)"/>
   <circle transform="rotate(-90)" cx="-23.5" cy="91.5" r="2.5" fill="url(#paint47_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-40.5" cy="22.5" r="2.5" fill="url(#paint48_linear_1182_32781-88)"/>
   <circle transform="rotate(-90)" cx="-23.5" cy="124.5" r="2.5" fill="url(#paint49_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-47.5" cy="86.5" r="2.5" fill="url(#paint50_linear_1182_32781-6)"/>
   <circle transform="rotate(-90)" cx="-21.5" cy="51.5" r="2.5" fill="url(#paint51_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-51.5" cy="48.5" r="2.5" fill="url(#paint52_linear_1182_32781-3)"/>
   <circle transform="rotate(-90)" cx="-64" cy="14" r="4" fill="url(#paint53_linear_1182_32781-83)"/>
   <circle transform="rotate(-90)" cx="-64" cy="135" r="4" fill="url(#paint54_linear_1182_32781-33)"/>
   <circle transform="rotate(-90)" cx="-58.5" cy="95.5" r="9.5" fill="url(#paint55_linear_1182_32781-8)"/>
   <path d="m0 0h172v72h-172z" fill="url(#paint84_linear_1182_32781-8)" stroke-width=".57735"/>
  </pattern>
  <pattern id="liquid" width="172" height="72" patternUnits="userSpaceOnUse"><rect width="688" height="72" x="-172" y="0" stroke-width="0" fill="url(#baseLiquid)"/></pattern>
  <linearGradient id="paint28_linear_1182_32781-5" x1="19.316" x2="21" y1="8.2632" y2="23" gradientTransform="translate(-36,6)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint29_linear_1182_32781-1" x1="148.32" x2="150" y1="8.2632" y2="23" gradientTransform="translate(-165,135)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint30_linear_1182_32781-7" x1="111.32" x2="113" y1="9.2632" y2="24" gradientTransform="translate(-129,97)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint31_linear_1182_32781-4" x1="32.316" x2="34" y1="51.263" y2="66" gradientTransform="translate(-92,-24)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint32_linear_1182_32781-3" x1="153.32" x2="155" y1="51.263" y2="66" gradientTransform="translate(-213,97)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint33_linear_1182_32781-1" x1="31.947" x2="33" y1="21.789" y2="31" gradientTransform="translate(-59,7)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint34_linear_1182_32781-4" x1="160.95" x2="162" y1="21.789" y2="31" gradientTransform="translate(-188,136)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint35_linear_1182_32781-6" x1="3.9474" x2="5" y1="32.79" y2="42" gradientTransform="translate(-42,-32)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint36_linear_1182_32781-9" x1="93.158" x2="94" y1="4.6316" y2="12" gradientTransform="translate(-102,86)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint37_linear_1182_32781-4" x1="71.158" x2="72" y1="56.632" y2="64" gradientTransform="translate(-132,12)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint38_linear_1182_32781-2" x1="110.95" x2="112" y1="32.79" y2="42" gradientTransform="translate(-149,75)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint39_linear_1182_32781-2" x1="57.947" x2="59" y1="34.79" y2="44" gradientTransform="translate(-98,20)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint40_linear_1182_32781-6" x1="113.95" x2="115" y1="57.79" y2="67" gradientTransform="translate(-177,53)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint41_linear_1182_32781-4" x1="137.95" x2="139" y1="37.79" y2="47" gradientTransform="translate(-181,97)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint42_linear_1182_32781-1" x1="74.947" x2="76" y1="16.789" y2="26" gradientTransform="translate(-97,55)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint43_linear_1182_32781-2" x1="125.97" x2="126.5" y1="48.395" y2="53" gradientTransform="translate(-177,76)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint44_linear_1182_32781-8" x1="168.97" x2="169.5" y1="44.395" y2="49" gradientTransform="translate(-216,123)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint45_linear_1182_32781-8" x1="56.974" x2="57.5" y1="7.3947" y2="12" gradientTransform="translate(-67,48)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint46_linear_1182_32781-9" x1="95.974" x2="96.5" y1="33.395" y2="38" gradientTransform="translate(-132,61)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint47_linear_1182_32781-2" x1="90.974" x2="91.5" y1="21.395" y2="26" gradientTransform="translate(-115,68)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint48_linear_1182_32781-88" x1="21.974" x2="22.5" y1="38.395" y2="43" gradientTransform="translate(-63,-18)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint49_linear_1182_32781-8" x1="123.97" x2="124.5" y1="21.395" y2="26" gradientTransform="translate(-148,101)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint50_linear_1182_32781-6" x1="85.974" x2="86.5" y1="45.395" y2="50" gradientTransform="translate(-134,39)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint51_linear_1182_32781-8" x1="50.974" x2="51.5" y1="19.395" y2="24" gradientTransform="translate(-73,30)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint52_linear_1182_32781-3" x1="47.974" x2="48.5" y1="49.395" y2="54" gradientTransform="translate(-100,-3)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint53_linear_1182_32781-83" x1="13.158" x2="14" y1="60.632" y2="68" gradientTransform="translate(-78,-50)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint54_linear_1182_32781-33" x1="134.16" x2="135" y1="60.632" y2="68" gradientTransform="translate(-199,71)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint55_linear_1182_32781-8" x1="93.5" x2="95.5" y1="50.5" y2="68" gradientTransform="translate(-154,37)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint84_linear_1182_32781-8" x1="248" x2="248" y1="1.8513e-7" y2="72" gradientTransform="scale(.33333 1)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" offset=".10895"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".11331"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".88736"/>
   <stop stop-color="#727171" offset=".89138"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <mask id="path-5-inside-1_1595_102361" fill="#ffffff">
   <path d="m300 100c0 55.228-44.772 100-100 100s-100-44.772-100-100c0-55.228 44.772-100 100-100s100 44.772 100 100zm-188 0c0 48.603 39.401 88.004 88.004 88.004s88.004-39.401 88.004-88.004c0-48.604-39.401-88.004-88.004-88.004s-88.004 39.401-88.004 88.004z"/>
  </mask>
  <linearGradient id="paint1_linear_1595_102361" x1="165.06" x2="182.56" y1="50.125" y2="69.188" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".3" offset="1"/>
  </linearGradient>
  <linearGradient id="paint2_linear_1595_102361" x1="218.06" x2="235.56" y1="50.125" y2="69.188" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".3" offset="1"/>
  </linearGradient>
  <linearGradient id="paint0_linear_1595_102361" x1="110.72" x2="110.63" y1="64" y2="136.01" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
 </defs><rect x="14" y="64" width="372" height="72" fill="#fff" tb:tag="pipe-background"/><rect x="14" y="64" width="372" height="72" fill="url(#paint0_linear_1595_102361)" style="fill:url(#paint0_linear_1595_102361)"/><rect x="15.5" y="65.5" width="369" height="69" stroke="#000" stroke-opacity=".12" stroke-width="3"/><rect x="387.5" y="51.5" width="11" height="97" rx="5.5" fill="#d9d9d9" stroke="#727171" stroke-width="3"/><rect x="1.5" y="51.5" width="11" height="97" rx="5.5" fill="#d9d9d9" stroke="#727171" stroke-width="3"/><rect x="14" y="64" width="372" height="72" fill="#1ec1f4" stroke-width="0" style="display: none;" tb:tag="fluid-background"/><rect x="14" y="64" width="372" height="72" fill="url(#liquid)" stroke-width="0" style="display: none;" tb:tag="fluid"/><path d="m300 100c0 55.228-44.772 100-100 100s-100-44.772-100-100c0-55.228 44.772-100 100-100s100 44.772 100 100zm-188 0c0 48.603 39.401 88.004 88.004 88.004s88.004-39.401 88.004-88.004c0-48.604-39.401-88.004-88.004-88.004s-88.004 39.401-88.004 88.004z" fill="#4a4848" mask="url(#path-5-inside-1_1595_102361)" stroke="#727171" stroke-width="6" tb:tag="border"/><circle cx="200" cy="100" r="88" fill="#fff" tb:tag="background"/><path d="m135.5 88.357c0-2.1302 1.727-3.8571 3.857-3.8571h121.29c2.13 0 3.857 1.7269 3.857 3.8571v23.286c0 2.13-1.727 3.857-3.857 3.857h-121.29c-2.13 0-3.857-1.727-3.857-3.857z" fill="#4a4848" fill-opacity=".06" tb:tag="border"/><path d="m139.36 83c-2.9408 0-5.3574 2.4165-5.3574 5.3574v23.285c0 2.9408 2.4166 5.3574 5.3574 5.3574h121.29c2.9408 0 5.3574-2.4166 5.3574-5.3574v-23.285c0-2.9409-2.4167-5.3574-5.3574-5.3574zm0 3h121.29c1.3192 0 2.3574 1.0379 2.3574 2.3574v23.285c0 1.3192-1.0382 2.3574-2.3574 2.3574h-121.29c-1.3192 0-2.3574-1.0382-2.3574-2.3574v-23.285c0-1.3195 1.0382-2.3574 2.3574-2.3574z" fill="#4a4848" tb:tag="border"/><circle cx="173.5" cy="59.5" r="12.5" fill="url(#paint1_linear_1595_102361)" style="fill:url(#paint1_linear_1595_102361)"/><circle cx="173.5" cy="59.5" r="12" stroke="#000" stroke-opacity=".12"/><circle cx="226.5" cy="59.5" r="12.5" fill="url(#paint2_linear_1595_102361)" style="fill:url(#paint2_linear_1595_102361)"/><circle cx="226.5" cy="59.5" r="12" stroke="#000" stroke-opacity=".12"/><text x="200.00586" y="102.19141" fill="#000000" fill-opacity=".87" style="font-family:Roboto;font-size:24px;font-variant-caps:normal;font-variant-east-asian:normal;font-variant-ligatures:normal;font-variant-numeric:normal;font-weight:500" tb:tag="value" xml:space="preserve" text-anchor="middle"><tspan dominant-baseline="middle">0</tspan></text><text x="199.61719" y="138.77344" fill="#000000" fill-opacity=".87" style="font-family:Roboto;font-size:16px;font-variant-caps:normal;font-variant-east-asian:normal;font-variant-ligatures:normal;font-variant-numeric:normal;font-weight:500" tb:tag="valueUnits" xml:space="preserve" text-anchor="middle"><tspan dominant-baseline="middle">m³/hr</tspan></text><g style="display: none;" tb:tag="broken">
  <circle cx="200" cy="100" r="88" fill="#000" fill-opacity=".24" style=""/>
  <path d="m253.92 84.931c-0.174 0.5228-0.309 1.1931-0.577 1.6622-0.577 1.6623-1.583 3.2037-2.991 4.1417-0.697 0.3484-1.488 0.6431-2.226 0.8439-1.971 0.6161-3.848 1.2858-5.725 1.9555-1.636 0.6832-3.513 1.3529-5.122 0.682 0.013-0.2414-0.027-0.3888-0.013-0.6302-2.977 0.4146-0.922 2.811-8.792 8.1516-11.067 2.815-27.05 6.112-29.128 6.044-0.724-0.041-1.408 0.066-2.038 0.08-1.113-0.014-2.172-0.122-3.218-0.471-0.764-0.188-1.515-0.617-2.225-0.899-2.038-0.792-4.183-1.396-6.448-1.571 0.603 0.47 1.206 0.939 1.809 1.409 0 0 0.121 0.442 0.081 0.295 0.201-0.134 0.482 0.027 0.616 0.228-0.751 0.442-1.502 0.884-2.199 1.232-3.647 2.024-7.389 3.993-11.304 5.614 3.875-0.897 7.683-2.33 11.411-4.058 0.845-0.389 1.595-0.831 2.574-1.018 2.306-0.549 4.666 0.551 6.824 1.785l0.094 0.054c0.187 0.107 0.469 0.268 0.455 0.509 0.081 0.295-0.228 0.617-0.483 0.845-1.368 1.085-2.642 2.225-3.916 3.364-1.582 1.461-2.883 3.082-4.56 4.489-1.475 1.274-3.084 2.346-4.64 3.324-0.75 0.442-1.408 0.938-2.239 1.085-0.885 0.241-1.756 0.241-2.668 0.093-1.153-0.161-2.212-0.269-3.311-0.524-6.314-1.37-17.674 5.248-23.787 2.873 2.44 1.395 5.027 2.75 7.466 4.145-3.66 0.522-7.133 2.022-10.7 3.469-0.992 0.428-1.984 0.857-3.111 1.085-0.884 0.241-1.903 0.281-2.775 0.28-1.743 0-3.526-0.148-4.973-1.101 0.831 0.724 1.769 1.261 2.667 1.65-0.255 0.228-0.563 0.55-0.818 0.778-2.146 2.01-4.198 4.074-6.29 5.99 1.14-0.469 2.199-1.232 3.165-2.05 1.274-1.139 2.226-2.587 3.768-3.324 1.743-0.87 3.808-0.561 5.806-0.789 3.164-0.307 6.154-1.834 9.131-3.12s5.228-3.036 10.264-4.758c3.63-1.241 9.543-3.131 12.411-2.486 0.912 0.148 1.864 0.443 2.762 0.832 0.617 0.228 1.18 0.55 1.836 0.926 0.751 0.429 1.408 0.805 2.212 1.14 0.469 0.268 0.992 0.443 1.475 0.47 0.576 0.08 1.22-0.174 1.716-0.388 1.783-0.724 3.567-1.447 5.35-2.17-0.295 0.08-0.737 0.2-1.032 0.281-2.36 0.642-4.666 1.191-6.972 0.869-0.483-0.027-1.006-0.202-1.327-0.51-0.094-0.054-0.188-0.107-0.134-0.201-0.362-0.456-0.214-1.368 0.322-1.435 0.282-0.71 1.046-1.394 1.556-1.849 4.157-3.351 8.408-6.647 12.606-9.85 0.456-0.362 0.912-0.724 1.555-0.978 0.644-0.255 1.18-0.322 1.81-0.335 4.572-0.374 19.939-3.129 23.095-4.335 5.049-1.929 5.348-0.685 7.45-3.208 0.335 0.067 7.054-8.7036 7.39-8.6365 2.252 0.4164 4.343 1.9858 6.099 3.4875 1.45 1.565-2.243 6.524-2.272 7.287-0.032 0.867-18.866 17.201-20.503 18.31-0.751 0.442-2.24 0.274-2.533 0.546-0.103 0.096-0.557 1.991 0.264 0.492-0.764-0.029-8.29 2.174-13.599 1.877-0.482-0.027-1.059-0.108-1.582-0.282-0.335-0.067-0.616-0.228-0.844-0.483-0.094-0.054-0.134-0.201-0.174-0.349-0.04-0.147 0.107-0.188 0.161-0.281 2.803-3.378 6.276-6.621 7.203-11.072-0.201 0.134-0.255 0.228-0.456 0.362-0.054 0.094-0.201 0.134-0.308 0.321-1.744 1.743-3.085 4.089-5.003 5.482-0.858 0.63-1.81 1.206-2.441 2.091-0.791 1.166-1.033 2.895-2.052 3.807-0.51 0.455-1.207 0.804-1.797 0.964-1.623 0.442-3.419 0.535-5.001 1.125-0.792 0.295-1.596 0.831-2.387 1.125-0.148 0.04-0.201 0.134-0.349 0.174-1.783 0.724-7.731 4.384-9.661 4.276 1.689 0.095 7.288-3.392 8.963-3.056-1.797 1.836-3.54 3.579-5.525 5.307 1.904-1.152 3.674-2.505 5.311-4.06 0.818-0.777 1.636-1.555 2.588-2.131 1.85-1.058 4.451-0.816 5.51 1.034 0.134 0.202 0.268 0.403 0.254 0.644 0.415 1.234 0.241 2.628 0.12 3.928-0.551 3.794-1.236 7.387-2.029 11.168-0.591 2.775-1.275 5.496-2.764 7.882-0.108 0.188-0.309 0.322-0.496 0.214 0.442 0.751 0.106 1.555-0.417 2.252-0.429 0.751-1.046 1.394-1.569 2.091-0.47 0.604-0.939 1.207-1.409 1.81-2.239 2.828-4.573 5.602-7.135 8.122 1.556-0.978 3.031-2.251 4.184-3.833 0.886-1.112 1.583-2.332 2.509-3.297 0.979-1.059 2.226-1.715 3.205-2.774 0.416-0.51 0.885-1.113 1.395-1.568 0.066 1.407 0.039 2.761 0.106 4.169 0.201-1.005 0.403-2.011 0.604-3.016 0.215-1.247 0.524-2.44 0.832-3.633 0.94-2.949 2.885-5.697 3.395-8.767 0.014-0.241 0.027-0.483 0.041-0.724-0.201 0.134-0.309 0.322-0.51 0.456-0.04-0.148 0.067-0.336 0.027-0.483 0.055-2.708 0.646-5.483 1.371-8.057 0.563-2.293 1.235-4.773 2.965-6.274 0.509-0.455 1.113-0.857 1.622-1.313 0.51-0.456 0.845-1.26 0.738-1.944 3.178 0.323 6.66 1.265 9.329-0.571 2.065-1.434 4.621-0.818 5.075-2.713-0.792 0.735 1.103 1.189 2.659 0.483s23.915-14.808 24.838-26.835c-0.911-1.8908-2.519-3.4332-4.316-4.2114-0.094-0.0537-0.094-0.0537-0.187-0.1073 2.923-1.1923 5.993-2.4246 8.796-4.0593 0.805-0.536 1.703-1.0184 2.28-1.8092 0.671-1.6086 0.832-3.633 0.94-5.5635z" fill="#5c5a5a" style=""/>
  <path d="m221.07 127.62c-1.005-0.202-1.526-0.498-2.558-0.217-2.065 0.562-2.591 1.325-4.575 2.183-2.226 0.844-3.364 0.839-5.79 0.946-1.261 0.026-2.467-0.041-3.62-0.203 0.187 0.979-1.8 2.31-2.564 2.993-0.764 0.684-1.717 1.26-2.106 2.158-0.442 0.992-0.309 2.065-0.416 3.124-0.162 1.153-0.658 2.238-1.061 3.378-0.711 2.332-0.605 4.759-0.351 7.146 0.054-0.094 0.054-0.094 0.108-0.188 0.054-2.708 0.645-5.483 1.37-8.057 0.564-2.293 1.235-4.773 2.965-6.274 0.51-0.455 1.113-0.857 1.623-1.313 0.509-0.456 2.054-1.666 1.946-2.349 3.178 0.322 5.388 1.065 8.056-0.771 2.066-1.434 2.736-1.244 5.15-1.98 0.402-0.268 1.14-0.469 1.823-0.576z" fill="#8b8b8b" style=""/>
  <path d="m210.29 116.29c-0.201 0.134-0.255 0.228-0.456 0.362-0.054 0.094-0.201 0.134-0.309 0.321-0.053 0.094-0.053 0.094-0.053 0.094-0.282 0.711-0.564 1.421-1.087 2.118-0.322 0.563-0.791 1.166-1.207 1.676-1.771 2.225-4.104 4.128-5.365 6.768 0.656 0.376 1.313 0.751 1.984 0.886-0.336-0.067-0.617-0.228-0.845-0.483-0.094-0.054-0.134-0.201-0.174-0.349-0.04-0.147 0.107-0.187 0.161-0.281 3.044-3.364 6.424-6.661 7.351-11.112z" fill="#8b8b8b" style=""/>
  <path d="m196.36 131.72c-0.751-0.429-1.756-0.631-2.682-0.537-0.925 0.094-1.917 0.522-2.896 0.71-0.147 0.04-0.201 0.134-0.348 0.174-1.784 0.723-7.024 3.988-8.955 3.88 1.69 0.094 6.581-2.996 8.257-2.66-1.797 1.836-3.782 3.616-5.766 5.345 1.904-1.152 3.915-2.544 5.551-4.099 0.818-0.777 1.636-1.554 2.588-2.131 1.851-1.058 4.452-0.816 5.51 1.035 0.134 0.201 0.268 0.402 0.255 0.643-0.067-0.536-0.188-0.978-0.456-1.381-0.174-0.348-0.589-0.711-1.058-0.979z" fill="#8b8b8b" style=""/>
  <path d="m198.9 148.65c-0.832 0.147-1.556 0.978-1.784 1.595-0.403 1.139-0.711 2.332-1.074 3.619-0.349 1.046-0.604 2.145-1.047 3.137-1.153 2.453-3.058 4.477-5.137 6.152 0 0-0.053 0.094-0.147 0.04-2.24 2.828-4.574 5.603-7.135 8.122 1.555-0.978 3.03-2.251 4.184-3.832 0.885-1.113 1.583-2.333 2.508-3.298 0.979-1.059 2.226-1.715 3.205-2.774 0.416-0.509 0.886-1.112 1.395-1.568 0.067 1.408 0.039 2.762 0.106 4.17 0.201-1.006 0.403-2.011 0.604-3.017 0.215-1.247 0.524-2.44 0.833-3.633 1.181-2.935 2.978-5.643 3.489-8.713z" fill="#8b8b8b" style=""/>
  <path d="m233.47 101.11c-0.266 0.482-0.521 1.116-0.87 1.529-0.381 0.952-0.775 1.751-1.557 2.506-0.92 0.92-2.089 1.63-3.2 2.105-2 0.856-4.12 1.185-6.24 1.514-0.984 0.158-1.955 0.468-2.584 1.211 0.152-0.013 0.152-0.013 0.305-0.026-0.083-0.069-0.083-0.069-0.165-0.139 3.091-0.64 6.335-1.291 9.389-2.388 0.888-0.38 1.86-0.691 2.571-1.363 1.365-1.11 1.893-3.071 2.351-4.949z" fill="#8b8b8b" style=""/>
  <path d="m211.4 113.38c-0.858-0.241-1.783-0.148-2.641-0.39-0.911-0.147-1.716-0.483-2.48-0.671-1.488-0.228-2.909 0.08-4.303-0.095-0.818-0.094-1.73-0.242-2.601-0.242-1.555 0.106-2.923 1.192-4.09 2.143-4.412 3.579-8.771 7.063-13.183 10.641-0.711 0.59-1.582 1.461-1.247 2.399 0.174 0.349 0.495 0.658 0.777 0.818-0.094-0.053-0.188-0.107-0.134-0.201-0.362-0.456-0.214-1.367 0.322-1.434 0.282-0.711 1.046-1.394 1.556-1.85 4.157-3.35 8.408-6.647 12.606-9.849 0.456-0.362 0.912-0.724 1.555-0.979 0.644-0.254 1.18-0.321 1.81-0.334 4.572-0.374 9.426-0.587 13.796 0.916 0.094 0.054 0.188 0.108 0.281 0.161 0 0-0.053 0.094-0.107 0.188 0.335 0.067 0.63-0.013 0.965 0.054-0.938-0.537-1.876-1.073-2.882-1.275z" fill="#8b8b8b" style=""/>
  <path d="m190.5 107.58c-1.474-0.47-3.097-0.028-4.665 0.32-0.148 0.04-0.148 0.04-0.295 0.08-3.648 2.023-7.389 3.993-11.304 5.614 3.875-0.897 7.683-2.33 11.41-4.059 0.845-0.388 1.596-0.83 2.575-1.018 2.306-0.549 4.665 0.552 6.823 1.786-0.321-0.309-0.884-0.631-1.072-0.738-0.563-0.322-1.032-0.59-1.595-0.912-0.55-0.563-1.113-0.885-1.877-1.073z" fill="#8b8b8b" style=""/>
  <path d="m190.69 127.59c-0.295 0.08-0.737 0.2-1.032 0.281-0.054 0.094-0.201 0.134-0.201 0.134-0.845 0.388-1.69 0.777-2.481 1.071-1.622 0.442-2.855 0.544-4.37-0.073-0.804-0.336-2.012-1.431-2.763-1.86-2.158-1.234-4.704-1.101-7.05-1.571-2.105-0.456-6.694 2.521-9.014 3.199-2.32 0.677-2.716 1.044-4.313 1.765-2.56 1.156-2.575 1.018-3.862 1.527-1.73 0.63-3.526 0.723-5.256 1.353-1.14 0.469-2.199 1.232-3.285 1.607-1.784 0.724-3.808 0.562-5.658 0.749-0.979 0.188-1.958 0.375-2.803 0.764-2.146 2.01-4.198 4.074-6.29 5.99 1.14-0.469 2.2-1.232 3.165-2.05 1.274-1.139 2.227-2.587 3.769-3.324 1.743-0.87 3.807-0.561 5.805-0.789 3.164-0.307 6.155-1.834 9.131-3.12 2.977-1.286 5.031-2.601 8.208-2.278 3.03 0.363 10.175-4.776 13.044-4.131 0.911 0.147 1.863 0.443 2.761 0.832 0.617 0.228 1.18 0.55 1.837 0.925 0.75 0.43 1.407 0.805 2.211 1.141 0.469 0.268 0.992 0.443 1.475 0.47 0.576 0.08 1.22-0.174 1.716-0.389 1.783-0.723 3.567-1.446 5.256-2.223z" fill="#8b8b8b" style=""/>
  <path d="m186.97 26.632c0.126 0.5365 0.199 1.2163 0.423 1.7079 0.424 1.7079 1.286 3.3344 2.603 4.3966 0.662 0.4103 1.423 0.7757 2.139 1.0428 1.907 0.7927 3.716 1.6304 5.524 2.468 1.567 0.8291 3.376 1.6668 5.039 1.1449 8e-3 -0.2415 0.062-0.3847 0.07-0.6263 2.927 0.6836 6.129 0.4094 9.061-0.4546 2.003-0.5582 4.023-1.5995 6.099-1.478 0.724 0.0255 1.395 0.1943 2.022 0.2647 1.109 0.0875 2.174 0.0766 3.247-0.1758 0.778-0.1177 1.564-0.4769 2.297-0.6929 2.102-0.6031 4.293-1.0096 6.565-0.9779-0.644 0.4126-1.287 0.8253-1.93 1.2379 0 0-0.161 0.4297-0.107 0.2865-0.188-0.1517-0.483-0.017-0.635 0.1711 0.708 0.5086 1.415 1.0173 2.078 1.4276 3.448 2.3465 6.995 4.6481 10.747 6.6183-3.778-1.2456-7.44-3.0192-10.995-5.0792-0.806-0.4638-1.513-0.9724-2.471-1.248-2.247-0.7563-4.696 0.1247-6.958 1.1575l-0.098 0.045c-0.197 0.0898-0.491 0.2245-0.5 0.466-0.107 0.2865 0.171 0.6348 0.404 0.8849 1.264 1.2054 2.429 2.4557 3.594 3.706 1.443 1.5987 2.591 3.3321 4.133 4.8858 1.353 1.4021 2.858 2.616 4.318 3.7316 0.708 0.5086 1.317 1.0621 2.132 1.2843 0.859 0.3205 1.727 0.3995 2.648 0.3352 1.163-0.0558 2.227-0.0666 3.345-0.2207 6.413-0.7898 17.124 6.8335 23.427 5.0242 1.601-0.343 3.48 0 1.601 2.1643-2.508 1.8804-2.507 2.5072-2.507 6.268 0.949 0.5172-2.508 4.3877-2.508 6.2681-0.626 1.2536 0.473 1.1803 1.34 1.2592 1.736 0.1579 3.525 0.1726 5.054-0.6442-0.894 0.6457-1.877 1.0948-2.807 1.4006 0.233 0.25 0.511 0.5984 0.744 0.8485 1.954 2.1971 3.81 4.4391 5.719 6.5379-1.092-0.5705-2.078-1.4276-2.965-2.3295-1.165-1.2503-1.982-2.7785-3.451-3.6525-1.656-1.0258-3.74-0.9058-5.709-1.3137-3.123-0.5938 4.377-8.5748 1.529-10.126-2.847-1.5514-4.93-3.4988-9.789-5.6716-3.502-1.5658-9.218-3.9854-12.133-3.6044-0.922 0.0642-1.896 0.2717-2.826 0.5776-0.635 0.1711-1.225 0.4405-1.913 0.7548-0.787 0.3593-1.475 0.6736-2.306 0.9345-0.492 0.2245-1.029 0.3507-1.512 0.3337-0.581 0.0279-1.199-0.2841-1.673-0.5427-1.711-0.8825-3.421-1.7651-5.131-2.6476 0.286 0.1068 0.716 0.2671 1.003 0.3739 2.291 0.8547 4.538 1.611 6.864 1.4995 0.483 0.017 1.019-0.1092 1.368-0.3871 0.098-0.0449 0.196-0.0898 0.151-0.1881 0.402-0.4212 0.338-1.3425-0.19-1.4578-0.216-0.7332-0.915-1.4834-1.381-1.9835-3.836-3.7145-7.77-7.3841-11.658-10.955-0.422-0.4018-0.843-0.8036-1.46-1.1156-0.618-0.3119-1.146-0.4273-1.773-0.4977-4.519-0.7881-9.332-1.4414-13.821-0.342-0.099 0.0449-0.197 0.0898-0.295 0.1347 0 0 0.045 0.0983 0.09 0.1967-0.34 0.0363-0.627-0.0705-0.967-0.0341-2.28 0.2099-4.505 1.5825-6.39 2.9188-1.588 1.4265-2.241 2.2161-2.282 2.9793-0.046 0.8664-0.208 3.8116 1.321 5.0646 0.707 0.5086 2.206 0.477 2.473 0.7741 0.094 0.1049 0.374 2.0333-0.308 0.4662 0.763 0.0407 8.058 2.9182 13.372 3.1055 0.483 0.017 1.065-0.0108 1.601-0.137 0.34-0.0364 0.635-0.1712 0.885-0.4042 0.098-0.0449 0.152-0.1881 0.205-0.3313 0.054-0.1433-0.09-0.1967-0.135-0.295-2.484-3.6185-5.648-7.1643-6.166-11.681 0.188 0.1518 0.233 0.2501 0.421 0.4018 0.045 0.0983 0.188 0.1518 0.278 0.3484 1.578 1.8936 2.7 4.3517 4.483 5.914 0.798 0.7052 1.694 1.3656 2.241 2.3039 0.682 1.2333 0.766 2.9775 1.698 3.9778 0.466 0.5001 1.129 0.9104 1.701 1.1241 1.576 0.5876 3.357 0.8438 4.879 1.5746 0.761 0.3654 1.513 0.9724 2.274 1.3378 0.143 0.0534 0.188 0.1517 0.332 0.2051 1.71 0.8826 7.3 5.0687 9.232 5.1368-1.691-0.0596-6.949-4.0406-8.649-3.8586 1.623 1.9919 3.201 3.8856 5.02 5.7877-1.791-1.3207-3.431-2.8296-4.919-4.5266-0.744-0.8485-1.488-1.697-2.384-2.3573-1.746-1.2224-4.358-1.2178-5.581 0.5288-0.152 0.1882-0.303 0.3763-0.312 0.6178-0.526 1.1908-0.478 2.5951-0.476 3.9012 0.204 3.8284 0.559 7.4686 1.004 11.306 0.336 2.8172 0.771 5.5896 2.037 8.1011 0.089 0.1966 0.278 0.3483 0.474 0.2585-0.508 0.7077-0.247 1.5391 0.21 2.2808 0.359 0.7866 0.915 1.4833 1.373 2.225 0.412 0.6433 0.825 1.2867 1.238 1.93 1.973 3.0201 4.045 5.9953 6.367 8.7374-1.46-1.1156-2.814-2.5176-3.819-4.1976-0.78-1.1883-1.364-2.4665-2.198-3.5117-0.878-1.1434-2.061-1.9106-2.939-3.0541-0.368-0.545-0.781-1.1884-1.247-1.6885-0.194 1.3959-0.29 2.7469-0.484 4.1427-0.109-1.0196-0.219-2.0392-0.328-3.0588-0.1-1.2611-0.299-2.4774-0.498-3.6936-0.668-3.0225-2.355-5.9357-2.584-9.0394 8e-3 -0.2416 0.017-0.4831 0.025-0.7247 0.189 0.1518 0.278 0.3484 0.467 0.5001 0.053-0.1432-0.037-0.3398 0.017-0.4831 0.192-2.7019-0.145-5.5192-0.632-8.1483-0.353-2.3342-0.796-4.865-2.383-6.5171-0.466-0.5001-1.03-0.9553-1.496-1.4554-0.466-0.5002-0.727-1.3316-0.558-2.0029-3.194 0.0326-6.748 0.6536-9.238-1.4173-1.927-1.6157-4.528-1.2346-4.808-3.163 0.723 0.8039-1.206 1.0837-2.692 0.2391-1.485-0.8447-2.114-7.5219-0.981-9.4652 1.079-1.8 2.821-3.1897 4.68-3.8013 0.099-0.0449 0.099-0.0449 0.197-0.0898-2.803-1.4531-5.748-2.9597-8.391-4.8424-0.752-0.607-1.603-1.169-2.106-2.009-0.521-1.663-0.498-3.6937-0.43-5.6261z" fill="#5c5a5a" style=""/>
  <path d="m194.49 52.611c1.02-0.1092 1.565-0.3571 2.568 0.0168 2.005 0.7478 2.459 1.5554 4.357 2.5897 2.14 1.0428 3.274 1.1419 5.681 1.4686 1.252 0.1409 2.46 0.1835 3.623 0.1277-0.276 0.9577 1.582 2.4634 2.281 3.2135 0.699 0.7502 1.595 1.4106 1.9 2.3404 0.351 1.0281 0.12 2.0841 0.131 3.1486 0.056 1.1628 0.452 2.2893 0.749 3.4606 0.496 2.3876 0.17 4.7946-0.3 7.1481-0.045-0.0983-0.045-0.0983-0.09-0.1966 0.192-2.7019-0.144-5.5192-0.632-8.1484-0.353-2.3341-0.796-4.8649-2.382-6.517-0.466-0.5002-1.031-0.9554-1.497-1.4555s-1.894-1.8455-1.725-2.5168c-3.193 0.0326-5.462 0.5706-7.952-1.5003-1.926-1.6157-2.612-1.4871-4.948-2.4401-0.376-0.3035-1.093-0.5706-1.764-0.7393z" fill="#8b8b8b" style=""/>
  <path d="m206.25 42.312c0.188 0.1518 0.233 0.2501 0.421 0.4018 0.045 0.0983 0.188 0.1518 0.278 0.3484 0.045 0.0983 0.045 0.0983 0.045 0.0983 0.216 0.7332 0.432 1.4663 0.889 2.208 0.27 0.5899 0.682 1.2333 1.05 1.7783 1.561 2.3767 3.712 4.484 4.728 7.2285-0.689 0.3144-1.377 0.6287-2.057 0.7015 0.34-0.0364 0.635-0.1711 0.885-0.4042 0.099-0.0449 0.152-0.1881 0.205-0.3313 0.054-0.1432-0.089-0.1967-0.134-0.295-2.726-3.627-5.792-7.2177-6.31-11.734z" fill="#8b8b8b" style=""/>
  <path d="m218.73 58.943c0.786-0.3592 1.806-0.4684 2.719-0.2911s1.862 0.6944 2.819 0.97c0.144 0.0534 0.189 0.1517 0.332 0.2051 1.71 0.8826 6.632 4.6101 8.564 4.6782-1.691-0.0596-6.281-3.5819-7.98-3.4 1.622 1.992 3.437 3.9459 5.256 5.8481-1.791-1.3208-3.668-2.8899-5.156-4.5869-0.744-0.8485-1.488-1.697-2.384-2.3574-1.746-1.2224-4.358-1.2177-5.581 0.5289-0.151 0.1881-0.303 0.3762-0.312 0.6178 0.116-0.528 0.276-0.9577 0.579-1.334 0.206-0.3313 0.652-0.6542 1.144-0.8787z" fill="#8b8b8b" style=""/>
  <path d="m214.65 75.569c0.814 0.2222 1.46 1.1156 1.631 1.7505 0.297 1.1713 0.496 2.3875 0.74 3.7021 0.252 1.073 0.407 2.191 0.757 3.2191 0.926 2.5478 2.639 4.7364 4.556 6.5937 0 0 0.045 0.0983 0.144 0.0534 1.973 3.0201 4.045 5.9953 6.367 8.7374-1.46-1.1156-2.814-2.5176-3.819-4.1976-0.78-1.1884-1.364-2.4665-2.198-3.5117-0.878-1.1434-2.061-1.9106-2.939-3.0541-0.368-0.545-0.781-1.1884-1.247-1.6885-0.194 1.3959-0.29 2.7468-0.484 4.1427-0.109-1.0196-0.219-2.0392-0.328-3.0588-0.1-1.2612-0.299-2.4774-0.498-3.6937-0.909-3.0309-2.453-5.8907-2.682-8.9945z" fill="#8b8b8b" style=""/>
  <path d="m186.97 26.632c0.126 0.5365 0.199 1.2162 0.424 1.7078 0.109 1.0196 0.272 1.896 0.819 2.8343 0.637 1.135 1.569 2.1352 2.51 2.8939 1.693 1.3657 3.645 2.2567 5.597 3.1478 0.904 0.4188 1.755 0.9808 2.159 1.8657-0.143-0.0534-0.143-0.0534-0.287-0.1068 0.099-0.0449 0.099-0.0449 0.197-0.0898-2.803-1.4531-5.748-2.9596-8.391-4.8424-0.752-0.607-1.603-1.169-2.106-2.009-1.013-1.4384-0.99-3.4691-0.922-5.4015z" fill="#8b8b8b" style=""/>
  <path d="m205.07 38.684c0.876-0.1626 1.789 0.0146 2.666-0.148 0.921-0.0642 1.752-0.3251 2.53-0.4428 1.503-0.0922 2.891 0.3437 4.295 0.2964 0.823-0.0194 1.744-0.0836 2.612-0.0047 1.539 0.2477 2.803 1.4531 3.878 2.5068 4.068 3.9645 8.092 7.8308 12.16 11.795 0.655 0.6519 1.443 1.5987 1.025 2.503-0.206 0.3313-0.554 0.6093-0.849 0.744 0.098-0.0449 0.197-0.0898 0.152-0.1881 0.402-0.4212 0.337-1.3425-0.191-1.4578-0.216-0.7332-0.915-1.4834-1.381-1.9835-3.835-3.7145-7.769-7.3841-11.658-10.955-0.421-0.4018-0.842-0.8036-1.46-1.1155-0.618-0.312-1.146-0.4274-1.772-0.4978-4.519-0.7881-9.333-1.4414-13.822-0.342-0.098 0.0449-0.196 0.0898-0.295 0.1347 0 0 0.045 0.0983 0.09 0.1967-0.34 0.0364-0.626-0.0705-0.966-0.0341 0.983-0.449 1.966-0.8981 2.986-1.0072z" fill="#8b8b8b" style=""/>
  <path d="m226.76 35.435c1.511-0.3337 3.086 0.2539 4.617 0.7431 0.143 0.0534 0.143 0.0534 0.286 0.1069 3.449 2.3465 6.995 4.6481 10.747 6.6183-3.777-1.2456-7.439-3.0192-10.994-5.0793-0.806-0.4637-1.514-0.9723-2.471-1.2479-2.247-0.7564-4.697 0.1247-6.958 1.1575 0.348-0.2779 0.938-0.5474 1.135-0.6372 0.59-0.2694 1.081-0.4939 1.671-0.7633 0.599-0.511 1.189-0.7804 1.967-0.8981z" fill="#8b8b8b" style=""/>
  <path d="m224.75 55.351c0.287 0.1068 0.716 0.2671 1.003 0.3739 0.045 0.0984 0.188 0.1518 0.188 0.1518 0.806 0.4637 1.612 0.9274 2.373 1.2928 1.575 0.5876 2.794 0.8009 4.358 0.3239 0.832-0.2609 2.135-1.2416 2.921-1.6008 2.262-1.0328 4.785-0.6681 7.164-0.9228 2.137-0.2633 6.436 3.1194 8.685 4.0047 2.249 0.8854 2.611 1.2874 4.136 2.1507 2.443 1.3835 2.471 1.248 3.706 1.8719 1.666 0.7843 2.494 0.8934 3.121 2.7738 0 2.5072-3.135 5.6324-3.135 8.1485 1.086 1.4326 1.873 0.6419 3.699 0.9965 0.957 0.2756 1.915 0.5512 2.721 1.0149 1.954 2.1971 3.81 4.4392 5.719 6.538-1.093-0.5706-2.078-1.4276-2.965-2.3295-1.165-1.2503-1.982-2.7786-3.451-3.6526-1.657-1.0258-3.741-0.9057-5.71-1.3137-3.123-0.5938 4.157-9.3207 1.31-10.872-2.848-1.5515-4.774-3.0474-7.967-3.0148-3.05 0.086-9.698-5.6815-12.614-5.3005-0.921 0.0643-1.896 0.2718-2.825 0.5776-0.635 0.1711-1.225 0.4405-1.913 0.7548-0.787 0.3593-1.475 0.6736-2.307 0.9345-0.491 0.2245-1.028 0.3508-1.511 0.3337-0.581 0.0279-1.199-0.2841-1.674-0.5426-1.71-0.8826-3.42-1.7651-5.032-2.6926z" fill="#8b8b8b" style=""/>
 </g><g transform="translate(4e-4,5e-4)" style="display: none;" tb:tag="leak">
  <path d="m47.912 134.05c0.428-0.101 0.9702-0.159 1.3624-0.338 1.3624-0.338 2.6599-1.025 3.5071-2.075 0.3273-0.529 0.6188-1.136 0.8319-1.707 0.6323-1.521 1.3005-2.964 1.9687-4.407 0.6614-1.25 1.3296-2.692 0.9133-4.019-0.1926-7e-3 -0.3069-0.049-0.4996-0.056 0.5453-2.335 0.3266-4.889-0.3626-7.228-0.4453-1.598-1.276-3.209-1.179-4.865 0.0203-0.578 0.155-1.113 0.2111-1.613 0.0698-0.885 0.0612-1.734-0.1402-2.59-0.0939-0.621-0.3804-1.248-0.5527-1.833-0.4812-1.676-0.8054-3.4236-0.7801-5.2362 0.3291 0.5132 0.6583 1.0264 0.9875 1.5396 0 0 0.3427 0.1278 0.2285 0.0852-0.121 0.1501-0.0136 0.3854 0.1365 0.5064 0.4057-0.5645 0.8115-1.1289 1.1388-1.6576 1.8718-2.7507 3.7078-5.5798 5.2794-8.5726-0.9936 3.0132-2.4084 5.9343-4.0517 8.7702-0.3699 0.6429-0.7756 1.2074-0.9955 1.971-0.6033 1.793 0.0995 3.746 0.9234 5.55l0.0358 0.079c0.0716 0.157 0.1791 0.392 0.3718 0.399 0.2285 0.085 0.5064-0.137 0.7058-0.323 0.9616-1.007 1.9589-1.937 2.9563-2.866 1.2753-1.152 2.658-2.067 3.8974-3.297 1.1184-1.0796 2.0868-2.2802 2.9767-3.4449 0.4057-0.5645 0.8472-1.0505 1.0245-1.7002 0.2556-0.6855 0.3186-1.3778 0.2674-2.1127-0.0445-0.9276-0.0532-1.7768-0.1761-2.6685-0.63-5.1154 5.4511-13.66 4.0078-18.688-0.2736-1.2766 0-2.776 1.7264-1.2765 1.5 2.0005 2.0001 1.9998 5.0001 1.9998 0.4125-0.7572 3.5 2 5 2 1 0.5 0.9415-0.3767 1.0045-1.069 0.1259-1.3846 0.1376-2.8118-0.5139-4.0309 0.5151 0.7127 0.8733 1.497 1.1172 2.2387 0.1995-0.1859 0.4774-0.4076 0.6769-0.5935 1.7526-1.5588 3.5411-3.0391 5.2153-4.5621-0.4552 0.8714-1.1388 1.6576-1.8583 2.3654-0.9973 0.9294-2.2164 1.581-2.9136 2.7525-0.8183 1.3216-0.7225 2.9841-1.048 4.5546-0.4736 2.4913-6.84-3.4917-8.0776-1.2202-1.2375 2.2714-2.791 3.9327-4.5242 7.8089-1.249 2.7934-3.1791 7.3531-2.8752 9.6789 0.0512 0.7349 0.2167 1.5124 0.4607 2.2541 0.1365 0.5064 0.3514 0.977 0.6022 1.526 0.2865 0.6274 0.5373 1.1765 0.7454 1.8397 0.1791 0.3922 0.2798 0.8202 0.2662 1.2055 0.0222 0.4638-0.2266 0.9571-0.4329 1.3351-0.704 1.364-1.408 2.729-2.112 4.093 0.0852-0.229 0.213-0.571 0.2982-0.8 0.6818-1.828 1.2851-3.62 1.1962-5.4754 0.0136-0.3853-0.0871-0.8133-0.3088-1.0912-0.0358-0.0784-0.0717-0.1569-0.1501-0.1211-0.336-0.3205-1.0709-0.2692-1.1629 0.152-0.5848 0.1723-1.1832 0.73-1.5822 1.1018-2.963 3.0599-5.8903 6.1969-8.739 9.2999-0.3206 0.336-0.6411 0.671-0.8899 1.164-0.2489 0.493-0.3409 0.914-0.3971 1.414-0.6286 3.605-1.1498 7.445-0.2728 11.025 0.0358 0.079 0.0716 0.157 0.1075 0.236l0.1568-0.072c0.029 0.271-0.0562 0.5-0.0271 0.771 0.1673 1.819 1.2623 3.594 2.3283 5.098 1.1378 1.266 1.7677 1.787 2.3765 1.82 0.6912 0.037 3.0405 0.166 4.04-1.054 0.4058-0.564 0.3806-1.76 0.6176-1.973 0.0837-0.075 1.622-0.298 0.3718 0.246 0.0325-0.609 2.3279-6.428 2.4773-10.667 0.0136-0.385-0.0086-0.849-0.1093-1.277-0.029-0.271-0.1365-0.506-0.3224-0.706-0.0358-0.078-0.1501-0.121-0.2643-0.164-0.1143-0.042-0.1569 0.072-0.2353 0.108-2.8865 1.982-5.7149 4.506-9.3178 4.919 0.121-0.15 0.1995-0.186 0.3205-0.336 0.0784-0.036 0.121-0.15 0.2779-0.222 1.5105-1.258 3.4713-2.154 4.7175-3.576 0.5626-0.636 1.0894-1.351 1.8379-1.787 0.9838-0.544 2.3752-0.611 3.1731-1.355 0.3989-0.372 0.7262-0.9 0.8966-1.357 0.4688-1.257 0.6732-2.677 1.2561-3.892 0.2915-0.607 0.7757-1.207 1.0672-1.814 0.0426-0.114 0.121-0.15 0.1636-0.264 0.704-1.364 4.0433-5.8235 4.0976-7.3649-0.0475 1.3487-3.2232 5.5439-3.078 6.8989 1.589-1.294 3.0995-2.553 4.6169-4.0043-1.0536 1.4293-2.2572 2.7373-3.6109 3.9243-0.6768 0.593-1.3537 1.187-1.8804 1.901-0.9752 1.394-0.9714 3.477 0.4218 4.452 0.1501 0.121 0.3002 0.242 0.4928 0.249 0.9499 0.42 2.0701 0.382 3.112 0.38 3.0539-0.162 5.9577-0.446 9.0184-0.801 2.2473-0.268 4.4588-0.615 6.4622-1.624 0.1568-0.072 0.2779-0.222 0.2062-0.379 0.5645 0.406 1.2278 0.198 1.8194-0.167 0.6274-0.287 1.1832-0.73 1.7749-1.095 0.5132-0.33 1.0264-0.659 1.5396-0.988 2.4095-1.574 4.7825-3.227 6.9695-5.079-0.89 1.165-2.008 2.244-3.348 3.046-0.948 0.622-1.968 1.088-2.801 1.753-0.9126 0.701-1.5246 1.644-2.4367 2.345-0.4348 0.294-0.948 0.623-1.3469 0.995 1.1135 0.155 2.1911 0.231 3.3046 0.386-0.8133 0.087-1.6267 0.174-2.44 0.261-1.006 0.081-1.9762 0.239-2.9464 0.398-2.411 0.533-4.7349 1.878-7.2107 2.061-0.1927-7e-3 -0.3854-0.013-0.5781-0.02 0.1211-0.15 0.2779-0.222 0.399-0.372-0.1143-0.043-0.2711 0.029-0.3854-0.014-2.1553-0.153-4.4026 0.115-6.4999 0.504-1.862 0.282-3.8808 0.635-5.1987 1.901-0.3989 0.372-0.762 0.822-1.161 1.194-0.3989 0.371-1.0622 0.579-1.5976 0.445 0.026 2.547 0.5213 5.382-1.1307 7.369-1.2888 1.537-0.9848 3.612-2.5231 3.835 0.6413-0.576 0.8645 0.962 0.1908 2.147-0.6738 1.185-6.0002 1.687-7.5504 0.783-1.4358-0.861-2.5444-2.25-3.0323-3.734l-0.0716-0.156c-1.1592 2.235-2.3609 4.585-3.8628 6.693-0.4842 0.6-0.9325 1.279-1.6026 1.68-1.3265 0.416-2.9464 0.397-4.4879 0.343z" fill="#5c5a5a" style=""/>
  <path d="m68.636 128.06c-0.0871-0.813-0.2849-1.248 0.0134-2.048 0.5965-1.599 1.2407-1.962 2.0658-3.476 0.8318-1.707 0.9109-2.611 1.1715-4.531 0.1124-0.999 0.1463-1.963 0.1019-2.89 0.7639 0.22 1.965-1.262 2.5634-1.82 0.5984-0.557 1.1252-1.272 1.8669-1.516 0.8201-0.28 1.6625-0.096 2.5116-0.104 0.9276-0.045 1.8262-0.36 2.7606-0.598 1.9045-0.395 3.8246-0.135 5.702 0.24-0.0784 0.036-0.0784 0.036-0.1569 0.072-2.1553-0.154-4.4026 0.115-6.4999 0.504-1.8619 0.281-3.8808 0.635-5.1986 1.9-0.399 0.372-0.7621 0.822-1.1611 1.194-0.3989 0.372-1.4722 1.51-2.0076 1.376 0.026 2.547 0.4552 4.357-1.1968 6.343-1.2888 1.537-1.1863 2.084-1.9465 3.947-0.2421 0.301-0.4551 0.872-0.5897 1.407z" fill="#8b8b8b" style=""/>
  <path d="m60.418 118.67c0.1211-0.15 0.1995-0.186 0.3206-0.336 0.0784-0.035 0.121-0.15 0.2779-0.221l0.0784-0.036c0.5848-0.173 1.1697-0.345 1.7613-0.71 0.4706-0.215 0.9838-0.544 1.4185-0.837 1.896-1.245 3.5769-2.961 5.7662-3.771 0.2508 0.549 0.5015 1.098 0.5596 1.64-0.0291-0.271-0.1365-0.506-0.3224-0.706-0.0358-0.078-0.1501-0.121-0.2643-0.164-0.1143-0.042-0.1569 0.072-0.2353 0.108-2.8933 2.174-5.7576 4.62-9.3605 5.033z" fill="#8b8b8b" style=""/>
  <path d="m73.687 108.72c-0.2865-0.628-0.3736-1.441-0.2322-2.169s0.5539-1.485 0.7738-2.249c0.0426-0.115 0.121-0.15 0.1636-0.265 0.704-1.364 3.6774-5.2898 3.7318-6.8313-0.0476 1.3488-2.8573 5.0103-2.7122 6.3663 1.589-1.295 3.1477-2.742 4.665-4.1933-1.0536 1.4293-2.3053 2.9253-3.659 4.1123-0.6768 0.594-1.3537 1.187-1.8804 1.902-0.9751 1.393-0.9714 3.477 0.4218 4.452 0.1501 0.121 0.3002 0.242 0.4929 0.249-0.4212-0.092-0.764-0.22-1.0641-0.462-0.2643-0.164-0.5219-0.52-0.701-0.912z" fill="#8b8b8b" style=""/>
  <path d="m86.95 111.97c0.1772-0.65 0.8899-1.165 1.3963-1.301 0.9344-0.237 1.9046-0.396 2.9532-0.591 0.856-0.201 1.7477-0.324 2.5679-0.604 2.0324-0.738 3.7782-2.105 5.2597-3.634 0 0 0.0785-0.036 0.0427-0.115 2.4093-1.574 4.7823-3.226 6.9693-5.079-0.889 1.165-2.008 2.245-3.348 3.046-0.948 0.623-1.968 1.089-2.8012 1.754-0.9122 0.701-1.5241 1.644-2.4363 2.345-0.4347 0.293-0.9479 0.622-1.3469 0.994 1.1135 0.155 2.1912 0.232 3.3046 0.387-0.8133 0.087-1.6266 0.174-2.44 0.261-1.006 0.08-1.9762 0.239-2.9464 0.398-2.4178 0.725-4.6991 1.956-7.1749 2.139z" fill="#8b8b8b" style=""/>
  <path d="m47.912 134.05c0.428-0.1 0.9702-0.158 1.3624-0.337 0.8133-0.088 1.5124-0.217 2.2609-0.654 0.9053-0.508 1.7032-1.252 2.3084-2.002 1.0894-1.351 1.8002-2.908 2.511-4.465 0.3341-0.721 0.7824-1.4 1.4883-1.722-0.0426 0.114-0.0426 0.114-0.0852 0.229-0.0358-0.079-0.0358-0.079-0.0717-0.157-1.1591 2.235-2.3609 4.585-3.8628 6.693-0.4841 0.6-0.9325 1.279-1.6025 1.68-1.1475 0.808-2.7673 0.79-4.3088 0.735z" fill="#8b8b8b" style=""/>
  <path d="m57.526 119.61c-0.1297-0.699 0.0117-1.427-0.118-2.126-0.0513-0.735-0.2594-1.398-0.3533-2.019-0.0735-1.199 0.2741-2.305 0.2365-3.426-0.0155-0.656-0.0668-1.391-0.0038-2.083 0.1976-1.228 1.1592-2.236 1.9997-3.094 3.1625-3.245 6.2466-6.455 9.4091-9.7 0.52-0.5219 1.2752-1.1512 1.9966-0.8171 0.2643 0.1637 0.486 0.4416 0.5935 0.6769-0.0358-0.0785-0.0717-0.1569-0.1501-0.1211-0.336-0.3205-1.0709-0.2692-1.1629 0.152-0.5848 0.1723-1.1832 0.7299-1.5822 1.1017-2.963 3.0596-5.8903 6.1976-8.7391 9.2996-0.3205 0.336-0.641 0.672-0.8899 1.165-0.2488 0.493-0.3408 0.914-0.397 1.413-0.6286 3.605-1.1498 7.445-0.2728 11.026 0.0358 0.078 0.0716 0.157 0.1074 0.235 0 0 0.0785-0.036 0.1569-0.072 0.029 0.272-0.0562 0.5-0.0271 0.771-0.3583-0.784-0.7165-1.568-0.8035-2.382z" fill="#8b8b8b" style=""/>
  <path d="m54.934 102.31c-0.2662-1.205 0.2026-2.4619 0.5928-3.6829 0.0426-0.1142 0.0426-0.1142 0.0853-0.2285 1.8718-2.7506 3.7078-5.5798 5.2794-8.5725-0.9936 3.0131-2.4084 5.9342-4.0517 8.7702-0.3699 0.6428-0.7757 1.2073-0.9955 1.9717-0.6033 1.792 0.0995 3.746 0.9234 5.55-0.2218-0.278-0.4367-0.749-0.5083-0.906-0.2149-0.47-0.394-0.862-0.609-1.333-0.4076-0.477-0.6225-0.948-0.7164-1.569z" fill="#8b8b8b" style=""/>
  <path d="m70.82 103.92c0.0852-0.229 0.2131-0.571 0.2983-0.8 0.0784-0.036 0.121-0.15 0.121-0.15 0.3699-0.643 0.7398-1.286 1.0313-1.893 0.4687-1.2566 0.6388-2.2284 0.2584-3.4765-0.2081-0.6633-0.9904-1.7028-1.277-2.3302-0.8238-1.8039-0.5329-3.8166-0.7361-5.7144-0.21-1.7051 2.4883-5.1345 3.1946-6.9285s1.0269-2.0821 1.7156-3.2985c1.1035-1.9491 0.9955-1.9713 1.4932-2.9569 0.6256-1.3285 0.7126-1.9891 2.2126-2.4891 2 0 4.493 2.5003 6.5 2.5003 1.1428-0.8664 0.5121-1.4939 0.795-2.9501 0.2198-0.7639 0.4397-1.5279 0.8096-2.1708 1.7526-1.5587 3.5411-3.0391 5.2153-4.562-0.4552 0.8714-1.1388 1.6575-1.8583 2.3653-0.9973 0.9294-2.2164 1.581-2.9136 2.7525-0.8183 1.3217-0.7225 2.9842-1.0479 4.5546-0.4737 2.4913-7.4351-3.3159-8.6727-1.0445-1.2376 2.2715-2.4309 3.8077-2.4049 6.3552 0.0686 2.4332-4.5321 7.736-4.2282 10.062 0.0513 0.7349 0.2168 1.5125 0.4608 2.2541 0.1364 0.5065 0.3514 0.977 0.6021 1.5261 0.2866 0.6274 0.5373 1.1764 0.7454 1.8397 0.1791 0.3921 0.2798 0.8201 0.2662 1.2055 0.0223 0.4638-0.2266 0.9566-0.4329 1.3352-0.704 1.3643-1.408 2.7283-2.1478 4.0143z" fill="#8b8b8b" style=""/>
  <path d="m303.34 75.498c0.097 0.4289 0.15 0.9717 0.325 1.3655 0.325 1.3655 1 2.6695 2.042 3.5267 0.526 0.3323 1.13 0.6295 1.699 0.8479 1.515 0.6467 2.952 1.3286 4.388 2.0104 1.244 0.6732 2.68 1.355 4.01 0.9513 9e-3 -0.1926 0.053-0.3065 0.061-0.4991 2.329 0.5674 4.886 0.3729 7.231-0.2942 1.602-0.4302 3.221-1.2455 4.876-1.1329 0.578 0.0258 1.112 0.1655 1.611 0.2264 0.884 0.0782 1.733 0.0775 2.591-0.1157 0.622-0.088 1.252-0.3686 1.838-0.5354 1.681-0.4652 3.432-0.7729 5.244-0.7305-0.517 0.3243-1.033 0.6486-1.549 0.9729 0 0-0.131 0.3416-0.087 0.2277-0.149-0.1224-0.386-0.0172-0.508 0.1317 0.56 0.4111 1.121 0.8221 1.647 1.1544 2.732 1.8978 5.544 3.7605 8.522 5.3604-3.004-1.0221-5.911-2.4645-8.732-4.1346-0.639-0.376-1.2-0.787-1.961-1.0141-1.787-0.6202-3.747 0.0641-5.559 0.8708l-0.079 0.0351c-0.157 0.0701-0.394 0.1754-0.402 0.368-0.088 0.2277 0.131 0.5077 0.315 0.7089 0.999 0.971 1.919 1.9771 2.839 2.9832 1.139 1.2861 2.042 2.6775 3.26 3.9285 1.069 1.1286 2.26 2.1082 3.416 3.0091 0.561 0.411 1.043 0.8572 1.691 1.0406 0.683 0.2621 1.374 0.3316 2.11 0.2873 0.928-0.0357 1.777-0.0363 2.67-0.1508 5.121-0.5816 13.607 5.5805 18.649 4.1845 1.279-0.261 2.776 0.026 1.26 1.738-2.015 1.481-2.019 1.981-2.047 4.981 0.753 0.42-2.033 3.481-2.047 4.981-0.51 0.995 0.367 0.945 1.059 1.015 1.383 0.139 2.81 0.164 4.036-0.476-0.718 0.508-1.506 0.859-2.25 1.096 0.184 0.201 0.404 0.481 0.588 0.682 1.542 1.768 3.005 3.57 4.512 5.259-0.867-0.464-1.647-1.155-2.348-1.881-0.92-1.006-1.56-2.231-2.724-2.939-1.314-0.831-2.978-0.751-4.545-1.091-2.487-0.498 3.556-6.807 1.297-8.066-2.26-1.259-3.906-2.828-7.766-4.598-2.782-1.275-7.323-3.2486-9.651-2.9668-0.736 0.0444-1.515 0.2025-2.259 0.4394-0.507 0.1317-0.98 0.3422-1.531 0.5877-0.63 0.2807-1.182 0.5257-1.847 0.7277-0.394 0.176-0.823 0.272-1.208 0.255-0.464 0.018-0.954-0.236-1.331-0.445-1.357-0.7173-2.715-1.4342-4.072-2.1511 0.227 0.0874 0.569 0.2185 0.796 0.3058 1.822 0.6991 3.608 1.3193 5.464 1.2479 0.385 0.0173 0.814-0.0793 1.094-0.2984 0.079-0.0351 0.158-0.0702 0.123-0.1489 0.323-0.3329 0.279-1.0683-0.141-1.1643-0.167-0.5864-0.719-1.1901-1.087-1.5925-3.031-2.9919-6.141-5.9487-9.216-8.8267-0.333-0.3237-0.666-0.6474-1.157-0.9009-0.49-0.2536-0.91-0.3495-1.409-0.4105-3.599-0.6627-7.434-1.2202-11.023-0.3771-0.078 0.0351-0.157 0.0702-0.236 0.1052 0 0 0.035 0.0788 0.07 0.1576-0.271 0.0264-0.499-0.0609-0.77-0.0345-1.821 0.1502-3.606 1.2283-5.12 2.28-1.277 1.1258-1.804 1.7507-1.843 2.3592-0.043 0.6908-0.194 3.0388 1.016 4.0498 0.56 0.4111 1.756 0.3972 1.967 0.6362 0.074 0.0844 0.283 1.6247-0.249 0.3695 0.608 0.0382 6.405 2.3886 10.643 2.5781 0.385 0.0173 0.849-6e-4 1.278-0.0972 0.271-0.0265 0.507-0.1317 0.709-0.3157 0.078-0.0351 0.122-0.1489 0.166-0.2628 0.043-0.1138-0.07-0.1575-0.106-0.2363-1.954-2.9051-4.451-5.7572-4.83-9.3639 0.149 0.1225 0.184 0.2012 0.333 0.3237 0.035 0.0787 0.149 0.1224 0.219 0.28 1.244 1.5224 2.121 3.4915 3.531 4.7512 0.631 0.5685 1.341 1.1021 1.77 1.8547 0.535 0.9888 0.588 2.3808 1.324 3.1857 0.368 0.4024 0.894 0.7347 1.349 0.9095 1.253 0.4807 2.671 0.6987 3.88 1.2927 0.604 0.297 1.2 0.787 1.804 1.084 0.114 0.044 0.149 0.123 0.263 0.167 1.357 0.716 5.784 4.098 7.325 4.167-1.348-0.061-5.513-3.276-6.869-3.144 1.279 1.602 2.523 3.124 3.96 4.655-1.419-1.067-2.715-2.283-3.89-3.648-0.587-0.682-1.174-1.365-1.883-1.898-1.384-0.988-3.468-1.004-4.456 0.38-0.123 0.148-0.245 0.297-0.254 0.49-0.428 0.946-0.401 2.066-0.409 3.108 0.134 3.055 0.39 5.962 0.716 9.026 0.247 2.25 0.572 4.464 1.563 6.477 0.07 0.158 0.219 0.28 0.377 0.21-0.411 0.56-0.21 1.226 0.15 1.821 0.28 0.63 0.719 1.19 1.078 1.785 0.324 0.516 0.649 1.032 0.973 1.549 1.551 2.424 3.181 4.812 5.013 7.017-1.157-0.901-2.226-2.029-3.014-3.377-0.614-0.954-1.07-1.978-1.727-2.817-0.692-0.919-1.63-1.54-2.322-2.459-0.289-0.437-0.614-0.954-0.981-1.356-0.166 1.112-0.253 2.189-0.418 3.301-0.08-0.814-0.159-1.629-0.238-2.443-0.071-1.006-0.221-1.978-0.37-2.95-0.51-2.416-1.834-4.752-1.993-7.23 9e-3 -0.192 0.017-0.385 0.026-0.577 0.149 0.122 0.219 0.28 0.368 0.402 0.044-0.114-0.027-0.271 0.017-0.385 0.174-2.154-0.073-4.404-0.442-6.505-0.264-1.864-0.599-3.886-1.851-5.216-0.368-0.402-0.815-0.77-1.183-1.172-0.368-0.403-0.57-1.068-0.43-1.6021-2.548 0.0019-5.387 0.4701-7.358-1.2003-1.525-1.3033-3.603-1.0189-3.811-2.5593 0.57 0.6467-0.97 0.8554-2.149 0.1704s-1.63-6.0159-0.712-7.5574c0.875-1.4276 2.274-2.523 3.762-2.9968l0.158-0.0702c-2.225-1.1802-4.563-2.4042-6.656-3.926-0.596-0.4898-1.271-0.9445-1.665-1.6183-0.404-1.3305-0.37-2.9501-0.301-4.491z" fill="#5c5a5a" style=""/>
  <path d="m309.14 96.278c0.814-0.0794 1.251-0.2731 2.048 0.0328 1.594 0.6116 1.95 1.2592 3.456 2.0986 1.699 0.8479 2.603 0.9356 4.52 1.2143 0.998 0.1219 1.961 0.1649 2.889 0.1292-0.227 0.7617 1.243 1.9767 1.795 2.5807s1.262 1.137 1.499 1.881c0.272 0.823 0.08 1.663 0.08 2.513 0.036 0.928 0.343 1.829 0.571 2.766 0.378 1.908 0.1 3.825-0.293 5.699-0.035-0.079-0.035-0.079-0.07-0.157 0.173-2.154-0.073-4.404-0.443-6.505-0.264-1.864-0.598-3.886-1.851-5.216-0.368-0.403-0.815-0.77-1.182-1.172-0.368-0.403-1.497-1.487-1.358-2.021-2.547 2e-3 -4.36 0.414-6.332-1.2568-1.524-1.3034-2.071-1.206-3.928-1.9838-0.298-0.2449-0.867-0.4634-1.401-0.603z" fill="#8b8b8b" style=""/>
  <path d="m318.6 88.15c0.149 0.1225 0.184 0.2012 0.333 0.3237 0.035 0.0788 0.149 0.1225 0.219 0.28 0.035 0.0788 0.035 0.0788 0.035 0.0788 0.167 0.5864 0.333 1.1728 0.693 1.7679 0.21 0.4726 0.535 0.9889 0.824 1.4264 1.227 1.9076 2.926 3.6048 3.716 5.8016-0.551 0.2455-1.103 0.4911-1.645 0.544 0.271-0.0265 0.507-0.1317 0.708-0.3157 0.079-0.0351 0.123-0.1489 0.167-0.2627 0.043-0.1139-0.071-0.1576-0.106-0.2363-2.147-2.9138-4.565-5.801-4.944-9.4077z" fill="#8b8b8b" style=""/>
  <path d="m328.43 101.51c0.63-0.281 1.444-0.36 2.171-0.212s1.48 0.568 2.242 0.795c0.114 0.044 0.149 0.122 0.263 0.166 1.357 0.717 5.255 3.727 6.796 3.796-1.348-0.06-4.983-2.904-6.34-2.772 1.279 1.601 2.712 3.173 4.148 4.704-1.419-1.067-2.903-2.332-4.078-3.697-0.587-0.683-1.174-1.365-1.883-1.899-1.384-0.988-3.468-1.004-4.456 0.38-0.122 0.149-0.245 0.298-0.254 0.49 0.096-0.42 0.228-0.761 0.472-1.059 0.167-0.263 0.525-0.517 0.919-0.692z" fill="#8b8b8b" style=""/>
  <path d="m325.05 114.74c0.648 0.184 1.157 0.901 1.288 1.409 0.229 0.936 0.378 1.908 0.563 2.958 0.193 0.858 0.307 1.751 0.579 2.574 0.72 2.039 2.069 3.798 3.585 5.294 0 0 0.035 0.079 0.114 0.043 1.551 2.424 3.181 4.813 5.013 7.018-1.157-0.901-2.226-2.029-3.014-3.377-0.614-0.954-1.07-1.978-1.727-2.818-0.692-0.919-1.63-1.539-2.322-2.458-0.289-0.438-0.613-0.954-0.981-1.356-0.166 1.112-0.253 2.188-0.418 3.3-0.08-0.814-0.159-1.628-0.238-2.442-0.071-1.007-0.221-1.978-0.37-2.95-0.702-2.425-1.912-4.717-2.072-7.195z" fill="#8b8b8b" style=""/>
  <path d="m303.34 75.498c0.097 0.4289 0.15 0.9717 0.325 1.3655 0.08 0.8141 0.203 1.5144 0.632 2.267 0.5 0.9101 1.236 1.715 1.981 2.3273 1.34 1.1021 2.89 1.8276 4.44 2.5531 0.718 0.3409 1.393 0.7956 1.708 1.5045-0.113-0.0437-0.113-0.0437-0.227-0.0874 0.078-0.035 0.078-0.035 0.157-0.0701-2.224-1.1803-4.563-2.4042-6.656-3.926-0.596-0.4898-1.27-0.9445-1.665-1.6184-0.797-1.155-0.763-2.7746-0.695-4.3155z" fill="#8b8b8b" style=""/>
  <path d="m317.69 85.248c0.701-0.123 1.427 0.0253 2.128-0.0978 0.735-0.0443 1.4-0.2462 2.022-0.3342 1.199-0.0621 2.302 0.296 3.423 0.2689 0.657-0.0092 1.392-0.0535 2.084 0.016 1.225 0.2092 2.224 1.1802 3.074 2.0288 3.215 3.1931 6.396 6.3074 9.611 9.5005 0.517 0.5249 1.139 1.2861 0.798 2.0043-0.166 0.2627-0.446 0.4818-0.682 0.587 0.078-0.0351 0.157-0.0701 0.122-0.1489 0.324-0.3329 0.279-1.0683-0.141-1.1643-0.167-0.5864-0.719-1.1901-1.087-1.5925-3.031-2.9919-6.141-5.9487-9.216-8.8267-0.333-0.3237-0.666-0.6474-1.156-0.9009-0.491-0.2535-0.911-0.3495-1.41-0.4104-3.599-0.6627-7.434-1.2202-11.023-0.3771-0.078 0.035-0.157 0.0701-0.236 0.1052 0 0 0.035 0.0787 0.07 0.1575-0.271 0.0265-0.499-0.0609-0.77-0.0344 0.788-0.3508 1.575-0.7016 2.389-0.781z" fill="#8b8b8b" style=""/>
  <path d="m335.01 82.82c1.208-0.2547 2.461 0.2259 3.678 0.6277 0.114 0.0437 0.114 0.0437 0.227 0.0874 2.733 1.8977 5.545 3.7604 8.523 5.3603-3.004-1.0221-5.912-2.4645-8.732-4.1345-0.639-0.376-1.2-0.7871-1.962-1.0141-1.786-0.6203-3.747 0.064-5.558 0.8708 0.28-0.2191 0.752-0.4296 0.91-0.4997 0.473-0.2105 0.866-0.3859 1.339-0.5963 0.481-0.4031 0.954-0.6136 1.575-0.7016z" fill="#8b8b8b" style=""/>
  <path d="m333.26 98.691c0.227 0.0874 0.569 0.2184 0.797 0.3058 0.035 0.0788 0.148 0.1225 0.148 0.1225 0.64 0.376 1.279 0.7519 1.884 1.0489 1.252 0.481 2.222 0.66 3.474 0.292 0.665-0.202 1.712-0.9747 2.342-1.2553 1.811-0.8068 3.821-0.4968 5.721-0.682 1.707-0.1939 5.111 2.5363 6.898 3.2603 1.787 0.723 2.072 1.046 3.282 1.746 1.939 1.122 1.962 1.014 2.943 1.522 1.322 0.638 1.982 0.731 2.468 2.236-0.019 2-2.543 4.469-2.562 6.476 0.855 1.151 1.489 0.526 2.942 0.822 0.762 0.228 1.524 0.455 2.163 0.831 1.543 1.767 3.006 3.569 4.513 5.258-0.867-0.464-1.647-1.155-2.348-1.881-0.92-1.006-1.56-2.231-2.725-2.939-1.313-0.831-2.977-0.751-4.544-1.091-2.487-0.497 3.386-7.404 1.126-8.663-2.259-1.259-3.784-2.466-6.332-2.465-2.433 0.046-7.692-4.6047-10.021-4.3228-0.735 0.0443-1.514 0.2025-2.258 0.4394-0.508 0.1317-0.981 0.3424-1.532 0.5874-0.63 0.281-1.181 0.526-1.847 0.728-0.393 0.176-0.822 0.272-1.208 0.255-0.464 0.018-0.954-0.236-1.331-0.445-1.357-0.717-2.715-1.4342-3.993-2.1862z" fill="#8b8b8b" style=""/>
 </g>
</svg>