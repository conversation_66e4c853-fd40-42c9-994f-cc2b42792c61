{"fqn": "temperature_chart_card_with_background", "name": "Temperature chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/temperature_chart_card_with_background_system_widget_image.png", "description": "Displays a temperature data by combining the latest and aggregated values with the background image and optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'temperature', label: 'Temperature', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: '°C', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'temperature', '°C', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#224AC2\"},{\"from\":-20,\"to\":0,\"color\":\"#2B54CE\"},{\"from\":0,\"to\":10,\"color\":\"#6083EC\"},{\"from\":10,\"to\":20,\"color\":\"#F89E0D\"},{\"from\":20,\"to\":30,\"color\":\"#F77410\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/temperature_range_chart_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Temperature\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"device_thermostat\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["temperature", "weather", "environment"], "resources": [{"link": "/api/images/system/temperature_chart_card_with_background_system_widget_image.png", "title": "\"Temperature chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "temperature_chart_card_with_background_system_widget_image.png", "publicResourceKey": "DEmsIaoK12ehx5KV6LppC15vK3J25gx1", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/temperature_range_chart_with_background_system_widget_background.png", "title": "\"Temperature range chart with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "temperature_range_chart_with_background_system_widget_background.png", "publicResourceKey": "cFDUeqscH9SGZzA7czgJGJuL8LwEUiKb", "mediaType": "image/png", "data": "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", "public": true}]}