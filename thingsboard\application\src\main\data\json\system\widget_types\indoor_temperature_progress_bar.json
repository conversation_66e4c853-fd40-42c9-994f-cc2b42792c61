{"fqn": "indoor_temperature_progress_bar", "name": "Indoor temperature progress bar", "deprecated": false, "image": "tb-image;/api/images/system/indoor_temperature_progress_bar_system_widget_image.png", "description": "Displays indoor temperature reading as a horizontal progress bar. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'temperature', label: 'Temperature', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nif (value < 15) {\\n\\tvalue = 15;\\n} else if (value > 30) {\\n\\tvalue = 30;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":18,\"color\":\"#234CC7\"},{\"from\":18,\"to\":24,\"color\":\"#3FA71A\"},{\"from\":24,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":40,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":18,\"color\":\"#234CC7\"},{\"from\":18,\"to\":24,\"color\":\"#3FA71A\"},{\"from\":24,\"to\":null,\"color\":\"#D81838\"}]},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Temperature\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"°C\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"device_thermostat\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["progress", "environment", "indoor", "temperature"], "resources": [{"link": "/api/images/system/indoor_temperature_progress_bar_system_widget_image.png", "title": "\"Indoor temperature progress bar\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_temperature_progress_bar_system_widget_image.png", "publicResourceKey": "HuE97PKUWyAbc7c1aGnCunqRoA15jJFK", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAqFBMVEXg4ODf39/g4OAAAADg4ODf39/////g4OD19fUjTMchISHj4+OsrKw9PT10dHSQkJDHx8fj6Pjx8fEvLy9YWFienp7v7++BgYG6urry9Ps/Ys51j9w+Ys6GhobV1dWXl5esvOpaedU8PDxLbdEwV8upqanI0vG5ubn39/fLy8vk6fiRpeODm+BKSkrj6fja3/Fng9jU1NTDw8O6x+6fsOdXdtKrq6tmZmbNeyDTAAAABnRSTlPvIL8Ar7DvmsykAAAEFElEQVR42uzPwREAQAQDwDiGv4f+S702wmQ7WLg91HZhDsPkeg1DdB4wD5Un4EqkFCGjCBtF2CjCRhE2irBRhI0in/1y13EchqFod4tLvSBZUCoVbly5nfn/P1tSNoJdON5iCjuDmQOEERmG4EmAxH43zkWS2xF8B85FAnccvoiLGadcJ5JFEr2IfHmbiYLLMJFThH5zqqEJgFglBgdpGsYHbpmRWrPDEiVWy0IV5OjZHlbTSuz2qG2blWBcLyKenqwAS9FDsBCBib6QE4DAUhjGs+aRZaUX8aRvWkvQkkO0Icgri81SrhcJjJBSMsgEIatlKmLlVUuOnxmfeghcU8+cM9pmKvhbZF76NmvWWbhBhEVDY8dIPUfY93RseujAg3EsbWRZGI4idX/jSIHLRYQb7pVIYtBkEHaRx0rlKDK25+AukdkZci5SnZJs6dGekE5FyjbrDhEUiiV4JRL4gTBW7PvSDzb8I7IA7SkyWQsyBleLVPrqJn8U8S6yCHop0QX2TSRpd/UM5hAeqKPrKbKMWeWebwRxJstyFAlaTgC6vV6xiSBq0jhZWWMO5ByeIviw3qt+fo+IvPzjzoKNLFnjIZE80v/Puv3qd+I7XU7+igA94Y34ATdW34xfkT/sz6EKwDAQwFB3cHKyTA3Gqf3/941SUdfaUPJEfGgcoXGExhEaR2gcoXGExhEaR2gcoXGExhEaR2gcoXGExhEaR2gcoXGEZj/SRq8piDYjrZ6M7s6pAmg5Ut+becDIz34drEYIQ1EY3h08S5dpskhIkEHFEWbm/V+tkJFSLUy5ljDXmm+j25/rDSYxW39aiQ4avQohw/BB4jtPemj0KmRogW1IVDqQ35Z9G9KRA1QShsxki6flaQxUkIX0pMXTGJ0B+kA6FTOShdivgZiA0QPOGtyShvUXhfRkwCIOroV3eTgB7ycKseQNi8l3wDzn1yveTxLSOzqsBAstJCGe9P8i5MdAYHOI0fA/LAjxpMXaPRkA9mDL7sgWa8aNBt3Bjt88kK0pMqU7FMghe6lZkHPd2Q+ihmhTQ7SpIdrUEG1qiDY1RJsThPSBe8VrU4w8JHK/R1PMRRzCv2jKEYcE7hebYuQTmSL3epTbkcuJT62DqSHa1JDP9u4lhUEgCMLwmEcpGCIELN00vRrQpd7/cBGyD0xW5aR/mAN8NEwvW62AqPUFsg8+Q6XdAbo7yyE0wlXGRTMg71i9HLKNwKwykrwaYEQ3lUPWrANZMw144ninhtBZByQPeZweMAJTOYRTh3GDQgvZfr6ezcshmG1wqEQDenPrf4BgWSAW/3uzn6uAqBUQtQKiVkDUCohaAVErIGoFRK2AqBUQtSqCJFRRmy4VnJ4GXvfUpAqOT/NgXJtbe/bSpbm+AQFDJgMgD9a7AAAAAElFTkSuQmCC", "public": true}]}