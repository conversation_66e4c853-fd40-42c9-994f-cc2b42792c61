{"fqn": "radon_level_chart_card", "name": "Radon level chart card", "deprecated": false, "image": "tb-image;/api/images/system/radon_level_chart_card_system_widget_image.png", "description": "Displays a radon level data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'radon', label: 'Radon level', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'Bq/m³', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'radon', 'Bq/m³', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Radon level\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 75 - 37.5;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 300) {\\n\\tvalue = 300;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"Bq/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":100,\"color\":\"#80C32C\"},{\"from\":100,\"to\":200,\"color\":\"#FFA600\"},{\"from\":200,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 75 - 37.5;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 300) {\\n\\tvalue = 300;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"Bq/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 8 - 4;\\nif (value < -15) {\\n\\tvalue = -15;\\n} else if (value > 15) {\\n\\tvalue = 15;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"Bq/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Radon level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:radioactive\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "indoor", "air", "radon"], "resources": [{"link": "/api/images/system/radon_level_chart_card_system_widget_image.png", "title": "\"Radon level chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "radon_level_chart_card_system_widget_image.png", "publicResourceKey": "NhM0b1i8ceseamzFQ7uoM8AlSWCvi63u", "mediaType": "image/png", "data": "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", "public": true}]}