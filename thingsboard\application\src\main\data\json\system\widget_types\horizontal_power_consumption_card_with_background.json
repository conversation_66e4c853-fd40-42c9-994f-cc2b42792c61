{"fqn": "horizontal_power_consumption_card_with_background", "name": "Horizontal power consumption card with background", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_power_consumption_card_with_background.svg", "description": "Displays the latest power consumption telemetry in a scalable horizontal layout with the background image.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'powerConsumption', label: 'Power consumption', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Power consumption\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"horizontal\",\"autoScale\":true,\"showLabel\":true,\"labelFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"#000000DE\",\"rangeList\":null,\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"bolt\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":5,\"color\":\"#3B911C\"},{\"from\":5,\"to\":15,\"color\":\"#F77410\"},{\"from\":15,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":5,\"color\":\"#3B911C\"},{\"from\":5,\"to\":15,\"color\":\"#F77410\"},{\"from\":15,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/horizontal_power_consumption_card_background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Horizontal power consumption card with background\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"units\":\"kW\",\"decimals\":0,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"showTitleIcon\":false,\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\"}"}, "tags": ["power", "energy", "energy usage", "electric load", "electricity", "power efficiency", "load profile"], "resources": [{"link": "/api/images/system/horizontal_power_consumption_card_background.png", "title": "horizontal_power_consumption_card_background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_power_consumption_card_background.png", "publicResourceKey": "3H8SQbLTmhaI7UhSzJzU9tKLs0g4AmsG", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/horizontal_power_consumption_card_with_background.svg", "title": "horizontal_power_consumption_card_with_background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_power_consumption_card_with_background.svg", "publicResourceKey": "Nm7XnxCpVdjGHfK59QLO3KDLUoGemCIr", "mediaType": "image/svg+xml", "data": "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", "public": true}]}