{"title": "Rule Engine Statistics", "image": null, "mobileHide": false, "mobileOrder": null, "configuration": {"widgets": {"5eb79712-5c24-3060-7e4f-6af36b8f842d": {"type": "timeseries", "sizeX": 24, "sizeY": 5, "config": {"datasources": [{"type": "entity", "entityAliasId": "140f23dd-e3a0-ed98-6189-03c49d2d8018", "dataKeys": [{"name": "ruleEngineException", "type": "timeseries", "label": "Rule Chain", "color": "#2196f3", "settings": {"useCellStyleFunction": false, "useCellContentFunction": true, "cellContentFunction": "return JSON.parse(value).ruleChainName;"}, "_hash": 0.9954481282345906}, {"name": "ruleEngineException", "type": "timeseries", "label": "Rule Node", "color": "#4caf50", "settings": {"useCellStyleFunction": false, "useCellContentFunction": true, "cellContentFunction": "return JSON.parse(value).ruleNodeName;"}, "_hash": 0.18580357036589978}, {"name": "ruleEngineException", "type": "timeseries", "label": "Latest Error", "color": "#f44336", "settings": {"useCellStyleFunction": false, "useCellContentFunction": true, "cellContentFunction": "return JSON.parse(value).message;"}, "_hash": 0.7255162989552142}], "alarmFilterConfig": {"statusList": ["ACTIVE"]}, "latestDataKeys": [{"name": "queueName", "type": "entityField", "label": "Queue name", "color": "#ffc107", "settings": {"show": false, "order": null, "useCellStyleFunction": false, "cellStyleFunction": "", "useCellContentFunction": false, "cellContentFunction": "", "defaultColumnVisibility": "visible", "columnSelectionToDisplay": "enabled"}, "_hash": 0.8104572478982748, "aggregationType": null, "units": null, "decimals": null, "funcBody": null, "usePostProcessing": null, "postFuncBody": null}, {"name": "serviceId", "type": "entityField", "label": "Service Id", "color": "#607d8b", "settings": {"show": false, "order": null, "useCellStyleFunction": false, "cellStyleFunction": "", "useCellContentFunction": false, "cellContentFunction": "", "defaultColumnVisibility": "visible", "columnSelectionToDisplay": "enabled"}, "_hash": 0.38329217099945034, "aggregationType": null, "units": null, "decimals": null, "funcBody": null, "usePostProcessing": null, "postFuncBody": null}]}], "timewindow": {"realtime": {"interval": 1000, "timewindowMs": 86400000}, "aggregation": {"type": "NONE", "limit": 200}}, "showTitle": true, "backgroundColor": "rgb(255, 255, 255)", "color": "rgba(0, 0, 0, 0.87)", "padding": "8px", "settings": {"showTimestamp": true, "displayPagination": true, "defaultPageSize": 10, "enableSearch": true, "enableSelectColumnDisplay": true}, "title": "Exceptions", "dropShadow": true, "enableFullscreen": true, "titleStyle": {"fontSize": "16px", "fontWeight": 400}, "useDashboardTimewindow": false, "showLegend": false, "widgetStyle": {}, "actions": {}, "showTitleIcon": false, "titleIcon": null, "iconColor": "rgba(0, 0, 0, 0.87)", "iconSize": "24px", "titleTooltip": "", "displayTimewindow": true, "configMode": "basic", "titleFont": null, "titleColor": null}, "id": "5eb79712-5c24-3060-7e4f-6af36b8f842d", "typeFullFqn": "system.cards.timeseries_table"}, "42face47-730d-f930-fef5-2a1ef6304b16": {"typeFullFqn": "system.time_series_chart", "type": "timeseries", "sizeX": 8, "sizeY": 5, "config": {"datasources": [{"type": "entity", "entityAliasId": "140f23dd-e3a0-ed98-6189-03c49d2d8018", "dataKeys": [{"name": "successfulMsgs", "type": "timeseries", "label": "{i18n:api-usage.successful}", "color": "#4caf50", "settings": {"yAxisId": "default", "showInLegend": true, "dataHiddenByDefault": false, "type": "line", "lineSettings": {"showLine": true, "step": false, "stepType": "start", "smooth": false, "lineType": "solid", "lineWidth": 2.5, "showPoints": false, "showPointLabel": false, "pointLabelPosition": "top", "pointLabelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "pointLabelColor": "rgba(0, 0, 0, 0.76)", "pointShape": "circle", "pointSize": 12, "fillAreaSettings": {"type": "none", "opacity": 0.4, "gradient": {"start": 100, "end": 0}}}, "barSettings": {"showBorder": false, "borderWidth": 2, "borderRadius": 0, "showLabel": false, "labelPosition": "top", "labelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.76)", "backgroundSettings": {"type": "none", "opacity": 0.4, "gradient": {"start": 100, "end": 0}}}}, "_hash": 0.15490750967648736, "aggregationType": null, "units": null, "decimals": null, "funcBody": null, "usePostProcessing": null, "postFuncBody": null}, {"name": "failedMsgs", "type": "timeseries", "label": "{i18n:api-usage.permanent-failures}", "color": "#ef5350", "settings": {"yAxisId": "default", "showInLegend": true, "dataHiddenByDefault": false, "type": "line", "lineSettings": {"showLine": true, "step": false, "stepType": "start", "smooth": false, "lineType": "solid", "lineWidth": 2.5, "showPoints": false, "showPointLabel": false, "pointLabelPosition": "top", "pointLabelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "pointLabelColor": "rgba(0, 0, 0, 0.76)", "pointShape": "circle", "pointSize": 12, "fillAreaSettings": {"type": "none", "opacity": 0.4, "gradient": {"start": 100, "end": 0}}}, "barSettings": {"showBorder": false, "borderWidth": 2, "borderRadius": 0, "showLabel": false, "labelPosition": "top", "labelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.76)", "backgroundSettings": {"type": "none", "opacity": 0.4, "gradient": {"start": 100, "end": 0}}}}, "_hash": 0.4186621166514697, "aggregationType": null, "units": null, "decimals": null, "funcBody": null, "usePostProcessing": null, "postFuncBody": null}, {"name": "tmpFailed", "type": "timeseries", "label": "{i18n:api-usage.processing-failures}", "color": "#ffc107", "settings": {"yAxisId": "default", "showInLegend": true, "dataHiddenByDefault": false, "type": "line", "lineSettings": {"showLine": true, "step": false, "stepType": "start", "smooth": false, "lineType": "solid", "lineWidth": 2.5, "showPoints": false, "showPointLabel": false, "pointLabelPosition": "top", "pointLabelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "pointLabelColor": "rgba(0, 0, 0, 0.76)", "pointShape": "circle", "pointSize": 12, "fillAreaSettings": {"type": "none", "opacity": 0.4, "gradient": {"start": 100, "end": 0}}}, "barSettings": {"showBorder": false, "borderWidth": 2, "borderRadius": 0, "showLabel": false, "labelPosition": "top", "labelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.76)", "backgroundSettings": {"type": "none", "opacity": 0.4, "gradient": {"start": 100, "end": 0}}}}, "_hash": 0.49891007198715376, "aggregationType": null, "units": null, "decimals": null, "funcBody": null, "usePostProcessing": null, "postFuncBody": null}], "alarmFilterConfig": {"statusList": ["ACTIVE"]}, "latestDataKeys": [{"name": "queueName", "type": "entityField", "label": "Queue name", "color": "#ffc107", "settings": {}, "_hash": 0.8012481564934415}, {"name": "serviceId", "type": "entityField", "label": "Service Id", "color": "#607d8b", "settings": {}, "_hash": 0.0724871638610094}]}], "timewindow": {"hideInterval": false, "hideLastInterval": false, "hideQuickInterval": false, "hideAggregation": false, "hideAggInterval": false, "hideTimezone": false, "selectedTab": 0, "realtime": {"realtimeType": 0, "timewindowMs": 300000, "quickInterval": "CURRENT_DAY", "interval": 1000}, "aggregation": {"type": "NONE", "limit": 8640}}, "showTitle": true, "backgroundColor": "#FFFFFF", "color": "rgba(0, 0, 0, 0.87)", "padding": "0px", "settings": {"yAxes": {"default": {"units": null, "decimals": 0, "show": true, "label": "", "labelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "600", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.54)", "position": "left", "showTickLabels": true, "tickLabelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "tickLabelColor": "rgba(0, 0, 0, 0.54)", "ticksFormatter": "var rounder = Math.pow(10, 1);\nvar powers = [\n  {key: 'Q', value: Math.pow(10, 15)},\n  {key: 'T', value: Math.pow(10, 12)},\n  {key: 'B', value: Math.pow(10, 9)},\n  {key: 'M', value: Math.pow(10, 6)},\n  {key: 'K', value: 1000}\n];\n\nvar key = '';\n\nfor (var i = 0; i < powers.length; i++) {\n    var reduced = value / powers[i].value;\n    reduced = Math.round(reduced * rounder) / rounder;\n    if (reduced >= 1) {\n        value = reduced;\n        key = powers[i].key;\n        break;\n    }\n}\nreturn value + key;", "showTicks": true, "ticksColor": "rgba(0, 0, 0, 0.54)", "showLine": true, "lineColor": "rgba(0, 0, 0, 0.54)", "showSplitLines": true, "splitLinesColor": "rgba(0, 0, 0, 0.12)", "id": "default", "order": 0, "min": null, "max": null}}, "thresholds": [], "dataZoom": true, "stack": false, "xAxis": {"show": true, "label": "", "labelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "600", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.54)", "position": "bottom", "showTickLabels": true, "tickLabelFont": {"family": "Roboto", "size": 10, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "tickLabelColor": "rgba(0, 0, 0, 0.54)", "showTicks": true, "ticksColor": "rgba(0, 0, 0, 0.54)", "showLine": true, "lineColor": "rgba(0, 0, 0, 0.54)", "showSplitLines": true, "splitLinesColor": "rgba(0, 0, 0, 0.12)"}, "noAggregationBarWidthSettings": {"strategy": "group", "groupWidth": {"relative": false, "relativeWidth": 2, "absoluteWidth": 1800000}, "barWidth": {"relative": true, "relativeWidth": 2, "absoluteWidth": 1000}}, "showLegend": true, "legendLabelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "16px"}, "legendLabelColor": "rgba(0, 0, 0, 0.76)", "legendConfig": {"direction": "column", "position": "bottom", "sortDataKeys": false, "showMin": true, "showMax": true, "showAvg": false, "showTotal": true, "showLatest": false}, "showTooltip": true, "tooltipTrigger": "axis", "tooltipValueFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "500", "lineHeight": "16px"}, "tooltipValueColor": "rgba(0, 0, 0, 0.76)", "tooltipShowDate": true, "tooltipDateFormat": {"format": "yyyy-MM-dd HH:mm:ss", "lastUpdateAgo": false, "custom": false}, "tooltipDateFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "16px"}, "tooltipDateColor": "rgba(0, 0, 0, 0.76)", "tooltipDateInterval": true, "tooltipHideZeroValues": true, "tooltipBackgroundColor": "rgba(255, 255, 255, 0.76)", "tooltipBackgroundBlur": 4, "animation": {"animation": true, "animationThreshold": 2000, "animationDuration": 300, "animationEasing": "cubicOut", "animationDelay": 0, "animationDurationUpdate": 300, "animationEasingUpdate": "cubicOut", "animationDelayUpdate": 0}, "background": {"type": "color", "color": "#fff", "overlay": {"enabled": false, "color": "rgba(255,255,255,0.72)", "blur": 3}}, "padding": "12px"}, "title": "{i18n:api-usage.queue-stats}", "dropShadow": true, "enableFullscreen": true, "titleStyle": null, "configMode": "basic", "actions": {}, "showTitleIcon": false, "titleIcon": "thermostat", "iconColor": "#1F6BDD", "useDashboardTimewindow": false, "displayTimewindow": true, "titleFont": {"size": 16, "sizeUnit": "px", "family": "Roboto", "weight": "500", "style": "normal", "lineHeight": "24px"}, "titleColor": "rgba(0, 0, 0, 0.87)", "titleTooltip": "", "widgetStyle": {}, "widgetCss": "", "pageSize": 1024, "units": "", "decimals": null, "noDataDisplayMessage": "", "timewindowStyle": {"showIcon": false, "iconSize": "24px", "icon": null, "iconPosition": "left", "font": {"size": 12, "sizeUnit": "px", "family": "Roboto", "weight": "400", "style": "normal", "lineHeight": "16px"}, "color": "rgba(0, 0, 0, 0.38)", "displayTypePrefix": true}, "margin": "0px", "borderRadius": "0px", "iconSize": "0px"}, "row": 0, "col": 0, "id": "42face47-730d-f930-fef5-2a1ef6304b16"}, "6a74ab56-cb36-e75e-c094-a51a896da94a": {"typeFullFqn": "system.time_series_chart", "type": "timeseries", "sizeX": 8, "sizeY": 5, "config": {"datasources": [{"type": "entity", "entityAliasId": "140f23dd-e3a0-ed98-6189-03c49d2d8018", "dataKeys": [{"name": "timeoutMsgs", "type": "timeseries", "label": "{i18n:api-usage.permanent-timeouts}", "color": "#4caf50", "settings": {"yAxisId": "default", "showInLegend": true, "dataHiddenByDefault": false, "type": "line", "lineSettings": {"showLine": true, "step": false, "stepType": "start", "smooth": false, "lineType": "solid", "lineWidth": 2.5, "showPoints": false, "showPointLabel": false, "pointLabelPosition": "top", "pointLabelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "pointLabelColor": "rgba(0, 0, 0, 0.76)", "pointShape": "circle", "pointSize": 12, "fillAreaSettings": {"type": "none", "opacity": 0.4, "gradient": {"start": 100, "end": 0}}}, "barSettings": {"showBorder": false, "borderWidth": 2, "borderRadius": 0, "showLabel": false, "labelPosition": "top", "labelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.76)", "backgroundSettings": {"type": "none", "opacity": 0.4, "gradient": {"start": 100, "end": 0}}}}, "_hash": 0.565222981550328, "aggregationType": null, "units": null, "decimals": null, "funcBody": null, "usePostProcessing": null, "postFuncBody": null}, {"name": "tmpTimeout", "type": "timeseries", "label": "{i18n:api-usage.processing-timeouts}", "color": "#9c27b0", "settings": {"yAxisId": "default", "showInLegend": true, "dataHiddenByDefault": false, "type": "line", "lineSettings": {"showLine": true, "step": false, "stepType": "start", "smooth": false, "lineType": "solid", "lineWidth": 2.5, "showPoints": false, "showPointLabel": false, "pointLabelPosition": "top", "pointLabelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "pointLabelColor": "rgba(0, 0, 0, 0.76)", "pointShape": "circle", "pointSize": 12, "fillAreaSettings": {"type": "none", "opacity": 0.4, "gradient": {"start": 100, "end": 0}}}, "barSettings": {"showBorder": false, "borderWidth": 2, "borderRadius": 0, "showLabel": false, "labelPosition": "top", "labelFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.76)", "backgroundSettings": {"type": "none", "opacity": 0.4, "gradient": {"start": 100, "end": 0}}}}, "_hash": 0.2679547062508352, "aggregationType": null, "units": null, "decimals": null, "funcBody": null, "usePostProcessing": null, "postFuncBody": null}], "alarmFilterConfig": {"statusList": ["ACTIVE"]}, "latestDataKeys": [{"name": "queueName", "type": "entityField", "label": "Queue name", "color": "#f44336", "settings": {}, "_hash": 0.7242351292118758}, {"name": "serviceId", "type": "entityField", "label": "Service Id", "color": "#ffc107", "settings": {}, "_hash": 0.3347262075244206}]}], "timewindow": {"hideInterval": false, "hideLastInterval": false, "hideQuickInterval": false, "hideAggregation": false, "hideAggInterval": false, "hideTimezone": false, "selectedTab": 0, "realtime": {"realtimeType": 0, "timewindowMs": 300000, "quickInterval": "CURRENT_DAY", "interval": 1000}, "aggregation": {"type": "NONE", "limit": 8640}}, "showTitle": true, "backgroundColor": "#FFFFFF", "color": "rgba(0, 0, 0, 0.87)", "padding": "0px", "settings": {"yAxes": {"default": {"units": null, "decimals": 0, "show": true, "label": "", "labelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "600", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.54)", "position": "left", "showTickLabels": true, "tickLabelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "tickLabelColor": "rgba(0, 0, 0, 0.54)", "ticksFormatter": "var rounder = Math.pow(10, 1);\nvar powers = [\n  {key: 'Q', value: Math.pow(10, 15)},\n  {key: 'T', value: Math.pow(10, 12)},\n  {key: 'B', value: Math.pow(10, 9)},\n  {key: 'M', value: Math.pow(10, 6)},\n  {key: 'K', value: 1000}\n];\n\nvar key = '';\n\nfor (var i = 0; i < powers.length; i++) {\n    var reduced = value / powers[i].value;\n    reduced = Math.round(reduced * rounder) / rounder;\n    if (reduced >= 1) {\n        value = reduced;\n        key = powers[i].key;\n        break;\n    }\n}\nreturn value + key;", "showTicks": true, "ticksColor": "rgba(0, 0, 0, 0.54)", "showLine": true, "lineColor": "rgba(0, 0, 0, 0.54)", "showSplitLines": true, "splitLinesColor": "rgba(0, 0, 0, 0.12)", "id": "default", "order": 0, "min": null, "max": null}}, "thresholds": [], "dataZoom": true, "stack": false, "xAxis": {"show": true, "label": "", "labelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "600", "lineHeight": "1"}, "labelColor": "rgba(0, 0, 0, 0.54)", "position": "bottom", "showTickLabels": true, "tickLabelFont": {"family": "Roboto", "size": 10, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "1"}, "tickLabelColor": "rgba(0, 0, 0, 0.54)", "showTicks": true, "ticksColor": "rgba(0, 0, 0, 0.54)", "showLine": true, "lineColor": "rgba(0, 0, 0, 0.54)", "showSplitLines": true, "splitLinesColor": "rgba(0, 0, 0, 0.12)"}, "noAggregationBarWidthSettings": {"strategy": "group", "groupWidth": {"relative": false, "relativeWidth": 2, "absoluteWidth": 1800000}, "barWidth": {"relative": true, "relativeWidth": 2, "absoluteWidth": 1000}}, "showLegend": true, "legendLabelFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "16px"}, "legendLabelColor": "rgba(0, 0, 0, 0.76)", "legendConfig": {"direction": "column", "position": "bottom", "sortDataKeys": false, "showMin": true, "showMax": true, "showAvg": false, "showTotal": true, "showLatest": false}, "showTooltip": true, "tooltipTrigger": "axis", "tooltipValueFont": {"family": "Roboto", "size": 12, "sizeUnit": "px", "style": "normal", "weight": "500", "lineHeight": "16px"}, "tooltipValueColor": "rgba(0, 0, 0, 0.76)", "tooltipShowDate": true, "tooltipDateFormat": {"format": "yyyy-MM-dd HH:mm:ss", "lastUpdateAgo": false, "custom": false}, "tooltipDateFont": {"family": "Roboto", "size": 11, "sizeUnit": "px", "style": "normal", "weight": "400", "lineHeight": "16px"}, "tooltipDateColor": "rgba(0, 0, 0, 0.76)", "tooltipDateInterval": true, "tooltipHideZeroValues": true, "tooltipBackgroundColor": "rgba(255, 255, 255, 0.76)", "tooltipBackgroundBlur": 4, "animation": {"animation": true, "animationThreshold": 2000, "animationDuration": 300, "animationEasing": "cubicOut", "animationDelay": 0, "animationDurationUpdate": 300, "animationEasingUpdate": "cubicOut", "animationDelayUpdate": 0}, "background": {"type": "color", "color": "#fff", "overlay": {"enabled": false, "color": "rgba(255,255,255,0.72)", "blur": 3}}, "padding": "12px"}, "title": "{i18n:api-usage.processing-failures-and-timeouts}", "dropShadow": true, "enableFullscreen": true, "titleStyle": null, "configMode": "basic", "actions": {}, "showTitleIcon": false, "titleIcon": "thermostat", "iconColor": "#1F6BDD", "useDashboardTimewindow": false, "displayTimewindow": true, "titleFont": {"size": 16, "sizeUnit": "px", "family": "Roboto", "weight": "500", "style": "normal", "lineHeight": "24px"}, "titleColor": "rgba(0, 0, 0, 0.87)", "titleTooltip": "", "widgetStyle": {}, "widgetCss": "", "pageSize": 1024, "units": "", "decimals": null, "noDataDisplayMessage": "", "timewindowStyle": {"showIcon": false, "iconSize": "24px", "icon": null, "iconPosition": "left", "font": {"size": 12, "sizeUnit": "px", "family": "Roboto", "weight": "400", "style": "normal", "lineHeight": "16px"}, "color": "rgba(0, 0, 0, 0.38)", "displayTypePrefix": true}, "margin": "0px", "borderRadius": "0px", "iconSize": "0px"}, "row": 0, "col": 0, "id": "6a74ab56-cb36-e75e-c094-a51a896da94a"}}, "states": {"default": {"name": "Rule Engine Statistics", "root": true, "layouts": {"main": {"widgets": {"5eb79712-5c24-3060-7e4f-6af36b8f842d": {"sizeX": 24, "sizeY": 5, "row": 7, "col": 0}, "42face47-730d-f930-fef5-2a1ef6304b16": {"sizeX": 12, "sizeY": 7, "row": 0, "col": 0}, "6a74ab56-cb36-e75e-c094-a51a896da94a": {"sizeX": 12, "sizeY": 7, "row": 0, "col": 12}}, "gridSettings": {"backgroundColor": "#eeeeee", "color": "rgba(0,0,0,0.870588)", "columns": 24, "backgroundSizeMode": "100%", "autoFillHeight": true, "mobileAutoFillHeight": false, "mobileRowHeight": 70, "margin": 10, "outerMargin": true}}}}}, "entityAliases": {"140f23dd-e3a0-ed98-6189-03c49d2d8018": {"id": "140f23dd-e3a0-ed98-6189-03c49d2d8018", "alias": "TbServiceQueues", "filter": {"type": "entityType", "resolveMultiple": true, "entityType": "QUEUE_STATS"}}}, "timewindow": {"displayValue": "", "selectedTab": 0, "hideInterval": false, "hideAggregation": false, "hideAggInterval": false, "realtime": {"interval": 1000, "timewindowMs": 60000}, "history": {"historyType": 0, "interval": 1000, "timewindowMs": 60000, "fixedTimewindow": {"startTimeMs": 1586176634823, "endTimeMs": 1586263034823}}, "aggregation": {"type": "AVG", "limit": 25000}}, "settings": {"stateControllerId": "entity", "showTitle": false, "showDashboardsSelect": true, "showEntitiesSelect": true, "showDashboardTimewindow": true, "showDashboardExport": true, "toolbarAlwaysOpen": true}, "filters": {}}, "name": "Rule Engine Statistics", "resources": []}