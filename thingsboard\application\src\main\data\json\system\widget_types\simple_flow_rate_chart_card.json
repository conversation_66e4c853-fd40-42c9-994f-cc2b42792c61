{"fqn": "simple_flow_rate_chart_card", "name": "Simple flow rate chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_flow_rate_chart_card_(1).svg", "description": "Displays historical flow rate values as a simplified chart. Optionally may display the corresponding latest flow rate value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'flowRate', label: 'Flow rate', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'flowRate', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Flow rate\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#305AD7\"},{\"from\":10,\"to\":30,\"color\":\"#3FA71A\"},{\"from\":30,\"to\":50,\"color\":\"#F36900\"},{\"from\":50,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Flow rate\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:hydro-power\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"units\":\"m³/hr\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true},\"margin\":\"0px\"}"}, "tags": ["liquid", "fluid", "flow rate", "fluid dynamics", "velocity", "mass flow", "volume flow"], "resources": [{"link": "/api/images/system/simple_flow_rate_chart_card_(1).svg", "title": "simple_flow_rate_chart_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_flow_rate_chart_card_(1).svg", "publicResourceKey": "TmvjIHJ1G4KUoNMm4i0P47gkQjGxhuly", "mediaType": "image/svg+xml", "data": "PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMTYwIiBmaWxsPSJub25lIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjExMiIgeT0iMjQiIGZpbGw9IiNmZmYiIHJ4PSI0Ii8+PHJlY3Qgd2lkdGg9IjE5OSIgaGVpZ2h0PSIxMTEiIHg9Ii41IiB5PSIyNC41IiBzdHJva2U9IiMwMDAiIHN0cm9rZS1vcGFjaXR5PSIuMSIgcng9IjMuNSIvPjxwYXRoIGZpbGw9IiMwMDAiIGZpbGwtb3BhY2l0eT0iLjkiIGQ9Ik0xOC43IDM5djEwSDE3VjM5aDEuN1ptNCA0LjR2MS40aC00LjR2LTEuNGg0LjVabS43LTQuNHYxLjRoLTUuMVYzOWg1Wm0zLjItLjVWNDloLTEuN1YzOC41aDEuN1ptMS43IDYuOXYtLjJjMC0uNSAwLTEgLjItMS41bC43LTEuMmEzIDMgMCAwIDEgMS0uN2wxLjUtLjNjLjYgMCAxIDAgMS41LjMuNC4xLjguNCAxIC43LjQuNC42LjguOCAxLjJsLjIgMS41di4yYzAgLjUgMCAxLS4yIDEuNWwtLjcgMS4yYTMuMSAzLjEgMCAwIDEtMi41IDFjLS42IDAtMSAwLTEuNS0uMmEzLjYgMy42IDAgMCAxLTEuOC0ybC0uMi0xLjVabTEuNy0uMnYxLjJsLjQuNy41LjVjLjMuMi41LjIuOS4yYTEuNiAxLjYgMCAwIDAgMS4zLS43Yy4yLS4yLjMtLjUuMy0uOGwuMS0xdi0xbC0uNC0uOGExLjYgMS42IDAgMCAwLTEuNC0uN2MtLjMgMC0uNSAwLS44LjJsLS41LjVjLS4yLjItLjMuNS0uMy44bC0uMSAxWm04LjggMi4yIDEuNy01LjhoMWwtLjIgMS43LTEuNyA1LjdoLTFsLjItMS42Wm0tMS01LjggMS40IDUuOFY0OWgtMWwtMi03LjRoMS42Wm01LjQgNS43IDEuMy01LjdINDZMNDQgNDloLTF2LTEuN1ptLTEuNC01LjcgMS43IDUuNy4yIDEuN2gtMUw0MSA0My4zbC0uMy0xLjdoMVptMTEgMS40djZoLTEuNnYtNy40aDEuNlY0M1ptMi4zLTEuNFY0M2EzLjIgMy4yIDAgMCAwLTEuNCAwIDEuNCAxLjQgMCAwIDAtLjkgMWwtLjEuNmgtLjRsLjEtMS4yLjUtMWMuMS0uNC40LS42LjYtLjhhMS45IDEuOSAwIDAgMSAxLjMtLjJoLjNabTUuMiA2VjQ0bC0uMS0uN2ExIDEgMCAwIDAtLjUtLjRsLS43LS4yLS43LjEtLjQuNC0uMi41aC0xLjZjMC0uMyAwLS41LjItLjguMS0uMy4zLS41LjYtLjdhMyAzIDAgMCAxIDEtLjUgNCA0IDAgMCAxIDEuMi0uMmMuNiAwIDEgMCAxLjUuMi40LjIuOC41IDEgLjkuMi40LjQuOC40IDEuNHY0LjJjMCAuMy4yLjUuMy43di4xaC0xLjdhMyAzIDAgMCAxLS4yLS43di0uOFptLjMtMy4xdjFoLTEuMmwtLjguMS0uNi4zYTEgMSAwIDAgMC0uNS45YzAgLjIgMCAuNC4yLjUgMCAuMi4yLjMuNC40bC42LjFhMS44IDEuOCAwIDAgMCAxLjUtLjdsLjItLjYuNi44LS4zLjZhMyAzIDAgMCAxLS41LjYgMi42IDIuNiAwIDAgMS0xLjguNmMtLjUgMC0xIDAtMS4zLS4zLS40LS4xLS43LS40LS45LS43YTIuNCAyLjQgMCAwIDEtLjEtMi4ybC43LS44IDEtLjQgMS41LS4yaDEuM1ptNi43LTIuOXYxLjJoLTQuMXYtMS4yaDQuMVptLTMtMS44SDY2VjQ3bC4xLjUuMy4yaC40YTIuNSAyLjUgMCAwIDAgLjYgMFY0OWE0LjIgNC4yIDAgMCAxLTEgLjFjLS41IDAtLjggMC0xLjEtLjItLjMtLjEtLjUtLjMtLjctLjZsLS4yLTEuMnYtNy4zWm03LjggOS4zYy0uNSAwLTEgMC0xLjUtLjJhMy4zIDMuMyAwIDAgMS0xLjgtMiA0IDQgMCAwIDEtLjItMS4zdi0uM2MwLS42IDAtMS4xLjItMS42cy40LS45LjctMS4yYTMgMyAwIDAgMSAxLS44bDEuNC0uMmMuNiAwIDEgMCAxLjQuMmwxIC44Yy4zLjMuNS43LjYgMS4xbC4yIDEuNXYuN2gtNS43di0xLjJoNHYtLjFsLS4xLS45LS41LS42LS45LS4yYy0uMyAwLS41IDAtLjcuMi0uMiAwLS40LjItLjUuNWwtLjQuN3YyLjNsLjQuNy43LjUuOC4xYTIuMyAyLjMgMCAwIDAgMS45LS45bC45LjktLjcuNmMtLjIuMi0uNS40LTEgLjUtLjMuMi0uNy4yLTEuMi4yWiIvPjxwYXRoIGZpbGw9IiMzRkE3MUEiIGQ9Ik0zMC44IDExMy40djIuNkgxNy41di0yLjNsNi41LTcgMS43LTIuMWMuNC0uNi43LTEuMS44LTEuNmE0IDQgMCAwIDAgMC0zYy0uMy0uNS0uNi0uOS0xLTEuMWEzIDMgMCAwIDAtMS43LS41Yy0uNyAwLTEuNC4yLTEuOS41YTMgMyAwIDAgMC0xLjEgMS4zYy0uMy42LS40IDEuMi0uNCAyaC0zLjNjMC0xLjIuMy0yLjMuOC0zLjIuNS0xIDEuMy0xLjggMi4zLTIuMyAxLS42IDIuMi0uOSAzLjYtLjlzMi41LjIgMy40LjdjMSAuNCAxLjcgMSAyLjEgMS45LjUuOC44IDEuOC44IDIuOWwtLjMgMS44Yy0uMi43LS41IDEuMy0uOSAxLjktLjMuNi0uOCAxLjItMS4zIDEuOGwtMS42IDEuOC00LjMgNC44aDkuMVptMTUuOS05djMuMmExNSAxNSAwIDAgMS0uNSA0IDcgNyAwIDAgMS0xLjMgMi43Yy0uNi43LTEuMyAxLjItMiAxLjVhNy43IDcuNyAwIDAgMS00LjkuMiA1LjMgNS4zIDAgMCAxLTMuMi0yLjZjLS40LS43LS43LTEuNS0uOS0yLjUtLjItMS0uMy0yLS4zLTMuM3YtMy4yYzAtMS42LjItMyAuNS00cy43LTIgMS4zLTIuNmE1IDUgMCAwIDEgMi0xLjUgNy42IDcuNiAwIDAgMSA0LjktLjIgNS4xIDUuMSAwIDAgMSAzLjIgMi42Yy40LjYuNyAxLjUuOSAyLjQuMiAxIC4zIDIgLjMgMy4zWm0tMy4zIDMuN3YtNC4yYzAtLjggMC0xLjUtLjItMi4xIDAtLjYtLjItMS4xLS40LTEuNWEzIDMgMCAwIDAtLjYtMWMtLjMtLjQtLjYtLjYtMS0uN2wtMS0uMmEzIDMgMCAwIDAtMS40LjNjLS40LjItLjguNS0xIDFhNSA1IDAgMCAwLS43IDEuN2wtLjIgMi41djQuMmMwIC44IDAgMS41LjIgMi4xIDAgLjYuMiAxLjIuNCAxLjYuMS40LjQuOC42IDFsMSAuNyAxIC4xYy42IDAgMSAwIDEuNC0uMy40LS4yLjgtLjUgMS0xIC4zLS40LjUtMSAuNy0xLjZsLjItMi42Wk01Ni4zIDEwNS45VjExNmgtMi44di0xMi43SDU2bC4yIDIuNlptLS41IDMuM2gtMWMwLS45LjItMS43LjQtMi40LjItLjguNS0xLjQgMS0yIC40LS41LjktMSAxLjUtMS4zYTUgNSAwIDAgMSAyLjItLjRjLjYgMCAxLjEgMCAxLjYuMnMuOS41IDEuMi44Yy40LjQuNy44LjkgMS40LjIuNi4zIDEuMy4zIDJ2OC41SDYxdi04LjJjMC0uNiAwLTEuMS0uMi0xLjUtLjItLjMtLjUtLjYtLjgtLjctLjQtLjItLjgtLjItMS4yLS4yLS42IDAtMSAwLTEuNC4zLS40LjItLjcuNC0uOS44bC0uNSAxLjItLjIgMS41Wm03LjktLjgtMS4zLjNjMC0uNyAwLTEuNS4zLTIuMS4yLS43LjUtMS4zIDEtMS44YTQuNSA0LjUgMCAwIDEgMy42LTEuN2MuNyAwIDEuMiAwIDEuNy4zLjYuMSAxIC40IDEuNC44LjMuNC42LjguOCAxLjQuMi42LjMgMS40LjMgMi4ydjguMmgtMi45di04LjJjMC0uNyAwLTEuMi0uMy0xLjUtLjEtLjQtLjQtLjYtLjctLjdhMy4xIDMuMSAwIDAgMC0yLjQgMGwtLjguN2EzIDMgMCAwIDAtLjYgMWwtLjEgMS4xWm0xMi40LTUuN2gxbC44LS4xLjUtLjRhMSAxIDAgMCAwLS4xLTEuNGMtLjItLjItLjYtLjMtMS4xLS4zLS40IDAtLjcgMC0xIC4zLS4yLjEtLjQuMy0uNC42aC0yLjFjMC0uNS4yLTEgLjUtMS40YTMgMyAwIDAgMSAxLjItLjkgNS4zIDUuMyAwIDAgMSAzLjYgMGMuNi4yIDEgLjUgMS4zLjkuMy40LjQuOS40IDEuNCAwIC41LS4xLjktLjQgMS4yLS4zLjQtLjcuNi0xLjEuOC0uNS4yLTEuMS4zLTEuOC4zaC0xLjN2LTFabTAgMS41di0xaDEuM2E2IDYgMCAwIDEgMS45LjNjLjUuMiAxIC40IDEuMi44LjMuMy40LjcuNCAxLjMgMCAuNi0uMiAxLS41IDEuNWEzIDMgMCAwIDEtMS40LjkgNS40IDUuNCAwIDAgMS0zLjYgMGMtLjUtLjItMS0uNS0xLjMtLjktLjQtLjQtLjUtMS0uNS0xLjZoMi4xYzAgLjMuMS42LjQuOC4zLjIuNy4zIDEuMi4zcy44LS4xIDEtLjNjLjMtLjMuNS0uNS41LS45IDAtLjMtLjEtLjUtLjMtLjdhMSAxIDAgMCAwLS41LS40bC0xLS4xaC0xWm0xNC42LTUuM0w4NCAxMTcuNWgtMi4ybDYuNy0xOC42aDIuMlptNC43LS45djE4aC0yLjhWOThoMi44Wm0tLjUgMTEuMkg5NGMwLS45LjEtMS43LjQtMi40LjItLjguNS0xLjQgMS0yYTQuNSA0LjUgMCAwIDEgMy42LTEuN2MuNiAwIDEuMiAwIDEuNy4ycy45LjUgMS4zLjljLjMuNC42LjguOCAxLjUuMi42LjMgMS4zLjMgMi4ydjguMWgtMi44di04LjJjMC0uNi0uMS0xLS4zLTEuNC0uMi0uNC0uNC0uNi0uOC0uOC0uMy0uMi0uNy0uMi0xLjItLjJzLTEgMC0xLjQuM2MtLjMuMi0uNy40LTEgLjhsLS41IDEuMi0uMiAxLjVabTEzLjktMy41VjExNkgxMDZ2LTEyLjdoMi43djIuNFptMy45LTIuNXYyLjdhNS43IDUuNyAwIDAgMC0yLjUgMCAyLjQgMi40IDAgMCAwLTEuNSAxLjZsLS4yIDEuMmgtLjZjMC0uOCAwLTEuNS4yLTIuMmE2IDYgMCAwIDEgLjctMS44Yy4zLS41LjctLjkgMS4yLTEuMmEzLjIgMy4yIDAgMCAxIDIuMi0uNGwuNS4xWiIvPjxwYXRoIHN0cm9rZT0iIzAwMCIgc3Ryb2tlLW9wYWNpdHk9Ii45IiBzdHJva2Utd2lkdGg9IjEuNSIgZD0iTTE4NyA1NGMtOS41IDAtOC40IDU2LjEtMTguMiA1Ni4xLTguMyAwLTkuNy0yMy44LTE2LjEtMjMuOC02LjUgMC05LjMgMzIuNy0xNy42IDMyLjctNi45IDAtMTAuNy02LjYtMTcuMS02LjYiLz48L3N2Zz4=", "public": true}]}