{"fqn": "horizontal_vibration_card", "name": "Horizontal vibration card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_vibration_card_system_widget_image.png", "description": "Displays the latest vibration telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'vibration', label: 'Vibration', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Vibration\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"let factor = 1000;\\nif (prevValue < 1) {\\n    factor = 1;\\n} else if (prevValue < 10) {\\n    factor = 10;\\n} else if (prevValue < 100) {\\n    factor = 100;\\n}\\nlet value = prevValue + Math.random() * factor;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"vibration\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0.1,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0.1,\"to\":1,\"color\":\"#FFA600\"},{\"from\":1,\"to\":10,\"color\":\"#F36900\"},{\"from\":10,\"to\":100,\"color\":\"#F04022\"},{\"from\":100,\"to\":1000,\"color\":\"#D81838\"},{\"from\":1000,\"to\":null,\"color\":\"#6F113A\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":0.1,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0.1,\"to\":1,\"color\":\"#FFA600\"},{\"from\":1,\"to\":10,\"color\":\"#F36900\"},{\"from\":10,\"to\":100,\"color\":\"#F04022\"},{\"from\":100,\"to\":1000,\"color\":\"#D81838\"},{\"from\":1000,\"to\":null,\"color\":\"#6F113A\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal vibration card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"m/s²\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "vibration", "tremor", "shake", "quiver", "jolt", "oscillation", "pulsation", "resonance"], "resources": [{"link": "/api/images/system/horizontal_vibration_card_system_widget_image.png", "title": "\"Horizontal vibration card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_vibration_card_system_widget_image.png", "publicResourceKey": "zmTY5bxRKfCK6CRvK8YsrPY8NCHiUsGz", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAn1BMVEUAAADf39/f39/g4ODg4OD////k5OTYGDjg4OD64ubwqLTdNVH1xc3nboLIyMjiUmnOzs7aJkXx8fHsi5v09PT98fL5+fm3t7f41Nrb29vCwsLzt8GQkJDV1dW8vLxYWFjn5+fumqjpfY90dHTgQ1398fPiUmqsrKw8PDyenp5KSkqCgoLkYHYvLy9mZmZLS0shISGwsLCdnZ3pfZDpfY64MVbEAAAABXRSTlMAIETfv0i0+14AAAO7SURBVHja7doNb6IwHMfxnbc/tS30eBZQQHyYD9vu+f2/tmsL7LadOpZcFrb8PomsApp+04kmegUAAAAAAAAAAAAAAAAAAAAAAAAAAADwlj69ezZj8tl576afJ7rDSendi53J1XVMH0B6fTWlD2GKkJFByNggZGwQMjYIGZsTIZnb4tQJD0RK/XQOJT1wqFI0Jv+GNKwjqXcfUq1UuJ45FIbmRuE+LEvSY3L0vaHc7k8TzZ+MOnNhuHTO3KXHJ7svhHgeWZxRb3Fw9rRW62N9UPfH6vj1sLvfLRbOt69rul/v1bCKVcDIiJgm+cMooU7AjBs6gzOPHmwCyZpLIfxxCE/IUvVubUJUuFc1OYfbmva0WNzO6JvznRY7etl2w7T2eaWbrcx8XTO6Y6v+FCaFNqczBMuol0lBG3kphPFHIV8Eteq6NCGz8KhDbg+zLmRHdTgwhMtItCErM/NtwBLymDDTD/rJPVmMhFMydxOdO+dkfZH2LOEm/QKdDUmy5yGJS8ZiTybkW73QIT/39ZGOC/2vVX+9pYEhGm9DvrC53m5Ypm/m2SXrJurqQk4dXekFTC+ZWcmofXijNzesu5/JTXI2RNw8DxGSHgu77ZNNZ3iI2267BKlHlmCbgAXRQ0jQzHXF3Txi9rzGbD224iLQj8iCFdHrQi76nyERk7/1KkR9SGMWKbCrJ8x5N/1yCs/d6sWSko8zhHhip96HCHthsOfqYaY3NnalU2grjGSkIVbA+MkQr93fSL0W0UtviDx7HsKz/x6iZ9i+2LuXfcCS/i0uuRAi76iViaBPf/XlN6YT4vD5jnhQSMPuuhlG7Jeel50r7xbJHjgV4tr8rdzYC3hE1uA3xCQha9bGxBTmZKUppVPf7JhSSkTL3ZLSPG8P6B3xhZCtZF4kWaRHgR2JbmpzFtgDJ0M8PbDDO9H0i/b6jyg6pCz9XClfjwoq8qJKZ4VflsqvQr9KifxlXFU5xb494KvqfAhxyVh78czMqLGvYbMV5m5EJ0MCj4ytx/Q5goaERKwjn4T4s1j5S78NWZa5Hut9y9wvlA2Z5rkOUbOlinVuEdMFCU/6uG7E/949STD34bF86Md4t5NQ74efF/6sVEVsVsSv8mIWz5Sv94XVUvlkQlKzIsvqhzlQFgW9qS7k1fIlnVeqnIYYQ0iaXjoY0xCjCBkjhIwNQsYGIWODkLGZXl1/gK/ZieLrq4nzAb5oT52J+QnH1HnvPk8+0o9qAAAAAAAAAAAAAAAAAAAAAAAAAAAAAN7IH7CH4Te59eAaAAAAAElFTkSuQmCC", "public": true}]}