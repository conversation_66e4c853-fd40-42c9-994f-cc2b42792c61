<?xml version="1.0" encoding="utf-8"?>
<!--Copyright 2021 <PERSON> (Malaysia) Berhad. 

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

-->
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>Periodic Activity</Name>
		<Description1>
		<![CDATA[Generic interface to set up periodic activity like hourly sensor reading/load activation and mechanism to retrieve the periodic data.]]>
		</Description1>
		<ObjectID>10376</ObjectID>
		<ObjectURN>urn:oma:lwm2m:x:10376</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>Activity Name</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource represents activity's name declared by device. It cannot be modified externally. Device can support multiple activities by declaring multiple instances of Periodic Activity object.]]>
				</Description>
			</Item>
			<Item ID="1">
				<Name>Activity Description</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource describes activity's operation including its supported options and data returned.]]>
				</Description>
			</Item>
			<Item ID="2">
				<Name>Activity Settings</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource allows activity to accept settings or input parameters when run. The available settings, if supported, should be described using Activity Description resource.]]>
				</Description>
			</Item>
			<Item ID="3">
				<Name>Start Mask</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource defines when to start the periodic activity. Start Mask needs to adhere to following string format:
"YYMMDDHHmmss"
YY: year, e.g 21
MM: month, 01 to 12
DD: day, 01 to 31
HH: hour, 00 to 23
mm: minute, 00 to 59
ss: second, 00 to 59

Any of the above can be replaced by character '-' for "don't care" masking.

Example:
1) "211027201801" the periodic activity will start at exactly 20:18:01 on 2021/10/27.
2) "------201801" the periodic activity will start at the next 20:18:01.
3) "------------" the periodic activity will start immediately.
4) "------20--00" the periodic activity will start between 8pm to 8.59pm at exactly 00 seconds.
5) "------01000000" the periodic activity will start at next 1st of the month at 12am.

Start Mask is adjusted to UTC offset provided by Device [/3/0/14] object instance resource, or adjusted to UTC if the resource is not provided.
]]></Description>
			</Item>
			<Item ID="4">
				<Name>Periodic Interval</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description><![CDATA[This resource represents the time interval between the run of consecutive activities. The next activity will run after Periodic Interval resource value has elapsed.]]>
				</Description>
			</Item>
			<Item ID="5">
				<Name>Run Period</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description><![CDATA[This resource defines the period to run this periodic activity. Periodic activity will stop when the Run Period has elapsed from the start time. Depending on the Start Mask's "don't care" field, this periodic activity may re-start again.]]>
				</Description>
			</Item>
			<Item ID="6">
				<Name>Record</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Record resource enable the accumulation of returned data (if any) when the activity is run/called. The accumulated data or records is represented by a read-only serialized Opaque (Binary) value.
The number of records available in this serialized resource can be calculated by Record Head Index - Record Current Read Index. Nevertheless, the number of records serialized is limited by device's memory allocation. After successful record read (manual read, observation or dispatch), device will increment Record Current Read Index as per how many already read.

The record use CBOR format encoded as following:

1. array(Record Index 1, Record Timestamp 1, Record Value 1)
2. array(Record Index 2, Record Timestamp 2, Record Value 2)
...
n. array(Record Index n, Record Timestamp n, Record Value n)

Record Timestamp is a 32-bit integer representing the number of seconds since Jan 1st, 1970 in the UTC time zone. Record Value can be any type and described by CBOR type. Value can also be an array of sub-values.]]></Description>
			</Item>
			<Item ID="7">
				<Name>Record Head Index</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource represents the index of the latest record. This index is incremented upon every new activity's record. Total recordings inside the device can be calculated by Record Head Index - Record Tail Index.]]>
				</Description>
			</Item>
			<Item ID="8">
				<Name>Record Tail Index</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource represents the index of the oldest record stored in device. This index is adjusted (incremented) by device based on its memory space availability.]]>
				</Description>
			</Item>
			<Item ID="9">
				<Name>Record Read Index</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource represents the index of the last read record. Device will increment this index upon successful record delivery to server (via manual read or scheduled dispatch). Server may modify this index to traverse within available records in device.]]>
				</Description>
			</Item>	
			<Item ID="10">
				<Name>Record Dispatch Start Mask</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource represents the start time to deliver Record resource. If Record resource is currently being observed, this resource enables accumulation of the Record changes before being dispatch (rather than delivering Record immediately as it changes). As per Start Mask, Dispatch Start Mask must adhere to following string format:
"YYMMDDHHmmss".

Dispatch Start Mask is adjusted to UTC offset provided by Device [/3/0/14] object instance resource, or adjusted to UTC if the resource is not provided.

If this resource is used together with Dispatch Interval resource, it is possible to automatically deliver Record resource at specific time. For example, dispatch accumulated record every 6 hours, at minute 5 (00:05, 06:05, 12:05, 18:05).
]]></Description>
			</Item>
			<Item ID="11">
				<Name>Record Dispatch Interval</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description><![CDATA[This resource represents the time interval between consecutive automatic Record dispatch. The next dispatch will run after Record Dispatch Interval resource value has elapsed.]]>
				</Description>
			</Item>
		</Resources>
		<Description2><![CDATA[]]></Description2>
	</Object>
</LWM2M>
