{"fqn": "vertical_cylinder_tank", "name": "Vertical cylinder tank", "deprecated": false, "image": "tb-image;/api/images/system/vertical_cylinder_tank_system_widget_image.png", "description": "Widget indicates the level of liquid in Vertical cylinder tank.", "descriptor": {"type": "latest", "sizeX": 4, "sizeY": 4, "resources": [], "templateHtml": "<tb-liquid-level-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-liquid-level-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.liquidLevelWidget.onInit();\n}\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.liquidLevelWidget.update();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true\n    };\n};\n\nself.onDestroy = function() {\n}\n\nself.actionSources = function() {    \n    return {        \n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false        \n        }    \n    };\n}", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-liquid-level-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-liquid-level-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"return Math.floor(Math.random() * 101);\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"tankSelectionType\":\"static\",\"selectedShape\":\"Vertical Cylinder\",\"shapeAttributeName\":\"tankShape\",\"tankColor\":{\"type\":\"range\",\"color\":\"#242770\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E73535DE\"},{\"from\":20,\"to\":null,\"color\":\"#242770\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E73535DE';\\n  }\\n}\\nreturn '#242770';\"},\"datasourceUnits\":\"%\",\"layout\":\"percentage\",\"volumeSource\":\"static\",\"volumeConstant\":500,\"volumeAttributeName\":\"volume\",\"volumeUnits\":\"L\",\"volumeFont\":{\"family\":\"Roboto\",\"size\":14,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"volumeColor\":\"rgba(0, 0, 0, 0.18)\",\"units\":\"%\",\"widgetUnitsSource\":\"static\",\"widgetUnitsAttributeName\":\"units\",\"liquidColor\":{\"type\":\"range\",\"color\":\"#7A8BFF\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E27C7CDE\"},{\"from\":20,\"to\":null,\"color\":\"#7A8BFF\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FF0000DE\"},{\"from\":20,\"to\":null,\"color\":\"rgba(0,0,0,0.87)\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FF0000DE';\\n  }\\n}\\nreturn '#000000DE';\"},\"showBackgroundOverlay\":true,\"backgroundOverlayColor\":{\"type\":\"range\",\"color\":\"rgba(255, 255, 255, 0.76)\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FFEFEFDE\"},{\"from\":20,\"to\":null,\"color\":\"#FFFFFFC2\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FFEFEFDE';\\n  }\\n}\\nreturn '#FFFFFFC2';\"},\"showTooltip\":true,\"showTooltipLevel\":true,\"tooltipUnits\":\"%\",\"tooltipLevelDecimals\":0,\"tooltipLevelFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipLevelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.76)\",\"rangeList\":[],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"showTooltipDate\":true,\"tooltipDateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"tooltipDateFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipDateColor\":\"rgba(0, 0, 0, 0.76)\",\"tooltipBackgroundColor\":\"rgba(255, 255, 255, 0.76)\",\"tooltipBackgroundBlur\":3,\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Liquid level\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"titleFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"1.5\"},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"showTitleIcon\":false,\"titleIcon\":\"water_drop\",\"iconColor\":\"#5469FF\",\"decimals\":0,\"enableDataExport\":false,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\"}"}, "tags": ["reservoir", "container", "vessel", "storage unit", "cistern", "canister", "vat", "basin", "repository", "bin", "hopper"], "resources": [{"link": "/api/images/system/vertical_cylinder_tank_system_widget_image.png", "title": "\"Vertical cylinder tank\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "vertical_cylinder_tank_system_widget_image.png", "publicResourceKey": "SG9d9jvqNl0x1LQNzi0NnCWddNEcya1b", "mediaType": "image/png", "data": "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", "public": true}]}