{"fqn": "fluid_pressure_progress_bar", "name": "Pressure progress bar", "deprecated": false, "image": "tb-image;/api/images/system/pressure_progress_bar_(1).svg", "description": "Displays fluid pressure reading as a horizontal progress bar. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pressure', label: 'Pressure', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 15 - 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":5,\"color\":\"#305AD7\"},{\"from\":5,\"to\":10,\"color\":\"#3FA71A\"},{\"from\":10,\"to\":15,\"color\":\"#F36900\"},{\"from\":15,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":25,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":5,\"color\":\"#305AD7\"},{\"from\":5,\"to\":10,\"color\":\"#3FA71A\"},{\"from\":10,\"to\":15,\"color\":\"#F36900\"},{\"from\":15,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Pressure\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"bar\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["fluid pressure", "liquid pressure", "pump pressure", "hydraulic", "pump", "compressibility", "compressive stress", "pipe", "pipeline"], "resources": [{"link": "/api/images/system/pressure_progress_bar_(1).svg", "title": "pressure_progress_bar.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "pressure_progress_bar_(1).svg", "publicResourceKey": "eXcqq0yfotRrYCnvW5RwqTvIFAfhMOQz", "mediaType": "image/svg+xml", "data": "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", "public": true}]}