{"fqn": "analogue_gauges.temperature_radial_gauge_canvas_gauges", "name": "Temperature radial gauge", "deprecated": false, "image": "tb-image;/api/images/system/temperature_radial_gauge_system_widget_image.png", "description": "Preconfigured gauge to display temperature. Allows to configure temperature range, gradient colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 6, "sizeY": 5, "resources": [], "templateHtml": "<canvas id=\"radialGauge\"></canvas>\n", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.gauge = new TbAnalogueRadialGauge(self.ctx, 'radialGauge');    \n}\n\nself.onDataUpdated = function() {\n    self.ctx.gauge.update();\n}\n\nself.onResize = function() {\n    self.ctx.gauge.resize();\n}\n\nself.onMobileModeChanged = function() {\n    self.ctx.gauge.mobileModeChanged();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'temperature', label: 'Temperature', type: 'timeseries' }];\n        }\n    };\n}\n\nself.onDestroy = function() {\n    self.ctx.gauge.destroy();\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-analogue-radial-gauge-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-radial-gauge-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.7282710489093589,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgb(255, 255, 255)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"startAngle\":67.5,\"ticksAngle\":225,\"needleCircleSize\":7,\"defaultColor\":\"#e65100\",\"minValue\":-60,\"maxValue\":60,\"majorTicksCount\":12,\"colorMajorTicks\":\"#444\",\"minorTicks\":12,\"colorMinorTicks\":\"#666\",\"numbersFont\":{\"family\":\"Roboto\",\"size\":20,\"style\":\"normal\",\"weight\":\"normal\",\"color\":\"#263238\"},\"numbersColor\":\"#263238\",\"showUnitTitle\":true,\"unitTitle\":\"Temperature\",\"titleFont\":{\"family\":\"Roboto\",\"size\":24,\"style\":\"normal\",\"weight\":\"normal\",\"color\":\"#263238\"},\"titleColor\":\"#263238\",\"unitsFont\":{\"family\":\"Roboto\",\"size\":28,\"style\":\"normal\",\"weight\":\"500\",\"color\":\"#616161\"},\"unitsColor\":\"#616161\",\"valueBox\":true,\"valueInt\":3,\"valueFont\":{\"family\":\"Segment7Standard\",\"size\":30,\"style\":\"normal\",\"weight\":\"normal\",\"shadowColor\":\"rgba(0, 0, 0, 0.49)\",\"color\":\"#444\"},\"valueColor\":\"#444\",\"valueColorShadow\":\"rgba(0, 0, 0, 0.49)\",\"colorValueBoxRect\":\"#888\",\"colorValueBoxRectEnd\":\"#666\",\"colorValueBoxBackground\":\"#babab2\",\"colorValueBoxShadow\":\"rgba(0,0,0,1)\",\"showBorder\":true,\"colorPlate\":\"#cfd8dc\",\"colorNeedle\":null,\"colorNeedleEnd\":null,\"colorNeedleShadowUp\":\"rgba(2, 255, 255, 0)\",\"colorNeedleShadowDown\":\"rgba(188, 143, 143, 0.78)\",\"highlightsWidth\":15,\"highlights\":[{\"from\":-60,\"to\":-50,\"color\":\"#42a5f5\"},{\"from\":-50,\"to\":-40,\"color\":\"rgba(66, 165, 245, 0.83)\"},{\"from\":-40,\"to\":-30,\"color\":\"rgba(66, 165, 245, 0.66)\"},{\"from\":-30,\"to\":-20,\"color\":\"rgba(66, 165, 245, 0.5)\"},{\"from\":-20,\"to\":-10,\"color\":\"rgba(66, 165, 245, 0.33)\"},{\"from\":-10,\"to\":0,\"color\":\"rgba(66, 165, 245, 0.16)\"},{\"from\":0,\"to\":10,\"color\":\"rgba(229, 115, 115, 0.16)\"},{\"from\":10,\"to\":20,\"color\":\"rgba(229, 115, 115, 0.33)\"},{\"from\":20,\"to\":30,\"color\":\"rgba(229, 115, 115, 0.5)\"},{\"from\":30,\"to\":40,\"color\":\"rgba(229, 115, 115, 0.66)\"},{\"from\":40,\"to\":50,\"color\":\"rgba(229, 115, 115, 0.83)\"},{\"from\":50,\"to\":60,\"color\":\"#e57373\"}],\"animation\":true,\"animationDuration\":1000,\"animationRule\":\"bounce\"},\"title\":\"Temperature radial gauge\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"decimals\":0,\"noDataDisplayMessage\":\"\",\"configMode\":\"basic\",\"units\":\"°C\"}"}, "resources": [{"link": "/api/images/system/temperature_radial_gauge_system_widget_image.png", "title": "\"Temperature radial gauge\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "temperature_radial_gauge_system_widget_image.png", "publicResourceKey": "N3qOm9qMjAqOnwF02Qk20OYIekPpksvE", "mediaType": "image/png", "data": "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", "public": true}]}