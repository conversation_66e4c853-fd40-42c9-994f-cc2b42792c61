{"fqn": "horizontal_individual_allergy_index_iai_card", "name": "Horizontal individual allergy index (IAI) card", "deprecated": false, "image": "tb-image;/api/images/system/IAI-value-card-horizontal.svg", "description": "Indicates the concentration of airborne allergens, including pollen and mold spores, which can trigger allergic reactions in sensitive individuals.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'IAI_level', label: 'IAI', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"IAI\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 12) {\\n\\tvalue = 12;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:flower-pollen\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#3FA71A\"},{\"from\":2,\"to\":6,\"color\":\"#80C32C\"},{\"from\":6,\"to\":9,\"color\":\"#F36900\"},{\"from\":9,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#3FA71A\"},{\"from\":2,\"to\":6,\"color\":\"#80C32C\"},{\"from\":6,\"to\":9,\"color\":\"#F36900\"},{\"from\":9,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"IAI\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":null,\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "air", "aqi", "pollution", "emission", "smog"], "resources": [{"link": "/api/images/system/IAI-value-card-horizontal.svg", "title": "IAI-value-card-horizontal.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "IAI-value-card-horizontal.svg", "publicResourceKey": "QZnAxtLyjuhL4DnbmkQyUeZDJKOLEiuX", "mediaType": "image/svg+xml", "data": "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", "public": true}]}