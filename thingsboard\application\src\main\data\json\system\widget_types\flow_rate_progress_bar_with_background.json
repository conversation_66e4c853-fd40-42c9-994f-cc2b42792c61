{"fqn": "flow_rate_progress_bar_with_background", "name": "Flow rate progress bar with background", "deprecated": false, "image": "tb-image;/api/images/system/flow_rate_progress_bar_with_background.svg", "description": "Displays flow rate reading as a horizontal progress bar with background. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'flowRate', label: 'Flow Rate', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"flowRate\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#2B54CE\"},{\"from\":10,\"to\":30,\"color\":\"#3B911C\"},{\"from\":30,\"to\":50,\"color\":\"#F77410\"},{\"from\":50,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":100,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/flow_rate_progress_bar_background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#2B54CE\"},{\"from\":10,\"to\":30,\"color\":\"#3B911C\"},{\"from\":30,\"to\":50,\"color\":\"#F77410\"},{\"from\":50,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Flow rate\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"m³/hr\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["liquid", "fluid", "flow rate", "fluid dynamics", "velocity", "mass flow", "volume flow"], "resources": [{"link": "/api/images/system/flow_rate_progress_bar_background.png", "title": "flow_rate_progress_bar_background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "flow_rate_progress_bar_background.png", "publicResourceKey": "Sk2hbPQoQ1sUPizIZlS0THwh6lbAsB59", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/flow_rate_progress_bar_with_background.svg", "title": "flow_rate_progress_bar_with_background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "flow_rate_progress_bar_with_background.svg", "publicResourceKey": "klz0t4BqeQb9CkLtRmlPCTYQ3ludCy0L", "mediaType": "image/svg+xml", "data": "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", "public": true}]}