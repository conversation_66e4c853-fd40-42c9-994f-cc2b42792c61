<?xml version="1.0" encoding="utf-8"?>
<!--BSD 3-Clause License

Copyright (c) 2019, Cloudminds
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>EC<PERSON>L, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON>
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>PM Threshold</Name>
    <Description1><![CDATA[Here defines all items needed in PM Threshold management.]]></Description1>
    <ObjectID>10333</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10333</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Single</MultipleInstances>
    <Mandatory>Optional</Mandatory>
    <Resources>
		<Item ID="1">
			<Name>Entity</Name>
			<Operations>RW</Operations>
			<MultipleInstances>Multiple</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>String</Type>
			<RangeEnumeration>4..63</RangeEnumeration>
			<Units/>
			<Description><![CDATA[Contains the object ID and object instance ID/name, for example: </10320/2>.]]></Description>
		</Item>
		<Item ID="2">
			<Name>Performance Type</Name>
			<Operations>RW</Operations>
			<MultipleInstances>Multiple</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>String</Type>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[Referrence to a PM item, similar with Corelnk in LwM2M 1.1, 
				</object ID/object instance ID/resoure ID>, for example:/10322/2/4]]></Description>
		</Item>
		<Item ID="3">
			<Name>High Threshold</Name>
			<Operations>RW</Operations>
			<MultipleInstances>Multiple</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>Float</Type>
			<RangeEnumeration/>
			<Units></Units>
			<Description><![CDATA[High Threshold of this PM item.]]></Description>
		</Item>
		<Item ID="4">
			<Name>Low Threshold</Name>
			<Operations>RW</Operations>
			<MultipleInstances>Multiple</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type>Float</Type>
			<RangeEnumeration/>
			<Units></Units>
			<Description><![CDATA[Low Threshold of this PM item.]]></Description>
		</Item>
	</Resources>
    <Description2/>
  </Object>
</LWM2M>
