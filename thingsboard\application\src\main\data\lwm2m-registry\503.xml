<?xml version="1.0" encoding="utf-8"?>

<!-- 
FILE INFORMATION
Public Reachable Information
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 503.xml
NORMATIVE INFORMATION
  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues
LEGAL DISCLAIMER
Copyright 2022 Open Mobile Alliance. 
Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
The above license is used as a license under copyright only.  Please
reference the OMA IPR Policy for patent licensing terms:
https://www.omaspecworks.org/about/intellectual-property-rights/
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
    <Object ObjectType="MODefinition">
    	<Name>Fire Alarm</Name>
  	    <Description1><![CDATA[This is an alarm that should be raised if the sensor(s) detect fire, based on the values reported from Temperature, Humidity, Smoke (CO2).]]></Description1>
        <ObjectID>503</ObjectID>
        <ObjectURN>urn:oma:lwm2m:oma:503</ObjectURN>
        <LWM2MVersion>1.0</LWM2MVersion>
        <ObjectVersion>1.0</ObjectVersion>
        <MultipleInstances>Multiple</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Resources>
          <Item ID="0">
            <Name>Temperature Sensor Location Tag</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>String</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Verbose identification of the location of the Temperature sensor location]]></Description>
          </Item>
          <Item ID="1">
            <Name>Temperature Unit</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Mandatory</Mandatory>
            <Type>Integer</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Temperature Units: 0 = Celsius, 1 = Fahrenheit, 2 = Kelvin. All temperature values are represented in this unit.]]></Description>
          </Item>
          <Item ID="2">
            <Name>Ambient Temperature</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Temperature for the area.]]></Description>
          </Item>
          <Item ID="3">
            <Name>Temperature Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Mandatory</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Last or Current Measured Temperature Value from the Sensor. The value 65534 indicates that the Sensor data is not available.]]></Description>
          </Item>
          <Item ID="4">
            <Name>Minimum Measured Temperature Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[The minimum Temperature value measured by the sensor since power ON or reset.]]></Description>
          </Item>
          <Item ID="5">
            <Name>Maximum Measured Temperature Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[The maximum Temperature value measured by the sensor since power ON or reset.]]></Description>
          </Item>
          <Item ID="6">
            <Name>Min Temperature Range Value</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[The minimum temperature value that can be measured by the sensor.]]></Description>
          </Item>
          <Item ID="7">
            <Name>Max Temperature Range Value</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[The maximum temperature value that can be measured by the sensor.]]></Description>
          </Item>
          <Item ID="8">
            <Name>Lower Temperature Accuracy</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Indicates the lower end of the accuracy range for the temperature sensor.]]></Description>
          </Item>
          <Item ID="9">
            <Name>Upper Temperature Accuracy</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Indicates the upper end of the accuracy range for the temperature sensor.]]></Description>
          </Item>
          <Item ID="10">
            <Name>Temperature Threshold</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Indicates the maximum threshold for the temperature.]]></Description>
          </Item>
          <Item ID="11">
            <Name>Temperature Sensor Error</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Boolean</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Temperature Sensor Error: 0 = false (Working correctly), 1 = true (Faulty).]]></Description>
          </Item>
          <Item ID="12">
            <Name>Reset Temperature</Name>
            <Operations>E</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type></Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Reset the "Temperature" related parameters to default.]]></Description>
          </Item>
          <Item ID="13">
            <Name>Temperature Alarm Loudness</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units>dB</Units>
            <Description><![CDATA[Indicates the loudness of temperature alarm.]]></Description>
          </Item>
          <Item ID="14">
            <Name>Humidity Sensor Location Tag</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>String</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Verbose identification of the location of the Humidity sensor.]]></Description>
          </Item>
          <Item ID="15">
            <Name>Ambient Relative Humidity</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0.0..100.0</RangeEnumeration>
            <Units>%RH</Units>
            <Description><![CDATA[Relative humidity for the area.]]></Description>
          </Item>
          <Item ID="16">
            <Name>Humidity Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Mandatory</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0.0..100.0</RangeEnumeration>
            <Units>/100</Units>
            <Description><![CDATA[Last or Current Measured Humidity Value from the Sensor. The value 0 indicates that the Sensor data is not available.]]></Description>
          </Item>
          <Item ID="17">
            <Name>Minimum Measured Humidity Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0.0..100.0</RangeEnumeration>
            <Units>/100</Units>
            <Description><![CDATA[The minimum Humidity value measured by the sensor since power ON or reset.]]></Description>
          </Item>
          <Item ID="18">
            <Name>Maximum Measured Humidity Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0.0..100.0</RangeEnumeration>
            <Units>/100</Units>
            <Description><![CDATA[The maximum Humidity value measured by the sensor since power ON or reset.]]></Description>
          </Item>
          <Item ID="19">
            <Name>Humidity Accuracy</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0.0..100.0</RangeEnumeration>
            <Units>/100</Units>
            <Description><![CDATA[Indicate the accuracy for the Humidity Sensor.]]></Description>
          </Item>
          <Item ID="20">
            <Name>Humidity Threshold</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0.0..100.0</RangeEnumeration>
            <Units>/100</Units>
            <Description><![CDATA[Indicate the threshold for the Humidity.]]></Description>
          </Item>
          <Item ID="21">
            <Name>Humidity Sensor Error</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Boolean</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Humidity Sensor Error: 0 = false (Working correctly), 1 = true (Faulty)]]></Description>
          </Item>
          <Item ID="22">
            <Name>Reset Humidity</Name>
            <Operations>E</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type></Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Reset the "Humidity" related parameters to default.]]></Description>
          </Item>
          <Item ID="23">
            <Name>Humidity Alarm Loudness</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units>dB</Units>
            <Description><![CDATA[Indicates the loudness of the Humidity alarm.]]></Description>
          </Item>
          <Item ID="24">
            <Name>Smoke Sensor Location Tag</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>String</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Verbose identification of the location of the Smoke sensor.]]></Description>
          </Item>
          <Item ID="25">
            <Name>CO2 Alarm State</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Mandatory</Mandatory>
            <Type>Boolean</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[0 = False (CO2 alarm threshold not exceeded), 1 = True (CO2 alarm threshold exceeded).]]></Description>
          </Item>
          <Item ID="26">
            <Name>Ambient CO2 Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[CO2 value for the area.]]></Description>
          </Item>
          <Item ID="27">
            <Name>CO2 Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Mandatory</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[Last or Current Measured CO2 Value from the sensor. The value 0 indicates that the Sensor data is not available.]]></Description>
          </Item>
          <Item ID="28">
            <Name>Minimum Measured CO2 Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[The minimum CO2 value measured by the sensor since power ON or reset.]]></Description>
          </Item>
          <Item ID="29">
            <Name>Maximum Measured CO2 Value</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[The maximum CO2 value measured by the sensor since power ON or reset.]]></Description>
          </Item>
          <Item ID="30">
            <Name>CO2 Threshold</Name>
            <Operations>RW</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units>ppm</Units>
            <Description><![CDATA[Indicate the alarm threshold for the CO2.]]></Description>
          </Item>
          <Item ID="31">
            <Name>Smoke Sensor Error</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Boolean</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Smoke Sensor Error: 0 = false (Working correctly), 1 = true (Faulty)]]></Description>
          </Item>
          <Item ID="32">
            <Name>Reset Smoke Detection</Name>
            <Operations>E</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type></Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Reset the "Smoke" related parameters to default.]]></Description>
          </Item>
          <Item ID="33">
            <Name>Smoke Alarm Loudness</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units>dB</Units>
            <Description><![CDATA[Indicates the loudness of smoke alarm]]></Description>
          </Item>
          <Item ID="6044">
            <Name>Battery Percentage</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration>0.00..100.00</RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[Current remaining battery level.]]></Description>
          </Item>
          <Item ID="5514">
            <Name>Latitude</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>String</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[The decimal notation of latitude, e.g. -43.5723 (World Geodetic System 1984).]]></Description>
          </Item>
          <Item ID="5515">
            <Name>Longitude</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>String</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units></Units>
            <Description><![CDATA[The decimal notation of longitude, e.g. 153.21760 (World Geodetic System 1984).]]></Description>
          </Item>
          <Item ID="6039">
            <Name>Altitude</Name>
            <Operations>R</Operations>
            <MultipleInstances>Single</MultipleInstances>
            <Mandatory>Optional</Mandatory>
            <Type>Float</Type>
            <RangeEnumeration></RangeEnumeration>
            <Units>m</Units>
            <Description><![CDATA[Altitude above sea level in meters.]]></Description>
          </Item>
        </Resources>
  	     <Description2><![CDATA[]]></Description2>
    </Object>
</LWM2M>
