{"fqn": "indoor_temperature_chart_card", "name": "Indoor temperature chart card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_temperature_chart_card_system_widget_image.png", "description": "Displays a indoor temperature data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'temperature', label: 'Temperature', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: '°C', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'temperature', '°C', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nif (value < 15) {\\n\\tvalue = 15;\\n} else if (value > 30) {\\n\\tvalue = 30;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":18,\"color\":\"#234CC7\"},{\"from\":18,\"to\":24,\"color\":\"#3FA71A\"},{\"from\":24,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nif (value < 15) {\\n\\tvalue = 15;\\n} else if (value > 30) {\\n\\tvalue = 30;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"°C\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Temperature\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"device_thermostat\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["temperature", "environment", "indoor"], "resources": [{"link": "/api/images/system/indoor_temperature_chart_card_system_widget_image.png", "title": "\"Indoor temperature chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_temperature_chart_card_system_widget_image.png", "publicResourceKey": "csZpTEyKPluheTEQZ9T5XMx8O5gZRRWK", "mediaType": "image/png", "data": "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", "public": true}]}