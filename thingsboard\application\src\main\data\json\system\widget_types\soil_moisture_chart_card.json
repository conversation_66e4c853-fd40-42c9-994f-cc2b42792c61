{"fqn": "soil_moisture_chart_card", "name": "Soil moisture chart card", "deprecated": false, "image": "tb-image;/api/images/system/soil_moisture_chart_card_system_widget_image.png", "description": "Displays a soil moisture data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'soilMoisture', label: 'Soil Moisture', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: '%', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'soilMoisture', '%', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Soil Moisture\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#D81838\"},{\"from\":20,\"to\":40,\"color\":\"#F36900\"},{\"from\":40,\"to\":60,\"color\":\"#4B70DD\"},{\"from\":60,\"to\":100,\"color\":\"#234CC7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Soil Moisture\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "soil", "moisture"], "resources": [{"link": "/api/images/system/soil_moisture_chart_card_system_widget_image.png", "title": "\"Soil moisture chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "soil_moisture_chart_card_system_widget_image.png", "publicResourceKey": "chD34ldYwbZVuuOX6Jf5A1UBcBdaJznX", "mediaType": "image/png", "data": "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", "public": true}]}