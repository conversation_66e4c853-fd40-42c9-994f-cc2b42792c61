{"widgetsBundle": {"alias": "liquid_level_tanks", "title": "Liquid level", "image": "data:image/png;base64,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", "description": "Visualize the level of liquid inside the tank. Supports various tank shapes.", "order": 12000, "externalId": null, "name": "Liquid level"}, "widgetTypeFqns": ["vertical_cylinder_tank", "rectangle_tank", "horizontal_cylinder_tank", "horizontal_ellipse_tank", "horizontal_oval_tank", "vertical_oval_tank", "horizontal_capsule_tank", "vertical_capsule_tank", "horizontal_2_1_elliptical_tank", "horizontal_dish_ends_tank"]}