{"fqn": "simple_rotational_speed_chart_card", "name": "Simple rotational speed chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_rotational_speed_chart_card.svg", "description": "Displays historical rotational speed values as a simplified chart. Optionally may display the corresponding latest rotational speed value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'rotationalSpeed', label: 'Rotaional speed', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'rotationalSpeed', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Rotational speed\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 4000 - 2000;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 4000) {\\n\\tvalue = 4000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 4000 - 2000;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 4000) {\\n\\tvalue = 4000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":500,\"color\":\"#305AD7\"},{\"from\":500,\"to\":1500,\"color\":\"#3FA71A\"},{\"from\":1500,\"to\":3000,\"color\":\"#FFA600\"},{\"from\":3000,\"to\":null,\"color\":\"#F04022\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Rotational speed\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"360\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"units\":\"RPM\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true},\"margin\":\"0px\"}"}, "tags": ["angular speed", "spin rate", "revolutions", "rotational frequency", "spin motion"], "resources": [{"link": "/api/images/system/simple_rotational_speed_chart_card.svg", "title": "simple_rotational_speed_chart_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_rotational_speed_chart_card.svg", "publicResourceKey": "sJKOQXWrT5oq7FbOmI8bzNU4E9gonKGj", "mediaType": "image/svg+xml", "data": "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", "public": true}]}