{"fqn": "pressure_chart_card", "name": "Pressure chart card", "deprecated": false, "image": "tb-image;/api/images/system/pressure_chart_card_system_widget_image.png", "description": "Displays a pressure data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pressure', label: 'Pressure', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'hPa', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'pressure', 'hPa', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < 980) {\\n\\tvalue = 980;\\n} else if (value > 1040) {\\n\\tvalue = 1040;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"hPa\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1000,\"color\":\"#D81838\"},{\"from\":1000,\"to\":1020,\"color\":\"#80C32C\"},{\"from\":1020,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < 980) {\\n\\tvalue = 980;\\n} else if (value > 1040) {\\n\\tvalue = 1040;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"hPa\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"hPa\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Pressure\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"compress\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "barometry"], "resources": [{"link": "/api/images/system/pressure_chart_card_system_widget_image.png", "title": "\"Pressure chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "pressure_chart_card_system_widget_image.png", "publicResourceKey": "h7FFCQRlVZphtPZjqSNSwQPArcpQCiNJ", "mediaType": "image/png", "data": "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", "public": true}]}