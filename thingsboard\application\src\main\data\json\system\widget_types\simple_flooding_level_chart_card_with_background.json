{"fqn": "simple_flooding_level_chart_card_with_background", "name": "Simple flooding level chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_flooding_level_chart_card_with_background_system_widget_image.png", "description": "Displays historical flooding level values as a simplified chart with background. Optionally may display the corresponding latest flooding level value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'flooding', label: 'Flooding level', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'flooding', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Flooding level\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 2 - 1;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 5) {\\n\\tvalue = 5;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 2 - 1;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 5) {\\n\\tvalue = 5;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"#224AC2\"},{\"from\":1,\"to\":3,\"color\":\"#F77410\"},{\"from\":3,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_flooding_level_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Flooding level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"flood\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":1,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"m\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "flood", "flooding", "water height", "flood depth", "flood stage", "inundation level", "water rise", "overflow level", "flood peak", "high water mark"], "resources": [{"link": "/api/images/system/simple_flooding_level_chart_card_with_background_system_widget_background.png", "title": "\"Simple flooding level chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_flooding_level_chart_card_with_background_system_widget_background.png", "publicResourceKey": "V1BhON4PQjYhbnxZHWmdUuijubWGUjng", "mediaType": "image/png", "data": "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*******************************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***************************************************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", "public": true}, {"link": "/api/images/system/simple_flooding_level_chart_card_with_background_system_widget_image.png", "title": "\"Simple flooding level chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_flooding_level_chart_card_with_background_system_widget_image.png", "publicResourceKey": "uGmm3FuJ8uyHDUp7GpBzRvWyRCxUfiyA", "mediaType": "image/png", "data": "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", "public": true}]}