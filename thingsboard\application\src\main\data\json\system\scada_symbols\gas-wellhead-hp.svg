<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="800" height="1000" fill="none" version="1.1" viewBox="0 0 800 1000"><tb:metadata xmlns=""><![CDATA[{
  "title": "HP Gas wellhead",
  "description": "Gas wellhead with various states.",
  "searchTags": [
    "extraction"
  ],
  "widgetSizeX": 4,
  "widgetSizeY": 5,
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.runningColor;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "critical",
      "stateRenderFunction": "element.attr({fill: ctx.properties.criticalColor});\nif (ctx.values.critical) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = ctx.values.critical && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'criticalClick');"
        }
      }
    },
    {
      "tag": "warning",
      "stateRenderFunction": "element.attr({fill: ctx.properties.warningColor});\nvar warning = ctx.values.warning && !(ctx.values.warning && ctx.values.critical)\nif (warning) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = warning && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'warningClick');"
        }
      }
    }
  ],
  "behavior": [
    {
      "id": "running",
      "name": "{i18n:scada.symbol.running}",
      "hint": "{i18n:scada.symbol.running-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.running}",
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "running"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warning",
      "name": "{i18n:scada.symbol.warning}",
      "hint": "{i18n:scada.symbol.warning-state-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.warning}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "MAJOR",
            "MINOR",
            "WARNING",
            "INDETERMINATE"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warningClick",
      "name": "{i18n:scada.symbol.warning-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "critical",
      "name": "{i18n:scada.symbol.critical}",
      "hint": "{i18n:scada.symbol.critical-state-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.critical}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "CRITICAL"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "criticalClick",
      "name": "{i18n:scada.symbol.critical-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "criticalAnimation",
      "name": "{i18n:scada.symbol.warning-critical-state-animation}",
      "hint": "{i18n:scada.symbol.warning-critical-state-animation-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.animation}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    }
  ],
  "properties": [
    {
      "id": "runningColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": "{i18n:scada.symbol.running}",
      "divider": true,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null,
      "disabled": false,
      "visible": true
    },
    {
      "id": "stoppedColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#666666",
      "required": null,
      "subLabel": "{i18n:scada.symbol.stopped}",
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null,
      "disabled": false,
      "visible": true
    },
    {
      "id": "warningColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#FAA405",
      "required": null,
      "subLabel": "{i18n:scada.symbol.warning}",
      "divider": true,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null,
      "disabled": false,
      "visible": true
    },
    {
      "id": "criticalColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#D12730",
      "required": null,
      "subLabel": "{i18n:scada.symbol.critical}",
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null,
      "disabled": false,
      "visible": true
    }
  ]
}]]></tb:metadata>
<path d="m368 869h1v-45c0-0.552 0.448-1 1-1h60c0.552 0 1 0.448 1 1v45h1 38c0.552 0 1 0.448 1 1v60c0 0.552-0.448 1-1 1h-39v45c0 0.552-0.448 1-1 1h-60c-0.552 0-1-0.448-1-1v-45h-39c-0.552 0-1-0.448-1-1v-60c0-0.552 0.448-1 1-1h38z" stroke="#1A1A1A" stroke-width="2"/><rect x="385" y="116" width="30" height="143" stroke="#1A1A1A" stroke-width="2"/><rect x="377" y="106" width="46" height="10" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="377" y="259" width="46" height="10" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="385" y="588" width="30" height="225" stroke="#1A1A1A" stroke-width="2"/><rect x="385" y="341" width="30" height="225" stroke="#1A1A1A" stroke-width="2"/><rect x="377" y="578" width="46" height="10" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="377" y="331" width="46" height="10" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="377" y="813" width="46" height="10" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="377" y="566" width="46" height="10" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="377" y="94" width="46" height="10" rx="1" stroke="#1A1A1A" stroke-width="2"/><circle cx="400" cy="30" r="29" stroke="#1A1A1A" stroke-width="2"/><circle cx="400" cy="30" r="20" stroke="#1A1A1A" stroke-width="2"/><rect x="390" y="57" width="2" height="37" fill="#1A1A1A"/><rect x="176" y="285" width="143" height="30" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="408" y="57" width="2" height="37" fill="#1A1A1A"/><rect x="176" y="885" width="143" height="30" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="646" y="285" width="143" height="30" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="646" y="885" width="143" height="30" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="11" y="285" width="143" height="30" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="11" y="885" width="143" height="30" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="481" y="285" width="143" height="30" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="481" y="885" width="143" height="30" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="319" y="277" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="319" y="877" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="789" y="277" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="789" y="877" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="329" y="269" width="142" height="62" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="166" y="277" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="166" y="877" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="636" y="277" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="636" y="877" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="154" y="277" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="154" y="877" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="624" y="277" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="624" y="877" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="1" y="277" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="1" y="877" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="471" y="277" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><rect x="471" y="877" width="10" height="46" rx="1" stroke="#1A1A1A" stroke-width="2"/><g fill="#fff" stroke="#1A1A1A" stroke-width="2" tb:tag="background">
  <rect x="281" y="977" width="238" height="22" rx="1"/>
  <path d="m436.76 159.34-0.692-0.919-0.813 0.814-27.767 27.766 27.767 27.766 0.813 0.814 0.692-0.919c5.803-7.698 9.243-17.279 9.243-27.661s-3.44-19.963-9.243-27.661zm-10.147-8.434 0.84-0.84-0.971-0.684c-7.488-5.28-16.623-8.381-26.479-8.381-10.382 0-19.963 3.44-27.661 9.243l-0.919 0.692 0.814 0.813 26.766 26.767 27.61-27.61zm-62.705 9.485-0.84-0.84-0.684 0.971c-5.28 7.488-8.381 16.623-8.381 26.479s3.101 18.991 8.381 26.479l0.684 0.971 27.45-27.45-26.61-26.61zm8.329 61.862-0.814 0.813 0.919 0.692c7.698 5.803 17.279 9.243 27.661 9.243 9.856 0 18.991-3.101 26.479-8.381l0.971-0.684-28.45-28.45-26.766 26.767zm27.766-98.252c34.794 0 63 28.206 63 63s-28.206 63-63 63-63-28.206-63-63 28.206-63 63-63z"/>
  <path d="m436.76 430.34-0.692-0.919-0.813 0.814-27.767 27.766 27.767 27.766 0.813 0.814 0.692-0.919c5.803-7.698 9.243-17.279 9.243-27.661s-3.44-19.963-9.243-27.661zm-10.147-8.434 0.84-0.84-0.971-0.684c-7.488-5.28-16.623-8.381-26.479-8.381-10.382 0-19.963 3.44-27.661 9.243l-0.919 0.692 0.814 0.813 26.766 26.767 27.61-27.61zm-62.705 9.485-0.84-0.84-0.684 0.971c-5.28 7.488-8.381 16.623-8.381 26.479s3.101 18.991 8.381 26.479l0.684 0.971 27.45-27.45-26.61-26.61zm8.329 61.862-0.814 0.813 0.919 0.692c7.698 5.803 17.279 9.243 27.661 9.243 9.856 0 18.991-3.101 26.479-8.381l0.971-0.684-28.45-28.45-26.766 26.767zm27.766-98.252c34.794 0 63 28.206 63 63s-28.206 63-63 63-63-28.206-63-63 28.206-63 63-63z"/>
  <path d="m436.76 677.34-0.692-0.919-0.813 0.814-27.767 27.766 27.767 27.766 0.813 0.814 0.692-0.919c5.803-7.698 9.243-17.279 9.243-27.661s-3.44-19.963-9.243-27.661zm-10.147-8.434 0.84-0.84-0.971-0.684c-7.488-5.28-16.623-8.381-26.479-8.381-10.382 0-19.963 3.44-27.661 9.243l-0.919 0.692 0.814 0.813 26.766 26.767 27.61-27.61zm-62.705 9.485-0.84-0.84-0.684 0.971c-5.28 7.488-8.381 16.623-8.381 26.479s3.101 18.991 8.381 26.479l0.684 0.971 27.45-27.45-26.61-26.61zm8.329 61.862-0.814 0.813 0.919 0.692c7.698 5.803 17.279 9.243 27.661 9.243 9.856 0 18.991-3.101 26.479-8.381l0.971-0.684-28.45-28.45-26.766 26.767zm27.766-98.252c34.794 0 63 28.206 63 63s-28.206 63-63 63-63-28.206-63-63 28.206-63 63-63z"/>
  <path d="m275.66 336.76 0.919-0.692-0.814-0.813-27.766-27.767-27.766 27.767-0.814 0.813 0.919 0.692c7.698 5.803 17.279 9.243 27.661 9.243s19.963-3.44 27.661-9.243zm8.434-10.147 0.84 0.84 0.684-0.971c5.28-7.488 8.381-16.623 8.381-26.479 0-10.382-3.44-19.963-9.243-27.661l-0.692-0.919-0.813 0.814-26.767 26.766 27.61 27.61zm-9.485-62.705 0.84-0.84-0.971-0.684c-7.488-5.28-16.623-8.381-26.479-8.381s-18.991 3.101-26.479 8.381l-0.971 0.684 27.45 27.45 26.61-26.61zm-61.862 8.329-0.813-0.814-0.692 0.919c-5.803 7.698-9.243 17.279-9.243 27.661 0 9.856 3.101 18.991 8.381 26.479l0.684 0.971 28.45-28.45-26.767-26.766zm98.252 27.766c0 34.794-28.206 63-63 63s-63-28.206-63-63 28.206-63 63-63 63 28.206 63 63z"/>
  <path d="m275.66 936.76 0.919-0.692-0.814-0.813-27.766-27.767-27.766 27.767-0.814 0.813 0.919 0.692c7.698 5.803 17.279 9.243 27.661 9.243s19.963-3.44 27.661-9.243zm8.434-10.147 0.84 0.84 0.684-0.971c5.28-7.488 8.381-16.623 8.381-26.479 0-10.382-3.44-19.963-9.243-27.661l-0.692-0.919-0.813 0.814-26.767 26.766 27.61 27.61zm-9.485-62.705 0.84-0.84-0.971-0.684c-7.488-5.28-16.623-8.381-26.479-8.381s-18.991 3.101-26.479 8.381l-0.971 0.684 27.45 27.45 26.61-26.61zm-61.862 8.329-0.813-0.814-0.692 0.919c-5.803 7.698-9.243 17.279-9.243 27.661 0 9.856 3.101 18.991 8.381 26.479l0.684 0.971 28.45-28.45-26.767-26.766zm98.252 27.766c0 34.794-28.206 63-63 63s-63-28.206-63-63 28.206-63 63-63 63 28.206 63 63z"/>
  <path d="m745.66 336.76 0.919-0.692-0.814-0.813-27.766-27.767-27.766 27.767-0.814 0.813 0.919 0.692c7.698 5.803 17.279 9.243 27.661 9.243s19.963-3.44 27.661-9.243zm8.434-10.147 0.84 0.84 0.684-0.971c5.28-7.488 8.381-16.623 8.381-26.479 0-10.382-3.44-19.963-9.243-27.661l-0.692-0.919-0.813 0.814-26.767 26.766 27.61 27.61zm-9.485-62.705 0.84-0.84-0.971-0.684c-7.488-5.28-16.623-8.381-26.479-8.381s-18.991 3.101-26.479 8.381l-0.971 0.684 27.45 27.45 26.61-26.61zm-61.862 8.329-0.813-0.814-0.692 0.919c-5.803 7.698-9.243 17.279-9.243 27.661 0 9.856 3.101 18.991 8.381 26.479l0.684 0.971 28.45-28.45-26.767-26.766zm98.252 27.766c0 34.794-28.206 63-63 63s-63-28.206-63-63 28.206-63 63-63 63 28.206 63 63z"/>
  <path d="m745.66 936.76 0.919-0.692-0.814-0.813-27.766-27.767-27.766 27.767-0.814 0.813 0.919 0.692c7.698 5.803 17.279 9.243 27.661 9.243s19.963-3.44 27.661-9.243zm8.434-10.147 0.84 0.84 0.684-0.971c5.28-7.488 8.381-16.623 8.381-26.479 0-10.382-3.44-19.963-9.243-27.661l-0.692-0.919-0.813 0.814-26.767 26.766 27.61 27.61zm-9.485-62.705 0.84-0.84-0.971-0.684c-7.488-5.28-16.623-8.381-26.479-8.381s-18.991 3.101-26.479 8.381l-0.971 0.684 27.45 27.45 26.61-26.61zm-61.862 8.329-0.813-0.814-0.692 0.919c-5.803 7.698-9.243 17.279-9.243 27.661 0 9.856 3.101 18.991 8.381 26.479l0.684 0.971 28.45-28.45-26.767-26.766zm98.252 27.766c0 34.794-28.206 63-63 63s-63-28.206-63-63 28.206-63 63-63 63 28.206 63 63z"/>
  <path d="m110.66 336.76 0.919-0.692-0.814-0.813-27.059-27.06-0.7071-0.707-0.7071 0.707-27.059 27.06-0.8134 0.813 0.9186 0.692c7.6985 5.803 17.28 9.243 27.661 9.243s19.963-3.44 27.661-9.243zm8.434-10.147 0.84 0.84 0.684-0.971c5.28-7.488 8.381-16.623 8.381-26.479 0-10.382-3.44-19.963-9.243-27.661l-0.692-0.919-0.813 0.814-26.06 26.059-0.7071 0.707 0.7071 0.707 26.903 26.903zm-9.485-62.705 0.84-0.84-0.971-0.684c-7.488-5.28-16.623-8.381-26.479-8.381s-18.991 3.101-26.479 8.381l-0.9707 0.684 0.8399 0.84 25.903 25.903 0.7071 0.707 0.7071-0.707 25.903-25.903zm-61.862 8.329-0.8134-0.814-0.6923 0.919c-5.8024 7.698-9.2427 17.279-9.2427 27.661 0 9.856 3.1011 18.991 8.3807 26.479l0.6844 0.971 0.8399-0.84 26.903-26.903 0.7071-0.707-0.7071-0.707-26.059-26.059zm98.252 27.766c0 34.794-28.206 63-63 63-34.794 0-63-28.206-63-63s28.206-63 63-63c34.794 0 63 28.206 63 63z"/>
  <path d="m110.66 936.76 0.919-0.692-0.814-0.813-27.059-27.06-0.7071-0.707-0.7071 0.707-27.059 27.06-0.8134 0.813 0.9186 0.692c7.6985 5.803 17.28 9.243 27.661 9.243s19.963-3.44 27.661-9.243zm8.434-10.147 0.84 0.84 0.684-0.971c5.28-7.488 8.381-16.623 8.381-26.479 0-10.382-3.44-19.963-9.243-27.661l-0.692-0.919-0.813 0.814-26.06 26.059-0.7071 0.707 0.7071 0.707 26.903 26.903zm-9.485-62.705 0.84-0.84-0.971-0.684c-7.488-5.28-16.623-8.381-26.479-8.381s-18.991 3.101-26.479 8.381l-0.9707 0.684 0.8399 0.84 25.903 25.903 0.7071 0.707 0.7071-0.707 25.903-25.903zm-61.862 8.329-0.8134-0.814-0.6923 0.919c-5.8024 7.698-9.2427 17.279-9.2427 27.661 0 9.856 3.1011 18.991 8.3807 26.479l0.6844 0.971 0.8399-0.84 26.903-26.903 0.7071-0.707-0.7071-0.707-26.059-26.059zm98.252 27.766c0 34.794-28.206 63-63 63-34.794 0-63-28.206-63-63s28.206-63 63-63c34.794 0 63 28.206 63 63z"/>
  <path d="m580.66 336.76 0.919-0.692-0.814-0.813-27.766-27.767-27.766 27.767-0.814 0.813 0.919 0.692c7.698 5.803 17.279 9.243 27.661 9.243s19.963-3.44 27.661-9.243zm8.434-10.147 0.84 0.84 0.684-0.971c5.28-7.488 8.381-16.623 8.381-26.479 0-10.382-3.44-19.963-9.243-27.661l-0.692-0.919-0.813 0.814-26.767 26.766 27.61 27.61zm-9.485-62.705 0.84-0.84-0.971-0.684c-7.488-5.28-16.623-8.381-26.479-8.381s-18.991 3.101-26.479 8.381l-0.971 0.684 27.45 27.45 26.61-26.61zm-61.862 8.329-0.813-0.814-0.692 0.919c-5.803 7.698-9.243 17.279-9.243 27.661 0 9.856 3.101 18.991 8.381 26.479l0.684 0.971 28.45-28.45-26.767-26.766zm98.252 27.766c0 34.794-28.206 63-63 63s-63-28.206-63-63 28.206-63 63-63 63 28.206 63 63z"/>
  <path d="m580.66 936.76 0.919-0.692-0.814-0.813-27.766-27.767-27.766 27.767-0.814 0.813 0.919 0.692c7.698 5.803 17.279 9.243 27.661 9.243s19.963-3.44 27.661-9.243zm8.434-10.147 0.84 0.84 0.684-0.971c5.28-7.488 8.381-16.623 8.381-26.479 0-10.382-3.44-19.963-9.243-27.661l-0.692-0.919-0.813 0.814-26.767 26.766 27.61 27.61zm-9.485-62.705 0.84-0.84-0.971-0.684c-7.488-5.28-16.623-8.381-26.479-8.381s-18.991 3.101-26.479 8.381l-0.971 0.684 27.45 27.45 26.61-26.61zm-61.862 8.329-0.813-0.814-0.692 0.919c-5.803 7.698-9.243 17.279-9.243 27.661 0 9.856 3.101 18.991 8.381 26.479l0.684 0.971 28.45-28.45-26.767-26.766zm98.252 27.766c0 34.794-28.206 63-63 63s-63-28.206-63-63 28.206-63 63-63 63 28.206 63 63z"/>
 </g><path d="m269.05 0s-269.05 0-269.05 167.5v820.9c0 6.628 7.1636 11.601 16 11.601h768c8.836 0 16-4.973 16-11.601v-820.9c0-167.5-264.28-167.5-264.28-167.5h-135.72zm268.28 203c-5.1548 0-9.3332 3.134-9.3332 7v751c0 3.866 4.1788 7 9.3332 7h58.664c5.1548 0 9.3332-3.134 9.3332-7v-751c0-3.866-4.1788-7-9.3332-7z" fill="#000" fill-opacity="0" tb:tag="clickArea"/><g transform="translate(0,916)" fill="#d12730" style="display: none;" tb:tag="critical">
  <rect width="84" height="84" rx="4" fill="#fff" style=""/>
  <rect width="84" height="84" rx="4" style=""/>
  <rect x="2" y="2" width="80" height="80" rx="2" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 27.562-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g><g transform="translate(0 916)" fill="#faa405" style="display: none;" tb:tag="warning">
  <path d="m38.422 7.1554c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" fill="#fff" style=""/>
  <path d="m38.422 7.1554c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" style=""/>
  <path d="m40.211 8.0498c0.7371-1.4741 2.8407-1.4741 3.5778-1e-5l35.528 71.056c0.6649 1.3298-0.3021 2.8944-1.7888 2.8944h-71.056c-1.4868 0-2.4538-1.5646-1.7889-2.8944z" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 37.562-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g>
</svg>