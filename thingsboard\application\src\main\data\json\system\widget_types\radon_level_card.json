{"fqn": "radon_level_card", "name": "Radon level card", "deprecated": false, "image": "tb-image;/api/images/system/radon_level_card_system_widget_image.png", "description": "Displays the latest radon level telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'radon', label: 'Radon level', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Radon level\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 75 - 37.5;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 300) {\\n\\tvalue = 300;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:radioactive\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":100,\"color\":\"#80C32C\"},{\"from\":100,\"to\":200,\"color\":\"#FFA600\"},{\"from\":200,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":32,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":100,\"color\":\"#80C32C\"},{\"from\":100,\"to\":200,\"color\":\"#FFA600\"},{\"from\":200,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Radon level card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"Bq/m³\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["environment", "indoor", "air", "radon"], "resources": [{"link": "/api/images/system/radon_level_card_system_widget_image.png", "title": "\"Radon level card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "radon_level_card_system_widget_image.png", "publicResourceKey": "NCZbqfSrg9PeBBi6GSveoAeYqMkc82Lv", "mediaType": "image/png", "data": "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", "public": true}]}