{"fqn": "humidity_card", "name": "Humidity card", "deprecated": false, "image": "tb-image;/api/images/system/humidity_card_system_widget_image.png", "description": "Displays the latest humidity telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'humidity', label: 'Humidity', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Humidity\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:water-percent\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#D81838\"},{\"from\":20,\"to\":40,\"color\":\"#FFA600\"},{\"from\":40,\"to\":60,\"color\":\"#5B7EE6\"},{\"from\":60,\"to\":80,\"color\":\"#305AD7\"},{\"from\":80,\"to\":100,\"color\":\"#234CC7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":52,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#D81838\"},{\"from\":20,\"to\":40,\"color\":\"#FFA600\"},{\"from\":40,\"to\":60,\"color\":\"#5B7EE6\"},{\"from\":60,\"to\":80,\"color\":\"#305AD7\"},{\"from\":80,\"to\":100,\"color\":\"#234CC7\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Humidity card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"%\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "moisture", "dampness", "wetness", "humidness", "moistness", "dew", "water vapor", "condensation", "dew point", "steaminess"], "resources": [{"link": "/api/images/system/humidity_card_system_widget_image.png", "title": "\"Humidity card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "humidity_card_system_widget_image.png", "publicResourceKey": "ocOORMW9DSOUXcoazVbXwNhEilM3ckir", "mediaType": "image/png", "data": "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", "public": true}]}