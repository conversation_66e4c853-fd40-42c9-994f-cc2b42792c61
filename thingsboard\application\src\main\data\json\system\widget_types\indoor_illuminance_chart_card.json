{"fqn": "indoor_illuminance_chart_card", "name": "Indoor illuminance chart card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_illuminance_chart_card_system_widget_image.png", "description": "Displays a indoor illuminance data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'illuminance', label: 'Illuminance', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'lx', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'illuminance', 'lx', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Illuminance\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 400 - 200;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"lx\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":100,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":100,\"to\":300,\"color\":\"#FFA600\"},{\"from\":300,\"to\":500,\"color\":\"#F36900\"},{\"from\":500,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 400 - 200;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"lx\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 12 - 6;\\nif (value < -20) {\\n\\tvalue = -20;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"lx\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Illuminance\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:lightbulb-on\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "illuminance", "indoor", "brightness", "luminance", "luminosity", "light", "light level", "light intensity", "lux", "candela", "foot-candle"], "resources": [{"link": "/api/images/system/indoor_illuminance_chart_card_system_widget_image.png", "title": "\"Indoor illuminance chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_illuminance_chart_card_system_widget_image.png", "publicResourceKey": "JPqHwk1yDozVHovb9ZZ58optcH9Ks07O", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAz1BMVEXg4ODf39/g4OAAAADg4ODf39/////g4ODzaQDr6+vCwsLj4+P5+fnz8/POzs7IyMghISHn5+fV1dU8PDz+7N+QkJB0dHTu7u7a2trx8fG8vLxYWFj5tID0fCAuLi6srKy2traCgoL+9u+rq6uenp5KSkrb29v82r+6urr2j0D7x6BmZmb2jkD0chD3oWD1hTDR0dH70K/4qm+wsLD6vZAWFhb948/5tH9hYWH3mFANDQ394s9xcXFTU1P70LChoaH6vY+BgYFFRUU3NzegoKB3nxmSAAAABnRSTlPvIL8Ar7DvmsykAAAIf0lEQVR42u2dCX+aMBiHd/7pQg4SBATmfdTVHd193/v+n2lJUHHiZnVTY8dTKiGAPx7fNwe2P71x++atG96pc+fm7Rs3byQ4eVKtcSfFNSDRaYVrwY3rIuLVIo5Ri7hGLeIatYhrXG+RzwmW8H0I9+djVZFL4I2HucokRhwh8PELvRiuURUJSHLpoU1g6eSFSJpCL36qoBcdJFuE3jD1qW+LxcORqIok3suX73oJfhXRD1HsB3k4Go1yXUQwGoWYhNPQF+08jDHQj6kf5qGPo1AVwfcgCF6S9SKpr4MT+EZE6Voh/Lwj2loIoUCs8gF6QxyBqshl4L0zIj/efFgnAr2gEPH1MhnF7Y4IIcKiGYWjPHdERPPNiHgrEdHNO66KTDsYzURGArHIe/DdSS1Pe0RYEVHhKKyKDNphGBUiKgxj+GHoUBvBm3fvcHKsE/G+PsfJsU4k+XyJk+N6z7VOkf9HhEpcGVmp2PDkBxSRfJyhilhj3EQXK2TYwOFEMu1CBbxEEUYEJTyRRCg5kIAiRAgFxRUEzyjv0ufcXLknBPVIxnTpeeKJjKosQYVDi3ShIWhKnpiVkplsgthqnjCOjBIoSeA1Gde1nOn6ptT+ibAnSo+YU6scWoRQNKkVYSBQXCalCGNc7xdWRJYiiczoQkQkypxa5dAirCs4FOkWIrQrxlYkk4WIlSMwIjSjhYjSaTUWGbMiKut6ToicDLWIa9QirrGDSII5lOoHR/4mvEmEc4BwGFQ5Rlr0iCLtSDOGA2wU0ZedcZhZRhNcGJnnQqBJEym1CBWc0CblwoMSnOJobBZRsqk4lYyYYRCZETFDOZNNjVQJJUz/oEszCIajsVkkyQjjjHgE9semlvTmIoLBiHB0mQB3WoRxxbQNz0oRlbExF1ZEEj4TARk/d1mEmoWCUWbXehOM6ceEWvSOoppBJpnLbeTqjPlWz+SuyHbUIrXIqVCLuEYt4hq1iGvUIq5Ri7hGLeIatciuNM7P+zC4LnL/7OzsPiwPzkrOC4tnj8zGkwtddlykcVaKtFZFGlZjdoTjIq2lyzxbEWmYna3HNlAvHBd5sfR693XpaWNGf5Z1r2dCj/pOi+jcKUXe69KrlWi1FroXTos8Pjt7vBA516XydS/EXixi9RhXglLKKGakqf035P2LXOiXvFGIFFuPsMQn01QWsXmCK8ESDSx+3BZAlE8AMdmjSJH756XIfXO1jadP5+PGizLVHmhjXInE19AiHiIXmMQqBXodv7M3kSKxUIrozdZDvaVrG/MRplGOMLgScvxhPNYivvABLRKF0zBFpE32J/LUJFYpUo6Hpn5XEf/yzeWlFhnEEyvSGyLu6OpIm+xJpN+yXVEp8kSXHj6+//bMrHcV6Qw/DmKKglxoIYxMS4kQ7UmkaBLLIuf3H3zBfBw831EEH162FWb0lP4dDXUpVcPJXkQKgUYpUlL0w892FcFXddhxpFWM2lURzSMbrKqIiwOiucqHBtMyWg8fvsISuq5V6X4dFdGXVlKOfMvXfWHmXguzhzZ87ok8rojcb7Va/UVqPSynKBpdemsNsSN0byL9xgzbsvVs1wbgomzsCx/NJ7OrEDnXZ2I7JGEge78fKRt7/9FsIOzb7re8eSwqGoXIs/OH24qwphZhypMHEsEXOzu5/+yRWRVqJqWKitewIlrqAlvBJYwIOMehRPBseYpSTGLmvMVMpPHkPralECGHE8FFcdf+6HV/vvPBrAJzkSeNt6+wJR4Fl57cc2rB3tfOefX04uK8v7z3l4o+Guj3nep+j04t4hq1iGvUIq5Ri7hGLeIatYhrVESo8sC4gic4FD8dy4oISzg4momAoAQEp0I1tZgVkQScnbyIKESomIlk5FgpptLfXjhRm0U8kkHZNsKhOV5cRBCMpkOx3maHW12F46DakZr08ra1wSrsZLpf7QGLsQnCWJ3oOOKHEZYYTINw6J+giPZYrRnkQd7x9yUy8bEZPxp2hPDTbTzidbW9MIgnexHxwyDCBtKoPcrDdhCE6a4eJSIOw1j8e5FeqDZ9zI4IQ1Fc3SjfFN5oRjj901G6uXT+sYgfTJD2/hQUP29H6bwc9v78bDpyBdP0zwcOJv9YJJ5CM/ltUNIomC7tUsEAfyCMjzWN9wO/WMfrg9JpjwSWGbb/kIZR6O9XJLn3O95M56Xh94+Xq3snH9vRat30m1cUqlwG3Xtb8C9H9o4NyOKfd4ZYRjedXlrNtbKZVPZER7tDXBl7w9hfTqJcrJ94TH7X/+FYIp2VnPani15R5Cvx2dhMdHSPJhIOqhdpg5LGZVZVyfMNM5JDi0QhKvijsGOySkGzTWOIcxxCZBIX9MTyazhYqxeE7cGm+yVRCWXoH0RERAVxMOpgRhz+boKYbp7XpCvnaPc9i1Snn2HPt6VAYGfyaWVI36cIUwyGBMsMRkGsHeIYu+O3h9sP6bvf6mZ0/ZsPQmfY8O96y0Fb+AvUTsGVVxchM4Uul6uv6PBvkyEKloh20ODq6iKn/QbdEiK5JiLMozgV3H0XpRY5cWoR17i2IlTit6iV2Q2QuNM9r4owYmyY/aWEAmahRQVhYBSeAlgxcQE3BVYcYs44KJtFKBkTdMdZ8lwCTcmI7KoM2XjA1DijgoCPjS7hzIh4gshEKHMGwUHZHJEmz8AFA8FMpAlCBQhLRCZlkw7IgEFXZUaEcdLkCcxB+pQDslnEfmisR7NCxEsKEYKMaT8jQmxuEXjWh8mmkpQkXB+EY1Ft7IJzEMKhhIIamzxTY5nxMTzOqSdEQjO9GhtFgFMgybj9TD0orrA1h+1+ZXPTAUKrbcdRRECxgZ17rHpArEVcpxZxjVrENWoR16hFXMO7cQPXAu/GLfe/IvAKpHf1t09fA5NUa9y+ecc7dW7cunn7Jw2lch4ARgY+AAAAAElFTkSuQmCC", "public": true}]}