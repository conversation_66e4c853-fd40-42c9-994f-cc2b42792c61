<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="600" height="400" fill="none" version="1.1" viewBox="0 0 600 400"><tb:metadata xmlns=""><![CDATA[{
  "title": "HP Four-rate energy meter",
  "description": "Four-rate energy meter with various states.",
  "searchTags": [
    "power",
    "energy"
  ],
  "widgetSizeX": 3,
  "widgetSizeY": 2,
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.runningColor;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "critical",
      "stateRenderFunction": "element.attr({fill: ctx.properties.criticalColor});\nif (ctx.values.critical) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = ctx.values.critical && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'criticalClick');"
        }
      }
    },
    {
      "tag": "export-label",
      "stateRenderFunction": "if (ctx.properties.showExportLabel) {\n    element.show();\n    ctx.api.font(element, ctx.properties.exportLabelFont, ctx.properties.exportLabelColor);\n    ctx.api.text(element, ctx.properties.exportLabel);\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "export-rate",
      "stateRenderFunction": "ctx.api.font(element, ctx.properties.exportValueFont, ctx.properties.exportValueColor);\nctx.api.text(element, ctx.api.formatValue(ctx.values.exportRate, {units: ctx.properties.units, decimals: 0, ignoreUnitSymbol: true}));",
      "actions": null
    },
    {
      "tag": "night-label",
      "stateRenderFunction": "if (ctx.properties.showNightLabel) {\n    element.show();\n    ctx.api.font(element, ctx.properties.nightLabelFont, ctx.properties.nightLabelColor);\n    ctx.api.text(element, ctx.properties.nightLabel);\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "night-rate",
      "stateRenderFunction": "ctx.api.font(element, ctx.properties.nightValueFont, ctx.properties.nightValueColor);\nctx.api.text(element, ctx.api.formatValue(ctx.values.nightRate, {units: ctx.properties.units, decimals: 0, ignoreUnitSymbol: true}));",
      "actions": null
    },
    {
      "tag": "off-peak-label",
      "stateRenderFunction": "if (ctx.properties.showOffPeakLabel) {\n    element.show();\n    ctx.api.font(element, ctx.properties.offPeakLabelFont, ctx.properties.offPeakLabelColor);\n    ctx.api.text(element, ctx.properties.offPeakLabel);\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "off-peak-rate",
      "stateRenderFunction": "ctx.api.font(element, ctx.properties.offPeakValueFont, ctx.properties.offPeakValueColor);\nctx.api.text(element, ctx.api.formatValue(ctx.values.offPeakRate, {units: ctx.properties.units, decimals: 0, ignoreUnitSymbol: true}));",
      "actions": null
    },
    {
      "tag": "peak-label",
      "stateRenderFunction": "if (ctx.properties.showPeakLabel) {\n    element.show();\n    ctx.api.font(element, ctx.properties.peakLabelFont, ctx.properties.peakLabelColor);\n    ctx.api.text(element, ctx.properties.peakLabel);\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "peak-rate",
      "stateRenderFunction": "ctx.api.font(element, ctx.properties.peakValueFont, ctx.properties.peakValueColor);\nctx.api.text(element, ctx.api.formatValue(ctx.values.peakRate, {units: ctx.properties.units, decimals: 0, ignoreUnitSymbol: true}));",
      "actions": null
    },
    {
      "tag": "units",
      "stateRenderFunction": "if (ctx.properties.showUnits) {\n    element.show();\n    ctx.api.font(element, ctx.properties.unitsFont, ctx.properties.unitsColor);\n    ctx.api.text(element, ctx.api.unitSymbol(ctx.properties.units));\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "value-box-export",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.exportValueBoxBackground;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "value-box-night",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.nightValueBoxBackground;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "value-box-off-peak",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.offPeakValueBoxBackground;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "value-box-peak",
      "stateRenderFunction": "var color = ctx.properties.stoppedColor;\nif (ctx.values.running) {\n    color = ctx.properties.peakValueBoxBackground;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "warning",
      "stateRenderFunction": "element.attr({fill: ctx.properties.warningColor});\nvar warning = ctx.values.warning && !(ctx.values.warning && ctx.values.critical)\nif (warning) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = warning && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'warningClick');"
        }
      }
    }
  ],
  "behavior": [
    {
      "id": "running",
      "name": "{i18n:scada.symbol.running}",
      "hint": "{i18n:scada.symbol.running-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.running}",
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "running"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "offPeakRate",
      "name": "{i18n:scada.symbol.off-peak-rate}",
      "hint": "{i18n:scada.symbol.measured-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_TIME_SERIES",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "offPeakRate"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "nightRate",
      "name": "{i18n:scada.symbol.night-rate}",
      "hint": "{i18n:scada.symbol.measured-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_TIME_SERIES",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "nightRate"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "peakRate",
      "name": "{i18n:scada.symbol.peak-rate}",
      "hint": "{i18n:scada.symbol.measured-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_TIME_SERIES",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "peakRate"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "exportRate",
      "name": "{i18n:scada.symbol.export-rate}",
      "hint": "{i18n:scada.symbol.measured-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_TIME_SERIES",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "exportRate"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warning",
      "name": "{i18n:scada.symbol.warning}",
      "hint": "{i18n:scada.symbol.warning-state-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.warning}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "MAJOR",
            "MINOR",
            "WARNING",
            "INDETERMINATE"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warningClick",
      "name": "{i18n:scada.symbol.warning-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "critical",
      "name": "{i18n:scada.symbol.critical}",
      "hint": "{i18n:scada.symbol.critical-state-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.critical}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "CRITICAL"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "criticalClick",
      "name": "{i18n:scada.symbol.critical-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "criticalAnimation",
      "name": "{i18n:scada.symbol.warning-critical-state-animation}",
      "hint": "{i18n:scada.symbol.warning-critical-state-animation-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.animation}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    }
  ],
  "properties": [
    {
      "id": "runningColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#FFFFFF",
      "subLabel": "{i18n:scada.symbol.running}",
      "divider": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "stoppedColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#666666",
      "subLabel": "{i18n:scada.symbol.stopped}",
      "disabled": false,
      "visible": true
    },
    {
      "id": "warningColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#FAA405",
      "subLabel": "{i18n:scada.symbol.warning}",
      "divider": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "criticalColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#D12730",
      "subLabel": "{i18n:scada.symbol.critical}",
      "disabled": false,
      "visible": true
    },
    {
      "id": "showOffPeakLabel",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.off-peak-rate}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "offPeakLabel",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.off-peak-rate}",
      "type": "text",
      "default": "T1",
      "disableOnProperty": "showOffPeakLabel",
      "fieldClass": "medium-width",
      "disabled": false,
      "visible": true
    },
    {
      "id": "offPeakLabelFont",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.off-peak-rate}",
      "type": "font",
      "default": {
        "size": 36,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disableOnProperty": "showOffPeakLabel",
      "disabled": false,
      "visible": true
    },
    {
      "id": "offPeakLabelColor",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.off-peak-rate}",
      "type": "color",
      "default": "#000000",
      "disableOnProperty": "showOffPeakLabel",
      "disabled": false,
      "visible": true
    },
    {
      "id": "offPeakValueFont",
      "name": "{i18n:scada.symbol.value}",
      "group": "{i18n:scada.symbol.off-peak-rate}",
      "type": "font",
      "default": {
        "size": 48,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "offPeakValueColor",
      "name": "{i18n:scada.symbol.value}",
      "group": "{i18n:scada.symbol.off-peak-rate}",
      "type": "color",
      "default": "#002878",
      "disabled": false,
      "visible": true
    },
    {
      "id": "offPeakValueBoxBackground",
      "name": "{i18n:scada.symbol.value-box-background}",
      "group": "{i18n:scada.symbol.off-peak-rate}",
      "type": "color",
      "default": "#DEDEDE",
      "disabled": false,
      "visible": true
    },
    {
      "id": "showNightLabel",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "nightLabel",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "text",
      "default": "T2",
      "disableOnProperty": "showNightLabel",
      "fieldClass": "medium-width",
      "disabled": false,
      "visible": true
    },
    {
      "id": "nightLabelFont",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "font",
      "default": {
        "size": 36,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disableOnProperty": "showNightLabel",
      "disabled": false,
      "visible": true
    },
    {
      "id": "nightLabelColor",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "color",
      "default": "#000",
      "disableOnProperty": "showNightLabel",
      "disabled": false,
      "visible": true
    },
    {
      "id": "nightValueFont",
      "name": "{i18n:scada.symbol.value}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "font",
      "default": {
        "size": 48,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "nightValueColor",
      "name": "{i18n:scada.symbol.value}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "color",
      "default": "#002878",
      "disabled": false,
      "visible": true
    },
    {
      "id": "nightValueBoxBackground",
      "name": "{i18n:scada.symbol.value-box-background}",
      "group": "{i18n:scada.symbol.night-rate}",
      "type": "color",
      "default": "#DEDEDE",
      "disabled": false,
      "visible": true
    },
    {
      "id": "showPeakLabel",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.peak-rate}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "peakLabel",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.peak-rate}",
      "type": "text",
      "default": "T3",
      "disableOnProperty": "showPeakLabel",
      "fieldClass": "medium-width",
      "disabled": false,
      "visible": true
    },
    {
      "id": "peakLabelFont",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.peak-rate}",
      "type": "font",
      "default": {
        "size": 36,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disableOnProperty": "showPeakLabel",
      "disabled": false,
      "visible": true
    },
    {
      "id": "peakLabelColor",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.peak-rate}",
      "type": "color",
      "default": "#000",
      "disableOnProperty": "showPeakLabel",
      "disabled": false,
      "visible": true
    },
    {
      "id": "peakValueFont",
      "name": "{i18n:scada.symbol.value}",
      "group": "{i18n:scada.symbol.peak-rate}",
      "type": "font",
      "default": {
        "size": 48,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "peakValueColor",
      "name": "{i18n:scada.symbol.value}",
      "group": "{i18n:scada.symbol.peak-rate}",
      "type": "color",
      "default": "#002878",
      "disabled": false,
      "visible": true
    },
    {
      "id": "peakValueBoxBackground",
      "name": "{i18n:scada.symbol.value-box-background}",
      "group": "{i18n:scada.symbol.peak-rate}",
      "type": "color",
      "default": "#DEDEDE",
      "disabled": false,
      "visible": true
    },
    {
      "id": "showExportLabel",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.export-rate}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "exportLabel",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.export-rate}",
      "type": "text",
      "default": "Export",
      "disableOnProperty": "showExportLabel",
      "fieldClass": "medium-width",
      "disabled": false,
      "visible": true
    },
    {
      "id": "exportLabelFont",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.export-rate}",
      "type": "font",
      "default": {
        "size": 36,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disableOnProperty": "showExportLabel",
      "disabled": false,
      "visible": true
    },
    {
      "id": "exportLabelColor",
      "name": "{i18n:scada.symbol.label}",
      "group": "{i18n:scada.symbol.export-rate}",
      "type": "color",
      "default": "#000",
      "disableOnProperty": "showExportLabel",
      "disabled": false,
      "visible": true
    },
    {
      "id": "exportValueFont",
      "name": "{i18n:scada.symbol.value}",
      "group": "{i18n:scada.symbol.export-rate}",
      "type": "font",
      "default": {
        "size": 48,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "exportValueColor",
      "name": "{i18n:scada.symbol.value}",
      "group": "{i18n:scada.symbol.export-rate}",
      "type": "color",
      "default": "#002878",
      "disabled": false,
      "visible": true
    },
    {
      "id": "exportValueBoxBackground",
      "name": "{i18n:scada.symbol.value-box-background}",
      "group": "{i18n:scada.symbol.export-rate}",
      "type": "color",
      "default": "#DEDEDE",
      "disabled": false,
      "visible": true
    },
    {
      "id": "showUnits",
      "name": "{i18n:scada.symbol.units}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "units",
      "name": "{i18n:scada.symbol.units}",
      "type": "units",
      "default": "kWh",
      "supportsUnitConversion": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "unitsFont",
      "name": "{i18n:scada.symbol.units}",
      "type": "font",
      "default": {
        "size": 36,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "normal",
        "style": "normal"
      },
      "disableOnProperty": "showUnits",
      "disabled": false,
      "visible": true
    },
    {
      "id": "unitsColor",
      "name": "{i18n:scada.symbol.units}",
      "type": "color",
      "default": "#000",
      "disableOnProperty": "showUnits",
      "disabled": false,
      "visible": true
    }
  ]
}]]></tb:metadata>
<rect x="1" y="1" width="598" height="398" fill="#fff" stroke="#1A1A1A" stroke-width="2" tb:tag="background"/><rect x="49" y="81" width="238" height="82" rx="3" fill="#dedede" stroke="#1a1a1a" stroke-width="2" tb:tag="value-box-off-peak"/><rect x="313" y="81" width="238" height="82" rx="3" fill="#dedede" stroke="#1a1a1a" stroke-width="2" tb:tag="value-box-night"/><rect x="49" y="237" width="238" height="82" rx="3" fill="#dedede" stroke="#1a1a1a" stroke-width="2" tb:tag="value-box-peak"/><rect x="313" y="237" width="238" height="82" rx="3" fill="#dedede" stroke="#1a1a1a" stroke-width="2" tb:tag="value-box-export"/><text x="171.2998" y="58.286133" fill="#000000" font-family="Roboto, sans-serif" font-size="34px" font-weight="400" text-anchor="middle" tb:tag="off-peak-label" xml:space="default"><tspan dominant-baseline="start">T1</tspan></text><text x="433.2998" y="58.734375" fill="#000000" font-family="Roboto, sans-serif" font-size="34px" font-weight="400" text-anchor="middle" tb:tag="night-label"><tspan dominant-baseline="start">T2</tspan></text><text x="169.2998" y="214.23438" fill="#000000" font-family="Roboto, sans-serif" font-size="34px" font-weight="400" text-anchor="middle" tb:tag="peak-label" xml:space="default"><tspan dominant-baseline="start">T3</tspan></text><text x="432.31152" y="213.78613" fill="#000000" font-family="Roboto, sans-serif" font-size="34px" font-weight="400" text-anchor="middle" tb:tag="export-label" xml:space="default"><tspan dominant-baseline="start">Export</tspan></text><text x="169.45312" y="139.625" fill="#002878" font-family="Roboto, sans-serif" font-size="48px" font-weight="400" text-anchor="middle" tb:tag="off-peak-rate" xml:space="default"><tspan dominant-baseline="start">000223</tspan></text><text x="433.45312" y="139.625" fill="#002878" font-family="Roboto, sans-serif" font-size="48px" font-weight="400" text-anchor="middle" tb:tag="night-rate" xml:space="default"><tspan dominant-baseline="start">000223</tspan></text><text x="169.45312" y="295.625" fill="#002878" font-family="Roboto, sans-serif" font-size="48px" font-weight="400" text-anchor="middle" tb:tag="peak-rate" xml:space="default"><tspan dominant-baseline="start">000223</tspan></text><text x="433.45312" y="295.625" fill="#002878" font-family="Roboto, sans-serif" font-size="48px" font-weight="400" text-anchor="middle" tb:tag="export-rate" xml:space="default"><tspan dominant-baseline="start">000223</tspan></text><text x="299.89453" y="371.67578" fill="#000000" font-family="Roboto, sans-serif" font-size="36px" font-weight="400" text-anchor="middle" tb:tag="units" xml:space="preserve"><tspan dominant-baseline="start">kWh</tspan></text><path d="m201.8-2e-4s-201.8 0-201.8 67v328.36c0 2.6512 5.3727 4.6404 12 4.6404h576c6.627 0 12-1.9892 12-4.6404v-328.36c0-67-198.21-67-198.21-67h-101.79zm201.21 81.2c-3.8661 0-6.9999 1.2536-6.9999 2.8v300.4c0 1.5464 3.1341 2.8 6.9999 2.8h43.998c3.8661 0 6.9999-1.2536 6.9999-2.8v-300.4c0-1.5464-3.1341-2.8-6.9999-2.8z" fill-opacity="0" fill="#000" tb:tag="clickArea"/><g fill="#d12730" style="display: none;" tb:tag="critical">
  <rect width="84" height="84" rx="4" fill="#fff" style=""/>
  <rect width="84" height="84" rx="4" style=""/>
  <rect x="2" y="2" width="80" height="80" rx="2" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 27.562-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g><g fill="#faa405" style="display: none;" tb:tag="warning">
  <path d="m35.955 2.2112c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" fill="#fff" style=""/>
  <path d="m35.955 2.2112c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" style=""/>
  <path d="m37.72 3.1061c0.7371-1.4741 2.8407-1.4741 3.5778-1e-5l35.528 71.056c0.6649 1.3298-0.3021 2.8944-1.7888 2.8944h-71.056c-1.4868 0-2.4538-1.5646-1.7889-2.8944z" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m42.092 32.618-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g>
</svg>