{"fqn": "simple_flow_rate_chart_card_with_background", "name": "Simple flow rate chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_flow_rate_chart_card_with_background.svg", "description": "Displays historical flow rate values as a simplified chart with background. Optionally may display the corresponding latest flow rate value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'flowRate', label: 'Flow rate', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'flowRate', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Flow rate\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#2B54CE\"},{\"from\":10,\"to\":30,\"color\":\"#3B911C\"},{\"from\":30,\"to\":50,\"color\":\"#F77410\"},{\"from\":50,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/simple_flow_rate_chart_card_background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Flow rate\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:hydro-power\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"units\":\"m³/hr\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true},\"margin\":\"0px\"}"}, "tags": ["liquid", "fluid", "flow rate", "fluid dynamics", "velocity", "mass flow", "volume flow"], "resources": [{"link": "/api/images/system/simple_flow_rate_chart_card_background.png", "title": "simple_flow_rate_chart_card_background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_flow_rate_chart_card_background.png", "publicResourceKey": "3F8gCPAhozpTaXB6mwhyOOy4aeSQ89QO", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_flow_rate_chart_card_with_background.svg", "title": "simple_flow_rate_chart_card_with_background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_flow_rate_chart_card_with_background.svg", "publicResourceKey": "9MmYBwRDHFzeHNygbEAYkSedDHH5UZ32", "mediaType": "image/svg+xml", "data": "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", "public": true}]}