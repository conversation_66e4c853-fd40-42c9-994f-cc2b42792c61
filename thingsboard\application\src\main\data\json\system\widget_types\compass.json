{"fqn": "analogue_gauges.analogue_compass", "name": "<PERSON>mp<PERSON>", "deprecated": false, "image": "tb-image;/api/images/system/compass_system_widget_image.png", "description": "Displays latest value of the attribute or time series data on the compass. Expects value to be in range of 0 to 360.", "descriptor": {"type": "latest", "sizeX": 6, "sizeY": 5, "resources": [], "templateHtml": "<canvas id=\"compass\"></canvas>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.gauge = new TbAnalogueCompass(self.ctx, 'compass');\n}\n\nself.onDataUpdated = function() {\n    self.ctx.gauge.update();\n}\n\nself.onResize = function() {\n    self.ctx.gauge.resize();\n}\n\nself.onMobileModeChanged = function() {\n    self.ctx.gauge.mobileModeChanged();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'direction', label: 'Direction', type: 'timeseries' }];\n        }\n    };\n}\n\nself.onDestroy = function() {\n    self.ctx.gauge.destroy();\n}\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-analogue-compass-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-compass-gauge-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"minorTicks\":22,\"needleCircleSize\":15,\"showBorder\":true,\"borderOuterWidth\":10,\"colorPlate\":\"#222\",\"colorMajorTicks\":\"#f5f5f5\",\"colorMinorTicks\":\"#ddd\",\"colorNeedle\":\"#f08080\",\"colorNeedleCircle\":\"#e8e8e8\",\"colorBorder\":\"#ccc\",\"majorTickFont\":{\"family\":\"Roboto\",\"style\":\"normal\",\"weight\":\"500\",\"color\":\"#ccc\"},\"animation\":true,\"animationDuration\":500,\"animationRule\":\"cycle\",\"animationTarget\":\"needle\",\"majorTicks\":[\"N\",\"NE\",\"E\",\"SE\",\"S\",\"SW\",\"W\",\"NW\"]},\"title\":\"Compass\",\"dropShadow\":true,\"enableFullscreen\":true,\"widgetStyle\":{},\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"decimals\":0,\"noDataDisplayMessage\":\"\",\"configMode\":\"basic\"}"}, "tags": ["direction finder", "magnetic needle", "navigator tool", "orienting device", "gyrocompass", "course plotter", "bearing pointer", "directional guide", "north pointer", "magnetometer"], "resources": [{"link": "/api/images/system/compass_system_widget_image.png", "title": "\"Compass\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "compass_system_widget_image.png", "publicResourceKey": "9VpFT3vSFZohPTeUMw9J8EZ0eQ26kNlJ", "mediaType": "image/png", "data": "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", "public": true}]}