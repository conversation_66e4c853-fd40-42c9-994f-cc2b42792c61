{"fqn": "horizontal_soil_moisture_card", "name": "Horizontal soil moisture card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_soil_moisture_card_system_widget_image.png", "description": "Displays the latest soil moisture telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'soilMoisture', label: 'Soil Moisture', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Soil Moisture\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:water-percent\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#D81838\"},{\"from\":20,\"to\":40,\"color\":\"#F36900\"},{\"from\":40,\"to\":60,\"color\":\"#4B70DD\"},{\"from\":60,\"to\":100,\"color\":\"#234CC7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#D81838\"},{\"from\":20,\"to\":40,\"color\":\"#F36900\"},{\"from\":40,\"to\":60,\"color\":\"#4B70DD\"},{\"from\":60,\"to\":100,\"color\":\"#234CC7\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal soil moisture card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"%\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "soil", "moisture"], "resources": [{"link": "/api/images/system/horizontal_soil_moisture_card_system_widget_image.png", "title": "\"Horizontal soil moisture card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_soil_moisture_card_system_widget_image.png", "publicResourceKey": "lCUovi2MPa9WcwjSDq67kw1mwniQZF1u", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAolBMVEUAAADf39/f39/g4ODg4ODf39/////k5OQjTMfg4ODy8vLk6fg+Ys7IyMjOzs5YWFjj4+Py9Pv5+fmRpeN1j9zV1dWsrKyQkJDb29u7u7uDmuDCwsK2trZaedUwV8t0dHTn5+eesefW3fVKSkrI0vFMbtE8PDyRpuNmZmasvOqCgoK6x+6rq6uenp7t7e2tvOoxV8rW3fRohNkhISGwsLAvLy8aKS4pAAAABnRSTlMAIEDfv1C6kOEmAAADj0lEQVR42u3aCW/aMBjGcbZ2b42P3CEXCYSrQKHXtu//1faaQQPTFCYNlbR6fhVSaquS/3UcoZYeAAAAAAAAAAAAAAAAAAAAAAAAAAAAwHv68uH1rK83dx+dd/ONO+4C+vDk3dferaRPILjt9elT6COkYxDSNQjpGoR0DUK65u8h9dynI3KbeGRlxgyIvEQSJTFZI0lMXv/92l9D7oXQihru1kyJuCWOBwmR+Z7xa0CSRzZ2XC63JPnLk/ZFl6OeZmTN6vCBDvw6VGdCmo7TEjfxPHJHrpcMdiFLl0buwJsu17Sg0dqNF27MQ8ZdbNZL17tYRilEaVdeHi9IaT0XL+0hTQc7urvkZpHwSgfrfcjIlW4yWGc09RY03cZyNPB2IUZ+H4y2l+rQonya8EUu5rNJZJus8NknHfHs7EzIROzldDAiWpgpbZN9iJtNN6NdiFyQMdOYQ6a0tCE/jfHoMiIR0s6rXbMvxD5EE5UR5frcjmhx8JacTN0RrZdT7xAif3p2E9zE3lp8M214a5YLDqHEXcZ0EUpoUqr59hBSC6V0roQ6E7ISIqorUb0KcdQs969T8nhONmMXsRLlnNfwQsxX6m1/fDuqdERnQrQtj6oJze2WXFHIJz18Fc/2qD6d3OmTCdVanQl54R+5f5hP6L752euIhP2ll6K2Sw9zzRdvVLWahKFqC5kJVtkOFtGVNGc9FPlh7c9+MxepquSHcEuI2ne81tWVd6QW+T7k6YmYFrPm+Kj8mUfylhDi9T/wfnC1vWg5wPLxzwFJlzQT2ieaixVVNkFVb88pX4cU7R7CbSGhEDq350Q3T63h4bn0mNJOEFDQd+xAnwIiGm/GFKTp7wkekJe5t3RY2kXUvKJQNzf6gx17VnxQ2kJ8LQ5WRyFx7KTGOHxVUJEWWTAsnDg2TvboZAGRM5ZZlpJ0dhOOyej/+TmvobTbwBWiCv2TyZLn/LYQUoeSmo5DnKE0ztj5HTKOU77msXHqFGYX0k9TDjHDsZGcW0i6AF8dlqrs1Smlzr3X8ldzIapoQm9+OGnhDGNTSLsjTpYWQzk0Do89ZmPjkA0J7I6Msx92Ii4KehftIeel45bJ2KT0DzoREgRtk5L+RSdCugghXYOQrkFI1yCka/q920/wb3YiecsfGLj+H27/W8AfGOh9u/HuPrqbr5/pQzUAAAAAAAAAAAAAAAAAAAAAAAAAAAAA7+QX7yoAidBTT3IAAAAASUVORK5CYII=", "public": true}]}