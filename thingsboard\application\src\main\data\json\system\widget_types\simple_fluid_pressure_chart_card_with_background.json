{"fqn": "simple_fluid_pressure_chart_card_with_background", "name": "Simple pressure chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_pressure_chart_card_with_background.svg", "description": "Displays historical fluid pressure values as a simplified chart with background. Optionally may display the corresponding latest fluid pressure value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pressure', label: 'Pressure', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'pressure', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 15 - 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 15 - 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":5,\"color\":\"#2B54CE\"},{\"from\":5,\"to\":10,\"color\":\"#3B911C\"},{\"from\":10,\"to\":15,\"color\":\"#F77410\"},{\"from\":15,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/simple_pressure_chart_card_background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Pressure\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"compress\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"units\":\"bar\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true},\"margin\":\"0px\"}"}, "tags": ["fluid pressure", "liquid pressure", "pump pressure", "hydraulic", "pump", "compressibility", "compressive stress", "pipe", "pipeline"], "resources": [{"link": "/api/images/system/simple_pressure_chart_card_background.png", "title": "simple_pressure_chart_card_background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_pressure_chart_card_background.png", "publicResourceKey": "99kZfm8RZ8LlARGC4eYcfMRqmCalBR8F", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_pressure_chart_card_with_background.svg", "title": "simple_pressure_chart_card_with_background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_pressure_chart_card_with_background.svg", "publicResourceKey": "LpdQcDz0AWF2G5qqnGfA0I45fVw2Jnvx", "mediaType": "image/svg+xml", "data": "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", "public": true}]}