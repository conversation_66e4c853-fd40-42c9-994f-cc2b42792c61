{"fqn": "simple_wind_speed_chart_card_with_background", "name": "Simple wind speed chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_wind_speed_chart_card_with_background_system_widget_image.png", "description": "Displays historical wind speed values as a simplified chart with background. Optionally may display the corresponding latest wind speed value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'speed', label: 'Wind Speed', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'speed', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Wind Speed\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 16 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 26) {\\n\\tvalue = 26;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 16 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 26) {\\n\\tvalue = 26;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0.2,\"color\":\"#6083EC\"},{\"from\":0.2,\"to\":3.4,\"color\":\"#5579E5\"},{\"from\":3.4,\"to\":8,\"color\":\"#4369DD\"},{\"from\":8,\"to\":10.8,\"color\":\"#2B54CE\"},{\"from\":10.8,\"to\":17.2,\"color\":\"#224AC2\"},{\"from\":17.2,\"to\":24.5,\"color\":\"#F04022\"},{\"from\":24.5,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_wind_speed_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Wind Speed\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:windsock\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":1,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"m/s\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "wind", "speed", "airspeed", "flow", "gust"], "resources": [{"link": "/api/images/system/simple_wind_speed_chart_card_with_background_system_widget_background.png", "title": "\"Simple wind speed chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_wind_speed_chart_card_with_background_system_widget_background.png", "publicResourceKey": "x4EV1LmAW7fCTOJGeVxQiWwxKwrDjEOv", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_wind_speed_chart_card_with_background_system_widget_image.png", "title": "\"Simple wind speed chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_wind_speed_chart_card_with_background_system_widget_image.png", "publicResourceKey": "cv1gxXstwBlyPE6PxF7KG8MynkfgCnhq", "mediaType": "image/png", "data": "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", "public": true}]}