{"fqn": "simple_individual_allergy_index_iai_chart_card", "name": "Simple individual allergy index (IAI) chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple-IAI-value-and-chart-card.svg", "description": "Displays the concentration of airborne allergens, including pollen and mold spores, which can trigger allergic reactions in sensitive individuals as a simplified chart. Optionally may display the corresponding latest concentration of allergens value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'IAI_level', label: 'IAI', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'IAI_level', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Air Quality Index\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 12) {\\n\\tvalue = 12;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 12) {\\n\\tvalue = 12;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#3FA71A\"},{\"from\":2,\"to\":6,\"color\":\"#80C32C\"},{\"from\":6,\"to\":9,\"color\":\"#F36900\"},{\"from\":9,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"IAI\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:flower-pollen\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":null,\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "air", "aqi", "pollution", "emission", "smog"], "resources": [{"link": "/api/images/system/simple-IAI-value-and-chart-card.svg", "title": "simple-IAI-value-and-chart-card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple-IAI-value-and-chart-card.svg", "publicResourceKey": "X0yY53zIvUvcSq0VbvuXC9ugByBmZos6", "mediaType": "image/svg+xml", "data": "PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMTYwIiBmaWxsPSJub25lIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwOCIgeT0iMjYiIGZpbGw9IiNmZmYiIHJ4PSI0Ii8+PHJlY3Qgd2lkdGg9IjE5OSIgaGVpZ2h0PSIxMDciIHg9Ii41IiB5PSIyNi41IiBzdHJva2U9IiMwMDAiIHN0cm9rZS1vcGFjaXR5PSIuMTIiIHJ4PSIzLjUiLz48ZyBmaWx0ZXI9InVybCgjYSkiPjxyZWN0IHdpZHRoPSIxODIiIGhlaWdodD0iOTAiIHg9IjkiIHk9IjM1IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9Ii43NiIgcng9IjMuMDA4Ii8+PC9nPjxwYXRoIGZpbGw9IiMwMDAiIGZpbGwtb3BhY2l0eT0iLjg3IiBkPSJNMjIuNzMzIDQ2LjQzOGExLjQ1OCAxLjQ1OCAwIDAgMC0xLjQ1OC0xLjQ1OWMtLjMwOSAwLS41OTUuMDkzLS44MTYuMjU3di0uMTExYTEuNDU4IDEuNDU4IDAgMSAwLTIuOTE3IDB2LjExYTEuMzc1IDEuMzc1IDAgMCAwLS44MTctLjI1NiAxLjQ1OCAxLjQ1OCAwIDAgMC0xLjQ1OCAxLjQ1OWMwIC41NzcuMzQ0IDEuMDc5LjgzNCAxLjMxMmExLjQ1MyAxLjQ1MyAwIDAgMC0uNDA3IDIuMzQ0Yy4yNzMuMjczLjY0NC40MjcgMS4wMzEuNDI3LjMxIDAgLjU5NS0uMS44MTctLjI1N3YuMTExYTEuNDU4IDEuNDU4IDAgMCAwIDIuOTE2IDB2LS4xMWMuMjIyLjE1Ny41MDguMjU2LjgxNy4yNTZhMS40NTggMS40NTggMCAwIDAgMS40NTktMS40NTljMC0uNTgzLS4zNDUtMS4wNzktLjgzNS0xLjMxMmExLjQ2IDEuNDYgMCAwIDAgLjgzNC0xLjMxM1pNMTkgNDkuMjA4YTEuNDU4IDEuNDU4IDAgMSAxIDAtMi45MTYgMS40NTggMS40NTggMCAwIDEgMCAyLjkxNlptLS41ODMtNi43MDhjMC0uMzIuMjYyLS41ODMuNTgzLS41ODMuMzIxIDAgLjU4NC4yNjIuNTg0LjU4MyAwIC4zMi0uMjYzLjU4My0uNTg0LjU4M2EuNTg1LjU4NSAwIDAgMS0uNTgzLS41ODNabS0yLjMzNCAxLjE2N2MwLS4zMjEuMjYzLS41ODQuNTg0LS41ODQuMzIgMCAuNTgzLjI2My41ODMuNTg0IDAgLjMyLS4yNjIuNTgzLS41ODMuNTgzYS41ODUuNTg1IDAgMCAxLS41ODQtLjU4M1pNMTQuOTE4IDQyLjVhLjU4NS41ODUgMCAwIDEtLjU4My0uNTgzYzAtLjMyMS4yNjItLjU4NC41ODMtLjU4NC4zMiAwIC41ODMuMjYzLjU4My41ODQgMCAuMzItLjI2Mi41ODMtLjU4My41ODNabTEuNzUtMS43NWMwLS4zMi4yNjItLjU4My41ODMtLjU4My4zMjEgMCAuNTg0LjI2Mi41ODQuNTgzIDAgLjMyLS4yNjMuNTgzLS41ODQuNTgzYS41ODUuNTg1IDAgMCAxLS41ODMtLjU4M1ptMy41IDBjMC0uMzIuMjYyLS41ODMuNTgzLS41ODMuMzIxIDAgLjU4NC4yNjIuNTg0LjU4MyAwIC4zMi0uMjYzLjU4My0uNTg0LjU4M2EuNTg1LjU4NSAwIDAgMS0uNTgzLS41ODNabTMuNSAxLjE2N2MwIC4zMi0uMjYzLjU4My0uNTg0LjU4M2EuNTg1LjU4NSAwIDAgMS0uNTgzLS41ODNjMC0uMzIxLjI2My0uNTg0LjU4NC0uNTg0LjMyIDAgLjU4My4yNjMuNTgzLjU4NFptLTIuMzM0IDEuMTY2Yy4zMjEgMCAuNTg0LjI2My41ODQuNTg0IDAgLjMyLS4yNjMuNTgzLS41ODQuNTgzYS41ODUuNTg1IDAgMCAxLS41ODMtLjU4M2MwLS4zMjEuMjYzLS41ODQuNTg0LS41ODRaTTM0Ljg1OSA0MS4wNDdWNTFoLTEuNzE2di05Ljk1M2gxLjcxNlptNi4zODIgMS4zMjZMMzguMjY3IDUxSDM2LjQ3bDMuNzQ2LTkuOTUzaDEuMTQ5bC0uMTIzIDEuMzI2Wk00My43MjkgNTFsLTIuOTgtOC42MjctLjEzLTEuMzI2aDEuMTU1TDQ1LjUzNCA1MWgtMS44MDVabS0uMTQ0LTMuNjkxdjEuMzZoLTUuNDE0di0xLjM2aDUuNDE0Wm01LjE0NS02LjI2MlY1MWgtMS43MTZ2LTkuOTUzaDEuNzE2WiIvPjxwYXRoIGZpbGw9IiM3Q0MzMjIiIGQ9Im0xNi4yNTQgMTA2LjA2Ni0yLjI1LS41NS45MjYtOC41NzloOS4xOTl2Mi4zOTFoLTYuODU2bC0uNDY4IDQuMTg0Yy4yNjUtLjE1Ny42MzMtLjMwOSAxLjEwMS0uNDU3LjQ2OS0uMTU3IDEuMDA0LS4yMzUgMS42MDYtLjIzNS44MDQgMCAxLjUyMy4xMzMgMi4xNTYuMzk5LjY0LjI1OCAxLjE4NC42MzYgMS42MjkgMS4xMzYuNDQ1LjQ5My43ODUgMS4wOTQgMS4wMiAxLjgwNS4yMzQuNzAzLjM1MSAxLjQ5Ni4zNTEgMi4zNzkgMCAuNzg5LS4xMTcgMS41MjctLjM1MiAyLjIxNWE1LjE1OSA1LjE1OSAwIDAgMS0xLjAzIDEuODE2IDQuNzQgNC43NCAwIDAgMS0xLjc0NyAxLjIzMWMtLjY5NS4yODktMS41Mi40MzMtMi40NzMuNDMzYTYuOSA2LjkgMCAwIDEtMi4wNS0uMzA0IDUuNTk3IDUuNTk3IDAgMCAxLTEuNzQ2LS45MjYgNC43MTcgNC43MTcgMCAwIDEtMS4yNDMtMS41MjRjLS4zMTItLjYwOS0uNDkyLTEuMzA4LS41MzktMi4wOTdoMi43NjZjLjA3LjU1NS4yMjMgMS4wMjcuNDU3IDEuNDE4LjI0Mi4zODMuNTYyLjY3Ni45Ni44NzkuNC4yMDMuODYuMzA0IDEuMzg0LjMwNC40NzYgMCAuODg2LS4wODIgMS4yMy0uMjQ2LjM0NC0uMTcyLjYzLS40MTQuODU2LS43MjYuMjM0LS4zMjEuNDA2LS42OTYuNTE1LTEuMTI1LjExNy0uNDMuMTc2LS45MDcuMTc2LTEuNDMgMC0uNS0uMDY2LS45NTctLjItMS4zNzFhMi45NzQgMi45NzQgMCAwIDAtLjU3My0xLjA3OCAyLjQ1MyAyLjQ1MyAwIDAgMC0uOTUtLjcwM2MtLjM4Mi0uMTcyLS44MjQtLjI1OC0xLjMyNC0uMjU4LS42NzIgMC0xLjE4Ny4wOTgtMS41NDcuMjkzLS4zNTEuMTk1LS42OC40MzctLjk4NC43MjZaIi8+PHBhdGggc3Ryb2tlPSIjMDAwIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1vcGFjaXR5PSIuODciIHN0cm9rZS13aWR0aD0iMiIgZD0ibTMwLjc1MiAxMTcuMTQ4IDE0LjMyMy04LjU0MWE5LjAyNCA5LjAyNCAwIDAgMCAzLjc2NC00LjQxOWw2LjE4OS0xNS41ODRjMi41MDEtNi4yOTggMTEuNDE0LTYuMjk3IDEzLjkxNSAwbC42OTQgMS43NDhjMi40MjQgNi4xMDMgMTEuMjE1IDUuNjMyIDEyLjk3My0uNjk1djBjMS41ODEtNS42OSA5LjEzOC02Ljg1IDEyLjM1My0xLjg5NWw4LjM3IDEyLjkwMWM0LjE5OSA2LjQ3MSAxNC4wNTUgNS4wMzEgMTYuMjI3LTIuMzdsNi42NzgtMjIuNzY2YzIuMjc5LTcuNzY4IDEyLjg0My04LjgzNyAxNi42MzEtMS42ODNsOS4xNTUgMTcuMjg4YTkuMDIyIDkuMDIyIDAgMCAwIDExLjAyNyA0LjI2OGw3Ljk2OC0yLjg2NmE5LjAyIDkuMDIgMCAwIDEgNi43MTcuMjQ0bDExLjg2MSA1LjI3Ii8+PGRlZnM+PGZpbHRlciBpZD0iYSIgd2lkdGg9IjE5MS4wMjMiIGhlaWdodD0iOTkuMDIzIiB4PSI0LjQ4OSIgeT0iMzAuNDg5IiBjb2xvci1pbnRlcnBvbGF0aW9uLWZpbHRlcnM9InNSR0IiIGZpbHRlclVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PGZlRmxvb2QgZmxvb2Qtb3BhY2l0eT0iMCIgcmVzdWx0PSJCYWNrZ3JvdW5kSW1hZ2VGaXgiLz48ZmVHYXVzc2lhbkJsdXIgaW49IkJhY2tncm91bmRJbWFnZUZpeCIgc3RkRGV2aWF0aW9uPSIyLjI1NiIvPjxmZUNvbXBvc2l0ZSBpbjI9IlNvdXJjZUFscGhhIiBvcGVyYXRvcj0iaW4iIHJlc3VsdD0iZWZmZWN0MV9iYWNrZ3JvdW5kQmx1cl8xMDQ5XzMzNzIiLz48ZmVCbGVuZCBpbj0iU291cmNlR3JhcGhpYyIgaW4yPSJlZmZlY3QxX2JhY2tncm91bmRCbHVyXzEwNDlfMzM3MiIgcmVzdWx0PSJzaGFwZSIvPjwvZmlsdGVyPjwvZGVmcz48L3N2Zz4=", "public": true}]}