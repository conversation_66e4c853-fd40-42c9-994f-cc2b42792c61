<?xml version="1.0" encoding="utf-8"?>
<!--BSD 3-Clause License

Copyright (c) 2019, Cloudminds
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>EC<PERSON>L, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON>
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>RCU Control</Name>
    <Description1><![CDATA[This LWM2M Object includes all operations on RCUs.]]></Description1>
    <ObjectID>10319</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10319</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Single</MultipleInstances>
    <Mandatory>Mandatory</Mandatory>
    <Resources>	
		<Item ID="1">
			<Name>RCU Diagnostics Mode</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>Boolean</Type>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[If the Diagnostics Mode is switched on or not, 
				the setting of which is a Boolean value (1,0) where 1 is On and 0 is Off.]]></Description>
		</Item>
		<Item ID="2">
			<Name>RCU Log Recording</Name>
			<Operations>R</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Optional</Mandatory>
			<Type>Boolean</Type>
			<RangeEnumeration/>
			<Units/>
			<Description><![CDATA[If the Log Recording is switched on or not,
				the setting of which is a Boolean value (1,0) where 1 is On and 0 is Off.]]></Description>
		</Item>
		
		<Item ID="100">
			<Name>RCU Shutdown</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type></Type>
			<RangeEnumeration></RangeEnumeration>
			<Units></Units>
			<Description><![CDATA[Shut down the RCU.]]></Description>
		</Item>
		<Item ID="101">
			<Name>RCU Restart</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type></Type>
			<RangeEnumeration></RangeEnumeration>
			<Units></Units>
			<Description><![CDATA[Restart the RCU.]]></Description>
		</Item>
		<Item ID="102">
			<Name>RCU Deactivate</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type></Type>
			<RangeEnumeration></RangeEnumeration>
			<Units></Units>
			<Description><![CDATA[Deactivate the RCU.]]></Description>
		</Item>
		<Item ID="103">
			<Name>RCU Reset</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type></Type>
			<RangeEnumeration></RangeEnumeration>
			<Units></Units>
			<Description><![CDATA[Reset the RCU to factory settings.]]></Description>
		</Item>	
		<Item ID="104">
			<Name>RCU Diagnostics Mode Control</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type></Type>
			<RangeEnumeration></RangeEnumeration>
			<Units></Units>
			<Description><![CDATA[Switch on/off Diagnostics Mode: Parameter: 1:On, 0:Off]]></Description>
		</Item>
		<Item ID="105">
			<Name>RCU Log Recording Control</Name>
			<Operations>E</Operations>
			<MultipleInstances>Single</MultipleInstances>
			<Mandatory>Mandatory</Mandatory>
			<Type></Type>
			<RangeEnumeration></RangeEnumeration>
			<Units></Units>
			<Description><![CDATA[Switch on/off the Log Recording, Parameter: 1:On, 0:Off]]></Description>
		</Item>		
    </Resources>
    <Description2 />
  </Object>
</LWM2M>
