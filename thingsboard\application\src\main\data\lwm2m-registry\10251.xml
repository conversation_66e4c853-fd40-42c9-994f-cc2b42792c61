<?xml version="1.0" encoding="utf-8"?>
<!--BSD 3-Clause License

Copyright (c) 2017, Cisco
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
this list of conditions and the following disclaimer in the documentation
and/or other materials provided with the distribution.

* Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived from
this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON><PERSON><PERSON>L, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON>
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
  <Object ObjectType="MODefinition">
    <Name>AT Command</Name>
    <Description1><![CDATA[Used to execute an AT command on a cellular modem]]></Description1>
    <ObjectID>10251</ObjectID>
    <ObjectURN>urn:oma:lwm2m:x:10251</ObjectURN>
    <LWM2MVersion>1.0</LWM2MVersion>
    <ObjectVersion>1.0</ObjectVersion>
    <MultipleInstances>Multiple</MultipleInstances>
    <Mandatory>Optional</Mandatory>
    <Resources>
      <Item ID="0">
        <Name>Command</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[The AT command to run. Example: AT+CREG? to query registration status]]></Description>
      </Item>
      <Item ID="1">
        <Name>Response</Name>
        <Operations>R</Operations>
        <MultipleInstances>Multiple</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[Response to the AT command. Example: +CREG:0,5 If multiple lines are returned as the modem response, each line will be returned in a separate resource.]]></Description>
      </Item>
      <Item ID="2">
        <Name>Status</Name>
        <Operations>R</Operations>
        <MultipleInstances>Multiple</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type>String</Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[Status of the command execution as returned by the modem. Typical values:OK ERROR]]></Description>
      </Item>
      <Item ID="3">
        <Name>Timeout</Name>
        <Operations>RW</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Type>Integer</Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[Amount of time in seconds allowed for the modem to respond to the command.]]></Description>
      </Item>
      <Item ID="4">
        <Name>Run</Name>
        <Operations>E</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type></Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[Executing this resource will cause the command to be sent to the modem. And the result to be populated using the Response (1) and Status (2) resources]]></Description>
      </Item>
    </Resources>
    <Description2></Description2>
  </Object>
</LWM2M>
