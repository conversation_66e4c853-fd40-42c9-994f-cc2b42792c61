{"fqn": "entity_admin_widgets.device_admin_table", "name": "Device admin table", "deprecated": false, "image": "tb-image;/api/images/system/device_admin_table_system_widget_image.png", "description": "Customized entity table widget with preconfigured actions to create, update and delete devices.", "descriptor": {"type": "latest", "sizeX": 7.5, "sizeY": 6.5, "resources": [], "templateHtml": "<tb-entities-table-widget \n    [ctx]=\"ctx\">\n</tb-entities-table-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n}\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.entitiesTableWidget.onDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.entitiesTableWidget.onEditModeChanged();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        hasDataPageLink: true,\n        warnOnPageDataOverflow: false,\n        dataKeysOptional: true,\n        supportsUnitConversion: true\n    };\n}\n\nself.actionSources = function() {\n    return {\n        'actionCellButton': {\n            name: 'widget-action.action-cell-button',\n            multiple: true,\n            hasShowCondition: true\n        },\n        'rowClick': {\n            name: 'widget-action.row-click',\n            multiple: false\n        },\n        'rowDoubleClick': {\n            name: 'widget-action.row-double-click',\n            multiple: false\n        },\n        'cellClick': {\n            name: 'widget-action.cell-click',\n            multiple: true\n        }\n    };\n}\n\nself.onDestroy = function() {\n}\n", "settingsSchema": "", "dataKeySettingsSchema": "", "settingsDirective": "tb-entities-table-widget-settings", "dataKeySettingsDirective": "tb-entities-table-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-entities-table-basic-config", "defaultConfig": "{\"timewindow\":{\"realtime\":{\"interval\":1000,\"timewindowMs\":86400000},\"aggregation\":{\"type\":\"NONE\",\"limit\":200}},\"showTitle\":true,\"backgroundColor\":\"rgb(255, 255, 255)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"4px\",\"settings\":{\"entitiesTitle\":\"Device admin table\",\"enableSearch\":true,\"enableSelectColumnDisplay\":true,\"enableStickyHeader\":true,\"enableStickyAction\":true,\"showCellActionsMenu\":true,\"reserveSpaceForHiddenAction\":\"true\",\"displayEntityName\":false,\"displayEntityLabel\":false,\"displayEntityType\":false,\"displayPagination\":true,\"defaultPageSize\":10,\"defaultSortOrder\":\"entityName\",\"useRowStyleFunction\":false},\"title\":\"Device admin table\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400,\"padding\":\"5px 10px 5px 10px\"},\"useDashboardTimewindow\":false,\"showLegend\":false,\"datasources\":[{\"type\":\"function\",\"name\":\"Simulated\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Entity name\",\"color\":\"#4caf50\",\"settings\":{},\"_hash\":0.8332260553092266,\"funcBody\":\"return 'Simulated';\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Entity type\",\"color\":\"#f44336\",\"settings\":{},\"_hash\":0.08583217494773887,\"funcBody\":\"return 'Device';\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#f44336\",\"settings\":{\"columnWidth\":\"0px\",\"useCellStyleFunction\":false,\"cellStyleFunction\":\"\",\"useCellContentFunction\":false,\"cellContentFunction\":\"\"},\"_hash\":0.6401141393938932,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"showTitleIcon\":false,\"titleIcon\":\"more_horiz\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"displayTimewindow\":true,\"actions\":{\"headerButton\":[{\"name\":\"Add device\",\"icon\":\"add\",\"type\":\"customPretty\",\"customHtml\":\"<form #addDeviceForm=\\\"ngForm\\\" [formGroup]=\\\"addDeviceFormGroup\\\"\\n      (ngSubmit)=\\\"save()\\\" style=\\\"width: 480px;\\\">\\n  <mat-toolbar class=\\\"flex flex-row\\\" color=\\\"primary\\\">\\n    <h2>Add device</h2>\\n    <span class=\\\"flex-1\\\"></span>\\n    <button mat-icon-button\\n            (click)=\\\"cancel()\\\"\\n            type=\\\"button\\\">\\n      <mat-icon class=\\\"material-icons\\\">close</mat-icon>\\n    </button>\\n  </mat-toolbar>\\n  <mat-progress-bar color=\\\"warn\\\" mode=\\\"indeterminate\\\" *ngIf=\\\"isLoading$ | async\\\">\\n  </mat-progress-bar>\\n  <div style=\\\"height: 4px;\\\" *ngIf=\\\"!(isLoading$ | async)\\\"></div>\\n  <div mat-dialog-content>\\n    <div class=\\\"mat-padding flex flex-col\\\">\\n      <mat-form-field class=\\\"mat-block\\\">\\n        <mat-label>Device name</mat-label>\\n        <input matInput formControlName=\\\"deviceName\\\" required>\\n        <mat-error *ngIf=\\\"addDeviceFormGroup.get('deviceName').hasError('required')\\\">\\n          Device name is required.\\n        </mat-error>\\n      </mat-form-field>\\n      <div class=\\\"flex flex-row gap-2\\\">\\n        <tb-entity-subtype-autocomplete\\n            class=\\\"max-w-50% flex-full\\\"\\n            formControlName=\\\"deviceType\\\"\\n            [required]=\\\"true\\\"\\n            [entityType]=\\\"'DEVICE'\\\"\\n        ></tb-entity-subtype-autocomplete>\\n        <mat-form-field class=\\\"mat-block max-w-50% flex-full\\\">\\n          <mat-label>Label</mat-label>\\n          <input matInput formControlName=\\\"deviceLabel\\\">\\n        </mat-form-field>\\n      </div>\\n      <div formGroupName=\\\"attributes\\\" class=\\\"flex flex-row gap-2\\\">\\n        <mat-form-field class=\\\"mat-block max-w-50% flex-full\\\">\\n          <mat-label>Latitude</mat-label>\\n          <input type=\\\"number\\\" step=\\\"any\\\" matInput formControlName=\\\"latitude\\\">\\n        </mat-form-field>\\n        <mat-form-field class=\\\"mat-block max-w-50% flex-full\\\">\\n          <mat-label>Longitude</mat-label>\\n          <input type=\\\"number\\\" step=\\\"any\\\" matInput formControlName=\\\"longitude\\\">\\n        </mat-form-field>\\n      </div>\\n    </div>\\n  </div>\\n  <div mat-dialog-actions class=\\\"flex flex-row\\\">\\n    <span class=\\\"flex-1\\\"></span>\\n    <button mat-button color=\\\"primary\\\"\\n            type=\\\"button\\\"\\n            [disabled]=\\\"(isLoading$ | async)\\\"\\n            (click)=\\\"cancel()\\\" cdkFocusInitial>\\n      Cancel\\n    </button>\\n    <button mat-button mat-raised-button color=\\\"primary\\\"\\n            style=\\\"margin-right: 20px;\\\"\\n            type=\\\"submit\\\"\\n            [disabled]=\\\"(isLoading$ | async) || addDeviceForm.invalid || !addDeviceForm.dirty\\\">\\n      Create\\n    </button>\\n  </div>\\n</form>\\n\",\"customCss\":\"\",\"customFunction\":\"let $injector = widgetContext.$scope.$injector;\\nlet customDialog = $injector.get(widgetContext.servicesMap.get('customDialog'));\\nlet deviceService = $injector.get(widgetContext.servicesMap.get('deviceService'));\\nlet attributeService = $injector.get(widgetContext.servicesMap.get('attributeService'));\\n\\nopenAddDeviceDialog();\\n\\nfunction openAddDeviceDialog() {\\n    customDialog.customDialog(htmlTemplate, AddDeviceDialogController).subscribe();\\n}\\n\\nfunction AddDeviceDialogController(instance) {\\n    let vm = instance;\\n    \\n    vm.addDeviceFormGroup = vm.fb.group({\\n      deviceName: ['', [vm.validators.required]],\\n      deviceType: ['', [vm.validators.required]],\\n      deviceLabel: [''],\\n      attributes: vm.fb.group({\\n          latitude: [null],\\n          longitude: [null]\\n      })      \\n    });\\n    \\n    vm.cancel = function() {\\n        vm.dialogRef.close(null);\\n    };\\n    \\n    vm.save = function() {\\n        vm.addDeviceFormGroup.markAsPristine();\\n        let device = {\\n            name: vm.addDeviceFormGroup.get('deviceName').value,\\n            type: vm.addDeviceFormGroup.get('deviceType').value,\\n            label: vm.addDeviceFormGroup.get('deviceLabel').value\\n        };\\n        deviceService.saveDevice(device).subscribe(\\n            function (device) {\\n                saveAttributes(device.id).subscribe(\\n                    function () {\\n                        widgetContext.updateAliases();\\n                        vm.dialogRef.close(null);\\n                    }\\n                );\\n            }\\n        );\\n    };\\n    \\n    function saveAttributes(entityId) {\\n        let attributes = vm.addDeviceFormGroup.get('attributes').value;\\n        let attributesArray = [];\\n        for (let key in attributes) {\\n            attributesArray.push({key: key, value: attributes[key]});\\n        }\\n        if (attributesArray.length > 0) {\\n            return attributeService.saveEntityAttributes(entityId, \\\"SERVER_SCOPE\\\", attributesArray);\\n        } else {\\n            return widgetContext.rxjs.of([]);\\n        }\\n    }\\n}\",\"customResources\":[],\"id\":\"70837a9d-c3de-a9a7-03c5-dccd14998758\"}],\"actionCellButton\":[{\"name\":\"Edit device\",\"icon\":\"edit\",\"useShowWidgetActionFunction\":null,\"showWidgetActionFunction\":\"return true;\",\"type\":\"customPretty\",\"customHtml\":\"<form #editDeviceForm=\\\"ngForm\\\" [formGroup]=\\\"editDeviceFormGroup\\\"\\n      (ngSubmit)=\\\"save()\\\" style=\\\"width: 480px;\\\">\\n  <mat-toolbar class=\\\"flex flex-row\\\" color=\\\"primary\\\">\\n    <h2>Edit device</h2>\\n    <span class=\\\"flex-1\\\"></span>\\n    <button mat-icon-button\\n            (click)=\\\"cancel()\\\"\\n            type=\\\"button\\\">\\n      <mat-icon class=\\\"material-icons\\\">close</mat-icon>\\n    </button>\\n  </mat-toolbar>\\n  <mat-progress-bar color=\\\"warn\\\" mode=\\\"indeterminate\\\" *ngIf=\\\"isLoading$ | async\\\">\\n  </mat-progress-bar>\\n  <div style=\\\"height: 4px;\\\" *ngIf=\\\"!(isLoading$ | async)\\\"></div>\\n  <div mat-dialog-content>\\n    <div class=\\\"mat-padding flex flex-col\\\">\\n      <mat-form-field class=\\\"mat-block\\\">\\n        <mat-label>Device name</mat-label>\\n        <input matInput formControlName=\\\"deviceName\\\" required>\\n        <mat-error *ngIf=\\\"editDeviceFormGroup.get('deviceName').hasError('required')\\\">\\n          Device name is required.\\n        </mat-error>\\n      </mat-form-field>\\n      <div class=\\\"flex flex-row gap-2\\\">\\n        <tb-entity-subtype-autocomplete\\n            class=\\\"max-w-50% flex-full\\\"\\n            formControlName=\\\"deviceType\\\"\\n            [required]=\\\"true\\\"\\n            [entityType]=\\\"'DEVICE'\\\"\\n        ></tb-entity-subtype-autocomplete>\\n        <mat-form-field class=\\\"mat-block max-w-50% flex-full\\\">\\n          <mat-label>Label</mat-label>\\n          <input matInput formControlName=\\\"deviceLabel\\\">\\n        </mat-form-field>\\n      </div>\\n      <div formGroupName=\\\"attributes\\\" class=\\\"flex flex-row gap-2\\\">\\n        <mat-form-field class=\\\"mat-block max-w-50% flex-full\\\">\\n          <mat-label>Latitude</mat-label>\\n          <input type=\\\"number\\\" step=\\\"any\\\" matInput formControlName=\\\"latitude\\\">\\n        </mat-form-field>\\n        <mat-form-field class=\\\"mat-block max-w-50% flex-full\\\">\\n          <mat-label>Longitude</mat-label>\\n          <input type=\\\"number\\\" step=\\\"any\\\" matInput formControlName=\\\"longitude\\\">\\n        </mat-form-field>\\n      </div>\\n    </div>\\n  </div>\\n  <div mat-dialog-actions class=\\\"flex flex-row\\\">\\n    <span class=\\\"flex-1\\\"></span>\\n    <button mat-button color=\\\"primary\\\"\\n            type=\\\"button\\\"\\n            [disabled]=\\\"(isLoading$ | async)\\\"\\n            (click)=\\\"cancel()\\\" cdkFocusInitial>\\n      Cancel\\n    </button>\\n    <button mat-button mat-raised-button color=\\\"primary\\\"\\n            style=\\\"margin-right: 20px;\\\"\\n            type=\\\"submit\\\"\\n            [disabled]=\\\"(isLoading$ | async) || editDeviceForm.invalid || !editDeviceForm.dirty\\\">\\n      Update\\n    </button>\\n  </div>\\n</form>\\n\",\"customCss\":\"\",\"customFunction\":\"let $injector = widgetContext.$scope.$injector;\\nlet customDialog = $injector.get(widgetContext.servicesMap.get('customDialog'));\\nlet deviceService = $injector.get(widgetContext.servicesMap.get('deviceService'));\\nlet attributeService = $injector.get(widgetContext.servicesMap.get('attributeService'));\\n\\nopenEditDeviceDialog();\\n\\nfunction openEditDeviceDialog() {\\n    customDialog.customDialog(htmlTemplate, EditDeviceDialogController).subscribe();\\n}\\n\\nfunction EditDeviceDialogController(instance) {\\n    let vm = instance;\\n    \\n    vm.device = null;\\n    vm.attributes = {};\\n    \\n    vm.editDeviceFormGroup = vm.fb.group({\\n      deviceName: ['', [vm.validators.required]],\\n      deviceType: ['', [vm.validators.required]],\\n      deviceLabel: [''],\\n      attributes: vm.fb.group({\\n          latitude: [null],\\n          longitude: [null]\\n      })      \\n    });\\n    \\n    vm.cancel = function() {\\n        vm.dialogRef.close(null);\\n    };\\n    \\n    vm.save = function() {\\n        vm.editDeviceFormGroup.markAsPristine();\\n        if (vm.editDeviceFormGroup.get('deviceType').value !== vm.device.type) {\\n            delete vm.device.deviceProfileId;\\n        }\\n        vm.device.name = vm.editDeviceFormGroup.get('deviceName').value,\\n        vm.device.type = vm.editDeviceFormGroup.get('deviceType').value,\\n        vm.device.label = vm.editDeviceFormGroup.get('deviceLabel').value\\n        deviceService.saveDevice(vm.device).subscribe(\\n            function () {\\n                saveAttributes().subscribe(\\n                    function () {\\n                        widgetContext.updateAliases();\\n                        vm.dialogRef.close(null);\\n                    }\\n                );\\n            }\\n        );\\n    };\\n    \\n    getEntityInfo();\\n    \\n    function getEntityInfo() {\\n        deviceService.getDevice(entityId.id).subscribe(\\n            function (device) {\\n                attributeService.getEntityAttributes(entityId, 'SERVER_SCOPE',\\n                                                    ['latitude', 'longitude']).subscribe(\\n                   function (attributes) {\\n                        for (let i = 0; i < attributes.length; i++) {\\n                            vm.attributes[attributes[i].key] = attributes[i].value; \\n                        }\\n                        vm.device = device;\\n                        vm.editDeviceFormGroup.patchValue(\\n                            {\\n                                deviceName: vm.device.name,\\n                                deviceType: vm.device.type,\\n                                deviceLabel: vm.device.label,\\n                                attributes: {\\n                                    latitude: vm.attributes.latitude,\\n                                    longitude: vm.attributes.longitude\\n                                }\\n                            }, {emitEvent: false}\\n                        );\\n                   } \\n                );\\n            }\\n        );    \\n    }\\n    \\n    function saveAttributes() {\\n        let attributes = vm.editDeviceFormGroup.get('attributes').value;\\n        let attributesArray = [];\\n        for (let key in attributes) {\\n            attributesArray.push({key: key, value: attributes[key]});\\n        }\\n        if (attributesArray.length > 0) {\\n            return attributeService.saveEntityAttributes(entityId, 'SERVER_SCOPE', attributesArray);\\n        } else {\\n            return widgetContext.rxjs.of([]);\\n        }\\n    }\\n}\",\"customResources\":[],\"openInSeparateDialog\":false,\"openInPopover\":false,\"id\":\"93931e52-5d7c-903e-67aa-b9435df44ff4\"},{\"name\":\"Delete device\",\"icon\":\"delete\",\"type\":\"custom\",\"customFunction\":\"let $injector = widgetContext.$scope.$injector;\\nlet dialogs = $injector.get(widgetContext.servicesMap.get('dialogs'));\\nlet deviceService = $injector.get(widgetContext.servicesMap.get('deviceService'));\\n\\nopenDeleteDeviceDialog();\\n\\nfunction openDeleteDeviceDialog() {\\n    let title = \\\"Are you sure you want to delete the device \\\" + entityName +  \\\"?\\\";\\n    let content = \\\"Be careful, after the confirmation, the device and all related data will become unrecoverable!\\\";\\n    dialogs.confirm(title, content, 'Cancel', 'Delete').subscribe(\\n        function (result) {\\n            if (result) {\\n                deleteDevice();\\n            }\\n        }\\n    );\\n}\\n\\nfunction deleteDevice() {\\n    deviceService.deleteDevice(entityId.id).subscribe(\\n        function () {\\n            widgetContext.updateAliases();\\n        }\\n    );\\n}\\n\",\"id\":\"ec2708f6-9ff0-186b-e4fc-7635ebfa3074\"}]},\"configMode\":\"basic\"}"}, "tags": ["provisioning", "management", "administration", "admin"], "resources": [{"link": "/api/images/system/device_admin_table_system_widget_image.png", "title": "\"Device admin table\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "device_admin_table_system_widget_image.png", "publicResourceKey": "DnXJbXoSVOcQDG0KNpkPf3uyvwDplmSa", "mediaType": "image/png", "data": "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", "public": true}]}