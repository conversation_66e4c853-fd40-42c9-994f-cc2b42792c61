{"fqn": "flooding_level_chart_card", "name": "Flooding level chart card", "deprecated": false, "image": "tb-image;/api/images/system/flooding_level_chart_card_system_widget_image.png", "description": "Displays flooding level data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'flooding', label: 'Flooding level', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'm', decimals: 1 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'flooding', 'm', 1);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Flooding level\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 2 - 1;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 5) {\\n\\tvalue = 5;\\n}\\nreturn value;\\n\",\"aggregationType\":null,\"units\":\"m\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"#234CC7\"},{\"from\":1,\"to\":3,\"color\":\"#F36900\"},{\"from\":3,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 2 - 1;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 5) {\\n\\tvalue = 5;\\n}\\nreturn value;\\n\",\"aggregationType\":null,\"units\":\"m\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 1 - 0.5;\\nif (value < -1.5) {\\n\\tvalue = -1.5;\\n} else if (value > 1.5) {\\n\\tvalue = 1.5;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Flooding level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"flood\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "flood", "flooding"], "resources": [{"link": "/api/images/system/flooding_level_chart_card_system_widget_image.png", "title": "\"Flooding level chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "flooding_level_chart_card_system_widget_image.png", "publicResourceKey": "E6YfpOlHgx4xuZGORCvUJFNtfqya4jyP", "mediaType": "image/png", "data": "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", "public": true}]}