{"fqn": "pressure_progress_bar", "name": "Pressure progress bar", "deprecated": false, "image": "tb-image;/api/images/system/pressure_progress_bar_system_widget_image.png", "description": "Displays pressure reading as a horizontal progress bar. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pressure', label: 'Pressure', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < 980) {\\n\\tvalue = 980;\\n} else if (value > 1040) {\\n\\tvalue = 1040;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1000,\"color\":\"#D81838\"},{\"from\":1000,\"to\":1020,\"color\":\"#80C32C\"},{\"from\":1020,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":870,\"tickMax\":1085,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":1000,\"color\":\"#D81838\"},{\"from\":1000,\"to\":1020,\"color\":\"#80C32C\"},{\"from\":1020,\"to\":null,\"color\":\"#D81838\"}]},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Pressure\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"hPa\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"compress\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["progress", "weather", "environment", "barometry"], "resources": [{"link": "/api/images/system/pressure_progress_bar_system_widget_image.png", "title": "\"Pressure progress bar\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "pressure_progress_bar_system_widget_image.png", "publicResourceKey": "FjlTixdYatxXaiab85BkTEq21o83JplN", "mediaType": "image/png", "data": "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", "public": true}]}