{"fqn": "vibration_chart_card", "name": "Vibration chart card", "deprecated": false, "image": "tb-image;/api/images/system/vibration_chart_card_system_widget_image.png", "description": "Displays a vibration data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'vibration', label: 'Vibration', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'm/s²', decimals: 1 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'vibration', 'm/s²', 1);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Vibration\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"let factor = 1000;\\nif (prevValue < 1) {\\n    factor = 1;\\n} else if (prevValue < 10) {\\n    factor = 10;\\n} else if (prevValue < 100) {\\n    factor = 100;\\n}\\nlet value = prevValue + Math.random() * factor;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m/s²\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0.1,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0.1,\"to\":1,\"color\":\"#FFA600\"},{\"from\":1,\"to\":10,\"color\":\"#F36900\"},{\"from\":10,\"to\":100,\"color\":\"#F04022\"},{\"from\":100,\"to\":1000,\"color\":\"#D81838\"},{\"from\":1000,\"to\":null,\"color\":\"#6F113A\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"let factor = 1000;\\nif (prevValue < 1) {\\n    factor = 1;\\n} else if (prevValue < 10) {\\n    factor = 10;\\n} else if (prevValue < 100) {\\n    factor = 100;\\n}\\nlet value = prevValue + Math.random() * factor;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m/s²\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 200 - 100;\\nif (value < -100) {\\n\\tvalue = -100;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m/s²\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Vibration\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"vibration\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "vibration", "tremor", "shake", "quiver", "jolt", "oscillation", "pulsation", "resonance"], "resources": [{"link": "/api/images/system/vibration_chart_card_system_widget_image.png", "title": "\"Vibration chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "vibration_chart_card_system_widget_image.png", "publicResourceKey": "YrdFuf2ttIWjWucFlAe47Ao2sOJFGPKN", "mediaType": "image/png", "data": "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", "public": true}]}