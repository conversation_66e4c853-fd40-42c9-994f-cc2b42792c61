{"fqn": "gpio_widgets.raspberry_pi_gpio_control", "name": "Raspberry Pi GPIO Control", "deprecated": false, "image": "tb-image;/api/images/system/raspberry_pi_gpio_control_system_widget_image.png", "description": "Allows to display the state of the GPIO for the target Raspberry Pi device using the latest attribute values. You should set the label of the selected data key to the GPIO PIN (e.g., '1') and use boolean values for the widget to display the data.", "descriptor": {"type": "rpc", "sizeX": 6, "sizeY": 10.5, "resources": [], "templateHtml": "<fieldset class=\"gpio-panel\" style=\"height: 100%;\">\n  <section class=\"gpio-row flex flex-row\" *ngFor=\"let row of rows\"\n           [style.height.px]=\"prefferedRowHeight\">\n    <section class=\"flex flex-1 flex-row\" *ngFor=\"let cell of row; let $index = index\">\n      <section class=\"flex flex-1 flex-row items-center\" [class.justify-end]=\"$index===0\" [class.justify-start]=\"$index!==0\" *ngIf=\"cell\">\n        <span class=\"gpio-left-label\" [class.!hidden]=\"$index!==0\">{{ cell.label }}</span>\n        <section class=\"switch-panel flex flex-row items-center justify-start\" [class.col-0]=\"$index===0\" [class.col-1]=\"$index!==0\"\n                 [style.height.px]=\"prefferedRowHeight\"\n                 [style.background-color]=\"switchPanelBackgroundColor\">\n          <span class=\"pin\" [class.!hidden]=\"$index!==0\">{{cell.pin}}</span>\n          <span class=\"flex-1\" [class.!hidden]=\"$index!==1\"></span>\n          <mat-slide-toggle\n              [disabled]=\"!rpcEnabled || executingRpcRequest\"\n              [checked]=\"cell.enabled\"\n              (change)=\"gpioToggleChange($event, cell)\"\n              (click)=\"gpioClick($event, cell)\">\n          </mat-slide-toggle>\n          <span class=\"flex-1\" [class.!hidden]=\"$index!==0\"></span>\n          <span class=\"pin\" [class.!hidden]=\"$index!==1\">{{cell.pin}}</span>\n        </section>\n        <span class=\"gpio-right-label\" [class.!hidden]=\"$index!==1\">{{ cell.label }}</span>\n      </section>\n      <section class=\"flex flex-1 flex-row\" *ngIf=\"!cell\">\n        <span class=\"flex-1\" [class.!hidden]=\"$index!==0\"></span>\n        <span class=\"switch-panel\"\n              [style.height.px]=\"prefferedRowHeight\"\n              [style.background-color]=\"switchPanelBackgroundColor\"></span>\n        <span class=\"flex-1\" [class.!hidden]=\"$index!==1\"></span>\n      </section>\n    </section>\n  </section>\n  <span class=\"error\" style=\"position: absolute; bottom: 5px;\" [class.!hidden]=\"!rpcErrorText\">{{rpcErrorText}}</span>\n  <mat-progress-bar [class.!hidden]=\"!executingRpcRequest\" style=\"position: absolute; bottom: 0;\" mode=\"indeterminate\"></mat-progress-bar>\n</fieldset>", "templateCss": ".error {\n    font-size: 14px !important;\n    color: maroon;/*rgb(250,250,250);*/\n    background-color: transparent;\n    padding: 6px;\n}\n\n.error span {\n    margin: auto;\n}\n\n.gpio-panel {\n    padding-top: 10px;\n    white-space: nowrap;\n}\n\n.gpio-panel section.flex-1 {\n    min-width: 0px;\n}\n\n\n.switch-panel {\n    margin: 0;\n    height: 32px;\n    width: 66px;\n    min-width: 66px;\n}\n\n.switch-panel mat-slide-toggle {\n    margin: 0;\n    width: 36px;\n    min-width: 36px;\n}\n\n.switch-panel.col-0 mat-slide-toggle {\n    margin-left: 8px;\n    margin-right: 4px;\n}\n\n.switch-panel.col-1 mat-slide-toggle {\n    margin-left: 4px;\n    margin-right: 8px;\n}\n\n.gpio-row {\n    height: 32px;\n}\n\n.pin {\n    margin-top: auto;\n    margin-bottom: auto;\n    color: white;\n    font-size: 12px;\n    width: 16px;\n    min-width: 16px;\n}\n\n.switch-panel.col-0 .pin {\n    margin-left: auto;\n    padding-left: 2px;\n    text-align: right;\n}\n\n.switch-panel.col-1 .pin {\n    margin-right: auto;\n\n    text-align: left;\n}\n\n.gpio-left-label {\n    margin-right: 8px;\n}\n\n.gpio-right-label {\n    margin-left: 8px;\n}", "controllerScript": "var namespace;\nvar cssParser = new cssjs();\n\nself.onInit = function() {\n    var utils = self.ctx.$injector.get(self.ctx.servicesMap.get('utils'));\n    namespace = 'gpio-control-' + utils.guid();\n    cssParser.testMode = false;\n    cssParser.cssPreviewNamespace = namespace;\n    self.ctx.$container.addClass(namespace);\n    self.ctx.ngZone.run(function() {\n       init(); \n    });\n}\n\nfunction init() {\n        \n    var i, gpio;\n    var scope = self.ctx.$scope;\n    var settings = self.ctx.settings;\n    scope.gpioList = [];\n    for (var g = 0; g < settings.gpioList.length; g++) {\n        gpio = settings.gpioList[g];\n        scope.gpioList.push(\n            {\n                row: gpio.row,\n                col: gpio.col,\n                pin: gpio.pin,\n                label: gpio.label,\n                enabled: false\n            }\n        );\n    }\n\n    scope.requestTimeout = settings.requestTimeout || 1000;\n\n    scope.switchPanelBackgroundColor = settings.switchPanelBackgroundColor || tinycolor('green').lighten(2).toRgbString();\n\n    scope.gpioStatusRequest = {\n        method: \"getGpioStatus\",\n        paramsBody: \"{}\"\n    };\n    \n    if (settings.gpioStatusRequest) {\n        scope.gpioStatusRequest.method = settings.gpioStatusRequest.method || scope.gpioStatusRequest.method;\n        scope.gpioStatusRequest.paramsBody = settings.gpioStatusRequest.paramsBody || scope.gpioStatusRequest.paramsBody;\n    }\n    \n    scope.gpioStatusChangeRequest = {\n        method: \"setGpioStatus\",\n        paramsBody: \"{\\n   \\\"pin\\\": \\\"{$pin}\\\",\\n   \\\"enabled\\\": \\\"{$enabled}\\\"\\n}\"\n    };\n    \n    if (settings.gpioStatusChangeRequest) {\n        scope.gpioStatusChangeRequest.method = settings.gpioStatusChangeRequest.method || scope.gpioStatusChangeRequest.method;\n        scope.gpioStatusChangeRequest.paramsBody = settings.gpioStatusChangeRequest.paramsBody || scope.gpioStatusChangeRequest.paramsBody;\n    }\n    \n    scope.parseGpioStatusFunction = \"return body[pin] === true;\";\n    \n    if (settings.parseGpioStatusFunction && settings.parseGpioStatusFunction.length > 0) {\n        scope.parseGpioStatusFunction = settings.parseGpioStatusFunction;\n    }\n    \n    scope.parseGpioStatusFunction = new Function(\"body, pin\", scope.parseGpioStatusFunction);\n    \n    function requestGpioStatus() {\n        self.ctx.controlApi.sendTwoWayCommand(scope.gpioStatusRequest.method, \n                            scope.gpioStatusRequest.paramsBody, \n                            scope.requestTimeout)\n            .subscribe(\n                function success(responseBody) {\n                    for (var g = 0; g < scope.gpioList.length; g++) {\n                        var gpio = scope.gpioList[g];\n                        var enabled = scope.parseGpioStatusFunction.apply(this, [responseBody, gpio.pin]);\n                        gpio.enabled = enabled;   \n                        self.ctx.detectChanges();\n                    }\n                }\n            );\n    }\n    \n    function changeGpioStatus(gpio) {\n        var pin = gpio.pin + '';\n        var enabled = !gpio.enabled;\n        enabled = enabled === true ? 'true' : 'false';\n        var paramsBody = scope.gpioStatusChangeRequest.paramsBody;\n        var requestBody = JSON.parse(paramsBody.replace(\"\\\"{$pin}\\\"\", pin).replace(\"\\\"{$enabled}\\\"\", enabled));\n        self.ctx.controlApi.sendTwoWayCommand(scope.gpioStatusChangeRequest.method, \n                                    requestBody, scope.requestTimeout)\n                    .subscribe(\n                        function success(responseBody) {\n                            var enabled = scope.parseGpioStatusFunction.apply(this, [responseBody, gpio.pin]);\n                            gpio.enabled = enabled;\n                            self.ctx.detectChanges();\n                        }\n                    );\n    }\n    \n    scope.gpioCells = {};\n    var rowCount = 0;\n    for (i = 0; i < scope.gpioList.length; i++) {\n        gpio = scope.gpioList[i];\n        scope.gpioCells[gpio.row+'_'+gpio.col] = gpio;\n        rowCount = Math.max(rowCount, gpio.row+1);\n    }\n    \n    scope.prefferedRowHeight = 32;\n    scope.rows = [];\n    for (i = 0; i < rowCount; i++) {\n        var row = [];\n        for (var c =0; c<2;c++) {\n            if (scope.gpioCells[i+'_'+c]) {\n                row[c] = scope.gpioCells[i+'_'+c];\n            } else {\n                row[c] = null;\n            }\n        }\n        scope.rows.push(row);\n    }\n\n    scope.gpioClick = function($event, gpio) {\n        if (scope.rpcEnabled && !scope.executingRpcRequest) {\n            changeGpioStatus(gpio);\n        }\n    };\n    \n    scope.gpioToggleChange = function($event, gpio) {\n        gpio.enabled = !$event.checked;\n        $event.source.toggle();\n        self.ctx.detectChanges();\n    }\n    \n    if (scope.rpcEnabled) {\n        requestGpioStatus();   \n    }\n    \n    self.onResize();\n}\n\nself.onResize = function() {\n    var scope = self.ctx.$scope;\n    var rowCount = scope.rows.length;\n    var prefferedRowHeight = (self.ctx.height - 35)/rowCount;\n    prefferedRowHeight = Math.min(32, prefferedRowHeight);\n    prefferedRowHeight = Math.max(12, prefferedRowHeight);\n    scope.prefferedRowHeight = prefferedRowHeight;\n    var ratio = prefferedRowHeight/32;\n    \n    var css = '.mat-slide-toggle .mat-slide-toggle-bar {\\n' +\n        '   height: ' + 14*ratio+'px;\\n'+\n        '   width: ' + 36*ratio+'px;\\n'+\n    '}\\n';\n    css += '.mat-slide-toggle .mat-slide-toggle-thumb-container {\\n' +\n        '   height: ' + 20*ratio+'px;\\n'+\n        '   width: ' + 20*ratio+'px;\\n'+\n    '}\\n';\n    css += '.mat-slide-toggle .mat-slide-toggle-thumb {\\n' +\n        '   height: ' + 20*ratio+'px;\\n'+\n        '   width: ' + 20*ratio+'px;\\n'+\n    '}\\n';\n    css += '.mat-slide-toggle .mat-slide-toggle-ripple {\\n' +\n        '   height: ' + 40*ratio+'px;\\n'+\n        '   width: ' + 40*ratio+'px;\\n'+\n        '   top: calc(50% - '+20*ratio+'px);\\n'+\n        '   left: calc(50% - '+20*ratio+'px);\\n'+\n    '}\\n';\n    css += '.gpio-left-label, .gpio-right-label {\\n' +\n        '   font-size: ' + 16*ratio+'px;\\n'+\n    '}\\n';\n    var pinsFontSize = Math.max(9, 12*ratio);\n    css += '.pin {\\n' +\n        '   font-size: ' + pinsFontSize+'px;\\n'+\n    '}\\n';\n\n    cssParser.createStyleElement(namespace, css);\n    \n    self.ctx.detectChanges();\n}\n\nself.onDestroy = function() {\n}\n", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-gpio-control-widget-settings", "defaultConfig": "{\"targetDeviceAliases\":[],\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"parseGpioStatusFunction\":\"return body[pin] === true;\",\"gpioStatusChangeRequest\":{\"method\":\"setGpioStatus\",\"paramsBody\":\"{\\n   \\\"pin\\\": \\\"{$pin}\\\",\\n   \\\"enabled\\\": \\\"{$enabled}\\\"\\n}\"},\"requestTimeout\":500,\"switchPanelBackgroundColor\":\"#008a00\",\"gpioStatusRequest\":{\"method\":\"getGpioStatus\",\"paramsBody\":\"{}\"},\"gpioList\":[{\"pin\":7,\"label\":\"GPIO 4 (GPCLK0)\",\"row\":3,\"col\":0,\"_uniqueKey\":0},{\"pin\":11,\"label\":\"GPIO 17\",\"row\":5,\"col\":0,\"_uniqueKey\":1},{\"pin\":12,\"label\":\"GPIO 18\",\"row\":5,\"col\":1,\"_uniqueKey\":2},{\"_uniqueKey\":3,\"pin\":13,\"label\":\"GPIO 27\",\"row\":6,\"col\":0},{\"_uniqueKey\":4,\"pin\":15,\"label\":\"GPIO 22\",\"row\":7,\"col\":0},{\"_uniqueKey\":5,\"pin\":16,\"label\":\"GPIO 23\",\"row\":7,\"col\":1},{\"_uniqueKey\":6,\"pin\":18,\"label\":\"GPIO 24\",\"row\":8,\"col\":1},{\"_uniqueKey\":7,\"pin\":22,\"label\":\"GPIO 25\",\"row\":10,\"col\":1},{\"_uniqueKey\":8,\"pin\":29,\"label\":\"GPIO 5\",\"row\":14,\"col\":0},{\"_uniqueKey\":9,\"pin\":31,\"label\":\"GPIO 6\",\"row\":15,\"col\":0},{\"_uniqueKey\":10,\"pin\":32,\"label\":\"GPIO 12\",\"row\":15,\"col\":1},{\"_uniqueKey\":11,\"pin\":33,\"label\":\"GPIO 13\",\"row\":16,\"col\":0},{\"_uniqueKey\":12,\"pin\":35,\"label\":\"GPIO 19\",\"row\":17,\"col\":0},{\"_uniqueKey\":13,\"pin\":36,\"label\":\"GPIO 16\",\"row\":17,\"col\":1},{\"_uniqueKey\":14,\"pin\":37,\"label\":\"GPIO 26\",\"row\":18,\"col\":0},{\"_uniqueKey\":15,\"pin\":38,\"label\":\"GPIO 20\",\"row\":18,\"col\":1},{\"_uniqueKey\":16,\"pin\":40,\"label\":\"GPIO 21\",\"row\":19,\"col\":1}]},\"title\":\"Raspberry Pi GPIO Control\"}"}, "tags": ["pin", "pins", "board", "circuit", "digital read", "digital write", "analog read", "analog write", "microcontroller", "i/o", "input/output", "hardware"], "resources": [{"link": "/api/images/system/raspberry_pi_gpio_control_system_widget_image.png", "title": "\"Raspberry Pi GPIO Control\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "raspberry_pi_gpio_control_system_widget_image.png", "publicResourceKey": "X8keIrtlwClDFtStEznSRgtpIElqFBPe", "mediaType": "image/png", "data": "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", "public": true}]}