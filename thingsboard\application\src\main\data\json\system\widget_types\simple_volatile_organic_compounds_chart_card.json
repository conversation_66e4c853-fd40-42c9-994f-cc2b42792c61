{"fqn": "simple_volatile_organic_compounds_chart_card", "name": "Simple volatile organic compounds chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_volatile_organic_compounds_chart_card_system_widget_image.png", "description": "Displays historical volatile organic compounds (VOCs) values as a simplified chart. Optionally may display the corresponding latest VOCs value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'voc', label: 'VOCs', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'voc', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"VOCs\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 2000) {\\n\\tvalue = 2000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 2000) {\\n\\tvalue = 2000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":500,\"color\":\"#80C32C\"},{\"from\":500,\"to\":1000,\"color\":\"#FFA600\"},{\"from\":1000,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"VOCs\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:molecule\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"ppb\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["environment", "indoor", "air", "vocs", "voc", "organic solvents", "hydrocarbons", "emissions", "fumes", "gaseous organics", "contaminants", "air pollutants"], "resources": [{"link": "/api/images/system/simple_volatile_organic_compounds_chart_card_system_widget_image.png", "title": "\"Simple volatile organic compounds chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_volatile_organic_compounds_chart_card_system_widget_image.png", "publicResourceKey": "J4tWwsIuP11WyuGhrxuOGvwnd0l1nya3", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAilBMVEUAAADf39/g4ODf39/f39/g4OD////k5OTYGDjg4OAhISHdNVH1xc08PDzHx8fmboIvLy/j4+Px8fH64uZYWFiQkJDiUmrsi5t0dHSsrKxKSkr98fOenp6CgoK6urr409rV1dXaJkXnb4LpfY/kYHZmZmbfQ13wqLTi4uLzt8Humqj19fXgRF3tmqjcQRJrAAAABnRSTlMAIL9AEN/GQiaNAAAEn0lEQVR42uzPwQ2AIBAAsBMQw5cpSNh/PxMn8Amk3aABAAAAAADwX7k2V75GTuPZ3LhzxDVm3V4fOVKvB5gpWj1CE1mMyGpEXvbrZtVWGAbD8OwbBBKS0kJbrHTS+7/F4w9nOXHtqXHvPoiCo7wYQb2ZId7MEG9miDczxJu/EZI7bgTDyTz9kv0U0ql8hqcGQKjAIhGvADoTcYcXtyGmglJKTb0FnCIDyGRglj60w3RYr+rmodyGhBqLxsgtfwZdSAAeWPdr0IZEBggLJNZseNxtCIJqAYyoX3fSsVmJsIkVg3EwasIRj7sPQWJsRsVHrGgK5DOE9+PQaYEJHvclJPACdNJrQiHjDCQKe0hEZpySUnS7WoAQs6Z0lQQdJIDsJ9N8FgmtwSBV8bhvIQuLjBxix3+NGJvK61rVELTKWhnr1pIZj/sSIrSc638RbthYJjryrBJFO1arOn5HkgEoDnZ/fmu91QzxZoZ4M0O8mSHezBBvZog3zkPMwi8ICUmJKMrbQ4xplJKY0stDWAW7TPLqkH/smeGOqjAQhf9NJqVVoFCqKCyKEM3d93+9O23XCd5AJO7NihtPsuGUNpvz7XTKiht+87zdvjTISQHw69pXBoktN73IXxhkNShDFr8eSPrxEYz14XlvvRrILhZCJc6p04Au3r8YSC5stVGOZBfOLN5biwdZ5XkFLGX992XqlInt7VG8+q8geuhLuCtj+jsP70yQVHKbN7FK5SmweG89AlJ+GhmcLLqQ3CDi9WbRIEYF3BHinSUqrii+UunXUMGEbJw8AlKeXWjpXEEu8hwRGX+XdERsCOX4TZCV2PCrf1JFwwklSlVuZRYLZZPZIJQxRJbeeJA1Rhp0hx156RMWtOR7IHsRrnHG3xxPkwiVKRHvcyvi3VwQNJ/nANKsZRtAEC8BofZQnndYklLW7LXzAYS8hill2fU5kQOkey7IqDanzO7S0Fg2mbm1fGLpXUjNY8QeoMMWSK0rT6jW4egq5+JHeDTOaw+ydr6dQElFzqWJs5hG80TgqpoFwsGdGKTm3UJheSYYxI4iN5rmnO+IpAQg23R0w8CoKrFiazO7gtlK1PZRkDOiHoAU/4JIv4T4Il+Mwu9E9P5Is1Mt8rgeBemnQdgYd4nwAOAu7bXZy2bieNtmCwUpED0I3wogrqUmWuQJIPIW5DgG0k+AGDQTLfIEkJpPrQuHbdFMVqS9D2IVPAHEEfDtNXYAnJlBWuwGPWIYZLxHdiL/YRDuZO3/0Dx5QTzzqVX7oh0cQaP9XH89tQpaN8axhaeA1A1GpqF8gQrNH/oBBqFxiB1dPYTniPdjnzyy9DkgICOX6+K9XiOpLYGXDJ7spnUs2oNcIu9HOCzAj4CA1mUwpb7mqGVd8rSUzjMI/39FPeIW8u+opR6rx344XMxL7K+iMcg9WdfnvwBk7zl+A8gOSEsEkf2Z/bmXMEtLBFmC3iBL0xtkaXqDLE1vkKXpDbI0vUH+tnNHNQACMRAFSxtOACouwb8/LBC+SjPjYAXs68aQbgYNyQHpoLWujNptTpHf3fuIqPP/WaqsSaEwAAAAAACAVx6ocPyABDUGtwAAAABJRU5ErkJggg==", "public": true}]}