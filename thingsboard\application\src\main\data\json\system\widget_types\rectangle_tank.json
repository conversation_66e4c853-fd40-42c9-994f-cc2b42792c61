{"fqn": "rectangle_tank", "name": "Rectangle tank", "deprecated": false, "image": "tb-image;/api/images/system/rectangle_tank_system_widget_image.png", "description": "Widget indicates the level of liquid in Rectangle tank.", "descriptor": {"type": "latest", "sizeX": 4, "sizeY": 4, "resources": [], "templateHtml": "<tb-liquid-level-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-liquid-level-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.liquidLevelWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.liquidLevelWidget.update();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true\n    };\n};\n\nself.onDestroy = function() {\n}\n\nself.actionSources = function() {    \n    return {        \n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false        \n        }    \n    };\n}", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-liquid-level-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-liquid-level-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"return Math.floor(Math.random() * 101);\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"tankSelectionType\":\"static\",\"selectedShape\":\"Rectangle\",\"shapeAttributeName\":\"tankShape\",\"tankColor\":{\"type\":\"range\",\"color\":\"#242770\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E73535DE\"},{\"from\":20,\"to\":null,\"color\":\"#242770\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E73535DE';\\n  }\\n}\\nreturn '#242770';\"},\"datasourceUnits\":\"%\",\"layout\":\"percentage\",\"volumeSource\":\"static\",\"volumeConstant\":500,\"volumeAttributeName\":\"volume\",\"volumeUnits\":\"L\",\"volumeFont\":{\"family\":\"Roboto\",\"size\":14,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"volumeColor\":\"rgba(0, 0, 0, 0.18)\",\"units\":\"%\",\"widgetUnitsSource\":\"static\",\"widgetUnitsAttributeName\":\"units\",\"liquidColor\":{\"type\":\"range\",\"color\":\"#7A8BFF\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E27C7CDE\"},{\"from\":20,\"to\":null,\"color\":\"#7A8BFF\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FF0000DE\"},{\"from\":20,\"to\":null,\"color\":\"rgba(0,0,0,0.87)\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FF0000DE';\\n  }\\n}\\nreturn '#000000DE';\"},\"showBackgroundOverlay\":true,\"backgroundOverlayColor\":{\"type\":\"range\",\"color\":\"#FFFFFFC2\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FFEFEFDE\"},{\"from\":20,\"to\":null,\"color\":\"#FFFFFFC2\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FFEFEFDE';\\n  }\\n}\\nreturn '#FFFFFFC2';\"},\"showTooltip\":true,\"showTooltipLevel\":true,\"tooltipUnits\":\"%\",\"tooltipLevelDecimals\":0,\"tooltipLevelFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipLevelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.76)\",\"rangeList\":[],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"showTooltipDate\":true,\"tooltipDateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"tooltipDateFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipDateColor\":\"rgba(0, 0, 0, 0.76)\",\"tooltipBackgroundColor\":\"rgba(255, 255, 255, 0.76)\",\"tooltipBackgroundBlur\":3,\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Liquid level\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"titleFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"1.5\"},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"showTitleIcon\":false,\"titleIcon\":\"water_drop\",\"iconColor\":\"#5469FF\",\"decimals\":0,\"enableDataExport\":false,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\"}"}, "tags": ["reservoir", "container", "vessel", "storage unit", "cistern", "canister", "vat", "basin", "repository", "bin", "hopper"], "resources": [{"link": "/api/images/system/rectangle_tank_system_widget_image.png", "title": "\"Rectangle tank\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "rectangle_tank_system_widget_image.png", "publicResourceKey": "SerqVM5brjnkdNAqJfjYzx8X1zigWz58", "mediaType": "image/png", "data": "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", "public": true}]}