{"fqn": "vibration_chart_card_with_background", "name": "Vibration chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/vibration_chart_card_with_background_system_widget_image.png", "description": "Displays a vibration data by combining the latest and aggregated values with the background image and optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'vibration', label: 'Vibration', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'm/s²', decimals: 1 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'vibration', 'm/s²', 1);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Vibration\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"let factor = 1000;\\nif (prevValue < 1) {\\n    factor = 1;\\n} else if (prevValue < 10) {\\n    factor = 10;\\n} else if (prevValue < 100) {\\n    factor = 100;\\n}\\nlet value = prevValue + Math.random() * factor;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m/s²\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0.1,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0.1,\"to\":1,\"color\":\"#F89E0D\"},{\"from\":1,\"to\":10,\"color\":\"#F77410\"},{\"from\":10,\"to\":100,\"color\":\"#F04022\"},{\"from\":100,\"to\":1000,\"color\":\"#DE2343\"},{\"from\":1000,\"to\":null,\"color\":\"#791541\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"let factor = 1000;\\nif (prevValue < 1) {\\n    factor = 1;\\n} else if (prevValue < 10) {\\n    factor = 10;\\n} else if (prevValue < 100) {\\n    factor = 100;\\n}\\nlet value = prevValue + Math.random() * factor;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m/s²\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 200 - 100;\\nif (value < -100) {\\n\\tvalue = -100;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m/s²\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/vibration_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Vibration\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"vibration\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "vibration", "tremor", "shake", "quiver", "jolt", "oscillation", "pulsation", "resonance"], "resources": [{"link": "/api/images/system/vibration_chart_card_with_background_system_widget_background.png", "title": "\"Vibration chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "vibration_chart_card_with_background_system_widget_background.png", "publicResourceKey": "n7a62ubTTUQTIN6oIf1QM8BqWUO8pl6g", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/vibration_chart_card_with_background_system_widget_image.png", "title": "\"Vibration chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "vibration_chart_card_with_background_system_widget_image.png", "publicResourceKey": "8PGd51PVHD79XvauHkiBxlZrbmbjDatm", "mediaType": "image/png", "data": "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", "public": true}]}