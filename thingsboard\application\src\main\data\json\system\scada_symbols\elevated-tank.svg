<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="1400" height="2e3" fill="none" version="1.1" viewBox="0 0 1400 2e3"><tb:metadata xmlns=""><![CDATA[{
  "title": "Elevated tank",
  "description": "Elevated tank with current volume value and level visualizations.",
  "searchTags": [
    "tank",
    "elevator"
  ],
  "widgetSizeX": 7,
  "widgetSizeY": 10,
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "var color = ctx.properties.tankColor;\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "fluid",
      "stateRenderFunction": "if (!ctx.properties.transparent) {\n    element.hide();\n} else {\n    var liquidPattern = element.reference('fill');\n\n    var valueSet = element.remember('valueSet');\n    if (!valueSet) {\n        element.remember('valueSet', true);\n        element.attr({height: 0});\n        liquidPattern.transform({translateY: 590});\n    }\n\n    var currentVolume = ctx.values.currentVolume; \n    var tankCapacity = ctx.values.tankCapacity; \n\n    var height = currentVolume / tankCapacity;\n    height = Math.max(0, Math.min(1, height))*895; \n    \n    var elementHeight = element.remember('height');\n    if (height !== elementHeight) {\n        element.remember('height', height);\n        ctx.api.cssAnimate(element, 500).attr({height: height});\n        ctx.api.cssAnimate(liquidPattern, 500).transform({ translateY: 590 + height });\n    }\n}\n",
      "actions": null
    },
    {
      "tag": "fluid-background",
      "stateRenderFunction": "if (!ctx.properties.transparent) {\n    element.hide();\n} else {\n    var color = ctx.properties.fluidColor;\n    element.attr({fill: color, 'fill-opacity': 1});\n    \n    var valueSet = element.remember('valueSet');\n    if (!valueSet) {\n        element.remember('valueSet', true);\n        element.attr({height: 0});\n    }\n    \n    var currentVolume = ctx.values.currentVolume; \n    var tankCapacity = ctx.values.tankCapacity; \n\n    var height = currentVolume / tankCapacity;\n    height = Math.max(0, Math.min(1, height))*895; \n    \n    var elementHeight = element.remember('height');\n    if (height !== elementHeight) {\n        element.remember('height', height);\n        ctx.api.cssAnimate(element, 500).attr({height: height});\n    }\n}\n",
      "actions": null
    },
    {
      "tag": "scale",
      "stateRenderFunction": "if (!ctx.properties.scale) {\n        element.hide();\n} else {\n    var scaleSet = element.remember('scaleSet');\n    if (!scaleSet) {\n        element.remember('scaleSet', true);\n        element.clear();\n        \n        var majorIntervals = ctx.properties.majorIntervals;\n        var minorIntervals = ctx.properties.minorIntervals;\n        \n        var start = 265;\n        var majorIntervalLength = 895 / majorIntervals;\n        var minorIntervalLength = majorIntervalLength / minorIntervals;\n        var tankCapacity = ctx.properties.scaleDisplayFormat ? 100 : (ctx.values.tankCapacity || 100);\n        for (var i = 0; i < majorIntervals + 1; i++) {\n            var y = start + i * majorIntervalLength;\n            var line = ctx.svg.line(825, y, 857, y).stroke({ width: 3 }).attr({class: 'majorTick'});\n            element.add(line);\n            var currentVolume = (tankCapacity - i * (tankCapacity/majorIntervals)).toFixed(0);\n            var majorText = ctx.properties.scaleDisplayFormat ? currentVolume : ctx.api.formatValue(currentVolume, {units: ctx.properties.valueUnits, decimals: 0, ignoreUnitSymbol: !ctx.properties.enableUnitScale});\n            var majorTickText = ctx.svg.text(majorText);\n            majorTickText.attr({x: 815, y: y + 2, 'text-anchor': 'end', class: 'majorTickText'});\n            majorTickText.first().attr({'dominant-baseline': 'middle'});\n            element.add(majorTickText);\n            if (i < majorIntervals) {\n                drawMinorTicks(y, minorIntervals, minorIntervalLength);\n            }\n        }\n    }\n    \n    var majorFont = ctx.properties.majorFont;\n    var majorColor = ctx.properties.majorColor;\n    var minorColor = ctx.properties.minorColor;\n    if (ctx.values.critical) {\n        majorColor = ctx.properties.majorCriticalColor;\n        minorColor = ctx.properties.minorCriticalColor;\n    } else if (ctx.values.warning) {\n        majorColor = ctx.properties.minorWarningColor;\n        minorColor = ctx.properties.minorWarningColor;\n    }\n    \n    var majorTicks = element.find('line.majorTick');\n    majorTicks.forEach(t => t.attr({stroke: majorColor}));\n    \n    var majorTicksText = element.find('text.majorTickText');\n    ctx.api.font(majorTicksText, majorFont, majorColor);\n    \n    var minorTicks = element.find('line.minorTick');\n    minorTicks.forEach(t => t.attr({stroke: minorColor}));\n    \n    var elementCriticalAnimation = element.remember('criticalAnimation');\n    var criticalAnimation = ctx.values.critical && ctx.values.criticalAnimation;\n\n    if (elementCriticalAnimation !== criticalAnimation) {\n        element.remember('criticalAnimation', criticalAnimation);\n        if (criticalAnimation) {\n            ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n        } else {\n            ctx.api.resetCssAnimation(element);\n        }\n    }\n}\n\nfunction drawMinorTicks(start, minorIntervals, minorIntervalLength) {\n    for (var i = 1; i < minorIntervals; i++) {\n        var minorY = start + i * minorIntervalLength;\n        var minorLine = ctx.svg.line(837, minorY, 857, minorY).stroke({ width: 3 }).attr({class: 'minorTick'});\n        element.add(minorLine);\n    }\n}",
      "actions": null
    },
    {
      "tag": "top-layer",
      "stateRenderFunction": "if (ctx.properties.transparent) {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "value-box",
      "stateRenderFunction": "if (!ctx.properties.valueBox) {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "value-box-background",
      "stateRenderFunction": "if (ctx.properties.valueBox) {\n    var color = ctx.properties.valueBoxColor;\n    element.attr({fill: color});\n}",
      "actions": null
    },
    {
      "tag": "value-text",
      "stateRenderFunction": "if (ctx.properties.valueBox) {\n  var valueTextFont = ctx.properties.valueTextFont;\n  var valueTextColor = ctx.properties.valueTextColor;\n  var currentVolume = ctx.values.currentVolume;\n  var valueText = ctx.api.formatValue(currentVolume, 0, ctx.properties.valueUnits, false);\n  ctx.api.font(element, valueTextFont, valueTextColor);\n  ctx.api.text(element, valueText);\n}",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "tankCapacity",
      "name": "{i18n:scada.symbol.tank-capacity}",
      "hint": "{i18n:scada.symbol.tank-capacity-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SERVER_SCOPE",
          "key": "tankCapacity"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "currentVolume",
      "name": "{i18n:scada.symbol.current-volume}",
      "hint": "{i18n:scada.symbol.current-volume-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_TIME_SERIES",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "liquidVolume"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warning",
      "name": "{i18n:scada.symbol.warning-state}",
      "hint": "{i18n:scada.symbol.warning-state-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.warning}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "MAJOR",
            "MINOR",
            "WARNING",
            "INDETERMINATE"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "critical",
      "name": "{i18n:scada.symbol.critical-state}",
      "hint": "{i18n:scada.symbol.critical-state-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.critical}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "CRITICAL"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "criticalAnimation",
      "name": "{i18n:scada.symbol.critical-state-animation}",
      "hint": "{i18n:scada.symbol.critical-state-animation-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.animation}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    }
  ],
  "properties": [
    {
      "id": "tankColor",
      "name": "{i18n:scada.symbol.tank-color}",
      "type": "color",
      "default": "#E5E5E5",
      "disabled": false,
      "visible": true
    },
    {
      "id": "fluidColor",
      "name": "{i18n:scada.symbol.fluid-color}",
      "type": "color",
      "default": "#1EC1F480",
      "disabled": false,
      "visible": true
    },
    {
      "id": "valueBox",
      "name": "{i18n:scada.symbol.value-box}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "valueBoxColor",
      "name": "{i18n:scada.symbol.value-box}",
      "type": "color",
      "default": "#F3F3F3",
      "disableOnProperty": "valueBox",
      "disabled": false,
      "visible": true
    },
    {
      "id": "valueUnits",
      "name": "{i18n:scada.symbol.value-text}",
      "type": "units",
      "default": "gal",
      "subLabel": "{i18n:scada.symbol.units}",
      "supportsUnitConversion": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "valueTextFont",
      "name": "{i18n:scada.symbol.value-text}",
      "type": "font",
      "default": {
        "size": 36,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "500",
        "style": "normal"
      },
      "disableOnProperty": "valueBox",
      "disabled": false,
      "visible": true
    },
    {
      "id": "valueTextColor",
      "name": "{i18n:scada.symbol.value-text}",
      "type": "color",
      "default": "#0000008A",
      "disableOnProperty": "valueBox",
      "disabled": false,
      "visible": true
    },
    {
      "id": "transparent",
      "name": "{i18n:scada.symbol.transparent-mode}",
      "type": "switch",
      "default": false,
      "disabled": false,
      "visible": true
    },
    {
      "id": "scale",
      "name": "{i18n:scada.symbol.scale}",
      "type": "switch",
      "default": false,
      "disableOnProperty": "transparent",
      "disabled": false,
      "visible": true
    },
    {
      "id": "scaleDisplayFormat",
      "name": "{i18n:scada.symbol.scale}",
      "type": "select",
      "default": true,
      "subLabel": "{i18n:scada.symbol.display-format}",
      "disableOnProperty": "scale",
      "items": [
        {
          "value": true,
          "label": "Percentage"
        },
        {
          "value": false,
          "label": "Absolute"
        }
      ],
      "disabled": false,
      "visible": true
    },
    {
      "id": "enableUnitScale",
      "name": "{i18n:scada.symbol.enable-units-scale}",
      "type": "switch",
      "default": false,
      "disableOnProperty": "scale",
      "disabled": false,
      "visible": true
    },
    {
      "id": "majorIntervals",
      "name": "{i18n:scada.symbol.major-ticks}",
      "type": "number",
      "default": 10,
      "subLabel": "{i18n:scada.symbol.intervals}",
      "divider": true,
      "disableOnProperty": "scale",
      "min": 1,
      "step": 1,
      "disabled": false,
      "visible": true
    },
    {
      "id": "majorFont",
      "name": "{i18n:scada.symbol.major-ticks}",
      "type": "font",
      "default": {
        "size": 24,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "500",
        "style": "normal"
      },
      "disableOnProperty": "scale",
      "disabled": false,
      "visible": true
    },
    {
      "id": "majorColor",
      "name": "{i18n:scada.symbol.major-ticks-color}",
      "type": "color",
      "default": "#00000061",
      "subLabel": "{i18n:scada.symbol.normal}",
      "divider": true,
      "disableOnProperty": "scale",
      "disabled": false,
      "visible": true
    },
    {
      "id": "majorWarningColor",
      "name": "{i18n:scada.symbol.major-ticks-color}",
      "type": "color",
      "default": "#FAA405",
      "subLabel": "{i18n:scada.symbol.warning}",
      "divider": true,
      "disableOnProperty": "scale",
      "disabled": false,
      "visible": true
    },
    {
      "id": "majorCriticalColor",
      "name": "{i18n:scada.symbol.major-ticks-color}",
      "type": "color",
      "default": "#D12730",
      "subLabel": "{i18n:scada.symbol.critical}",
      "disableOnProperty": "scale",
      "disabled": false,
      "visible": true
    },
    {
      "id": "minorIntervals",
      "name": "{i18n:scada.symbol.minor-ticks}",
      "type": "number",
      "default": 5,
      "subLabel": "{i18n:scada.symbol.intervals}",
      "disableOnProperty": "scale",
      "min": 1,
      "disabled": false,
      "visible": true
    },
    {
      "id": "minorColor",
      "name": "{i18n:scada.symbol.minor-ticks-color}",
      "type": "color",
      "default": "#0000001F",
      "subLabel": "{i18n:scada.symbol.normal}",
      "divider": true,
      "disableOnProperty": "scale",
      "disabled": false,
      "visible": true
    },
    {
      "id": "minorWarningColor",
      "name": "{i18n:scada.symbol.minor-ticks-color}",
      "type": "color",
      "default": "#FAA405",
      "subLabel": "{i18n:scada.symbol.warning}",
      "divider": true,
      "disableOnProperty": "scale",
      "disabled": false,
      "visible": true
    },
    {
      "id": "minorCriticalColor",
      "name": "{i18n:scada.symbol.minor-ticks-color}",
      "type": "color",
      "default": "#D12730",
      "subLabel": "{i18n:scada.symbol.critical}",
      "disableOnProperty": "scale",
      "disabled": false,
      "visible": true
    }
  ]
}]]></tb:metadata>
<path d="m0 1210c125.45-31.8 1246.3-44.14 1400 0v-58.31c0-5.78-3.73-10.68-9.43-11.66-284.65-48.94-1160.1-38.84-1381.3-0.03-5.6502 0.99-9.2295 5.85-9.2295 11.59l-1.5658e-4 58.41z" fill="#E5E5E5" tb:tag="background"/><path d="m0 1210c125.45-31.8 1246.3-44.14 1400 0v-58.31c0-5.78-3.73-10.68-9.43-11.66-284.65-48.94-1160.1-38.84-1381.3-0.03-5.6502 0.99-9.2295 5.85-9.2295 11.59l-1.5658e-4 58.41z" fill="url(#paint0_linear_2188_188555)"/><path d="m1.5002 1151.6-1.5e-4 56.5c16.355-3.83 46.95-7.36 88.256-10.53 42.98-3.29 97.676-6.21 160.25-8.68 125.15-4.94 281.84-8.09 439.4-8.86 157.57-0.77 316.02 0.83 444.7 5.39 64.34 2.27 121.25 5.29 166.88 9.12 44.13 3.7 77.83 8.17 97.51 13.49v-56.33c0-5.13-3.28-9.34-8.18-10.18-142.18-24.45-432.07-34.16-714.18-32.9s-556.16 13.49-666.65 32.87c-4.8403 0.85-7.9887 5-7.9887 10.11z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><path d="m3.0002 1001.2-1.7e-4 141.92c16.902-3.89 46.868-7.45 86.63-10.66 43.008-3.46 97.726-6.53 160.31-9.13 125.17-5.19 281.88-8.49 439.46-9.3s316.06 0.87 444.77 5.66c64.35 2.39 121.29 5.56 166.96 9.59 42.72 3.76 75.8 8.29 95.87 13.7v-141.67c0-4.444-2.78-7.948-6.86-8.685-141.99-25.67-431.83-35.886-714.03-34.562-282.21 1.325-556.18 14.187-666.44 34.533-3.9914 0.736-6.666 4.162-6.666 8.604z" stroke="#647484" stroke-width="6"/><path d="m100 214c0-6.627 5.373-12 12-12h1176c6.63 0 12 5.373 12 12v994.75c0 5.65-3.62 10.48-9.16 11.59-255.85 51.35-983.13 40.77-1181.9 0.03-5.481-1.12-8.959-5.9-8.959-11.5z" fill="#e5e5e5"/><path d="m100 214c0-6.627 5.373-12 12-12h1176c6.63 0 12 5.373 12 12v994.75c0 5.65-3.62 10.48-9.16 11.59-255.85 51.35-983.13 40.77-1181.9 0.03-5.481-1.12-8.959-5.9-8.959-11.5v-994.87z" fill="url(#paint1_linear_2188_188555)"/><path d="m101.5 214c0-5.799 4.701-10.5 10.5-10.5h1176c5.8 0 10.5 4.701 10.5 10.5v994.75c0 5-3.18 9.16-7.95 10.12-127.76 25.64-373.4 35.84-612.05 34.52-238.66-1.32-470.03-14.15-569.24-34.49l-0.301 1.47 0.301-1.47c-4.703-0.96-7.76-5.06-7.76-10.03v-994.87z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><path d="m100 202h1200v1008h-1200v-1008z" fill="#4A4848" fill-opacity=".5"/><rect transform="scale(1,-1)" x="108" y="-1160" width="1184" height="200" fill="#1ec1f4" fill-opacity=".5" tb:tag="fluid-background"/><rect transform="scale(1,-1)" x="108" y="-1160" width="1184" height="200" fill="url(#liquid)" tb:tag="fluid"/><g transform="translate(100)" tb:tag="scale">
  <line x1="824" x2="856" y1="267.58" y2="267.58" stroke="#000" stroke-opacity=".54" stroke-width="3"/>
  <line x1="836" x2="856" y1="285.44" y2="285.44" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="303.32" y2="303.32" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="321.18" y2="321.18" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="339.06" y2="339.06" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="824" x2="856" y1="356.92" y2="356.92" stroke="#000" stroke-opacity=".54" stroke-width="3"/>
  <line x1="836" x2="856" y1="374.79" y2="374.79" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="392.66" y2="392.66" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="410.53" y2="410.53" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="428.4" y2="428.4" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="824" x2="856" y1="446.27" y2="446.27" stroke="#000" stroke-opacity=".54" stroke-width="3"/>
  <line x1="836" x2="856" y1="464.13" y2="464.13" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="482" y2="482" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="499.87" y2="499.87" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="517.74" y2="517.74" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="824" x2="856" y1="535.61" y2="535.61" stroke="#000" stroke-opacity=".54" stroke-width="3"/>
  <line x1="836" x2="856" y1="553.48" y2="553.48" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="571.35" y2="571.35" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="589.22" y2="589.22" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="607.09" y2="607.09" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="824" x2="856" y1="624.95" y2="624.95" stroke="#000" stroke-opacity=".54" stroke-width="3"/>
  <line x1="836" x2="856" y1="642.82" y2="642.82" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="660.69" y2="660.69" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="678.56" y2="678.56" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="696.43" y2="696.43" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="824" x2="856" y1="714.3" y2="714.3" stroke="#000" stroke-opacity=".54" stroke-width="3"/>
  <line x1="836" x2="856" y1="732.17" y2="732.17" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="750.04" y2="750.04" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="767.9" y2="767.9" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="785.77" y2="785.77" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="824" x2="856" y1="803.64" y2="803.64" stroke="#000" stroke-opacity=".54" stroke-width="3"/>
  <line x1="836" x2="856" y1="821.51" y2="821.51" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="839.38" y2="839.38" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="857.25" y2="857.25" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="875.12" y2="875.12" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="824" x2="856" y1="892.98" y2="892.98" stroke="#000" stroke-opacity=".54" stroke-width="3"/>
  <line x1="836" x2="856" y1="910.86" y2="910.86" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="928.72" y2="928.72" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="946.59" y2="946.59" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="964.46" y2="964.46" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="824" x2="856" y1="982.33" y2="982.33" stroke="#000" stroke-opacity=".54" stroke-width="3"/>
  <line x1="836" x2="856" y1="1000.2" y2="1000.2" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="1018.1" y2="1018.1" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="1035.9" y2="1035.9" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="1053.8" y2="1053.8" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="824" x2="856" y1="1071.7" y2="1071.7" stroke="#000" stroke-opacity=".54" stroke-width="3"/>
  <line x1="836" x2="856" y1="1089.5" y2="1089.5" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="1107.4" y2="1107.4" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="1125.3" y2="1125.3" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="836" x2="856" y1="1143.2" y2="1143.2" stroke="#000" stroke-opacity=".38" stroke-width="3"/>
  <line x1="824" x2="856" y1="1161" y2="1161" stroke="#000" stroke-opacity=".54" stroke-width="3"/>
  <path d="m783.58 255.88v17.121h-2.824v-13.77l-4.184 1.418v-2.332l6.668-2.437zm17.301 7.16v2.789c0 1.336-0.133 2.477-0.399 3.422-0.257 0.938-0.632 1.699-1.125 2.285-0.492 0.586-1.082 1.016-1.769 1.289-0.68 0.274-1.441 0.41-2.285 0.41-0.672 0-1.297-0.086-1.875-0.257-0.571-0.172-1.086-0.442-1.547-0.809s-0.856-0.84-1.184-1.418c-0.32-0.586-0.57-1.285-0.75-2.098-0.172-0.812-0.257-1.754-0.257-2.824v-2.789c0-1.344 0.132-2.477 0.398-3.398 0.266-0.93 0.644-1.684 1.137-2.262 0.492-0.586 1.078-1.012 1.757-1.277 0.688-0.266 1.454-0.399 2.297-0.399 0.68 0 1.305 0.086 1.875 0.258 0.578 0.164 1.094 0.426 1.547 0.785 0.461 0.359 0.852 0.828 1.172 1.406 0.328 0.571 0.578 1.262 0.75 2.075 0.172 0.804 0.258 1.742 0.258 2.812zm-2.824 3.188v-3.61c0-0.679-0.039-1.277-0.117-1.793-0.079-0.523-0.196-0.965-0.352-1.324-0.148-0.367-0.336-0.664-0.563-0.891-0.226-0.234-0.484-0.402-0.773-0.504-0.289-0.109-0.613-0.164-0.973-0.164-0.437 0-0.828 0.086-1.172 0.258-0.343 0.164-0.632 0.43-0.867 0.797-0.234 0.367-0.414 0.852-0.539 1.453-0.117 0.594-0.176 1.317-0.176 2.168v3.61c0 0.687 0.039 1.293 0.118 1.816 0.078 0.523 0.195 0.973 0.351 1.348 0.156 0.367 0.344 0.671 0.563 0.914 0.226 0.234 0.484 0.406 0.773 0.515 0.297 0.11 0.621 0.164 0.973 0.164 0.445 0 0.84-0.086 1.183-0.257 0.344-0.172 0.633-0.446 0.868-0.821 0.234-0.383 0.41-0.879 0.527-1.488s0.176-1.34 0.176-2.191zm16.715-3.188v2.789c0 1.336-0.133 2.477-0.399 3.422-0.258 0.938-0.633 1.699-1.125 2.285s-1.082 1.016-1.769 1.289c-0.68 0.274-1.442 0.41-2.286 0.41-0.671 0-1.296-0.086-1.875-0.257-0.57-0.172-1.086-0.442-1.546-0.809-0.461-0.367-0.856-0.84-1.184-1.418-0.32-0.586-0.57-1.285-0.75-2.098-0.172-0.812-0.258-1.754-0.258-2.824v-2.789c0-1.344 0.133-2.477 0.399-3.398 0.265-0.93 0.644-1.684 1.136-2.262 0.492-0.586 1.078-1.012 1.758-1.277 0.688-0.266 1.453-0.399 2.297-0.399 0.68 0 1.305 0.086 1.875 0.258 0.578 0.164 1.094 0.426 1.547 0.785 0.461 0.359 0.851 0.828 1.172 1.406 0.328 0.571 0.578 1.262 0.75 2.075 0.172 0.804 0.258 1.742 0.258 2.812zm-2.825 3.188v-3.61c0-0.679-0.039-1.277-0.117-1.793-0.078-0.523-0.195-0.965-0.351-1.324-0.149-0.367-0.336-0.664-0.563-0.891-0.226-0.234-0.484-0.402-0.773-0.504-0.289-0.109-0.614-0.164-0.973-0.164-0.438 0-0.828 0.086-1.172 0.258-0.344 0.164-0.633 0.43-0.867 0.797s-0.414 0.852-0.539 1.453c-0.117 0.594-0.176 1.317-0.176 2.168v3.61c0 0.687 0.039 1.293 0.117 1.816s0.196 0.973 0.352 1.348c0.156 0.367 0.344 0.671 0.562 0.914 0.227 0.234 0.485 0.406 0.774 0.515 0.297 0.11 0.621 0.164 0.972 0.164 0.446 0 0.84-0.086 1.184-0.257 0.344-0.172 0.633-0.446 0.867-0.821 0.235-0.383 0.41-0.879 0.528-1.488 0.117-0.609 0.175-1.34 0.175-2.191z" fill="#000" fill-opacity=".54"/>
  <path d="m792.03 360.47h0.235c1.078 0 1.976-0.141 2.695-0.422 0.727-0.289 1.305-0.688 1.734-1.196 0.43-0.507 0.739-1.105 0.926-1.793 0.188-0.687 0.281-1.433 0.281-2.238v-2.941c0-0.695-0.074-1.305-0.222-1.828-0.141-0.532-0.344-0.973-0.61-1.325-0.258-0.359-0.558-0.628-0.902-0.808-0.336-0.18-0.699-0.27-1.09-0.27-0.43 0-0.816 0.098-1.16 0.293-0.336 0.188-0.621 0.45-0.856 0.785-0.226 0.329-0.402 0.715-0.527 1.161-0.117 0.437-0.176 0.906-0.176 1.406 0 0.469 0.055 0.922 0.164 1.359 0.118 0.43 0.289 0.813 0.516 1.149s0.512 0.601 0.856 0.797c0.343 0.195 0.746 0.293 1.207 0.293 0.437 0 0.839-0.083 1.207-0.247 0.367-0.171 0.687-0.402 0.961-0.691 0.273-0.289 0.488-0.613 0.644-0.973 0.156-0.359 0.242-0.726 0.258-1.101l1.078 0.328c0 0.594-0.125 1.18-0.375 1.758-0.242 0.57-0.582 1.093-1.02 1.57-0.429 0.469-0.933 0.844-1.511 1.125-0.571 0.281-1.192 0.422-1.864 0.422-0.812 0-1.531-0.152-2.156-0.457-0.617-0.313-1.133-0.734-1.547-1.266-0.406-0.531-0.711-1.14-0.914-1.828-0.203-0.687-0.304-1.41-0.304-2.168 0-0.82 0.125-1.59 0.375-2.308 0.25-0.719 0.613-1.352 1.089-1.899 0.477-0.555 1.055-0.984 1.735-1.289 0.687-0.312 1.465-0.469 2.332-0.469 0.922 0 1.73 0.18 2.426 0.539 0.695 0.36 1.281 0.856 1.757 1.489 0.477 0.632 0.836 1.367 1.079 2.203 0.242 0.836 0.363 1.734 0.363 2.695v0.996c0 1.008-0.09 1.981-0.27 2.918-0.179 0.93-0.472 1.793-0.879 2.59-0.398 0.789-0.925 1.484-1.582 2.086-0.648 0.594-1.445 1.059-2.39 1.394-0.938 0.329-2.039 0.493-3.305 0.493h-0.258zm22.739-7.735v2.789c0 1.336-0.133 2.477-0.399 3.422-0.258 0.938-0.633 1.699-1.125 2.285s-1.082 1.016-1.769 1.289c-0.68 0.274-1.442 0.411-2.286 0.411-0.671 0-1.296-0.086-1.875-0.258-0.57-0.172-1.086-0.442-1.546-0.809-0.461-0.367-0.856-0.84-1.184-1.418-0.32-0.586-0.57-1.285-0.75-2.097-0.172-0.813-0.258-1.754-0.258-2.825v-2.789c0-1.343 0.133-2.476 0.399-3.398 0.265-0.93 0.644-1.684 1.136-2.262 0.492-0.586 1.078-1.012 1.758-1.277 0.688-0.266 1.453-0.399 2.297-0.399 0.68 0 1.305 0.086 1.875 0.258 0.578 0.164 1.094 0.426 1.547 0.785 0.461 0.36 0.851 0.828 1.172 1.407 0.328 0.57 0.578 1.261 0.75 2.074 0.172 0.804 0.258 1.742 0.258 2.812zm-2.825 3.188v-3.61c0-0.679-0.039-1.277-0.117-1.793-0.078-0.523-0.195-0.964-0.351-1.324-0.149-0.367-0.336-0.664-0.563-0.89-0.226-0.235-0.484-0.403-0.773-0.504-0.289-0.11-0.614-0.164-0.973-0.164-0.438 0-0.828 0.086-1.172 0.257-0.344 0.164-0.633 0.43-0.867 0.797s-0.414 0.852-0.539 1.453c-0.117 0.594-0.176 1.317-0.176 2.168v3.61c0 0.687 0.039 1.293 0.117 1.816 0.078 0.524 0.196 0.973 0.352 1.348 0.156 0.367 0.344 0.672 0.562 0.914 0.227 0.234 0.485 0.406 0.774 0.516 0.297 0.109 0.621 0.164 0.972 0.164 0.446 0 0.84-0.086 1.184-0.258s0.633-0.446 0.867-0.821c0.235-0.382 0.41-0.878 0.528-1.488 0.117-0.609 0.175-1.34 0.175-2.191z" fill="#000" fill-opacity=".54"/>
  <path d="m800.91 447.75c0 1.063-0.246 1.957-0.738 2.684-0.492 0.726-1.164 1.277-2.016 1.652-0.844 0.367-1.797 0.551-2.859 0.551-1.063 0-2.02-0.184-2.871-0.551-0.852-0.375-1.524-0.926-2.016-1.652-0.492-0.727-0.738-1.621-0.738-2.684 0-0.703 0.136-1.34 0.41-1.91 0.273-0.578 0.66-1.074 1.16-1.488 0.508-0.422 1.102-0.746 1.781-0.973 0.688-0.227 1.438-0.34 2.25-0.34 1.078 0 2.043 0.199 2.895 0.598 0.851 0.398 1.519 0.949 2.004 1.652 0.492 0.703 0.738 1.524 0.738 2.461zm-2.836-0.141c0-0.57-0.117-1.07-0.351-1.5-0.235-0.429-0.563-0.761-0.985-0.996-0.422-0.234-0.91-0.351-1.465-0.351-0.562 0-1.05 0.117-1.465 0.351-0.414 0.235-0.738 0.567-0.972 0.996-0.227 0.43-0.34 0.93-0.34 1.5 0 0.579 0.113 1.079 0.34 1.5 0.226 0.415 0.551 0.731 0.972 0.95 0.422 0.218 0.918 0.328 1.489 0.328 0.57 0 1.062-0.11 1.476-0.328 0.414-0.219 0.735-0.535 0.961-0.95 0.227-0.421 0.34-0.921 0.34-1.5zm2.449-7.781c0 0.852-0.226 1.61-0.679 2.274-0.446 0.664-1.063 1.187-1.852 1.57-0.789 0.375-1.687 0.562-2.695 0.562-1.016 0-1.922-0.187-2.719-0.562-0.789-0.383-1.41-0.906-1.863-1.57-0.446-0.664-0.668-1.422-0.668-2.274 0-1.015 0.222-1.871 0.668-2.566 0.453-0.703 1.074-1.238 1.863-1.606 0.789-0.367 1.691-0.55 2.707-0.55s1.918 0.183 2.707 0.55c0.789 0.368 1.406 0.903 1.852 1.606 0.453 0.695 0.679 1.551 0.679 2.566zm-2.824 0.094c0-0.508-0.101-0.953-0.305-1.336-0.195-0.391-0.472-0.695-0.832-0.914-0.359-0.219-0.785-0.328-1.277-0.328s-0.918 0.105-1.277 0.316c-0.36 0.211-0.637 0.508-0.832 0.891-0.196 0.383-0.293 0.84-0.293 1.371 0 0.523 0.097 0.98 0.293 1.371 0.195 0.383 0.472 0.684 0.832 0.902 0.367 0.219 0.797 0.329 1.289 0.329s0.918-0.11 1.277-0.329c0.36-0.218 0.637-0.519 0.832-0.902 0.195-0.391 0.293-0.848 0.293-1.371zm17.067 2.52v2.789c0 1.335-0.133 2.476-0.399 3.421-0.258 0.938-0.633 1.7-1.125 2.286s-1.082 1.015-1.769 1.289c-0.68 0.273-1.442 0.41-2.286 0.41-0.671 0-1.296-0.086-1.875-0.258-0.57-0.172-1.086-0.441-1.546-0.809-0.461-0.367-0.856-0.839-1.184-1.418-0.32-0.586-0.57-1.285-0.75-2.097-0.172-0.813-0.258-1.754-0.258-2.824v-2.789c0-1.344 0.133-2.477 0.399-3.399 0.265-0.93 0.644-1.684 1.136-2.262 0.492-0.586 1.078-1.011 1.758-1.277 0.688-0.266 1.453-0.398 2.297-0.398 0.68 0 1.305 0.086 1.875 0.257 0.578 0.164 1.094 0.426 1.547 0.786 0.461 0.359 0.851 0.828 1.172 1.406 0.328 0.57 0.578 1.261 0.75 2.074 0.172 0.805 0.258 1.742 0.258 2.813zm-2.825 3.187v-3.609c0-0.68-0.039-1.278-0.117-1.793-0.078-0.524-0.195-0.965-0.351-1.325-0.149-0.367-0.336-0.664-0.563-0.89-0.226-0.235-0.484-0.403-0.773-0.504-0.289-0.109-0.614-0.164-0.973-0.164-0.438 0-0.828 0.086-1.172 0.258-0.344 0.164-0.633 0.429-0.867 0.797-0.234 0.367-0.414 0.851-0.539 1.453-0.117 0.593-0.176 1.316-0.176 2.168v3.609c0 0.687 0.039 1.293 0.117 1.816 0.078 0.524 0.196 0.973 0.352 1.348 0.156 0.367 0.344 0.672 0.562 0.914 0.227 0.235 0.485 0.406 0.774 0.516 0.297 0.109 0.621 0.164 0.972 0.164 0.446 0 0.84-0.086 1.184-0.258s0.633-0.445 0.867-0.82c0.235-0.383 0.41-0.879 0.528-1.489 0.117-0.609 0.175-1.339 0.175-2.191z" fill="#000" fill-opacity=".54"/>
  <path d="m801.09 525.04v1.547l-6.82 15.516h-2.977l6.809-14.813h-8.836v-2.25zm13.68 7.102v2.789c0 1.336-0.133 2.476-0.399 3.422-0.258 0.937-0.633 1.699-1.125 2.285s-1.082 1.015-1.769 1.289c-0.68 0.273-1.442 0.41-2.286 0.41-0.671 0-1.296-0.086-1.875-0.258-0.57-0.172-1.086-0.441-1.546-0.808-0.461-0.368-0.856-0.84-1.184-1.418-0.32-0.586-0.57-1.286-0.75-2.098-0.172-0.813-0.258-1.754-0.258-2.824v-2.789c0-1.344 0.133-2.477 0.399-3.399 0.265-0.93 0.644-1.683 1.136-2.262 0.492-0.585 1.078-1.011 1.758-1.277 0.688-0.265 1.453-0.398 2.297-0.398 0.68 0 1.305 0.086 1.875 0.257 0.578 0.165 1.094 0.426 1.547 0.786 0.461 0.359 0.851 0.828 1.172 1.406 0.328 0.57 0.578 1.262 0.75 2.074 0.172 0.805 0.258 1.742 0.258 2.813zm-2.825 3.187v-3.609c0-0.68-0.039-1.278-0.117-1.793-0.078-0.524-0.195-0.965-0.351-1.324-0.149-0.368-0.336-0.664-0.563-0.891-0.226-0.234-0.484-0.402-0.773-0.504-0.289-0.109-0.614-0.164-0.973-0.164-0.438 0-0.828 0.086-1.172 0.258-0.344 0.164-0.633 0.429-0.867 0.797-0.234 0.367-0.414 0.851-0.539 1.453-0.117 0.594-0.176 1.316-0.176 2.168v3.609c0 0.688 0.039 1.293 0.117 1.817 0.078 0.523 0.196 0.972 0.352 1.347 0.156 0.367 0.344 0.672 0.562 0.914 0.227 0.235 0.485 0.406 0.774 0.516 0.297 0.109 0.621 0.164 0.972 0.164 0.446 0 0.84-0.086 1.184-0.258s0.633-0.445 0.867-0.82c0.235-0.383 0.41-0.879 0.528-1.488 0.117-0.61 0.175-1.34 0.175-2.192z" fill="#000" fill-opacity=".54"/>
  <path d="m798.29 614.63h0.352v2.308h-0.2c-1.007 0-1.879 0.157-2.613 0.469-0.726 0.313-1.324 0.742-1.793 1.289s-0.82 1.188-1.055 1.922c-0.226 0.727-0.339 1.5-0.339 2.32v2.684c0 0.68 0.074 1.281 0.222 1.805 0.149 0.515 0.356 0.949 0.621 1.3 0.274 0.344 0.586 0.606 0.938 0.786 0.351 0.179 0.73 0.269 1.137 0.269 0.421 0 0.804-0.086 1.148-0.258 0.344-0.179 0.637-0.425 0.879-0.738 0.242-0.312 0.426-0.684 0.551-1.113 0.125-0.43 0.187-0.899 0.187-1.406 0-0.485-0.062-0.938-0.187-1.36-0.117-0.43-0.293-0.805-0.528-1.125-0.234-0.328-0.527-0.582-0.879-0.762-0.343-0.187-0.742-0.281-1.195-0.281-0.562 0-1.074 0.133-1.535 0.399-0.453 0.265-0.82 0.613-1.102 1.043-0.273 0.421-0.421 0.871-0.445 1.347l-1.078-0.351c0.063-0.727 0.223-1.379 0.48-1.957 0.266-0.578 0.614-1.071 1.043-1.477 0.43-0.406 0.926-0.715 1.489-0.926 0.57-0.218 1.195-0.328 1.875-0.328 0.828 0 1.547 0.156 2.156 0.469 0.609 0.312 1.113 0.738 1.512 1.277 0.406 0.532 0.707 1.141 0.902 1.828 0.203 0.68 0.305 1.391 0.305 2.133 0 0.821-0.125 1.586-0.375 2.297-0.25 0.703-0.617 1.32-1.102 1.852-0.476 0.531-1.058 0.945-1.746 1.242-0.68 0.297-1.453 0.445-2.32 0.445-0.914 0-1.731-0.176-2.449-0.527-0.711-0.352-1.317-0.836-1.817-1.453-0.492-0.617-0.867-1.328-1.125-2.133s-0.387-1.656-0.387-2.555v-1.172c0-1.297 0.164-2.519 0.493-3.668 0.328-1.156 0.832-2.175 1.511-3.058 0.688-0.883 1.567-1.574 2.637-2.074 1.07-0.508 2.348-0.762 3.832-0.762zm16.481 7.207v2.789c0 1.336-0.133 2.476-0.399 3.422-0.258 0.937-0.633 1.699-1.125 2.285s-1.082 1.016-1.769 1.289c-0.68 0.273-1.442 0.41-2.286 0.41-0.671 0-1.296-0.086-1.875-0.258-0.57-0.172-1.086-0.441-1.546-0.808-0.461-0.367-0.856-0.84-1.184-1.418-0.32-0.586-0.57-1.285-0.75-2.098-0.172-0.812-0.258-1.754-0.258-2.824v-2.789c0-1.344 0.133-2.477 0.399-3.399 0.265-0.929 0.644-1.683 1.136-2.261 0.492-0.586 1.078-1.012 1.758-1.278 0.688-0.265 1.453-0.398 2.297-0.398 0.68 0 1.305 0.086 1.875 0.258 0.578 0.164 1.094 0.426 1.547 0.785 0.461 0.359 0.851 0.828 1.172 1.406 0.328 0.57 0.578 1.262 0.75 2.074 0.172 0.805 0.258 1.743 0.258 2.813zm-2.825 3.187v-3.609c0-0.68-0.039-1.277-0.117-1.793-0.078-0.523-0.195-0.965-0.351-1.324-0.149-0.367-0.336-0.664-0.563-0.891-0.226-0.234-0.484-0.402-0.773-0.504-0.289-0.109-0.614-0.164-0.973-0.164-0.438 0-0.828 0.086-1.172 0.258-0.344 0.164-0.633 0.43-0.867 0.797s-0.414 0.851-0.539 1.453c-0.117 0.594-0.176 1.316-0.176 2.168v3.609c0 0.688 0.039 1.293 0.117 1.817 0.078 0.523 0.196 0.972 0.352 1.347 0.156 0.368 0.344 0.672 0.562 0.914 0.227 0.235 0.485 0.407 0.774 0.516 0.297 0.109 0.621 0.164 0.972 0.164 0.446 0 0.84-0.086 1.184-0.258s0.633-0.445 0.867-0.82c0.235-0.383 0.41-0.879 0.528-1.488 0.117-0.61 0.175-1.34 0.175-2.192z" fill="#000" fill-opacity=".54"/>
  <path d="m792.72 713.57-2.25-0.55 0.925-8.578h9.2v2.39h-6.856l-0.469 4.184c0.266-0.157 0.633-0.309 1.102-0.457 0.469-0.157 1.004-0.235 1.605-0.235 0.805 0 1.524 0.133 2.157 0.399 0.64 0.258 1.183 0.636 1.629 1.136 0.445 0.493 0.785 1.094 1.019 1.805 0.235 0.703 0.352 1.496 0.352 2.379 0 0.789-0.117 1.527-0.352 2.215-0.226 0.687-0.57 1.293-1.031 1.816-0.461 0.524-1.043 0.934-1.746 1.231-0.696 0.289-1.52 0.433-2.473 0.433-0.711 0-1.394-0.101-2.051-0.304-0.648-0.211-1.23-0.52-1.746-0.926-0.515-0.414-0.929-0.922-1.242-1.524-0.312-0.609-0.492-1.308-0.539-2.097h2.766c0.07 0.555 0.222 1.027 0.457 1.418 0.242 0.383 0.562 0.676 0.961 0.879 0.398 0.203 0.859 0.304 1.382 0.304 0.477 0 0.887-0.082 1.231-0.246 0.344-0.172 0.629-0.414 0.855-0.726 0.235-0.321 0.407-0.696 0.516-1.125 0.117-0.43 0.176-0.907 0.176-1.43 0-0.5-0.067-0.957-0.199-1.371-0.125-0.414-0.317-0.774-0.575-1.078-0.25-0.305-0.566-0.539-0.949-0.703-0.383-0.172-0.824-0.258-1.324-0.258-0.672 0-1.188 0.098-1.547 0.293-0.352 0.195-0.68 0.437-0.984 0.726zm22.047-2.027v2.789c0 1.336-0.133 2.477-0.399 3.422-0.258 0.938-0.633 1.699-1.125 2.285s-1.082 1.016-1.769 1.289c-0.68 0.274-1.442 0.41-2.286 0.41-0.671 0-1.296-0.086-1.875-0.257-0.57-0.172-1.086-0.442-1.546-0.809-0.461-0.367-0.856-0.84-1.184-1.418-0.32-0.586-0.57-1.285-0.75-2.098-0.172-0.812-0.258-1.754-0.258-2.824v-2.789c0-1.344 0.133-2.477 0.399-3.398 0.265-0.93 0.644-1.684 1.136-2.262 0.492-0.586 1.078-1.012 1.758-1.277 0.688-0.266 1.453-0.399 2.297-0.399 0.68 0 1.305 0.086 1.875 0.258 0.578 0.164 1.094 0.426 1.547 0.785 0.461 0.359 0.851 0.828 1.172 1.406 0.328 0.571 0.578 1.262 0.75 2.075 0.172 0.804 0.258 1.742 0.258 2.812zm-2.825 3.188v-3.61c0-0.679-0.039-1.277-0.117-1.793-0.078-0.523-0.195-0.965-0.351-1.324-0.149-0.367-0.336-0.664-0.563-0.891-0.226-0.234-0.484-0.402-0.773-0.504-0.289-0.109-0.614-0.164-0.973-0.164-0.438 0-0.828 0.086-1.172 0.258-0.344 0.164-0.633 0.43-0.867 0.797s-0.414 0.852-0.539 1.453c-0.117 0.594-0.176 1.317-0.176 2.168v3.61c0 0.687 0.039 1.293 0.117 1.816s0.196 0.973 0.352 1.348c0.156 0.367 0.344 0.671 0.562 0.914 0.227 0.234 0.485 0.406 0.774 0.515 0.297 0.11 0.621 0.164 0.972 0.164 0.446 0 0.84-0.086 1.184-0.257 0.344-0.172 0.633-0.446 0.867-0.821 0.235-0.383 0.41-0.879 0.528-1.488 0.117-0.609 0.175-1.34 0.175-2.191z" fill="#000" fill-opacity=".54"/>
  <path d="m801.51 805.12v2.25h-12.305l-0.094-1.699 7.36-11.531h2.261l-2.449 4.195-4.23 6.785zm-2.133-10.98v17.062h-2.824v-17.062zm15.391 7.101v2.789c0 1.336-0.133 2.477-0.399 3.422-0.258 0.938-0.633 1.699-1.125 2.285s-1.082 1.016-1.769 1.289c-0.68 0.274-1.442 0.411-2.286 0.411-0.671 0-1.296-0.086-1.875-0.258-0.57-0.172-1.086-0.442-1.546-0.809-0.461-0.367-0.856-0.84-1.184-1.418-0.32-0.586-0.57-1.285-0.75-2.097-0.172-0.813-0.258-1.754-0.258-2.825v-2.789c0-1.343 0.133-2.476 0.399-3.398 0.265-0.93 0.644-1.684 1.136-2.262 0.492-0.586 1.078-1.012 1.758-1.277 0.688-0.266 1.453-0.399 2.297-0.399 0.68 0 1.305 0.086 1.875 0.258 0.578 0.164 1.094 0.426 1.547 0.785 0.461 0.36 0.851 0.828 1.172 1.407 0.328 0.57 0.578 1.261 0.75 2.074 0.172 0.804 0.258 1.742 0.258 2.812zm-2.825 3.188v-3.61c0-0.679-0.039-1.277-0.117-1.793-0.078-0.523-0.195-0.964-0.351-1.324-0.149-0.367-0.336-0.664-0.563-0.89-0.226-0.235-0.484-0.403-0.773-0.504-0.289-0.11-0.614-0.164-0.973-0.164-0.438 0-0.828 0.086-1.172 0.257-0.344 0.164-0.633 0.43-0.867 0.797s-0.414 0.852-0.539 1.453c-0.117 0.594-0.176 1.317-0.176 2.168v3.61c0 0.687 0.039 1.293 0.117 1.816 0.078 0.524 0.196 0.973 0.352 1.348 0.156 0.367 0.344 0.672 0.562 0.914 0.227 0.234 0.485 0.406 0.774 0.516 0.297 0.109 0.621 0.164 0.972 0.164 0.446 0 0.84-0.086 1.184-0.258s0.633-0.446 0.867-0.821c0.235-0.382 0.41-0.878 0.528-1.488 0.117-0.609 0.175-1.34 0.175-2.191z" fill="#000" fill-opacity=".54"/>
  <path d="m793.07 891.09h1.688c0.656 0 1.199-0.113 1.629-0.34 0.437-0.227 0.761-0.539 0.972-0.938 0.211-0.398 0.317-0.855 0.317-1.371 0-0.539-0.098-1-0.293-1.382-0.188-0.391-0.477-0.692-0.867-0.903-0.383-0.211-0.872-0.316-1.465-0.316-0.5 0-0.953 0.101-1.36 0.305-0.398 0.195-0.715 0.476-0.949 0.843-0.234 0.36-0.351 0.789-0.351 1.289h-2.836c0-0.906 0.238-1.711 0.714-2.414 0.477-0.703 1.125-1.254 1.946-1.652 0.828-0.406 1.758-0.609 2.789-0.609 1.101 0 2.062 0.183 2.883 0.55 0.828 0.36 1.472 0.899 1.933 1.618 0.461 0.718 0.692 1.609 0.692 2.671 0 0.485-0.114 0.977-0.34 1.477-0.227 0.5-0.563 0.957-1.008 1.371-0.445 0.406-1 0.738-1.664 0.996-0.664 0.25-1.434 0.375-2.309 0.375h-2.121zm0 2.203v-1.547h2.121c1 0 1.852 0.117 2.555 0.352 0.711 0.234 1.289 0.558 1.734 0.972 0.446 0.407 0.77 0.871 0.973 1.395 0.211 0.523 0.316 1.078 0.316 1.664 0 0.797-0.144 1.508-0.433 2.133-0.281 0.617-0.684 1.14-1.207 1.57-0.524 0.43-1.137 0.754-1.84 0.973-0.695 0.218-1.453 0.328-2.273 0.328-0.735 0-1.438-0.102-2.11-0.305s-1.273-0.504-1.805-0.902c-0.531-0.406-0.953-0.91-1.265-1.512-0.305-0.609-0.457-1.312-0.457-2.109h2.824c0 0.507 0.117 0.957 0.352 1.347 0.242 0.383 0.578 0.684 1.007 0.903 0.438 0.218 0.938 0.328 1.5 0.328 0.594 0 1.106-0.106 1.536-0.317 0.429-0.211 0.757-0.523 0.984-0.937 0.234-0.414 0.352-0.914 0.352-1.5 0-0.664-0.129-1.203-0.387-1.617s-0.625-0.719-1.102-0.914c-0.476-0.203-1.039-0.305-1.687-0.305zm21.696-2.355v2.789c0 1.335-0.133 2.476-0.399 3.421-0.258 0.938-0.633 1.7-1.125 2.286s-1.082 1.015-1.769 1.289c-0.68 0.273-1.442 0.41-2.286 0.41-0.671 0-1.296-0.086-1.875-0.258-0.57-0.172-1.086-0.441-1.546-0.809-0.461-0.367-0.856-0.839-1.184-1.418-0.32-0.586-0.57-1.285-0.75-2.097-0.172-0.813-0.258-1.754-0.258-2.824v-2.789c0-1.344 0.133-2.477 0.399-3.399 0.265-0.93 0.644-1.684 1.136-2.262 0.492-0.586 1.078-1.011 1.758-1.277 0.688-0.266 1.453-0.398 2.297-0.398 0.68 0 1.305 0.086 1.875 0.257 0.578 0.164 1.094 0.426 1.547 0.786 0.461 0.359 0.851 0.828 1.172 1.406 0.328 0.57 0.578 1.261 0.75 2.074 0.172 0.805 0.258 1.742 0.258 2.813zm-2.825 3.187v-3.609c0-0.68-0.039-1.278-0.117-1.793-0.078-0.524-0.195-0.965-0.351-1.325-0.149-0.367-0.336-0.664-0.563-0.89-0.226-0.235-0.484-0.403-0.773-0.504-0.289-0.109-0.614-0.164-0.973-0.164-0.438 0-0.828 0.086-1.172 0.258-0.344 0.164-0.633 0.429-0.867 0.797-0.234 0.367-0.414 0.851-0.539 1.453-0.117 0.593-0.176 1.316-0.176 2.168v3.609c0 0.687 0.039 1.293 0.117 1.816 0.078 0.524 0.196 0.973 0.352 1.348 0.156 0.367 0.344 0.672 0.562 0.914 0.227 0.235 0.485 0.406 0.774 0.516 0.297 0.109 0.621 0.164 0.972 0.164 0.446 0 0.84-0.086 1.184-0.258s0.633-0.445 0.867-0.82c0.235-0.383 0.41-0.879 0.528-1.489 0.117-0.609 0.175-1.339 0.175-2.191z" fill="#000" fill-opacity=".54"/>
  <path d="m801.2 988.35v2.25h-11.437v-1.934l5.554-6.059c0.61-0.687 1.09-1.281 1.442-1.781 0.351-0.5 0.598-0.949 0.738-1.348 0.149-0.406 0.223-0.8 0.223-1.183 0-0.539-0.102-1.012-0.305-1.418-0.195-0.414-0.484-0.738-0.867-0.973-0.383-0.242-0.848-0.363-1.395-0.363-0.632 0-1.164 0.137-1.593 0.41-0.43 0.274-0.754 0.652-0.973 1.137-0.219 0.476-0.328 1.023-0.328 1.64h-2.824c0-0.992 0.226-1.898 0.679-2.718 0.453-0.828 1.11-1.485 1.969-1.969 0.86-0.492 1.895-0.738 3.106-0.738 1.14 0 2.109 0.191 2.906 0.574s1.402 0.926 1.816 1.629c0.422 0.703 0.633 1.535 0.633 2.496 0 0.531-0.086 1.058-0.258 1.582-0.172 0.523-0.418 1.047-0.738 1.57-0.313 0.516-0.684 1.035-1.113 1.559-0.43 0.515-0.903 1.039-1.418 1.57l-3.692 4.067zm13.575-7.711v2.789c0 1.336-0.133 2.476-0.399 3.422-0.258 0.937-0.633 1.699-1.125 2.285s-1.082 1.015-1.769 1.289c-0.68 0.273-1.442 0.41-2.286 0.41-0.671 0-1.296-0.086-1.875-0.258-0.57-0.172-1.086-0.441-1.546-0.808-0.461-0.368-0.856-0.84-1.184-1.418-0.32-0.586-0.57-1.286-0.75-2.098-0.172-0.813-0.258-1.754-0.258-2.824v-2.789c0-1.344 0.133-2.477 0.399-3.399 0.265-0.93 0.644-1.683 1.136-2.262 0.492-0.585 1.078-1.011 1.758-1.277 0.688-0.265 1.453-0.398 2.297-0.398 0.68 0 1.305 0.086 1.875 0.257 0.578 0.165 1.094 0.426 1.547 0.786 0.461 0.359 0.851 0.828 1.172 1.406 0.328 0.57 0.578 1.262 0.75 2.074 0.172 0.805 0.258 1.742 0.258 2.813zm-2.825 3.187v-3.609c0-0.68-0.039-1.278-0.117-1.793-0.078-0.524-0.195-0.965-0.351-1.324-0.149-0.368-0.336-0.664-0.563-0.891-0.226-0.234-0.484-0.402-0.773-0.504-0.289-0.109-0.614-0.164-0.973-0.164-0.438 0-0.828 0.086-1.172 0.258-0.344 0.164-0.633 0.429-0.867 0.797-0.234 0.367-0.414 0.851-0.539 1.453-0.117 0.594-0.176 1.316-0.176 2.168v3.609c0 0.688 0.039 1.293 0.117 1.817 0.078 0.523 0.196 0.972 0.352 1.347 0.156 0.367 0.344 0.672 0.562 0.914 0.227 0.235 0.485 0.406 0.774 0.516 0.297 0.109 0.621 0.164 0.972 0.164 0.446 0 0.84-0.086 1.184-0.258s0.633-0.445 0.867-0.82c0.235-0.383 0.41-0.879 0.528-1.488 0.117-0.61 0.175-1.34 0.175-2.192z" fill="#000" fill-opacity=".54"/>
  <path d="m797.47 1063.2v17.12h-2.824v-13.77l-4.184 1.42v-2.33l6.668-2.44zm17.301 7.16v2.79c0 1.33-0.133 2.48-0.399 3.42-0.258 0.94-0.633 1.7-1.125 2.29-0.492 0.58-1.082 1.01-1.769 1.28-0.68 0.28-1.442 0.42-2.286 0.42-0.671 0-1.296-0.09-1.875-0.26-0.57-0.17-1.086-0.44-1.546-0.81-0.461-0.37-0.856-0.84-1.184-1.42-0.32-0.59-0.57-1.28-0.75-2.1-0.172-0.81-0.258-1.75-0.258-2.82v-2.79c0-1.34 0.133-2.48 0.399-3.4 0.265-0.93 0.644-1.68 1.136-2.26 0.492-0.59 1.078-1.01 1.758-1.28 0.688-0.26 1.453-0.4 2.297-0.4 0.68 0 1.305 0.09 1.875 0.26 0.578 0.17 1.094 0.43 1.547 0.79 0.461 0.36 0.851 0.83 1.172 1.4 0.328 0.57 0.578 1.26 0.75 2.08 0.172 0.8 0.258 1.74 0.258 2.81zm-2.825 3.19v-3.61c0-0.68-0.039-1.28-0.117-1.8s-0.195-0.96-0.351-1.32c-0.149-0.37-0.336-0.66-0.563-0.89-0.226-0.23-0.484-0.4-0.773-0.5-0.289-0.11-0.614-0.17-0.973-0.17-0.438 0-0.828 0.09-1.172 0.26-0.344 0.16-0.633 0.43-0.867 0.8-0.234 0.36-0.414 0.85-0.539 1.45-0.117 0.59-0.176 1.32-0.176 2.17v3.61c0 0.68 0.039 1.29 0.117 1.81 0.078 0.53 0.196 0.98 0.352 1.35s0.344 0.67 0.562 0.92c0.227 0.23 0.485 0.4 0.774 0.51 0.297 0.11 0.621 0.17 0.972 0.17 0.446 0 0.84-0.09 1.184-0.26s0.633-0.45 0.867-0.82c0.235-0.39 0.41-0.88 0.528-1.49 0.117-0.61 0.175-1.34 0.175-2.19z" fill="#000" fill-opacity=".54"/>
  <path d="m814.77 1160v2.79c0 1.33-0.133 2.47-0.399 3.42-0.258 0.94-0.633 1.7-1.125 2.29-0.492 0.58-1.082 1.01-1.769 1.28-0.68 0.28-1.442 0.41-2.286 0.41-0.671 0-1.296-0.08-1.875-0.25-0.57-0.18-1.086-0.44-1.546-0.81-0.461-0.37-0.856-0.84-1.184-1.42-0.32-0.59-0.57-1.29-0.75-2.1-0.172-0.81-0.258-1.75-0.258-2.82v-2.79c0-1.34 0.133-2.48 0.399-3.4 0.265-0.93 0.644-1.68 1.136-2.26 0.492-0.59 1.078-1.01 1.758-1.28 0.688-0.26 1.453-0.4 2.297-0.4 0.68 0 1.305 0.09 1.875 0.26 0.578 0.16 1.094 0.43 1.547 0.79 0.461 0.36 0.851 0.82 1.172 1.4 0.328 0.57 0.578 1.26 0.75 2.08 0.172 0.8 0.258 1.74 0.258 2.81zm-2.825 3.19v-3.61c0-0.68-0.039-1.28-0.117-1.8s-0.195-0.96-0.351-1.32c-0.149-0.37-0.336-0.66-0.563-0.89-0.226-0.23-0.484-0.4-0.773-0.5-0.289-0.11-0.614-0.17-0.973-0.17-0.438 0-0.828 0.09-1.172 0.26-0.344 0.16-0.633 0.43-0.867 0.8-0.234 0.36-0.414 0.85-0.539 1.45-0.117 0.59-0.176 1.32-0.176 2.17v3.61c0 0.68 0.039 1.29 0.117 1.81 0.078 0.53 0.196 0.98 0.352 1.35s0.344 0.67 0.562 0.91c0.227 0.24 0.485 0.41 0.774 0.52 0.297 0.11 0.621 0.16 0.972 0.16 0.446 0 0.84-0.08 1.184-0.25 0.344-0.18 0.633-0.45 0.867-0.82 0.235-0.39 0.41-0.88 0.528-1.49 0.117-0.61 0.175-1.34 0.175-2.19z" fill="#000" fill-opacity=".54"/>
 </g><g tb:tag="top-layer">
  <path d="m100 214c0-6.627 5.373-12 12-12h1176c6.63 0 12 5.373 12 12v994.75c0 5.65-3.62 10.48-9.16 11.59-255.85 51.35-983.13 40.77-1181.9 0.03-5.481-1.12-8.959-5.9-8.959-11.5z" fill="#e5e5e5" tb:tag="background"/>
  <path d="m100 214c0-6.627 5.373-12 12-12h1176c6.63 0 12 5.373 12 12v994.75c0 5.65-3.62 10.48-9.16 11.59-255.85 51.35-983.13 40.77-1181.9 0.03-5.481-1.12-8.959-5.9-8.959-11.5v-994.87z" fill="url(#paint1_linear_2188_188555)"/>
  <path d="m101.5 214c0-5.799 4.701-10.5 10.5-10.5h1176c5.8 0 10.5 4.701 10.5 10.5v994.75c0 5-3.18 9.16-7.95 10.12-127.76 25.64-373.4 35.84-612.05 34.52-238.66-1.32-470.03-14.15-569.24-34.49l-0.301 1.47 0.301-1.47c-4.703-0.96-7.76-5.06-7.76-10.03v-994.87z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 </g><path d="m3.0002 1143.6-1.7e-4 -141.73c16.904 3.91 46.87 7.48 86.628 10.71 43.009 3.48 97.728 6.56 160.31 9.17 125.17 5.22 281.88 8.54 439.46 9.36 157.58 0.81 316.06-0.88 444.77-5.69 64.35-2.41 121.29-5.59 166.96-9.64 42.72-3.78 75.8-8.33 95.87-13.77v141.48c0 4.44-2.78 7.94-6.85 8.68-141.99 25.81-431.84 36.08-714.04 34.75-282.22-1.34-556.2-14.27-666.45-34.72-3.9871-0.74-6.6571-4.16-6.6571-8.6z" stroke="#647484" stroke-width="6"/><path d="m1023 1030v145" stroke="#647484" stroke-width="6"/><path d="m701 1030v150" stroke="#647484" stroke-width="6"/><path d="m379 1023v151" stroke="#647484" stroke-width="6"/><path d="m0 1147c125.45 33.35 1246.3 46.29 1400 0v61.71c0 5.74-3.68 10.62-9.33 11.64-284.6 51.35-1160.4 40.74-1381.5 0.03-5.601-1.04-9.1261-5.87-9.1261-11.56l-1.5804e-4 -61.82z" fill="#E5E5E5" tb:tag="background"/><path d="m0 1147c125.45 33.35 1246.3 46.29 1400 0v61.71c0 5.74-3.68 10.62-9.33 11.64-284.6 51.35-1160.4 40.74-1381.5 0.03-5.601-1.04-9.1261-5.87-9.1261-11.56l-1.5804e-4 -61.82z" fill="url(#paint301_linear_2188_188555)"/><path d="m1.5002 1208.8-1.6e-4 -59.89c16.364 4.02 46.959 7.72 88.25 11.04 42.982 3.45 97.679 6.52 160.25 9.11 125.15 5.18 281.84 8.47 439.41 9.28s316.02-0.87 444.71-5.65c64.33-2.38 121.24-5.55 166.88-9.56 44.11-3.88 77.81-8.56 97.5-14.15v59.71c0 5.09-3.23 9.28-8.1 10.16-142.14 25.65-432.11 35.84-714.3 34.52s-556.29-14.16-666.71-34.49c-4.797-0.88-7.8977-5.01-7.8977-10.08z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><path d="m700 0 597.89 200.16c4 1.341 3.46 7.138-0.73 7.694-189.06 25.141-364.28 25.141-597.16 25.141-230.8 0-478.09 0-600.31-24.472-3.9498-0.791-4.1701-6.156-0.3503-7.435l600.66-201.09z" fill="#E5E5E5" tb:tag="background"/><path d="m700 0 597.89 200.16c4 1.341 3.46 7.138-0.73 7.694-189.06 25.141-364.28 25.141-597.16 25.141-230.8 0-478.09 0-600.31-24.472-3.9498-0.791-4.1701-6.156-0.3503-7.435l600.66-201.09z" fill="url(#paint302_linear_2188_188555)"/><path d="m99.815 202.52 600.18-200.93 597.41 200.01c2.51 0.84 2.15 4.439-0.44 4.785-188.96 25.125-364.07 25.127-596.97 25.127-115.41 0-234.9 0-340.83-3.058-105.95-3.059-198.21-9.177-259.19-21.385-2.3046-0.462-2.6651-3.706-0.1686-4.541z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><g filter="url(#filter0_ii_2188_188555)" tb:tag="value-box">
  <path d="m580 634c0-6.627 5.373-12 12-12h216c6.627 0 12 5.373 12 12v56c0 6.627-5.373 12-12 12h-216c-6.627 0-12-5.373-12-12v-56z" fill="#fff" tb:tag="value-box-background"/>
  <path d="m581.5 634c0-5.799 4.701-10.5 10.5-10.5h216c5.799 0 10.5 4.701 10.5 10.5v56c0 5.799-4.701 10.5-10.5 10.5h-216c-5.799 0-10.5-4.701-10.5-10.5v-56z" stroke="#727171" stroke-width="3"/>
  <text x="705.52533" y="666.35553" fill="#727171" font-family="Roboto, sans-serif" font-size="32px" font-weight="500" tb:tag="value-text" xml:space="preserve" text-anchor="middle"><tspan dominant-baseline="middle">1660 gal</tspan></text>
 </g><g fill="#D9D9D9" filter="url(#filter1_d_2188_188555)" stroke="#727171" stroke-width="3">
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 414.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 368.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 322.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 276.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 460.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 506.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 552.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 598.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 644.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 690.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 736.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 782.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 828.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 874.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(0 -1 .99255 -.12187 1087.2 920.35)" x="1.4888" y="-1.6828" width="11" height="77" rx="5.5"/>
  <rect transform="matrix(.99255 -.12187 0 1 1084 173.93)" x="1.4888" y="1.3172" width="11" height="788" rx="5.5"/>
  <rect transform="matrix(.99255 -.12187 0 1 1149.5 165.89)" x="1.4888" y="1.3172" width="11" height="788" rx="5.5"/>
 </g><path d="m226.93 1953.6 38.257-729.99-49.931-2.62-38.258 730 49.932 2.61z" fill="#fff" tb:tag="background"/><path d="m226.93 1953.6 38.257-729.99-49.931-2.62-38.258 730 49.932 2.61z" fill="url(#paint303_linear_2188_188555)"/><path d="m225.51 1952 38.101-727.01-46.936-2.46-38.1 727.01 46.935 2.46z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><mask id="path-338-inside-1_2188_188555" fill="white">
  <rect transform="rotate(261.95 289.62 1553)" x="289.62" y="1553" width="297.2" height="12" rx="2"/>
 </mask><rect transform="rotate(261.95 289.62 1553)" x="289.62" y="1553" width="297.2" height="12" rx="2" fill="#fff"/><rect transform="rotate(261.95 289.62 1553)" x="289.62" y="1553" width="297.2" height="12" rx="2" fill="url(#paint304_linear_2188_188555)"/><rect transform="rotate(261.95 289.62 1553)" x="289.62" y="1553" width="297.2" height="12" rx="2" mask="url(#path-338-inside-1_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><mask id="path-339-inside-2_2188_188555" fill="white">
  <rect transform="rotate(262.42 274.16 1866.6)" x="274.16" y="1866.6" width="313.46" height="12" rx="2"/>
 </mask><rect transform="rotate(262.42 274.16 1866.6)" x="274.16" y="1866.6" width="313.46" height="12" rx="2" fill="#fff"/><rect transform="rotate(262.42 274.16 1866.6)" x="274.16" y="1866.6" width="313.46" height="12" rx="2" fill="url(#paint305_linear_2188_188555)"/><rect transform="rotate(262.42 274.16 1866.6)" x="274.16" y="1866.6" width="313.46" height="12" rx="2" mask="url(#path-339-inside-2_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><mask id="path-340-inside-3_2188_188555" fill="white">
  <rect transform="matrix(.86976 .49348 .49348 -.86976 234 1555.4)" width="60.504" height="12" rx="2"/>
 </mask><rect transform="matrix(.86976 .49348 .49348 -.86976 234 1555.4)" width="60.504" height="12" rx="2" fill="#fff"/><rect transform="matrix(.86976 .49348 .49348 -.86976 234 1555.4)" width="60.504" height="12" rx="2" fill="url(#paint306_linear_2188_188555)"/><rect transform="matrix(.86976 .49348 .49348 -.86976 234 1555.4)" width="60.504" height="12" rx="2" mask="url(#path-340-inside-3_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><mask id="path-341-inside-4_2188_188555" fill="white">
  <rect transform="matrix(.22875 -.97349 -.97349 -.22875 245.68 1553.1)" width="297.2" height="12" rx="2"/>
 </mask><rect transform="matrix(.22875 -.97349 -.97349 -.22875 245.68 1553.1)" width="297.2" height="12" rx="2" fill="#fff"/><rect transform="matrix(.22875 -.97349 -.97349 -.22875 245.68 1553.1)" width="297.2" height="12" rx="2" fill="url(#paint307_linear_2188_188555)"/><rect transform="matrix(.22875 -.97349 -.97349 -.22875 245.68 1553.1)" width="297.2" height="12" rx="2" mask="url(#path-341-inside-4_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><mask id="path-342-inside-5_2188_188555" fill="white">
  <rect transform="matrix(.22875 -.97349 -.97349 -.22875 233.68 1853.1)" width="297.2" height="12" rx="2"/>
 </mask><rect transform="matrix(.22875 -.97349 -.97349 -.22875 233.68 1853.1)" width="297.2" height="12" rx="2" fill="#fff"/><rect transform="matrix(.22875 -.97349 -.97349 -.22875 233.68 1853.1)" width="297.2" height="12" rx="2" fill="url(#paint307_linear_2188_188555)"/><rect transform="matrix(.22875 -.97349 -.97349 -.22875 233.68 1853.1)" width="297.2" height="12" rx="2" mask="url(#path-342-inside-5_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><path d="m314.93 2e3 39.848-760.35-49.931-2.62-39.849 760.35 49.932 2.62z" fill="#fff" tb:tag="background"/><path d="m314.93 2e3 39.848-760.35-49.931-2.62-39.849 760.35 49.932 2.62z" fill="url(#paint309_linear_2188_188555)"/><path d="m313.51 1998.4 39.692-757.36-46.936-2.46-39.691 757.36 46.935 2.46z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><path d="m1173.3 1956.6-38.26-729.99 49.93-2.62 38.26 730-49.93 2.61z" fill="#fff" tb:tag="background"/><path d="m1173.3 1956.6-38.26-729.99 49.93-2.62 38.26 730-49.93 2.61z" fill="url(#paint310_linear_2188_188555)"/><path d="m1174.7 1955-38.1-727.01 46.93-2.46 38.1 727.01-46.93 2.46z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><mask id="path-347-inside-6_2188_188555" fill="white">
  <rect transform="rotate(255.92 1156.3 1552.6)" x="1156.3" y="1552.6" width="297.2" height="12" rx="2"/>
 </mask><rect transform="rotate(255.92 1156.3 1552.6)" x="1156.3" y="1552.6" width="297.2" height="12" rx="2" fill="#fff"/><rect transform="rotate(255.92 1156.3 1552.6)" x="1156.3" y="1552.6" width="297.2" height="12" rx="2" fill="url(#paint311_linear_2188_188555)"/><rect transform="rotate(255.92 1156.3 1552.6)" x="1156.3" y="1552.6" width="297.2" height="12" rx="2" mask="url(#path-347-inside-6_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><mask id="path-348-inside-7_2188_188555" fill="white">
  <rect transform="rotate(257.32 1168.7 1861)" x="1168.7" y="1861" width="297.2" height="12" rx="2"/>
 </mask><rect transform="rotate(257.32 1168.7 1861)" x="1168.7" y="1861" width="297.2" height="12" rx="2" fill="#fff"/><rect transform="rotate(257.32 1168.7 1861)" x="1168.7" y="1861" width="297.2" height="12" rx="2" fill="url(#paint312_linear_2188_188555)"/><rect transform="rotate(257.32 1168.7 1861)" x="1168.7" y="1861" width="297.2" height="12" rx="2" mask="url(#path-348-inside-7_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><mask id="path-349-inside-8_2188_188555" fill="white">
  <rect transform="rotate(150.43 1164.5 1547.4)" x="1164.5" y="1547.4" width="60.504" height="12" rx="2"/>
 </mask><rect transform="rotate(150.43 1164.5 1547.4)" x="1164.5" y="1547.4" width="60.504" height="12" rx="2" fill="#fff"/><rect transform="rotate(150.43 1164.5 1547.4)" x="1164.5" y="1547.4" width="60.504" height="12" rx="2" fill="url(#paint313_linear_2188_188555)"/><rect transform="rotate(150.43 1164.5 1547.4)" x="1164.5" y="1547.4" width="60.504" height="12" rx="2" mask="url(#path-349-inside-8_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><mask id="path-350-inside-9_2188_188555" fill="white">
  <rect transform="matrix(.1253 -.99212 -.99212 -.1253 1112.6 1557.4)" width="297.2" height="12" rx="2"/>
 </mask><rect transform="matrix(.1253 -.99212 -.99212 -.1253 1112.6 1557.4)" width="297.2" height="12" rx="2" fill="#fff"/><rect transform="matrix(.1253 -.99212 -.99212 -.1253 1112.6 1557.4)" width="297.2" height="12" rx="2" fill="url(#paint307_linear_2188_188555)"/><rect transform="matrix(.1253 -.99212 -.99212 -.1253 1112.6 1557.4)" width="297.2" height="12" rx="2" mask="url(#path-350-inside-9_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><mask id="path-351-inside-10_2188_188555" fill="white">
  <rect transform="matrix(.10794 -.99416 -.99416 -.10794 1130 1886.2)" width="337.15" height="12" rx="2"/>
 </mask><rect transform="matrix(.10794 -.99416 -.99416 -.10794 1130 1886.2)" width="337.15" height="12" rx="2" fill="#fff"/><rect transform="matrix(.10794 -.99416 -.99416 -.10794 1130 1886.2)" width="337.15" height="12" rx="2" fill="url(#paint315_linear_2188_188555)"/><rect transform="matrix(.10794 -.99416 -.99416 -.10794 1130 1886.2)" width="337.15" height="12" rx="2" mask="url(#path-351-inside-10_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><path d="m1084.8 2e3 -39.85-760.35 49.93-2.62 39.85 760.35-49.93 2.62z" fill="#fff" tb:tag="background"/><path d="m1084.8 2e3 -39.85-760.35 49.93-2.62 39.85 760.35-49.93 2.62z" fill="url(#paint316_linear_2188_188555)"/><path d="m1086.3 1998.4-39.69-757.36 46.93-2.46 39.69 757.36-46.93 2.46z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><path d="m664 1986v-731h72v731h-72z" fill="#fff" tb:tag="background"/><path d="m664 1986v-731h72v731h-72z" fill="url(#paint317_linear_2188_188555)"/><path d="m665.5 1984.5v-728h69v728h-69z" stroke="#000" stroke-opacity=".12" stroke-width="3"/><mask id="path-356-inside-11_2188_188555" fill="white">
  <rect x="335" y="1592" width="733" height="12" rx="2"/>
 </mask><rect x="335" y="1592" width="733" height="12" rx="2" fill="#fff"/><rect x="335" y="1592" width="733" height="12" rx="2" fill="url(#paint318_linear_2188_188555)"/><rect x="335" y="1592" width="733" height="12" rx="2" mask="url(#path-356-inside-11_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><rect transform="rotate(-90 651.5 1998.5)" x="651.5" y="1998.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/><mask id="path-358-inside-12_2188_188555" fill="white">
  <rect transform="rotate(25 349.5 1256.9)" x="349.5" y="1256.9" width="798.44" height="12" rx="2"/>
 </mask><rect transform="rotate(25 349.5 1256.9)" x="349.5" y="1256.9" width="798.44" height="12" rx="2" fill="#fff"/><rect transform="rotate(25 349.5 1256.9)" x="349.5" y="1256.9" width="798.44" height="12" rx="2" fill="url(#paint319_linear_2188_188555)"/><rect transform="rotate(25 349.5 1256.9)" x="349.5" y="1256.9" width="798.44" height="12" rx="2" mask="url(#path-358-inside-12_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><mask id="path-359-inside-13_2188_188555" fill="white">
  <rect transform="rotate(25 332.84 1596.6)" x="332.84" y="1596.6" width="834.84" height="12" rx="2"/>
 </mask><rect transform="rotate(25 332.84 1596.6)" x="332.84" y="1596.6" width="834.84" height="12" rx="2" fill="#fff"/><rect transform="rotate(25 332.84 1596.6)" x="332.84" y="1596.6" width="834.84" height="12" rx="2" fill="url(#paint320_linear_2188_188555)"/><rect transform="rotate(25 332.84 1596.6)" x="332.84" y="1596.6" width="834.84" height="12" rx="2" mask="url(#path-359-inside-13_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><mask id="path-360-inside-14_2188_188555" fill="white">
  <rect transform="rotate(-25 324.93 1592.7)" x="324.93" y="1592.7" width="802.62" height="12" rx="2"/>
 </mask><rect transform="rotate(-25 324.93 1592.7)" x="324.93" y="1592.7" width="802.62" height="12" rx="2" fill="#fff"/><rect transform="rotate(-25 324.93 1592.7)" x="324.93" y="1592.7" width="802.62" height="12" rx="2" fill="url(#paint321_linear_2188_188555)"/><rect transform="rotate(-25 324.93 1592.7)" x="324.93" y="1592.7" width="802.62" height="12" rx="2" mask="url(#path-360-inside-14_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><mask id="path-361-inside-15_2188_188555" fill="white">
  <rect transform="rotate(-25 310.93 1948.7)" x="310.93" y="1948.7" width="836.46" height="12" rx="2"/>
 </mask><rect transform="rotate(-25 310.93 1948.7)" x="310.93" y="1948.7" width="836.46" height="12" rx="2" fill="#fff"/><rect transform="rotate(-25 310.93 1948.7)" x="310.93" y="1948.7" width="836.46" height="12" rx="2" fill="url(#paint322_linear_2188_188555)"/><rect transform="rotate(-25 310.93 1948.7)" x="310.93" y="1948.7" width="836.46" height="12" rx="2" mask="url(#path-361-inside-15_2188_188555)" stroke="#000" stroke-opacity=".12" stroke-width="6"/><path d="m470.84 0s-470.84 0-470.84 335v1641.8c0 13.256 12.536 23.202 28 23.202h1344c15.463 0 28-9.946 28-23.202v-1641.8c0-335-462.49-335-462.49-335h-237.51zm469.49 406c-9.0207 0-16.333 6.268-16.333 14v1502c0 7.732 7.3127 14 16.333 14h102.67c9.0207 0 16.333-6.268 16.333-14v-1502c0-7.732-7.3126-14-16.333-14z" fill="#000" fill-opacity="0" tb:tag="clickArea"/><defs>
  <filter id="filter0_ii_2188_188555" x="576" y="618" width="248" height="88" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
   <feFlood flood-opacity="0" result="BackgroundImageFix"/>
   <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="4" dy="4"/>
   <feGaussianBlur stdDeviation="3"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
   <feBlend in2="shape" result="effect1_innerShadow_2188_188555"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="-4" dy="-4"/>
   <feGaussianBlur stdDeviation="2"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
   <feBlend in2="effect1_innerShadow_2188_188555" result="effect2_innerShadow_2188_188555"/>
  </filter>
  <filter id="filter1_d_2188_188555" x="1064" y="164.8" width="99.404" height="819.15" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
   <feFlood flood-opacity="0" result="BackgroundImageFix"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="-16" dy="16"/>
   <feGaussianBlur stdDeviation="2"/>
   <feComposite in2="hardAlpha" operator="out"/>
   <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
   <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_2188_188555"/>
   <feBlend in="SourceGraphic" in2="effect1_dropShadow_2188_188555" result="shape"/>
  </filter>
  <linearGradient id="paint0_linear_2188_188555" x1="1364.9" x2="35.761" y1="1611.2" y2="1642.7" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint1_linear_2188_188555" x1="1300" x2="100.4" y1="723.86" y2="699.51" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint301_linear_2188_188555" x1="1364.9" x2="35.694" y1="726.31" y2="696.27" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint302_linear_2188_188555" x1="1314" x2="99.646" y1="101.87" y2="-27.334" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint303_linear_2188_188555" x1="236.88" x2="186.94" y1="1763.8" y2="1761.2" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint304_linear_2188_188555" x1="366.9" x2="366.9" y1="1553" y2="1565" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint305_linear_2188_188555" x1="355.66" x2="355.66" y1="1866.6" y2="1878.6" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint306_linear_2188_188555" x1="15.731" x2="15.716" y1="-2.0208e-10" y2="12.001" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint307_linear_2188_188555" x1="77.273" x2="77.27" y1="-2.0208e-10" y2="12.001" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint309_linear_2188_188555" x1="325.29" x2="275.36" y1="1802.3" y2="1799.7" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint310_linear_2188_188555" x1="1163.3" x2="1213.2" y1="1766.8" y2="1764.2" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint311_linear_2188_188555" x1="1233.6" x2="1233.6" y1="1552.6" y2="1564.6" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint312_linear_2188_188555" x1="1246" x2="1246" y1="1861" y2="1873" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint313_linear_2188_188555" x1="1180.3" x2="1180.3" y1="1547.4" y2="1559.4" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint315_linear_2188_188555" x1="87.658" x2="87.656" y1="-2.0208e-10" y2="12.001" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint316_linear_2188_188555" x1="1074.5" x2="1124.4" y1="1802.3" y2="1799.7" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint317_linear_2188_188555" x1="664" x2="736.01" y1="1795.9" y2="1796" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint318_linear_2188_188555" x1="525.58" x2="525.58" y1="1592" y2="1604" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint319_linear_2188_188555" x1="557.09" x2="557.09" y1="1256.9" y2="1268.9" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint320_linear_2188_188555" x1="549.9" x2="549.9" y1="1596.6" y2="1608.6" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint321_linear_2188_188555" x1="533.61" x2="533.6" y1="1592.7" y2="1604.7" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint322_linear_2188_188555" x1="528.41" x2="528.41" y1="1948.7" y2="1960.7" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <pattern id="base-liquid" width="584" height="201" patternTransform="translate(8,791)" patternUnits="userSpaceOnUse" preserveAspectRatio="xMidYMid">
   <circle cx="93" cy="33" r="8" fill="url(#paint1_linear_1687_130892)" style="fill:url(#paint1_linear_1687_130892)"/>
   <circle cx="67" cy="166" r="8" fill="url(#paint2_linear_1687_130892)" style="fill:url(#paint2_linear_1687_130892)"/>
   <circle cx="27" cy="193" r="8" fill="url(#paint3_linear_1687_130892)" style="fill:url(#paint3_linear_1687_130892)"/>
   <circle cx="59" cy="130" r="8" fill="url(#paint4_linear_1687_130892)" style="fill:url(#paint4_linear_1687_130892)"/>
   <circle cx="10" cy="49" r="8" fill="url(#paint5_linear_1687_130892)" style="fill:url(#paint5_linear_1687_130892)"/>
   <circle cx="10" cy="170" r="8" fill="url(#paint6_linear_1687_130892)" style="fill:url(#paint6_linear_1687_130892)"/>
   <circle cx="56" cy="41" r="5" fill="url(#paint7_linear_1687_130892)" style="fill:url(#paint7_linear_1687_130892)"/>
   <circle cx="41" cy="20" r="5" fill="url(#paint8_linear_1687_130892)" style="fill:url(#paint8_linear_1687_130892)"/>
   <circle cx="74" cy="110" r="4" fill="url(#paint9_linear_1687_130892)" style="fill:url(#paint9_linear_1687_130892)"/>
   <circle cx="223" cy="128" r="4" fill="url(#paint10_linear_1687_130892)" style="fill:url(#paint10_linear_1687_130892)"/>
   <circle cx="8" cy="87" r="4" fill="url(#paint11_linear_1687_130892)" style="fill:url(#paint11_linear_1687_130892)"/>
   <circle cx="37" cy="73" r="5" fill="url(#paint12_linear_1687_130892)" style="fill:url(#paint12_linear_1687_130892)"/>
   <circle cx="61" cy="92" r="5" fill="url(#paint13_linear_1687_130892)" style="fill:url(#paint13_linear_1687_130892)"/>
   <circle cx="33.5" cy="150.5" r="2.5" fill="url(#paint14_linear_1687_130892)" style="fill:url(#paint14_linear_1687_130892)"/>
   <circle cx="56.5" cy="182.5" r="2.5" fill="url(#paint15_linear_1687_130892)" style="fill:url(#paint15_linear_1687_130892)"/>
   <circle cx="72.5" cy="73.5" r="2.5" fill="url(#paint16_linear_1687_130892)" style="fill:url(#paint16_linear_1687_130892)"/>
   <circle cx="44.5" cy="106.5" r="2.5" fill="url(#paint17_linear_1687_130892)" style="fill:url(#paint17_linear_1687_130892)"/>
   <circle cx="27.5" cy="117.5" r="2.5" fill="url(#paint18_linear_1687_130892)" style="fill:url(#paint18_linear_1687_130892)"/>
   <circle cx="16.5" cy="63.5" r="2.5" fill="url(#paint19_linear_1687_130892)" style="fill:url(#paint19_linear_1687_130892)"/>
   <circle cx="4" cy="29" r="4" fill="url(#paint20_linear_1687_130892)" style="fill:url(#paint20_linear_1687_130892)"/>
   <circle cx="4" cy="150" r="4" fill="url(#paint21_linear_1687_130892)" style="fill:url(#paint21_linear_1687_130892)"/>
   <circle cx="9.5" cy="110.5" r="9.5" fill="url(#paint22_linear_1687_130892)" style="fill:url(#paint22_linear_1687_130892)"/>
   <circle cx="101" cy="196" r="5" fill="url(#paint23_linear_1687_130892)" style="fill:url(#paint23_linear_1687_130892)"/>
   <circle cx="75.5" cy="62.5" r="2.5" fill="url(#paint24_linear_1687_130892)" style="fill:url(#paint24_linear_1687_130892)"/>
   <circle cx="75.5" cy="49.5" r="2.5" fill="url(#paint25_linear_1687_130892)" style="fill:url(#paint25_linear_1687_130892)"/>
   <circle cx="109" cy="119" r="5" fill="url(#paint26_linear_1687_130892)" style="fill:url(#paint26_linear_1687_130892)"/>
   <circle cx="80.5" cy="140.5" r="2.5" fill="url(#paint27_linear_1687_130892)" style="fill:url(#paint27_linear_1687_130892)"/>
   <circle cx="73" cy="189" r="5" fill="url(#paint28_linear_1687_130892)" style="fill:url(#paint28_linear_1687_130892)"/>
   <circle cx="112" cy="153" r="5" fill="url(#paint29_linear_1687_130892)" style="fill:url(#paint29_linear_1687_130892)"/>
   <circle cx="93.5" cy="89.5" r="2.5" fill="url(#paint30_linear_1687_130892)" style="fill:url(#paint30_linear_1687_130892)"/>
   <circle cx="219" cy="33" r="8" fill="url(#paint31_linear_1687_130892)" style="fill:url(#paint31_linear_1687_130892)"/>
   <circle cx="193" cy="166" r="8" fill="url(#paint32_linear_1687_130892)" style="fill:url(#paint32_linear_1687_130892)"/>
   <circle cx="153" cy="193" r="8" fill="url(#paint33_linear_1687_130892)" style="fill:url(#paint33_linear_1687_130892)"/>
   <circle cx="181" cy="138" r="8" fill="url(#paint34_linear_1687_130892)" style="fill:url(#paint34_linear_1687_130892)"/>
   <circle cx="119" cy="63" r="8" fill="url(#paint35_linear_1687_130892)" style="fill:url(#paint35_linear_1687_130892)"/>
   <circle cx="136" cy="170" r="8" fill="url(#paint36_linear_1687_130892)" style="fill:url(#paint36_linear_1687_130892)"/>
   <circle cx="182" cy="41" r="5" fill="url(#paint37_linear_1687_130892)" style="fill:url(#paint37_linear_1687_130892)"/>
   <circle cx="167" cy="20" r="5" fill="url(#paint38_linear_1687_130892)" style="fill:url(#paint38_linear_1687_130892)"/>
   <circle cx="200" cy="110" r="4" fill="url(#paint39_linear_1687_130892)" style="fill:url(#paint39_linear_1687_130892)"/>
   <circle cx="134" cy="87" r="4" fill="url(#paint40_linear_1687_130892)" style="fill:url(#paint40_linear_1687_130892)"/>
   <circle cx="163" cy="73" r="5" fill="url(#paint41_linear_1687_130892)" style="fill:url(#paint41_linear_1687_130892)"/>
   <circle cx="187" cy="92" r="5" fill="url(#paint42_linear_1687_130892)" style="fill:url(#paint42_linear_1687_130892)"/>
   <circle cx="159.5" cy="150.5" r="2.5" fill="url(#paint43_linear_1687_130892)" style="fill:url(#paint43_linear_1687_130892)"/>
   <circle cx="182.5" cy="182.5" r="2.5" fill="url(#paint44_linear_1687_130892)" style="fill:url(#paint44_linear_1687_130892)"/>
   <circle cx="198.5" cy="73.5" r="2.5" fill="url(#paint45_linear_1687_130892)" style="fill:url(#paint45_linear_1687_130892)"/>
   <circle cx="170.5" cy="106.5" r="2.5" fill="url(#paint46_linear_1687_130892)" style="fill:url(#paint46_linear_1687_130892)"/>
   <circle cx="153.5" cy="117.5" r="2.5" fill="url(#paint47_linear_1687_130892)" style="fill:url(#paint47_linear_1687_130892)"/>
   <circle cx="142.5" cy="63.5" r="2.5" fill="url(#paint48_linear_1687_130892)" style="fill:url(#paint48_linear_1687_130892)"/>
   <circle cx="130" cy="29" r="4" fill="url(#paint49_linear_1687_130892)" style="fill:url(#paint49_linear_1687_130892)"/>
   <circle cx="130" cy="150" r="4" fill="url(#paint50_linear_1687_130892)" style="fill:url(#paint50_linear_1687_130892)"/>
   <circle cx="135.5" cy="110.5" r="9.5" fill="url(#paint51_linear_1687_130892)" style="fill:url(#paint51_linear_1687_130892)"/>
   <circle cx="232" cy="180" r="5" fill="url(#paint52_linear_1687_130892)" style="fill:url(#paint52_linear_1687_130892)"/>
   <circle cx="213.5" cy="66.5" r="2.5" fill="url(#paint53_linear_1687_130892)" style="fill:url(#paint53_linear_1687_130892)"/>
   <circle cx="165.5" cy="36.5" r="2.5" fill="url(#paint54_linear_1687_130892)" style="fill:url(#paint54_linear_1687_130892)"/>
   <circle cx="233" cy="103" r="5" fill="url(#paint55_linear_1687_130892)" style="fill:url(#paint55_linear_1687_130892)"/>
   <circle cx="206.5" cy="140.5" r="2.5" fill="url(#paint56_linear_1687_130892)" style="fill:url(#paint56_linear_1687_130892)"/>
   <circle cx="224.5" cy="198.5" r="2.5" fill="url(#paint57_linear_1687_130892)" style="fill:url(#paint57_linear_1687_130892)"/>
   <circle cx="199" cy="189" r="5" fill="url(#paint58_linear_1687_130892)" style="fill:url(#paint58_linear_1687_130892)"/>
   <circle cx="238" cy="153" r="5" fill="url(#paint59_linear_1687_130892)" style="fill:url(#paint59_linear_1687_130892)"/>
   <circle cx="219.5" cy="89.5" r="2.5" fill="url(#paint60_linear_1687_130892)" style="fill:url(#paint60_linear_1687_130892)"/>
   <circle cx="68.5" cy="7.5" r="2.5" fill="url(#paint61_linear_1687_130892)" style="fill:url(#paint61_linear_1687_130892)"/>
   <circle cx="202.5" cy="12.5" r="2.5" fill="url(#paint62_linear_1687_130892)" style="fill:url(#paint62_linear_1687_130892)"/>
   <circle cx="114" cy="5" r="5" fill="url(#paint63_linear_1687_130892)" style="fill:url(#paint63_linear_1687_130892)"/>
   <circle cx="355" cy="33" r="8" fill="url(#paint64_linear_1687_130892)" style="fill:url(#paint64_linear_1687_130892)"/>
   <circle cx="329" cy="166" r="8" fill="url(#paint65_linear_1687_130892)" style="fill:url(#paint65_linear_1687_130892)"/>
   <circle cx="289" cy="193" r="8" fill="url(#paint66_linear_1687_130892)" style="fill:url(#paint66_linear_1687_130892)"/>
   <circle cx="320" cy="136" r="8" fill="url(#paint67_linear_1687_130892)" style="fill:url(#paint67_linear_1687_130892)"/>
   <circle cx="272" cy="49" r="8" fill="url(#paint68_linear_1687_130892)" style="fill:url(#paint68_linear_1687_130892)"/>
   <circle cx="272" cy="170" r="8" fill="url(#paint69_linear_1687_130892)" style="fill:url(#paint69_linear_1687_130892)"/>
   <circle cx="318" cy="41" r="5" fill="url(#paint70_linear_1687_130892)" style="fill:url(#paint70_linear_1687_130892)"/>
   <circle cx="303" cy="20" r="5" fill="url(#paint71_linear_1687_130892)" style="fill:url(#paint71_linear_1687_130892)"/>
   <circle cx="336" cy="110" r="4" fill="url(#paint72_linear_1687_130892)" style="fill:url(#paint72_linear_1687_130892)"/>
   <circle cx="485" cy="128" r="4" fill="url(#paint73_linear_1687_130892)" style="fill:url(#paint73_linear_1687_130892)"/>
   <circle cx="547" cy="92" r="4" fill="url(#paint74_linear_1687_130892)" style="fill:url(#paint74_linear_1687_130892)"/>
   <circle cx="270" cy="87" r="4" fill="url(#paint75_linear_1687_130892)" style="fill:url(#paint75_linear_1687_130892)"/>
   <circle cx="299" cy="73" r="5" fill="url(#paint76_linear_1687_130892)" style="fill:url(#paint76_linear_1687_130892)"/>
   <circle cx="323" cy="92" r="5" fill="url(#paint77_linear_1687_130892)" style="fill:url(#paint77_linear_1687_130892)"/>
   <circle cx="295.5" cy="150.5" r="2.5" fill="url(#paint78_linear_1687_130892)" style="fill:url(#paint78_linear_1687_130892)"/>
   <circle cx="318.5" cy="182.5" r="2.5" fill="url(#paint79_linear_1687_130892)" style="fill:url(#paint79_linear_1687_130892)"/>
   <circle cx="334.5" cy="73.5" r="2.5" fill="url(#paint80_linear_1687_130892)" style="fill:url(#paint80_linear_1687_130892)"/>
   <circle cx="306.5" cy="106.5" r="2.5" fill="url(#paint81_linear_1687_130892)" style="fill:url(#paint81_linear_1687_130892)"/>
   <circle cx="289.5" cy="117.5" r="2.5" fill="url(#paint82_linear_1687_130892)" style="fill:url(#paint82_linear_1687_130892)"/>
   <circle cx="243.5" cy="67.5" r="2.5" fill="url(#paint83_linear_1687_130892)" style="fill:url(#paint83_linear_1687_130892)"/>
   <circle cx="257" cy="20" r="4" fill="url(#paint84_linear_1687_130892)" style="fill:url(#paint84_linear_1687_130892)"/>
   <circle cx="266" cy="150" r="4" fill="url(#paint85_linear_1687_130892)" style="fill:url(#paint85_linear_1687_130892)"/>
   <circle cx="261.5" cy="126.5" r="9.5" fill="url(#paint86_linear_1687_130892)" style="fill:url(#paint86_linear_1687_130892)"/>
   <circle cx="510.5" cy="43.5" r="9.5" fill="url(#paint87_linear_1687_130892)" style="fill:url(#paint87_linear_1687_130892)"/>
   <circle cx="520.5" cy="128.5" r="9.5" fill="url(#paint88_linear_1687_130892)" style="fill:url(#paint88_linear_1687_130892)"/>
   <circle cx="363" cy="196" r="5" fill="url(#paint89_linear_1687_130892)" style="fill:url(#paint89_linear_1687_130892)"/>
   <circle cx="342.5" cy="83.5" r="2.5" fill="url(#paint90_linear_1687_130892)" style="fill:url(#paint90_linear_1687_130892)"/>
   <circle cx="368.5" cy="89.5" r="2.5" fill="url(#paint91_linear_1687_130892)" style="fill:url(#paint91_linear_1687_130892)"/>
   <circle cx="371" cy="119" r="5" fill="url(#paint92_linear_1687_130892)" style="fill:url(#paint92_linear_1687_130892)"/>
   <circle cx="547" cy="119" r="5" fill="url(#paint93_linear_1687_130892)" style="fill:url(#paint93_linear_1687_130892)"/>
   <circle cx="342.5" cy="140.5" r="2.5" fill="url(#paint94_linear_1687_130892)" style="fill:url(#paint94_linear_1687_130892)"/>
   <circle cx="335" cy="189" r="5" fill="url(#paint95_linear_1687_130892)" style="fill:url(#paint95_linear_1687_130892)"/>
   <circle cx="374" cy="153" r="5" fill="url(#paint96_linear_1687_130892)" style="fill:url(#paint96_linear_1687_130892)"/>
   <circle cx="515" cy="176" r="5" fill="url(#paint97_linear_1687_130892)" style="fill:url(#paint97_linear_1687_130892)"/>
   <circle cx="355.5" cy="89.5" r="2.5" fill="url(#paint98_linear_1687_130892)" style="fill:url(#paint98_linear_1687_130892)"/>
   <circle cx="481" cy="33" r="8" fill="url(#paint99_linear_1687_130892)" style="fill:url(#paint99_linear_1687_130892)"/>
   <circle cx="547" cy="23" r="8" fill="url(#paint100_linear_1687_130892)" style="fill:url(#paint100_linear_1687_130892)"/>
   <circle cx="455" cy="166" r="8" fill="url(#paint101_linear_1687_130892)" style="fill:url(#paint101_linear_1687_130892)"/>
   <circle cx="415" cy="193" r="8" fill="url(#paint102_linear_1687_130892)" style="fill:url(#paint102_linear_1687_130892)"/>
   <circle cx="443" cy="138" r="8" fill="url(#paint103_linear_1687_130892)" style="fill:url(#paint103_linear_1687_130892)"/>
   <circle cx="576" cy="153" r="8" fill="url(#paint104_linear_1687_130892)" style="fill:url(#paint104_linear_1687_130892)"/>
   <circle cx="381" cy="63" r="8" fill="url(#paint105_linear_1687_130892)" style="fill:url(#paint105_linear_1687_130892)"/>
   <circle cx="398" cy="170" r="8" fill="url(#paint106_linear_1687_130892)" style="fill:url(#paint106_linear_1687_130892)"/>
   <circle cx="539" cy="193" r="8" fill="url(#paint107_linear_1687_130892)" style="fill:url(#paint107_linear_1687_130892)"/>
   <circle cx="444" cy="41" r="5" fill="url(#paint108_linear_1687_130892)" style="fill:url(#paint108_linear_1687_130892)"/>
   <circle cx="579" cy="39" r="5" fill="url(#paint109_linear_1687_130892)" style="fill:url(#paint109_linear_1687_130892)"/>
   <circle cx="568" cy="196" r="5" fill="url(#paint110_linear_1687_130892)" style="fill:url(#paint110_linear_1687_130892)"/>
   <circle cx="515" cy="10" r="5" fill="url(#paint111_linear_1687_130892)" style="fill:url(#paint111_linear_1687_130892)"/>
   <circle cx="429" cy="20" r="5" fill="url(#paint112_linear_1687_130892)" style="fill:url(#paint112_linear_1687_130892)"/>
   <circle cx="462" cy="110" r="4" fill="url(#paint113_linear_1687_130892)" style="fill:url(#paint113_linear_1687_130892)"/>
   <circle cx="396" cy="87" r="4" fill="url(#paint114_linear_1687_130892)" style="fill:url(#paint114_linear_1687_130892)"/>
   <circle cx="425" cy="73" r="5" fill="url(#paint115_linear_1687_130892)" style="fill:url(#paint115_linear_1687_130892)"/>
   <circle cx="505" cy="76" r="5" fill="url(#paint116_linear_1687_130892)" style="fill:url(#paint116_linear_1687_130892)"/>
   <circle cx="449" cy="92" r="5" fill="url(#paint117_linear_1687_130892)" style="fill:url(#paint117_linear_1687_130892)"/>
   <circle cx="421.5" cy="150.5" r="2.5" fill="url(#paint118_linear_1687_130892)" style="fill:url(#paint118_linear_1687_130892)"/>
   <circle cx="444.5" cy="182.5" r="2.5" fill="url(#paint119_linear_1687_130892)" style="fill:url(#paint119_linear_1687_130892)"/>
   <circle cx="460.5" cy="73.5" r="2.5" fill="url(#paint120_linear_1687_130892)" style="fill:url(#paint120_linear_1687_130892)"/>
   <circle cx="528.5" cy="90.5" r="2.5" fill="url(#paint121_linear_1687_130892)" style="fill:url(#paint121_linear_1687_130892)"/>
   <circle cx="432.5" cy="106.5" r="2.5" fill="url(#paint122_linear_1687_130892)" style="fill:url(#paint122_linear_1687_130892)"/>
   <circle cx="568.5" cy="89.5" r="2.5" fill="url(#paint123_linear_1687_130892)" style="fill:url(#paint123_linear_1687_130892)"/>
   <circle cx="415.5" cy="117.5" r="2.5" fill="url(#paint124_linear_1687_130892)" style="fill:url(#paint124_linear_1687_130892)"/>
   <circle cx="404.5" cy="63.5" r="2.5" fill="url(#paint125_linear_1687_130892)" style="fill:url(#paint125_linear_1687_130892)"/>
   <circle cx="540.5" cy="46.5" r="2.5" fill="url(#paint126_linear_1687_130892)" style="fill:url(#paint126_linear_1687_130892)"/>
   <circle cx="392" cy="29" r="4" fill="url(#paint127_linear_1687_130892)" style="fill:url(#paint127_linear_1687_130892)"/>
   <circle cx="392" cy="150" r="4" fill="url(#paint128_linear_1687_130892)" style="fill:url(#paint128_linear_1687_130892)"/>
   <circle cx="397.5" cy="110.5" r="9.5" fill="url(#paint129_linear_1687_130892)" style="fill:url(#paint129_linear_1687_130892)"/>
   <circle cx="494" cy="180" r="5" fill="url(#paint130_linear_1687_130892)" style="fill:url(#paint130_linear_1687_130892)"/>
   <circle cx="475.5" cy="66.5" r="2.5" fill="url(#paint131_linear_1687_130892)" style="fill:url(#paint131_linear_1687_130892)"/>
   <circle cx="427.5" cy="36.5" r="2.5" fill="url(#paint132_linear_1687_130892)" style="fill:url(#paint132_linear_1687_130892)"/>
   <circle cx="495" cy="103" r="5" fill="url(#paint133_linear_1687_130892)" style="fill:url(#paint133_linear_1687_130892)"/>
   <circle cx="468.5" cy="140.5" r="2.5" fill="url(#paint134_linear_1687_130892)" style="fill:url(#paint134_linear_1687_130892)"/>
   <circle cx="540.5" cy="148.5" r="2.5" fill="url(#paint135_linear_1687_130892)" style="fill:url(#paint135_linear_1687_130892)"/>
   <circle cx="486.5" cy="198.5" r="2.5" fill="url(#paint136_linear_1687_130892)" style="fill:url(#paint136_linear_1687_130892)"/>
   <circle cx="461" cy="189" r="5" fill="url(#paint137_linear_1687_130892)" style="fill:url(#paint137_linear_1687_130892)"/>
   <circle cx="557" cy="167" r="5" fill="url(#paint138_linear_1687_130892)" style="fill:url(#paint138_linear_1687_130892)"/>
   <circle cx="557" cy="66" r="5" fill="url(#paint139_linear_1687_130892)" style="fill:url(#paint139_linear_1687_130892)"/>
   <circle cx="500" cy="153" r="5" fill="url(#paint140_linear_1687_130892)" style="fill:url(#paint140_linear_1687_130892)"/>
   <circle cx="481.5" cy="89.5" r="2.5" fill="url(#paint141_linear_1687_130892)" style="fill:url(#paint141_linear_1687_130892)"/>
   <circle cx="525.5" cy="73.5" r="2.5" fill="url(#paint142_linear_1687_130892)" style="fill:url(#paint142_linear_1687_130892)"/>
   <circle cx="330.5" cy="7.5" r="2.5" fill="url(#paint143_linear_1687_130892)" style="fill:url(#paint143_linear_1687_130892)"/>
   <circle cx="464.5" cy="12.5" r="2.5" fill="url(#paint144_linear_1687_130892)" style="fill:url(#paint144_linear_1687_130892)"/>
   <circle cx="376" cy="5" r="5" fill="url(#paint145_linear_1687_130892)" style="fill:url(#paint145_linear_1687_130892)"/>
   <path d="m0 0h584v197c0 2.209-1.791 4-4 4h-576c-2.2092 0-4-1.791-4-4z" fill="#1EC1F4" fill-opacity=".5" style="fill-opacity:0"/>
  </pattern>
  <pattern id="liquid" width="584" height="201" patternUnits="userSpaceOnUse"><rect width="584" height="201" x="0" y="0" stroke-width="0" fill="url(#base-liquid)"/></pattern>
  <linearGradient id="paint1_linear_1687_130892" x1="99.316" x2="101" y1="817.26" y2="832" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint2_linear_1687_130892" x1="73.316" x2="75" y1="950.26" y2="965" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint3_linear_1687_130892" x1="33.316" x2="35" y1="977.26" y2="992" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint4_linear_1687_130892" x1="65.316" x2="67" y1="914.26" y2="929" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint5_linear_1687_130892" x1="16.316" x2="18" y1="833.26" y2="848" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint6_linear_1687_130892" x1="16.316" x2="18" y1="954.26" y2="969" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint7_linear_1687_130892" x1="62.947" x2="64" y1="827.79" y2="837" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint8_linear_1687_130892" x1="47.947" x2="49" y1="806.79" y2="816" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint9_linear_1687_130892" x1="81.158" x2="82" y1="897.63" y2="905" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint10_linear_1687_130892" x1="230.16" x2="231" y1="915.63" y2="923" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint11_linear_1687_130892" x1="15.158" x2="16" y1="874.63" y2="882" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint12_linear_1687_130892" x1="43.947" x2="45" y1="859.79" y2="869" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint13_linear_1687_130892" x1="67.947" x2="69" y1="878.79" y2="888" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint14_linear_1687_130892" x1="40.974" x2="41.5" y1="939.4" y2="944" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint15_linear_1687_130892" x1="63.974" x2="64.5" y1="971.4" y2="976" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint16_linear_1687_130892" x1="79.974" x2="80.5" y1="862.4" y2="867" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint17_linear_1687_130892" x1="51.974" x2="52.5" y1="895.4" y2="900" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint18_linear_1687_130892" x1="34.974" x2="35.5" y1="906.4" y2="911" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint19_linear_1687_130892" x1="23.974" x2="24.5" y1="852.4" y2="857" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".3" offset="1"/>
  </linearGradient>
  <linearGradient id="paint20_linear_1687_130892" x1="11.158" x2="12" y1="816.63" y2="824" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint21_linear_1687_130892" x1="11.158" x2="12" y1="937.63" y2="945" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint22_linear_1687_130892" x1="15.5" x2="17.5" y1="893.5" y2="911" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint23_linear_1687_130892" x1="107.95" x2="109" y1="982.79" y2="992" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint24_linear_1687_130892" x1="82.974" x2="83.5" y1="851.4" y2="856" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint25_linear_1687_130892" x1="82.974" x2="83.5" y1="838.4" y2="843" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint26_linear_1687_130892" x1="115.95" x2="117" y1="905.79" y2="915" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint27_linear_1687_130892" x1="87.974" x2="88.5" y1="929.4" y2="934" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint28_linear_1687_130892" x1="79.947" x2="81" y1="975.79" y2="985" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint29_linear_1687_130892" x1="118.95" x2="120" y1="939.79" y2="949" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".2" offset="1"/>
  </linearGradient>
  <linearGradient id="paint30_linear_1687_130892" x1="100.97" x2="101.5" y1="878.4" y2="883" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint31_linear_1687_130892" x1="225.32" x2="227" y1="817.26" y2="832" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint32_linear_1687_130892" x1="199.32" x2="201" y1="950.26" y2="965" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint33_linear_1687_130892" x1="159.32" x2="161" y1="977.26" y2="992" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint34_linear_1687_130892" x1="187.32" x2="189" y1="922.26" y2="937" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint35_linear_1687_130892" x1="125.32" x2="127" y1="847.26" y2="862" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint36_linear_1687_130892" x1="142.32" x2="144" y1="954.26" y2="969" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint37_linear_1687_130892" x1="188.95" x2="190" y1="827.79" y2="837" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint38_linear_1687_130892" x1="173.95" x2="175" y1="806.79" y2="816" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint39_linear_1687_130892" x1="207.16" x2="208" y1="897.63" y2="905" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint40_linear_1687_130892" x1="141.16" x2="142" y1="874.63" y2="882" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint41_linear_1687_130892" x1="169.95" x2="171" y1="859.79" y2="869" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint42_linear_1687_130892" x1="193.95" x2="195" y1="878.79" y2="888" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint43_linear_1687_130892" x1="166.97" x2="167.5" y1="939.4" y2="944" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint44_linear_1687_130892" x1="189.97" x2="190.5" y1="971.4" y2="976" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint45_linear_1687_130892" x1="205.97" x2="206.5" y1="862.4" y2="867" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint46_linear_1687_130892" x1="177.97" x2="178.5" y1="895.4" y2="900" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint47_linear_1687_130892" x1="160.97" x2="161.5" y1="906.4" y2="911" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint48_linear_1687_130892" x1="149.97" x2="150.5" y1="852.4" y2="857" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".3" offset="1"/>
  </linearGradient>
  <linearGradient id="paint49_linear_1687_130892" x1="137.16" x2="138" y1="816.63" y2="824" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint50_linear_1687_130892" x1="137.16" x2="138" y1="937.63" y2="945" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint51_linear_1687_130892" x1="141.5" x2="143.5" y1="893.5" y2="911" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint52_linear_1687_130892" x1="238.95" x2="240" y1="966.79" y2="976" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint53_linear_1687_130892" x1="220.97" x2="221.5" y1="855.4" y2="860" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint54_linear_1687_130892" x1="172.97" x2="173.5" y1="825.4" y2="830" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint55_linear_1687_130892" x1="239.95" x2="241" y1="889.79" y2="899" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint56_linear_1687_130892" x1="213.97" x2="214.5" y1="929.4" y2="934" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint57_linear_1687_130892" x1="231.97" x2="232.5" y1="987.4" y2="992" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint58_linear_1687_130892" x1="205.95" x2="207" y1="975.79" y2="985" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint59_linear_1687_130892" x1="244.95" x2="246" y1="939.79" y2="949" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".2" offset="1"/>
  </linearGradient>
  <linearGradient id="paint60_linear_1687_130892" x1="226.97" x2="227.5" y1="878.4" y2="883" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint61_linear_1687_130892" x1="75.974" x2="76.5" y1="796.4" y2="801" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint62_linear_1687_130892" x1="209.97" x2="210.5" y1="801.4" y2="806" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint63_linear_1687_130892" x1="120.95" x2="122" y1="791.79" y2="801" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint64_linear_1687_130892" x1="361.32" x2="363" y1="817.26" y2="832" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint65_linear_1687_130892" x1="335.32" x2="337" y1="950.26" y2="965" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint66_linear_1687_130892" x1="295.32" x2="297" y1="977.26" y2="992" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint67_linear_1687_130892" x1="326.32" x2="328" y1="920.26" y2="935" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint68_linear_1687_130892" x1="278.32" x2="280" y1="833.26" y2="848" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint69_linear_1687_130892" x1="278.32" x2="280" y1="954.26" y2="969" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint70_linear_1687_130892" x1="324.95" x2="326" y1="827.79" y2="837" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint71_linear_1687_130892" x1="309.95" x2="311" y1="806.79" y2="816" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint72_linear_1687_130892" x1="343.16" x2="344" y1="897.63" y2="905" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint73_linear_1687_130892" x1="492.16" x2="493" y1="915.63" y2="923" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint74_linear_1687_130892" x1="554.16" x2="555" y1="879.63" y2="887" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint75_linear_1687_130892" x1="277.16" x2="278" y1="874.63" y2="882" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint76_linear_1687_130892" x1="305.95" x2="307" y1="859.79" y2="869" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint77_linear_1687_130892" x1="329.95" x2="331" y1="878.79" y2="888" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint78_linear_1687_130892" x1="302.97" x2="303.5" y1="939.4" y2="944" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint79_linear_1687_130892" x1="325.97" x2="326.5" y1="971.4" y2="976" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint80_linear_1687_130892" x1="341.97" x2="342.5" y1="862.4" y2="867" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint81_linear_1687_130892" x1="313.97" x2="314.5" y1="895.4" y2="900" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint82_linear_1687_130892" x1="296.97" x2="297.5" y1="906.4" y2="911" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint83_linear_1687_130892" x1="250.97" x2="251.5" y1="856.4" y2="861" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".3" offset="1"/>
  </linearGradient>
  <linearGradient id="paint84_linear_1687_130892" x1="264.16" x2="265" y1="807.63" y2="815" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint85_linear_1687_130892" x1="273.16" x2="274" y1="937.63" y2="945" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint86_linear_1687_130892" x1="267.5" x2="269.5" y1="909.5" y2="927" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint87_linear_1687_130892" x1="516.5" x2="518.5" y1="826.5" y2="844" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint88_linear_1687_130892" x1="526.5" x2="528.5" y1="911.5" y2="929" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint89_linear_1687_130892" x1="369.95" x2="371" y1="982.79" y2="992" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint90_linear_1687_130892" x1="349.97" x2="350.5" y1="872.4" y2="877" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint91_linear_1687_130892" x1="375.97" x2="376.5" y1="878.4" y2="883" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint92_linear_1687_130892" x1="377.95" x2="379" y1="905.79" y2="915" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint93_linear_1687_130892" x1="553.95" x2="555" y1="905.79" y2="915" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint94_linear_1687_130892" x1="349.97" x2="350.5" y1="929.4" y2="934" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint95_linear_1687_130892" x1="341.95" x2="343" y1="975.79" y2="985" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint96_linear_1687_130892" x1="380.95" x2="382" y1="939.79" y2="949" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".2" offset="1"/>
  </linearGradient>
  <linearGradient id="paint97_linear_1687_130892" x1="521.95" x2="523" y1="962.79" y2="972" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".2" offset="1"/>
  </linearGradient>
  <linearGradient id="paint98_linear_1687_130892" x1="362.97" x2="363.5" y1="878.4" y2="883" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint99_linear_1687_130892" x1="487.32" x2="489" y1="817.26" y2="832" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint100_linear_1687_130892" x1="553.32" x2="555" y1="807.26" y2="822" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint101_linear_1687_130892" x1="461.32" x2="463" y1="950.26" y2="965" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint102_linear_1687_130892" x1="421.32" x2="423" y1="977.26" y2="992" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint103_linear_1687_130892" x1="449.32" x2="451" y1="922.26" y2="937" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint104_linear_1687_130892" x1="582.32" x2="584" y1="937.26" y2="952" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint105_linear_1687_130892" x1="387.32" x2="389" y1="847.26" y2="862" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint106_linear_1687_130892" x1="404.32" x2="406" y1="954.26" y2="969" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint107_linear_1687_130892" x1="545.32" x2="547" y1="977.26" y2="992" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint108_linear_1687_130892" x1="450.95" x2="452" y1="827.79" y2="837" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint109_linear_1687_130892" x1="585.95" x2="587" y1="825.79" y2="835" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint110_linear_1687_130892" x1="574.95" x2="576" y1="982.79" y2="992" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint111_linear_1687_130892" x1="521.95" x2="523" y1="796.79" y2="806" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint112_linear_1687_130892" x1="435.95" x2="437" y1="806.79" y2="816" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint113_linear_1687_130892" x1="469.16" x2="470" y1="897.63" y2="905" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint114_linear_1687_130892" x1="403.16" x2="404" y1="874.63" y2="882" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint115_linear_1687_130892" x1="431.95" x2="433" y1="859.79" y2="869" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint116_linear_1687_130892" x1="511.95" x2="513" y1="862.79" y2="872" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint117_linear_1687_130892" x1="455.95" x2="457" y1="878.79" y2="888" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint118_linear_1687_130892" x1="428.97" x2="429.5" y1="939.4" y2="944" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint119_linear_1687_130892" x1="451.97" x2="452.5" y1="971.4" y2="976" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint120_linear_1687_130892" x1="467.97" x2="468.5" y1="862.4" y2="867" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint121_linear_1687_130892" x1="535.97" x2="536.5" y1="879.4" y2="884" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint122_linear_1687_130892" x1="439.97" x2="440.5" y1="895.4" y2="900" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint123_linear_1687_130892" x1="575.97" x2="576.5" y1="878.4" y2="883" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint124_linear_1687_130892" x1="422.97" x2="423.5" y1="906.4" y2="911" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint125_linear_1687_130892" x1="411.97" x2="412.5" y1="852.4" y2="857" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".3" offset="1"/>
  </linearGradient>
  <linearGradient id="paint126_linear_1687_130892" x1="547.97" x2="548.5" y1="835.4" y2="840" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".3" offset="1"/>
  </linearGradient>
  <linearGradient id="paint127_linear_1687_130892" x1="399.16" x2="400" y1="816.63" y2="824" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint128_linear_1687_130892" x1="399.16" x2="400" y1="937.63" y2="945" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint129_linear_1687_130892" x1="403.5" x2="405.5" y1="893.5" y2="911" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint130_linear_1687_130892" x1="500.95" x2="502" y1="966.79" y2="976" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint131_linear_1687_130892" x1="482.97" x2="483.5" y1="855.4" y2="860" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint132_linear_1687_130892" x1="434.97" x2="435.5" y1="825.4" y2="830" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint133_linear_1687_130892" x1="501.95" x2="503" y1="889.79" y2="899" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".2" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
  <linearGradient id="paint134_linear_1687_130892" x1="475.97" x2="476.5" y1="929.4" y2="934" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint135_linear_1687_130892" x1="547.97" x2="548.5" y1="937.4" y2="942" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint136_linear_1687_130892" x1="493.97" x2="494.5" y1="987.4" y2="992" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".25" offset="1"/>
  </linearGradient>
  <linearGradient id="paint137_linear_1687_130892" x1="467.95" x2="469" y1="975.79" y2="985" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint138_linear_1687_130892" x1="563.95" x2="565" y1="953.79" y2="963" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint139_linear_1687_130892" x1="563.95" x2="565" y1="852.79" y2="862" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".05" offset="1"/>
  </linearGradient>
  <linearGradient id="paint140_linear_1687_130892" x1="506.95" x2="508" y1="939.79" y2="949" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".2" offset="1"/>
  </linearGradient>
  <linearGradient id="paint141_linear_1687_130892" x1="488.97" x2="489.5" y1="878.4" y2="883" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint142_linear_1687_130892" x1="532.97" x2="533.5" y1="862.4" y2="867" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint143_linear_1687_130892" x1="337.97" x2="338.5" y1="796.4" y2="801" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint144_linear_1687_130892" x1="471.97" x2="472.5" y1="801.4" y2="806" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint145_linear_1687_130892" x1="382.95" x2="384" y1="791.79" y2="801" gradientTransform="translate(-8,-791)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".25" offset="0"/>
   <stop stop-opacity=".1" offset="1"/>
  </linearGradient>
 </defs>
</svg>