{"fqn": "ground_temperature_card", "name": "Ground temperature card", "deprecated": false, "image": "tb-image;/api/images/system/ground_temperature_card_system_widget_image.png", "description": "Displays the latest ground temperature telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'temperature', label: 'Ground temperature', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Ground temperature\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"thermostat\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#234CC7\"},{\"from\":-20,\"to\":0,\"color\":\"#305AD7\"},{\"from\":0,\"to\":10,\"color\":\"#7191EF\"},{\"from\":10,\"to\":20,\"color\":\"#FFA600\"},{\"from\":20,\"to\":30,\"color\":\"#F36900\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":52,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#234CC7\"},{\"from\":-20,\"to\":0,\"color\":\"#305AD7\"},{\"from\":0,\"to\":10,\"color\":\"#7191EF\"},{\"from\":10,\"to\":20,\"color\":\"#FFA600\"},{\"from\":20,\"to\":30,\"color\":\"#F36900\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Ground temperature card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"°C\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "soil temperature", "terrestrial temperature", "subsurface temperature", "earth temperature", "below surface temperature", "surface temp", "soil warmth", "land temperature", "geothermal reading", "ground warmth"], "resources": [{"link": "/api/images/system/ground_temperature_card_system_widget_image.png", "title": "\"Ground temperature card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "ground_temperature_card_system_widget_image.png", "publicResourceKey": "YaygQARWsMsBEjNzaAZ65NkKRmpGM3Mg", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAApVBMVEXg4ODf39/g4ODg4OAAAAD///8jTMfg4OAhISE9PT2tvOpYWFjv7+/k6fjHx8d1j9yqqqp0dHTI0vGenp6QkJCRpePz8/NaedW7u7vCwsI+Ys7y9Pvn5+c/Ys6srKz5+fnj4+PV1dXOzs62trawsLCCgoLW3vUvLy+Dmt+fsedMbtFKSkrb29swV8u6x+5mZmakpKTU1NSRpuMxV8pohNlMbdFng9irdSDNAAAABXRSTlPvIL+vAC9A4IoAAAaTSURBVHja7M/JDQAwCAOwcIkP+8/bjhFQvIGR5ujtwhKGqfUGhjjw+BNH1wm4EmlFyCjCRhE2irBRhI0ibBRho8hjtw522wiBMACf/tWMh5AQSI2UFsGB0pjDvv/bdYK3llu72kSOIx/ySwjWDFp9Aou9tbwBcvd4h9vPKuR5O03T/e1TViHq0GxxLqYUrKYmrOf6kMdp2m120/SI0/hGxKsUdqOLFu9KLB8L2ajh569p2uAkiYLJnd8I0e5d4fDxkKfpLGRmAMVlSOgenlmHRcpoEoQrkIVdGwIhfTaBe8CYdGUUuKpDg7HawDuR16Hkfb0kwCXUWRy0XsqFENydhXTBPtSqTVQtMyzZ0Yhj0E44ehoQy1Lg9HE/6ZcC5vhAAYmC5QBHzluqpQusUkFh7IyQe8g8x7mZq0DYwYjI/n0iUEE5QBIMJUP++GgZCjl3N+q1acGYdAzpObsGJ4DJ2ToagANEoFSbLdkL/yMv5yEdJlWy+/c5IFM6gmjTH44hmV5zgGCBBAKPmQHJ0oRPIWFUpMsgLz/OQmozgF0gs4zxMeTMjnho/oXMHSLQDEiijHAK8WSgucrRyo1jnBfIA/nIjEw1ugMEwjbQAukF0lKU+BeEbdQCT8HWOiCefBw7IlrfbaU9xLRuk5irQJDnxmIB9lBJb5KBwFzZgiO0jXMidbl0BMY1rmbUjxbBosvMfnVBncdG98pZ62eU3mbxcDPGkWOPiyD3T+MeuULefbtcBtF8QdYS7SdBnncLZHfr378rkO30J1vcdhSyfrBGNrjpKGRlQy7Zkm8G/833z4RMx/nNjh28yA1CcRw//cjlNX2szEPBIpP3KD1YMLTb//9Pqyaz2zCdPQxswINfgkjUwAdvwV1KOJYy/msJ2KJ4AHjajK4bCEfUpI0pJkQnbZbaI2l/2yBESLOvr0lQu4YSgURxhxC1QVJq43mQnwfHr4eQhT0nMm8U3LzfQX2cN8NUPAeY5zUaT8J1z/s5VtsgXFfbN0oWVl5Pg/w5QH4/hJDEElejKOLwDwIU0hVzSFEWbq9mL3W8nYuMbXsiWbRuheXsQSWdBXk5QL4/vhFbSkRWvodw1FynieewQYKFsLydy7pDpK4qnEBzUzo5C4LLu+OC+ziLJJ6k3sgKv4hLAFaPUCGVJfNcp1Sw8sZTiLxBqEhskMhYDZYT52iSGadBcPnQAWXmKZvqJKoqCAxAjHWGC3y9TQMHA9k1BVbaz0Vg4W13uq0aZyx1/UQIvnx9/fb64wVP5fBMmYhjn/9+Fc9E6q/jJ/aA9NqA9NaA9NaA9NaA9NaA9NaA9NaA9NaA9NaA9NaA9NaA9NaA9NaA9NaA9NZnQP6ybwYrjoRAGD5ZB0GEAhGUXEUbug+Zmfd/te1te/9YGNLLHJzuId9NByf1paJWkkrSzrrCUUlMnVbP4Zy1UYK4lJyxYriInqgiG8/ZUyVo1WO2v4YiF+wEa4aKICLwsWD+Tg+m2K0KVMGCmAlAfpgIIgLp4fGqjcWudpwcUXi0U0nug0Us/SUvvNg2ZFMHmrlOFyUJFGJdzXWieszWFbenmIeK8PbwqWmJ4oefw3TX/rH3USxEukmgi9vAbFZhmAgC1u0go4clYBp+YE9d2TeJmUXaYsC/HSUyt882o2OFoSQHwBOVusEMuhKsAgut+IEiUTyg2UYIjFvbqX9JTtP8L/pAMmlxy1AcKGJXtBD5RFMR7oKvJ5vEtQdz6vaExwk4ukTBC6IgDtkrFZUk+ZluLmKh7KJivWJ+SsQiD18QkfMSmR53glqrkh4b5nYs0p99ZxGpZ6a5vsh+i1xGJDmA2HA3B3UdEU2AOw9zeZE6m9TVRRLVUK4kYjRAbKLm++49kn/+1DIBF1p/l+Oe73hVIxa7kgaKSA+QsWNQax3Wnh+ruEzpSBF45H4j6a5E7hA5WxQwBPdhInGSFZ8s6GFV1CsKrYQoD3M7VuQT1XiLb041lC5H5Y2VhyAPFcm4CAUa0zEjO4c/JvBps7rP0BomUqgjNJ+KeGtnEgk5aMGfvb/RRjBDRewzERwBwKlDHO3A4xwirclc1H+gW3Ub1ViRfOvwiMzTCt7PHoEFdMt8tq8VEidhcYhhNlG9vx/5FbxFzsZb5Gy8Rc7GW+RPe3dABAAIAjAQAY/+ja0xuX2DJRiNITSG0BhCYwiNITSG0BhCYwiNITSG0BhCYwiNITSG0BhCYwiNITSLQuKuMJE7ls21Z6Ldp+Z3kacfxIjiadZuT4oAAAAASUVORK5CYII=", "public": true}]}