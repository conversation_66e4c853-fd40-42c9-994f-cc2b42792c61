{"fqn": "simple_rainfall_chart_card", "name": "Simple rainfall chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_rainfall_chart_card_system_widget_image.png", "description": "Displays historical rainfall values as a simplified chart. Optionally may display the corresponding latest rainfall value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'rainfall', label: 'Rainfall', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'rainfall', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Rainfall\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 8) {\\n\\tvalue = 8;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 8) {\\n\\tvalue = 8;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#7191EF\"},{\"from\":0,\"to\":2.5,\"color\":\"#4B70DD\"},{\"from\":2.5,\"to\":7.6,\"color\":\"#305AD7\"},{\"from\":7.6,\"to\":null,\"color\":\"#234CC7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Rainfall\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:weather-pouring\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":1,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"mm\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "rain", "precipitation", "downpour", "rain shower", "drizzle", "raindrop", "cloudburst", "rainwater"], "resources": [{"link": "/api/images/system/simple_rainfall_chart_card_system_widget_image.png", "title": "\"Simple rainfall chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_rainfall_chart_card_system_widget_image.png", "publicResourceKey": "P5tlBu2cVuK75C6D7bFlWjlw76c0qk7p", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAgVBMVEUAAADg4ODf39/g4ODg4OD////g4OAjTMchISHj4+M9PT3x8fGsrKxYWFiQkJDI0vHHx8d1j9zj6PitvOp0dHQvLy9aeNWRpeM8PDyCgoKenp7V1dXy9Ps+Ys5mZmYwV8u6urpKSkrW3fXk6fifsedLS0tohNm6x+0xV8qDm99MbtG6RkKAAAAABXRSTlMA7yC/r1EOHTEAAAQeSURBVHja7M9JAcAgDAAwytEP/v0OGdAlDtIAAAAAAAB+p4/I181+HrHX83b0Ngs8zmS0XCVElUiKXEbkNh+79bJaIQxFYXi0YN8S9iABY0B0cN7/FVsF5VApdNZtm3+U6Ogj5DIg0RqQaA1ItAYkWv8AwoqfxYjQdxDfiAq/zXPOds156ddQyIAjySgJv9H3EK6mXF64yrUsk5wSTZepVUZYCC/V8FlyXBABtDbAbQXUFMZuDE/VdP/IASEsbcWeSeE3CGpGq2VKYHKQbJNoniip0fI5jAdZt1NkSzoh1eZUGdlhpAckw6mjCGAGJosHsQ3onVcYmE4IpY060BepxAdk/+sHhNtLIkJ0akjzvHhF3k6IHFtEpandIOnFIVcEbKh9p2B+3yN5F2S0G0QKLCQE8AnUk+HLqZXQiBbyL5CZKNEcEqLuE1xxTxn39Pdv9xNyay54VP/g0fiwBiRaAxKtAYnWgERrQKI1INGKDNHu/Q9ANFcikqwPh7BQW30tJPxoiMrUsec1PRrywZ657bgKQmH4aoUFiudjRZ3a03Te/wE3MILd7phx146pid9F8wterC8slNgDUpuCLYswbmPKNyxSoWuzh852RVRnGSh6mxOhpvwLhwHONyZySBFZoIXw8DiOwaZEPOTuocBKxgQpDDjovbWI45al64CBskYNFph4fFQ5T39FJPI7eAEVwzRFVo32N03k4AH+4gODp0X8e+sDhMQAhnMrr+oclkJZEQAEhf0vlSXTa1cw5ykRkWey2keRGnpyQtqsJuQMCyn7BUhL0LgYwCQBMqqEPM5Y484XIRofDCEJ+3QiKp6+yKeAZVy46X/Hvsqn+SiQJzyVP41MdK5IG/q+FrHVR6axvmNsRkQkIIpvkUx+7H+PRCBuOndqYgL7gA2w1Av0wzagLmfHUt/jFqk3t7UAHkVCcjXRz3MAPduZqTuRnLtaNWCkHeNW5kxkhEy3oIuVfeqysmTowWyoOtvP3uyDyKm1cVCrTSBteFUObZjJ6rUIyUKp9akmJlswKQYnjikP4H9wi+YJkdhUbekIia1IpG+uhb4QSiQHEK1eM9/eOIY1sATHeUKkHhdzqklml0YnXbzeOWb7XPWE0L01fbpdznwRXdnYo45+Ehkmpk63q4tkJIRHxJfyWCjCj7C2iD9ekCtpI1goQrFcWcRWZLmTtoOFIs6loGuLnOxzpwtvAJCr6yUi2gM/YG0R+8YAeSxRw4bsaRHKUhdgZRFR2+dnTrKXiFT9F6sVRCYQ8Arc9N/9scWP2B4ee49ti3iYOH3ctkjj2bhtEcsusotshV3k3dhF3o1d5N3YRd6NXeRPO3dCAAAIAjAQP/pHtgbiXYMVWDVCqskYp4WM1WO5s/tMkGLufN1YMwAAAAAAAH5zAZS0p15sQX4JAAAAAElFTkSuQmCC", "public": true}]}