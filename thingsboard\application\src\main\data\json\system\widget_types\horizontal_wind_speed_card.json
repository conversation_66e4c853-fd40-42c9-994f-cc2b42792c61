{"fqn": "horizontal_wind_speed_card", "name": "Horizontal wind speed card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_wind_speed_card_system_widget_image.png", "description": "Displays the latest wind speed telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'speed', label: 'Wind Speed', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Wind Speed\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 16 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 26) {\\n\\tvalue = 26;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:windsock\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0.2,\"color\":\"#7191EF\"},{\"from\":0.2,\"to\":3.4,\"color\":\"#5B7EE6\"},{\"from\":3.4,\"to\":8,\"color\":\"#4B70DD\"},{\"from\":8,\"to\":10.8,\"color\":\"#305AD7\"},{\"from\":10.8,\"to\":17.2,\"color\":\"#234CC7\"},{\"from\":17.2,\"to\":24.5,\"color\":\"#F04022\"},{\"from\":24.5,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":0.2,\"color\":\"#7191EF\"},{\"from\":0.2,\"to\":3.4,\"color\":\"#5B7EE6\"},{\"from\":3.4,\"to\":8,\"color\":\"#4B70DD\"},{\"from\":8,\"to\":10.8,\"color\":\"#305AD7\"},{\"from\":10.8,\"to\":17.2,\"color\":\"#234CC7\"},{\"from\":17.2,\"to\":24.5,\"color\":\"#F04022\"},{\"from\":24.5,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal wind speed card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"m/s\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "wind", "speed", "airspeed", "flow", "gust"], "resources": [{"link": "/api/images/system/horizontal_wind_speed_card_system_widget_image.png", "title": "\"Horizontal wind speed card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_wind_speed_card_system_widget_image.png", "publicResourceKey": "n7MQH8hjX7fdGQ6x8XFSioHpNLB9pEdq", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAn1BMVEUAAADg4ODf39/g4ODg4OD////g4OAjTMd1j9zk6fjx8fGtvOrOzs7IyMiRpePy9Pvj4+O3t7fI0vFaedU+Ys7z8/NYWFj5+fnb29swV8vCwsI8PDy8vLzn5+eesefV1dWenp6QkJCDmuCCgoJmZma6x+2rq6tohNkvLy90dHRLS0shISHW3fS6x+7W3fXU1NSRpuNMbtFMbdGsu+qtra1mE7QcAAAABXRSTlMA7yC/r1EOHTEAAAN3SURBVHja7dprU5tAGIbhJPpmOW2AcAgg4ZRzGrXa/v/f1neJLbY2FJypg5nnMgSy5MPeLMw4oyMAAAAAAAAAAAAAAAAAAAAAAAAAAAAA6G1yM55+drcT7hgb9OkZ48no9go6uORmNKWrML6WkClCBgYhQ4OQoUHI0FxtyIneMgpyUnKd0qCzkDenKB36Q5HTB2oPERW9tTdWIYUO9xgGdxlfeOwo3Tsy1Od6c9T+IKmfOSkPZs1uxu05XbYxO4VolGWbk02vHWQYGnfEP4e7rXNcq5CwcBzny+FopMew4LGVwSdlr4oqFqRooqb/qvB4Fpf5WqeQ+N4TLPZ2M3OuU60ow7JcqxBnW662pEKc7V3phLTahmWxXq23e7mmPitiPwlGiieyVyuyiYVoC9mI+44r4vH2IITnPQsxJyXfF3Jf/BZirIi+OEdauWEh89VBSnmgdY8Q3Z+ZLyHPvGvMvCxrQmzdtjcbnei0OVFN84npfJW7hJAQ30hXIbV9zq+XkDxc73moDMOVs18fHTfkWytUt9Z6L6kP/SVExGTTa2YTookqFiKeq9ukqsvUOdsT7Gu3EG22UyF/YfzcOaFRHzTbe0JsEfNcd/qFkHhm8kSezCyuZ2Oq969i9zDnz20hPj8jvi9E7O+qbGNTG6OkFh1DeBdrlS/8CyGz+ou6OvzGA5763k49J6a2aV+RSpuZDzr9Z00I2TpvfH3bQppDfqNM+Jq6yO0hH6IJaWbcJcQUOjHTV8/IwELmpppb1S3k2aMznVvMviEB/W3Q+HMgeF9IJnb87os5j/wr5HSe/JNvE83ErG+IW8fwy4iotljQYmqpgSktiGi5XdIiis4neCDoE2L7Qpt5wldJWnsIb3VrJTwz88X9O0Ly3IqktPgooSRK0oWbWHkurdSwUp65tQzSNKLAqk9YMu0RQronhPB0FVL9I8TfkWJX6pePjN4R8mi5gbSW1jlk+RjxMY8tIyuRdcg0ijhEuksZcG4SUB+2bp/31G7ePBY6h7eFzHV667sVJZabyyRQK2KlUeIGrrR4zEiX0iIVslArsky/qxN5ktBHUyG9RUu67FFG1MUQQhaLtpMBdTGIkCFCyNAgZGgQMjRXFDKmqzAd3VzHn6dvr+cfBkaT2+lnN76ZjAAAAAAAAAAAAAAAAAAAAAAAAAAAAACgrx9YOdsEpKcg1gAAAABJRU5ErkJggg==", "public": true}]}