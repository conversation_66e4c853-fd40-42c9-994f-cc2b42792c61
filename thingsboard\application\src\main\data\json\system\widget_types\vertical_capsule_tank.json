{"fqn": "vertical_capsule_tank", "name": "Vertical capsule tank", "deprecated": false, "image": "tb-image;/api/images/system/vertical_capsule_tank_system_widget_image.png", "description": "Widget indicates the level of liquid in Vertical capsule tank.", "descriptor": {"type": "latest", "sizeX": 4, "sizeY": 4, "resources": [], "templateHtml": "<tb-liquid-level-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-liquid-level-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.liquidLevelWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.liquidLevelWidget.update();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true\n    };\n};\n\nself.onDestroy = function() {\n}\n\nself.actionSources = function() {    \n    return {        \n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false        \n        }    \n    };\n}", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-liquid-level-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-liquid-level-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"return Math.floor(Math.random() * 101);\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"tankSelectionType\":\"static\",\"selectedShape\":\"Vertical Capsule\",\"shapeAttributeName\":\"tankShape\",\"tankColor\":{\"type\":\"range\",\"color\":\"#242770\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E73535DE\"},{\"from\":20,\"to\":null,\"color\":\"#242770\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E73535DE';\\n  }\\n}\\nreturn '#242770';\"},\"datasourceUnits\":\"%\",\"layout\":\"percentage\",\"volumeSource\":\"static\",\"volumeConstant\":500,\"volumeAttributeName\":\"volume\",\"volumeUnits\":\"L\",\"volumeFont\":{\"family\":\"Roboto\",\"size\":14,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"volumeColor\":\"rgba(0, 0, 0, 0.18)\",\"units\":\"%\",\"widgetUnitsSource\":\"static\",\"widgetUnitsAttributeName\":\"units\",\"liquidColor\":{\"type\":\"range\",\"color\":\"#7A8BFF\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E27C7CDE\"},{\"from\":20,\"to\":null,\"color\":\"#7A8BFF\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FF0000DE\"},{\"from\":20,\"to\":null,\"color\":\"rgba(0,0,0,0.87)\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FF0000DE';\\n  }\\n}\\nreturn '#000000DE';\"},\"showBackgroundOverlay\":true,\"backgroundOverlayColor\":{\"type\":\"range\",\"color\":\"#FFFFFFC2\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FFEFEFDE\"},{\"from\":20,\"to\":null,\"color\":\"#FFFFFFC2\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FFEFEFDE';\\n  }\\n}\\nreturn '#FFFFFFC2';\"},\"showTooltip\":true,\"showTooltipLevel\":true,\"tooltipUnits\":\"%\",\"tooltipLevelDecimals\":0,\"tooltipLevelFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipLevelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.76)\",\"rangeList\":[],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"showTooltipDate\":true,\"tooltipDateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"tooltipDateFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipDateColor\":\"rgba(0, 0, 0, 0.76)\",\"tooltipBackgroundColor\":\"rgba(255, 255, 255, 0.76)\",\"tooltipBackgroundBlur\":3,\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Liquid level\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"titleFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"1.5\"},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"showTitleIcon\":false,\"titleIcon\":\"water_drop\",\"iconColor\":\"#5469FF\",\"decimals\":0,\"enableDataExport\":false,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\"}"}, "tags": ["reservoir", "container", "vessel", "storage unit", "cistern", "canister", "vat", "basin", "repository", "bin", "hopper"], "resources": [{"link": "/api/images/system/vertical_capsule_tank_system_widget_image.png", "title": "\"Vertical capsule tank\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "vertical_capsule_tank_system_widget_image.png", "publicResourceKey": "VGC8MEmSvdENIMzvZFaxtXSACz7cRua9", "mediaType": "image/png", "data": "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", "public": true}]}