{"fqn": "horizontal_temperature_card_with_background", "name": "Horizontal temperature card with background", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_temperature_card_with_background_system_widget_image.png", "description": "Displays the latest temperature telemetry in a scalable horizontal layout with the background image.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"thermostat\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#224AC2\"},{\"from\":-20,\"to\":0,\"color\":\"#2B54CE\"},{\"from\":0,\"to\":10,\"color\":\"#6083EC\"},{\"from\":10,\"to\":20,\"color\":\"#F89E0D\"},{\"from\":20,\"to\":30,\"color\":\"#F77410\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#224AC2\"},{\"from\":-20,\"to\":0,\"color\":\"#2B54CE\"},{\"from\":0,\"to\":10,\"color\":\"#6083EC\"},{\"from\":10,\"to\":20,\"color\":\"#F89E0D\"},{\"from\":20,\"to\":30,\"color\":\"#F77410\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#DE2343\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/horizontal_temperature_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal temperature card with background\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"°C\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["temperature", "weather", "environment"], "resources": [{"link": "/api/images/system/horizontal_temperature_card_with_background_system_widget_background.png", "title": "\"Horizontal temperature card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_temperature_card_with_background_system_widget_background.png", "publicResourceKey": "kqKHODzwRig7KUnsGPD0Zb60dEsVSaVj", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/horizontal_temperature_card_with_background_system_widget_image.png", "title": "\"Horizontal temperature card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_temperature_card_with_background_system_widget_image.png", "publicResourceKey": "4ShFE8qoLMGIT60iKikufhR0gsMziZMX", "mediaType": "image/png", "data": "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", "public": true}]}