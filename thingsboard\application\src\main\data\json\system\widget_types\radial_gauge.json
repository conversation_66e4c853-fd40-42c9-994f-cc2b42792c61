{"fqn": "analogue_gauges.radial_gauge_canvas_gauges", "name": "Radial gauge", "deprecated": false, "image": "tb-image;/api/images/system/radial_gauge_system_widget_image.png", "description": "Preconfigured gauge to display any value reading. Allows to configure value range, gradient colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 6, "sizeY": 5, "resources": [], "templateHtml": "<canvas id=\"radialGauge\"></canvas>\n", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.gauge = new TbAnalogueRadialGauge(self.ctx, 'radialGauge');    \n}\n\nself.onDataUpdated = function() {\n    self.ctx.gauge.update();\n}\n\nself.onResize = function() {\n    self.ctx.gauge.resize();\n}\n\nself.onMobileModeChanged = function() {\n    self.ctx.gauge.mobileModeChanged();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'temperature', label: 'Temperature', type: 'timeseries' }];\n        }\n    };\n}\n\nself.onDestroy = function() {\n    self.ctx.gauge.destroy();\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-analogue-radial-gauge-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-radial-gauge-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temp\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.7282710489093589,\"funcBody\":\"var value = prevValue + Math.random() * 50 - 25;\\nif (value < -100) {\\n\\tvalue = -100;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgb(255, 255, 255)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"maxValue\":100,\"startAngle\":45,\"ticksAngle\":270,\"showBorder\":true,\"defaultColor\":\"#e65100\",\"needleCircleSize\":10,\"highlights\":[],\"showUnitTitle\":true,\"colorPlate\":\"#fff\",\"colorMajorTicks\":\"#444\",\"colorMinorTicks\":\"#666\",\"minorTicks\":10,\"valueInt\":3,\"highlightsWidth\":15,\"valueBox\":true,\"animation\":true,\"animationDuration\":500,\"animationRule\":\"cycle\",\"colorNeedleShadowUp\":\"rgba(2, 255, 255, 0)\",\"numbersFont\":{\"family\":\"Roboto\",\"size\":18,\"style\":\"normal\",\"weight\":\"500\",\"color\":\"#616161\"},\"titleFont\":{\"family\":\"Roboto\",\"size\":24,\"style\":\"normal\",\"weight\":\"500\",\"color\":\"#888\"},\"unitsFont\":{\"family\":\"Roboto\",\"size\":22,\"style\":\"normal\",\"weight\":\"500\",\"color\":\"#616161\"},\"valueFont\":{\"family\":\"Segment7Standard\",\"size\":36,\"style\":\"normal\",\"weight\":\"normal\",\"shadowColor\":\"rgba(0, 0, 0, 0.49)\",\"color\":\"#444\"},\"minValue\":-100,\"colorNeedleShadowDown\":\"rgba(188,143,143,0.45)\",\"colorValueBoxRect\":\"#888\",\"colorValueBoxRectEnd\":\"#666\",\"colorValueBoxBackground\":\"#babab2\",\"colorValueBoxShadow\":\"rgba(0,0,0,1)\"},\"title\":\"Radial gauge\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"decimals\":0,\"noDataDisplayMessage\":\"\",\"configMode\":\"basic\"}"}, "resources": [{"link": "/api/images/system/radial_gauge_system_widget_image.png", "title": "\"Radial gauge\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "radial_gauge_system_widget_image.png", "publicResourceKey": "QW7U6ZV1m7bYQv8EC3pPWl9l2VimCi9u", "mediaType": "image/png", "data": "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", "public": true}]}