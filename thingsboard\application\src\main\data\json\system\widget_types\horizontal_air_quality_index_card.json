{"fqn": "horizontal_air_quality_card", "name": "Horizontal air quality index card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_air_quality_index_card_system_widget_image.png", "description": "Displays the latest air quality index telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'air', label: 'Air Quality Index', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Air Quality Index\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 320) {\\n\\tvalue = 320;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:weather-windy\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":100,\"color\":\"#FFA600\"},{\"from\":100,\"to\":150,\"color\":\"#F36900\"},{\"from\":150,\"to\":200,\"color\":\"#D81838\"},{\"from\":200,\"to\":300,\"color\":\"#8D268C\"},{\"from\":300,\"to\":null,\"color\":\"#6F113A\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":100,\"color\":\"#FFA600\"},{\"from\":100,\"to\":150,\"color\":\"#F36900\"},{\"from\":150,\"to\":200,\"color\":\"#D81838\"},{\"from\":200,\"to\":300,\"color\":\"#8D268C\"},{\"from\":300,\"to\":null,\"color\":\"#6F113A\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal air quality card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"AQI\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "air", "aqi", "pollution", "emission", "smog"], "resources": [{"link": "/api/images/system/horizontal_air_quality_index_card_system_widget_image.png", "title": "\"Horizontal air quality index card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_air_quality_index_card_system_widget_image.png", "publicResourceKey": "P4dN0033AhtHUkLkhRA5VboVyayg96tc", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAmVBMVEUAAADf39/f39/g4ODg4ODf39/////k5OSAwyzg4OCg0mHy8vLQ6a+srKyQkJCQy0bf8Mrv9+Xj4+POzs5YWFiv2Xv5+fn3+/KIxznb29u2trbA4ZXV1dXHx8fCwsK8vLzn5+fIyMi43Yio1m47Ozt0dHTn9NjH5aNmZmZKSkrY7L2enp6YzlOCgoJLS0vt7e2dnZ3U1NQhISGmKTEOAAAABnRSTlMAIEDfv1C6kOEmAAADrUlEQVR42u3aa3OaQBiG4bRJn7yynEEOAh7xrEna///jykKrtjZK2iZDMs/1QWGXD3sP4OjIDREREREREREREREREREREREREREREdFb+vTu3Wifb+/fO//2S9VxH+HdU/efb+4UPoDo7qaHD6HHkI5hSNcwpGsY0jUM6Zo/hmSJGB5O+FvAylErbAsHlpVbUGgU+c/REa57g5CFGDNxYhzNS4U8V0oBVlisttWGX+Up2HZeFGvlQ0/ZllK+zrYtvV8f1IpnujgRuxcOcJfZJj6OXQzBAjDFxFH4tK2WaZdPwKpAHhYhQn8/Lwu7GrXLQan2lg6xwlWIh9Xa8h9W2yK05q0yDBEDB+4sMfCbmSSHYyvOBnqFRpt7xD0NGc1VqEMs3VTAX9chxTa0dIhfzW0f/DrExje9a833Vol9NXid54hhLvCDm8gh68ATEReaI7uN26/2roZ4bm2zEw8LD435OiyLHyF7G9Y81yEP+b4JWcN/CHEIWesQuygwL9uE9CXDkWksl2chpsxkWm80V/xUjKsh8tMShkgGzS+Bp70OqXfC0lfrVemvVuutDlFrG6F1CMF8FVp5OK9qihBXeeLA83DCPAvZySJJdIEhS1RikfhayMZsxIh3mSEezqknu3r5ZcQv1dlRaEWvZyfiLC6ELGSHaZ3gHK4w90LIuUxctPK0xd/KRIxsJkn8fEhfTLhivCzES+SEg1fXl35zzTwf4ugLIxHvRSHxtH+UxXhVx3s9k+mzIaYk1VocyXTABlrShHTqK8pSpldCZtJIgGlz2EIE10JmxonNpRtXPf4+oPAX3PoTdScm4P0xxBNZeBV9FlxJvDqtfz3EOUpkgdqgiVF4TFGLIkS9QA/0EAEYW2NEadpMRE15W31xMkOcemXnIcfdTGb6RWZTRxzvQsi5jZiHkDwP0skkqLaGGKbDUTQYBnk+CUaPwSgCgrEajVKooJ4IJiO0Fk9FxKhXNvtTiPNjFW79yWY6UpniBSE7xxD3EPI1GKhJMA6akPHXtNquxsZpMJzUIb00rUImg/FEVblDhfZiL27e0YbnuY7M4va/R7IkWaJhBekwGOSTodJnJBilw4EaTIJq7HE0ngTQIZE+I+ORpSfy4RCvyXOm/+eHVTrG875OUryNfw+JokuTCm10IqSLGNI1DOkahnQNQ7qmd3P3Af5mB9Rd9cDAB/ijPaoeGLj5cuvfv3e3nz/SQzVEREREREREREREREREREREREREREREb+Q79h0E444zJWUAAAAASUVORK5CYII=", "public": true}]}