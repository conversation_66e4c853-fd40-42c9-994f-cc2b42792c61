{"fqn": "indoor_co2_chart_card", "name": "Indoor CO2 chart card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_co2_chart_card_system_widget_image.png", "description": "Displays a indoor CO2 level data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'co2', label: 'CO2 level', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'ppm', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'co2', 'ppm', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"CO2 level\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 160 - 80;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 400) {\\n\\tvalue = 400;\\n} else if (value > 1600) {\\n\\tvalue = 1600;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"ppm\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":600,\"color\":\"#3FA71A\"},{\"from\":600,\"to\":800,\"color\":\"#F36900\"},{\"from\":800,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 160 - 80;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 400) {\\n\\tvalue = 400;\\n} else if (value > 1600) {\\n\\tvalue = 1600;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"ppm\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"ppm\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"CO2 level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"co2\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "co2", "indoor", "carbon dioxide", "carbon emission", "carbon footprint", "carbon output", "carbon pollution", "carbon capture", "carbon offset", "carbon reduction", "carbon neutral", "climate gas", "fossil fuel emission", "carbon cycle"], "resources": [{"link": "/api/images/system/indoor_co2_chart_card_system_widget_image.png", "title": "\"Indoor CO2 chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_co2_chart_card_system_widget_image.png", "publicResourceKey": "DSsTJwpXXrmRPVGbCr2KfCUqSjhxlvVO", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAt1BMVEXg4ODf39/g4ODg4OAAAAD////g4OA/pxrs7Ozz8/PCwsLOzs7j4+PV1dX5+fl0dHTb29sgICDn5+fIyMiQkJBYWFg9PT3Hx8e33qnn9OO8vLyHyHDn9OI7OztvvVO2trYuLi5Xsjbz+vHP6cadnZ2srKyCgoK6urpKSkqf04yqqqplZWWTzn5LrCjb79SwsLB7wmHD47ir2Zuf041juEVjt0XD5LiFhYWTzn/z+fFTU1Pe3t6VlZV3MWaiAAAABXRSTlPvIL+vAC9A4IoAAAjESURBVHja7ZyHeto6GIaT037Gv2R5yTaEYFZYgTSj+4z7v64jGTsmjDq0BUTqtx6yZJ7qrX4NuzxcvL/868I6d95dvr+4vLBx9pDSeEd4A9gqrPAmuHgrIlYtYhi1iGnUIqZRi5jGHy7SpIqcao4uMo0TzwF5yWwKjPRREThYQ+WckmoRJ2hiEsNb0DRwpik1+0W1HdIbkU6dgcgoBpwpAgHEI0dApEsRJ5klIppg4jmzWdo0UETEC1IbciIPCkfX04sASqKlyMTDaKGaZ9YcL1T4GSjSHMOJImcU5S2yAKhJup6LKJNZinhpHE8Qj1PolGegiPBiWsQ0muVefUf3EVVpp9+EaodcRLUUOYhmngo+lTJQJCeOitjqJzMHTtpPRmgGSZKITITSJJlnQwEtVMpcEaes2DJFhJcQraROzx8+sxtILWIaf46Ia+PV2O76h4WLCo4lQm2/vaUygrCBBA/Xs0JUcDQRbsO2BWwrZJaQrs8FmOT0IACXSckkiDOymRThPORtZit3po5t7qs7HrhKCLQlNji2CCN9QOj7oTrZLHxAGyzLJq6SnNpwZeirvB54qPJgSzu0QrSzT7t22FbJCo4gIkKENnNLEXUqRZgSkQB7IeISF76VixAjff8mxxZxe5zBZ72liAoUnokI/ixiMUmZSDssWkQKtyf5skXasmeESK6zkXJ/dJeb79XU80gtYjx/sgi5KLDVbsj/CVeJWBzwGTRiZY7MM3RBT50NoErE77lgbfjcgoDgXItw7oYhfPhKRMiHvICkuudkVItwTrwHn/TC5AGSlIhtc9/PJ3iVmRcwsjhORrVIKBn10PN7y0WIFiGwQiT0URQwhGaLCIme27NWRHzp21JkIm7besgLhJAmi5DrurBBttqhd8AmGwhtgv5TFrih5WNvDJxHiHEXr8dckf2oRWqRc6EWMY1axDRqEdOoRUyjFjGNWsQ0apEtDK+uroZFsmRYFH9ttW6GKDBY5K7RaFwj47FR0oKm011eXV9hibkinUYp0l0X6QyKy0FuYq7IYEVksC6iM7p3d9kJGcaK3DZWRJb1L2mpjE9FzN1AY6pIp7Ei0lmvbleFFDR/l7KGinRVBZ8reaNS91hBXT+uKL0el9y1L4YJ56AiLVW/zrPIk0oNUXKvrm/LoW2PMZhsBTKcOFAOXtwExuNDiehB6b4U+dhofMB9q/X1Cs8t1Cq70h7jlu0oivZIHIw93SLRSEwPI3KtFUqRWyXypaHpZpVuvRS5x6txfM/39bnpAEpkMvMSQjQZ0UFEdGBdlSLaq+DD/ZrIfsOWzxdCqPM0amYi3lhv2Td6DyCiA6uFFZFuNmvcXg/UeTD8FZFv89logRwlMvcoUUpRhPEBRO6WY1Ip0mldf8zOOr5uf0WEZkHqIGdBwCQe6WwRid8vogWuCpHNou6viIA852jzyEDP2ttFoJrkw24RsyZEXbWu5rPuEd3u09pw1tg+/Jon0m2ssL7IesxEOlsmRPNErjdEWoPB4L6w1KGFD6sL/C/IME5keJWjA+hRPxHqxG3Z2XOf4Yt+dHPzFX/ftL6ic9+6umlVRxtJeawnxLKzf8gnwqHuNq188XX3Yhk/ePp43fn89L3Verz5/P2mi2oYfNkmwZkrhVTHI4h8bCgeb++UUL7W/aJTywerx2WIqa2jljXXrRbU42+ViMW1CLdJMFg+02k6ggg+NVaebDcfdUuR+7tCpBpdeVuLWMcTwVN3udL6NCwKr5cZ3/OMz3fXnzqD2+7V60UEuGTEBNMp+IcLLeh3P6XW09rbn+HNakZ3eKXkhkPoTX0Ur0G3AjPsBd2ddv1o1PBrBrWIadQiplGLmEYtYhq1iGm8XRHiAj5nocU5ODfi+/s/J+LaXB05JDhJdTwXNkOLuNp9cCXCwHAubBdhpFvEZWhDw5iPk9BsErbjs3aliA9Xueg+IvI+wnEa5kE/mC1GTWyD/8SoZeMkTIMIYj6Jg2DmjZ2NSp3N8Cv6URFhI28WxGM6z3nE0R4lwkv7XvMMRZwk2uj6XpB4zsFEoikqociLovG0uY/HBJvQPA5mY+cgIk4QTKhqFE1SbxHPkiB6vYe3q2SUBovxAURiz0kT54eVivsR5d13VDFOeTnJDLtpemky/t0i00BJREH0g6jqx8+eo774cTssChOq+GvF7xZZhkBTNcrOqGqiZJIQduOlJ1vGj9OlgOMF0e6oKqGZh52M+86BRcjeRfqtSI3Sf8Ra4b/f+v/wtTze/8/egUgjew+oQmTPBinTjpeMd0ZVyXznP/siPtkTopPMscKo7zm7oqq6m4xS52QiUbompobFtbFqE0on21ckc5xKxAnmG2rBhIqo2oUImtuHv5OJeDE20LPjrqgqQ9DZ0rjOUURknDNebRCxNd4CHVU/JI4rGvdwIiJaohcHRS09b9fzaWVMbixVksmx32vRPA08p/g1zZ9l2hdrgUUHEvFB3ILN7S1PldM4iKd6tYjXUT0GN/f4N9nvUVf0wFzmSrS3vnxwvH7iBQ5+Hkq95jPTJML+8FeIkM9evNdqM39dZZ5G+BWcNChJsTc+Y68QYX7PlkpEnvULOgVR2xWhCiyrjXNhR2cnIHSz/Vww9i1KLXLu1CKm8XZFVi/XBt/QXuaWl2TIz51tE2G6qiHp3e3ZKr38qSMCbB7CVlvbhb4E7yH0s7uXn9C5R6VaRM3oamfUViKhr7IerJ7LLBZaUlp2z+UWd5WIz7WIK0OOnjWHtGSIY1ItIngPXGqDXIRlK0g/JMm4umhzZkFlcd9f3h3y7CbiOCLVIq5qD/joZSK2yC6ZOoqQ22Em4hJpEXeuRCyfVHNB3aSSOBrVIoy5QnJYXCAUKkOqRE/tJIVNklvgNnHhApaLMNShJQR8/qBvwOnQItUwVCBP/h2J3zWPnPzH/N/uhHiu1CKmUYuYRi1iGrWIabwhkQu8CayLv06+3vsd0LuLy4s3YEJK4/3lO+vcufjr8v3/Kxvvi4dhHsAAAAAASUVORK5CYII=", "public": true}]}