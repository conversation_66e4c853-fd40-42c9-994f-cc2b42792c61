<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="400" height="200" fill="none" version="1.1" viewBox="0 0 400 200"><tb:metadata xmlns=""><![CDATA[{
  "title": "Long horizontal pipe",
  "description": "Long horizontal pipe with fluid and leak visualizations.",
  "searchTags": [
    "pipe",
    "horizontal pipe"
  ],
  "widgetSizeX": 2,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "fluid",
      "stateRenderFunction": "var fluid = ctx.values.fluid && !ctx.values.leak;\nvar flow = ctx.values.flow;\nvar flowDirection = ctx.values.flowDirection;\n\nvar elementFluid = element.remember('fluid');\nvar elementFlow = null;\nvar elementFlowDirection = null;\n\nif (fluid !== elementFluid) {\n    element.remember('fluid', fluid);\n    elementFlow = null;\n    elementFlowDirection = null;\n} else {\n    elementFlow = element.remember('flow');\n    elementFlowDirection = element.remember('flowDirection');\n}\n\nvar liquidPattern = element.reference('fill').first();\n\nvar fluidAnimation = ctx.api.cssAnimation(liquidPattern);\n\n\nif (fluid) {\n    element.show();\n    if (flow !== elementFlow) {\n        element.remember('flow', flow);\n        if (flow) {\n            if (elementFlowDirection !== flowDirection || !fluidAnimation) {\n                element.remember('flowDirection', flowDirection);\n                fluidAnimation = animateFlow(liquidPattern, flowDirection);\n            } else {\n                fluidAnimation.play();\n            }\n        } else {\n            if (fluidAnimation) {\n                fluidAnimation.pause();\n            }\n        }\n    } else if (flow && elementFlowDirection !== flowDirection) {\n        element.remember('flowDirection', flowDirection);\n        fluidAnimation = animateFlow(liquidPattern, flowDirection);\n    }\n    if (flow) {\n        if (fluidAnimation) {\n            fluidAnimation.speed(ctx.values.flowAnimationSpeed);\n        }\n    }\n} else {\n    if (fluidAnimation) {\n        fluidAnimation.pause();\n    }\n    element.hide();\n}\n\nfunction animateFlow(liquidPattern, forwardElseReverse) {\n    ctx.api.resetCssAnimation(liquidPattern);\n    var deltaX = forwardElseReverse ? 172 : -172;\n    return ctx.api.cssAnimate(liquidPattern, 1000).relative(deltaX, 0).loop();\n}",
      "actions": null
    },
    {
      "tag": "fluid-background",
      "stateRenderFunction": "var fluid = ctx.values.fluid && !ctx.values.leak;\nif (fluid) {\n    var color = ctx.properties.fluidColor;\n    element.attr({fill: color});\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "leak",
      "stateRenderFunction": "var leak = ctx.values.leak;\nif (leak) {\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "pipe-background",
      "stateRenderFunction": "var color = ctx.properties.pipeColor;\nelement.attr({fill: color});",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "fluid",
      "name": "{i18n:scada.symbol.fluid-presence}",
      "hint": "{i18n:scada.symbol.fluid-presence-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.present}",
      "falseLabel": "{i18n:scada.symbol.absent}",
      "stateLabel": "{i18n:scada.symbol.fluid-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "flow",
      "name": "{i18n:scada.symbol.flow-presence}",
      "hint": "{i18n:scada.symbol.flow-presence-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.present}",
      "falseLabel": "{i18n:scada.symbol.absent}",
      "stateLabel": "{i18n:scada.symbol.flow-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "flowDirection",
      "name": "{i18n:scada.symbol.flow-direction}",
      "hint": "{i18n:scada.symbol.flow-direction-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.forward}",
      "falseLabel": "{i18n:scada.symbol.reverse}",
      "stateLabel": "{i18n:scada.symbol.forward}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": true,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "flowAnimationSpeed",
      "name": "{i18n:scada.symbol.flow-animation-speed}",
      "hint": "{i18n:scada.symbol.flow-animation-speed-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": 1,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "leak",
      "name": "{i18n:scada.symbol.leak}",
      "hint": "{i18n:scada.symbol.leak-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.leak-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    }
  ],
  "properties": [
    {
      "id": "fluidColor",
      "name": "{i18n:scada.symbol.fluid-color}",
      "type": "color",
      "default": "#1EC1F480",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "pipeColor",
      "name": "{i18n:scada.symbol.pipe-color}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata>
<path d="m14 64h372v72h-372v-72z" fill="#fff" tb:tag="pipe-background"/><path d="m14 64h372v72h-372v-72z" fill="url(#paint0_linear_1239_51060)"/><g stroke-width="3">
  <path d="m15.5 65.5h369v69h-369v-69z" stroke="#000" stroke-opacity=".12"/>
  <rect x="387.5" y="51.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171"/>
  <rect x="1.5" y="51.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171"/>
 </g><defs>
  <linearGradient id="paint0_linear_1239_51060" x1="110.72" x2="110.63" y1="64" y2="136.01" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <pattern id="baseLiquid" width="172" height="72" patternTransform="translate(14,-8)" patternUnits="userSpaceOnUse">
   <circle transform="rotate(-90)" cx="-15" cy="21" r="8" fill="url(#paint28_linear_1182_32781-5)"/>
   <circle transform="rotate(-90)" cx="-15" cy="150" r="8" fill="url(#paint29_linear_1182_32781-1)"/>
   <circle transform="rotate(-90)" cx="-16" cy="113" r="8" fill="url(#paint30_linear_1182_32781-7)"/>
   <circle transform="rotate(-90)" cx="-58" cy="34" r="8" fill="url(#paint31_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-58" cy="155" r="8" fill="url(#paint32_linear_1182_32781-3)"/>
   <circle transform="rotate(-90)" cx="-26" cy="33" r="5" fill="url(#paint33_linear_1182_32781-1)"/>
   <circle transform="rotate(-90)" cx="-26" cy="162" r="5" fill="url(#paint34_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-37" cy="5" r="5" fill="url(#paint35_linear_1182_32781-6)"/>
   <circle transform="rotate(-90)" cx="-8" cy="94" r="4" fill="url(#paint36_linear_1182_32781-9)"/>
   <circle transform="rotate(-90)" cx="-60" cy="72" r="4" fill="url(#paint37_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-37" cy="112" r="5" fill="url(#paint38_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-39" cy="59" r="5" fill="url(#paint39_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-62" cy="115" r="5" fill="url(#paint40_linear_1182_32781-6)"/>
   <circle transform="rotate(-90)" cx="-42" cy="139" r="5" fill="url(#paint41_linear_1182_32781-4)"/>
   <circle transform="rotate(-90)" cx="-21" cy="76" r="5" fill="url(#paint42_linear_1182_32781-1)"/>
   <circle transform="rotate(-90)" cx="-50.5" cy="126.5" r="2.5" fill="url(#paint43_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-46.5" cy="169.5" r="2.5" fill="url(#paint44_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-9.5" cy="57.5" r="2.5" fill="url(#paint45_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-35.5" cy="96.5" r="2.5" fill="url(#paint46_linear_1182_32781-9)"/>
   <circle transform="rotate(-90)" cx="-23.5" cy="91.5" r="2.5" fill="url(#paint47_linear_1182_32781-2)"/>
   <circle transform="rotate(-90)" cx="-40.5" cy="22.5" r="2.5" fill="url(#paint48_linear_1182_32781-88)"/>
   <circle transform="rotate(-90)" cx="-23.5" cy="124.5" r="2.5" fill="url(#paint49_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-47.5" cy="86.5" r="2.5" fill="url(#paint50_linear_1182_32781-6)"/>
   <circle transform="rotate(-90)" cx="-21.5" cy="51.5" r="2.5" fill="url(#paint51_linear_1182_32781-8)"/>
   <circle transform="rotate(-90)" cx="-51.5" cy="48.5" r="2.5" fill="url(#paint52_linear_1182_32781-3)"/>
   <circle transform="rotate(-90)" cx="-64" cy="14" r="4" fill="url(#paint53_linear_1182_32781-83)"/>
   <circle transform="rotate(-90)" cx="-64" cy="135" r="4" fill="url(#paint54_linear_1182_32781-33)"/>
   <circle transform="rotate(-90)" cx="-58.5" cy="95.5" r="9.5" fill="url(#paint55_linear_1182_32781-8)"/>
   <path d="m0 0h172v72h-172z" fill="url(#paint84_linear_1182_32781-8)" stroke-width=".57735"/>
  </pattern>
  <pattern id="liquid" width="172" height="72" patternUnits="userSpaceOnUse"><rect width="688" height="72" x="-172" y="0" stroke-width="0" fill="url(#baseLiquid)"/></pattern>
  <linearGradient id="paint28_linear_1182_32781-5" x1="19.316" x2="21" y1="8.2632" y2="23" gradientTransform="translate(-36,6)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint29_linear_1182_32781-1" x1="148.32" x2="150" y1="8.2632" y2="23" gradientTransform="translate(-165,135)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint30_linear_1182_32781-7" x1="111.32" x2="113" y1="9.2632" y2="24" gradientTransform="translate(-129,97)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint31_linear_1182_32781-4" x1="32.316" x2="34" y1="51.263" y2="66" gradientTransform="translate(-92,-24)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint32_linear_1182_32781-3" x1="153.32" x2="155" y1="51.263" y2="66" gradientTransform="translate(-213,97)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint33_linear_1182_32781-1" x1="31.947" x2="33" y1="21.789" y2="31" gradientTransform="translate(-59,7)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint34_linear_1182_32781-4" x1="160.95" x2="162" y1="21.789" y2="31" gradientTransform="translate(-188,136)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint35_linear_1182_32781-6" x1="3.9474" x2="5" y1="32.79" y2="42" gradientTransform="translate(-42,-32)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint36_linear_1182_32781-9" x1="93.158" x2="94" y1="4.6316" y2="12" gradientTransform="translate(-102,86)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint37_linear_1182_32781-4" x1="71.158" x2="72" y1="56.632" y2="64" gradientTransform="translate(-132,12)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint38_linear_1182_32781-2" x1="110.95" x2="112" y1="32.79" y2="42" gradientTransform="translate(-149,75)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint39_linear_1182_32781-2" x1="57.947" x2="59" y1="34.79" y2="44" gradientTransform="translate(-98,20)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint40_linear_1182_32781-6" x1="113.95" x2="115" y1="57.79" y2="67" gradientTransform="translate(-177,53)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint41_linear_1182_32781-4" x1="137.95" x2="139" y1="37.79" y2="47" gradientTransform="translate(-181,97)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint42_linear_1182_32781-1" x1="74.947" x2="76" y1="16.789" y2="26" gradientTransform="translate(-97,55)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint43_linear_1182_32781-2" x1="125.97" x2="126.5" y1="48.395" y2="53" gradientTransform="translate(-177,76)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint44_linear_1182_32781-8" x1="168.97" x2="169.5" y1="44.395" y2="49" gradientTransform="translate(-216,123)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint45_linear_1182_32781-8" x1="56.974" x2="57.5" y1="7.3947" y2="12" gradientTransform="translate(-67,48)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint46_linear_1182_32781-9" x1="95.974" x2="96.5" y1="33.395" y2="38" gradientTransform="translate(-132,61)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint47_linear_1182_32781-2" x1="90.974" x2="91.5" y1="21.395" y2="26" gradientTransform="translate(-115,68)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint48_linear_1182_32781-88" x1="21.974" x2="22.5" y1="38.395" y2="43" gradientTransform="translate(-63,-18)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint49_linear_1182_32781-8" x1="123.97" x2="124.5" y1="21.395" y2="26" gradientTransform="translate(-148,101)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint50_linear_1182_32781-6" x1="85.974" x2="86.5" y1="45.395" y2="50" gradientTransform="translate(-134,39)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint51_linear_1182_32781-8" x1="50.974" x2="51.5" y1="19.395" y2="24" gradientTransform="translate(-73,30)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint52_linear_1182_32781-3" x1="47.974" x2="48.5" y1="49.395" y2="54" gradientTransform="translate(-100,-3)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint53_linear_1182_32781-83" x1="13.158" x2="14" y1="60.632" y2="68" gradientTransform="translate(-78,-50)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint54_linear_1182_32781-33" x1="134.16" x2="135" y1="60.632" y2="68" gradientTransform="translate(-199,71)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint55_linear_1182_32781-8" x1="93.5" x2="95.5" y1="50.5" y2="68" gradientTransform="translate(-154,37)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#fff" stop-opacity=".4" offset="0"/>
   <stop stop-opacity=".15" offset="1"/>
  </linearGradient>
  <linearGradient id="paint84_linear_1182_32781-8" x1="248" x2="248" y1="1.8513e-7" y2="72" gradientTransform="scale(.33333 1)" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" offset=".10895"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".11331"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".88736"/>
   <stop stop-color="#727171" offset=".89138"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <clipPath id="clipPath3031">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5877">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5871">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5865">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5859">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5853">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5847">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5841">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5835">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5829">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5823">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5817">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5811">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5805">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5799">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5793">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5787">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5781">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
  <clipPath id="clipPath5775">
   <rect width="400" height="200" fill="#fff"/>
  </clipPath>
 </defs><rect x="14" y="64" width="372" height="72" fill="#1ec1f4" stroke-width="0" style="display: none;" tb:tag="fluid-background"/><rect x="14" y="64" width="372" height="72" fill="url(#liquid)" stroke-width="0" style="display: none;" tb:tag="fluid"/><path transform="translate(0 -.1506)" d="m270.53 93.395c0-0.3006 7.47-0.7515 7.47-0.7515l-7.975 0.1503s-6.461-1.9539-7.874-2.4799c-1.212-0.5261-11.319 3.3214-12.328 3.4717h-0.202l0.101-0.0752 3.533-3.0812c-0.505 0.3006-8.694 6.1772-9.905 6.6281-1.312 0.3758-11.29 1.1273-12.098 1.503-0.202 0.0752-2.782 0.0752-3.085 0.0752-1.11-0.1503-2.624-0.9018-3.331-1.1273-0.909-0.3006-2.827-1.1272-2.423-1.503s2.928-5.486 3.836-5.8618c0.808-0.3757 10.455-2.1794 10.858-2.48 0.404-0.3006 1.313-2.48 1.414-2.7054l-1.515 2.4048s-0.807 0.3758-1.716 0.4509c-0.908 0.0752-9.243 1.4279-9.243 1.4279l0.606-2.7054c-0.101 0.1503-1.009 1.8787-1.817 3.1563-0.303 0.5261-0.606 0.977-0.808 1.2776-0.807 1.0521-2.322 3.1563-3.23 3.5321-0.808 0.3757-2.625 0.1503-4.038-0.4509s-5.034-1.7757-5.942-2.0012c-0.909-0.3006-2.538-2.057-3.043-2.7334v-0.0751c-0.504-0.6764-0.605-1.9539-0.303-2.5551 0.404-0.6764 0.303-1.6534 0.606-2.7055s5.653-4.9599 5.653-4.9599-0.101 0-0.201 0.0751c-0.606 0.3006-2.928 1.503-4.039 2.1794-1.211 0.7515-2.119 1.1273-2.523 1.1273-0.101 0-0.202-0.0752-0.404-0.1504-0.606-0.526-1.817-2.029-2.726-3.3066-1.11-1.5782-4.442-4.0582-4.946-4.6594-0.505-0.6012 0.706-3.6824 0.908-3.983-0.202 0.3006-1.918 3.0812-2.322 3.457l-0.101 0.0751c-0.504 0.1503-1.817-0.3757-3.028-0.9018-1.413-0.6012-5.451-0.6763-6.36-0.977-0.909-0.3006-6.966-2.7054-7.369-2.8557 0.303 0.1503 2.523 1.4279 4.441 2.5551 0.707 0.3758 1.313 0.7516 1.717 1.0522 1.918 1.2024 0.908 0.6012 2.322 0.8266 1.312 0.2255 4.542 1.0521 6.359 0.977 1.717-0.0752 2.726 0.526 3.635 1.1273 0.908 0.6012 1.615 2.5551 4.038 4.3587 2.422 1.8036 2.725 4.8097 3.23 5.4109 0.101 0.0751 0.101 0.1503 0.101 0.2254 0.101 0.6764-1.312 1.954-1.918 3.2315-0.707 1.3528-3.029 1.2025-3.836 1.2025-0.909 0.0751-1.313 0.0751-1.716 0.4509-0.404 0.3757-2.726-0.1503-3.534-0.1503-0.908 0.0751-4.34 1.3527-5.249 0.7515s1.413-4.1333 1.312-4.8097v-0.3006c-0.101-0.3758-0.101-0.5261-0.605-1.3527-0.505-0.9018 2.624-2.7055 2.826-2.7806-0.202 0.0751-2.12 0.8267-2.524 1.1273-0.403 0.3006-0.706 0.9018-0.807 0.9769 0 0-1.413-0.6012-2.322-0.8266-0.909-0.3006-4.139-1.0522-5.048-1.3528-0.908-0.3006-2.523-1.3728-3.028-2.0492-0.505-0.6012-8.189-3.6273-8.694-4.3037-0.504-0.6012-3.723-0.5593-3.723-0.5593s1.366 1.1072 2.228 1.1072c0.569 0 0.688-0.0218 0.991 0.3539 0.302 0.3758 2.102 0.9387 3.564 1.6078 1.625 0.7443 3.878 1.3075 4.322 1.8693 1.009 1.2776 2.12 1.7486 3.028 2.3498 0.909 0.6012 1.514 1.2024 2.827 1.503 1.312 0.2255 3.129 0.4509 4.543 0.7515 1.413 0.2255 0.504 0.977 0.605 1.6534 0.101 0.6763 0.505 0.3006 0.505 0.9769 0.101 0.6764-0.303 0.977-0.707 1.3527-0.101 0.0752-0.101 0.1503-0.202 0.2255-0.302 0.6012-0.605 1.8788-1.312 2.48-0.808 0.6763-7.167 3.7575-8.48 4.1333-0.101 0-0.202 0.0751-0.303 0.0751-0.504 0.0752-1.312 0.1503-2.221 0.0752-1.918-0.0752-4.239-0.3006-5.047-0.6764-1.413-0.6012-4.24-1.7284-4.745-2.7054-0.504-0.977-4.744-2.7055-5.653-3.3067s-10.87-5.6108-11.678-5.2351c-0.807 0.3758-4.442-0.3757-8.076-0.526-1.817-0.0752-3.23-0.4509-4.744-1.1273-1.515-0.6012-3.13-1.4279-5.452-2.2545-4.442-1.6533-9.287-6.6133-9.59-6.9891 0.202 0.3006 1.615 2.5552 2.524 3.1564 0.908 0.6012 2.423 1.8036 2.423 1.8036l-2.726-0.5261s2.322 0.8267 3.634 1.1273c1.413 0.2255 1.514 1.2024 4.24 2.4048 2.827 1.1273 6.562 2.9309 7.47 3.2315 0.909 0.3006 1.817 0.5261 2.726 0.8267s7.067-0.0752 8.48 0.1503c1.413 0.2254 3.23 0.8267 3.23 1.1273s0 0.3006-1.211 1.0521c-1.212 0.7515-7.47 0.7515-7.874 0.8266-0.404 0-3.13-0.4509-4.947-0.0751-1.716 0.4509-4.442-0.3758-4.946-0.3758-0.404 0 2.725 0.8267 3.23 0.8267 0.404 0 3.533 0.1503 4.038 0.0752 0.404 0-0.404 0.3757-0.808 0.3757-0.403 0-0.807 1.0521-1.211 1.4279-0.404 0.3757-1.514 2.1042-2.322 3.1563-0.807 1.0521 0.404 3.3067 0.404 3.3067s-0.202-1.6534 0.202-2.3297c0.404-0.6764 1.11-1.7285 1.615-1.7285 0.404 0 0.303-1.3527 1.918-2.4048 1.615-1.1273 3.029-1.2024 4.745-1.2776 1.817-0.0751 13.764 3.6824 16.159 4.4339 1.708 0.536 3.292 2.0036 4.604 2.2291 1.413 0.2254 5.956 1.2776 9.086 2.1042 3.23 0.8267 8.479 4.4337 8.479 4.4337h-0.201c-0.404 0-1.313 0-2.019-0.225-0.808-0.226-3.837-1.654-4.543-1.9541l-0.101-0.0752 0.101 0.0752c0.202 0.0751 0.505 0.3001 0.908 0.5261 0.303 0.225 1.212 0.827 1.817 1.202 0.303 0.151 0.505 0.301 0.505 0.301-0.101 0-6.106-1.122-8.563-0.561s-10.073 6.874-10.073 6.874 7.971-5.036 10.817-5.537c2.494-0.439 5.148-0.207 7.648 0.09 2.537 0.301 5.037 0.787 5.219 0.787 0.403 0 1.413 0.902 2.322 1.203 0.908 0.3 4.34 2.705 4.845 3.381 0.505 0.602-1.716 5.036-1.312 4.735 0.404-0.376 1.817-4.058 2.221-4.133 0.404 0 0.504 0.3 0.908 0.601 0.505 0.3 2.524 3.156 3.029 3.156 0.404 0-0.808 0.677-1.212 1.052-0.403 0.376-0.706 1.729-0.605 2.33s-1.01 1.278-1.212 1.353c0.202-0.075 1.716-0.752 1.615-1.052 0-0.301 1.01-2.706 1.919-3.082 0.807-0.375 1.817 0.226 2.725 0.451 0.909 0.301 2.12 2.33 2.625 2.631 0.505 0.3 2.423 1.321 2.927 1.997 0.505 0.601 3.13 1.46 3.635 2.136 0.504 0.601-1.817 3.457-2.12 4.734-0.303 1.353 0.908 4.585 0.908 4.585s-0.303-2.631-0.404-3.983c-0.1-1.278 1.919-3.082 2.322-3.157 0.404 0 0.909 0.301 1.414 0.902 0.504 0.601 1.211 2.555 1.716 3.232 0.505 0.601 0.606 1.653 1.615 2.855 0.606 0.827 0.909 1.203 1.01 1.428-0.202-0.451-0.606-1.503-0.606-1.803 0-0.301-1.615-2.255-1.615-2.856-0.101-0.676-2.221-4.509-2.524-6.463-0.202-1.954 2.322-7.741 2.12-8.718-0.101-0.977-1.514-1.578-1.615-2.855-0.101-1.353 1.817-3.758 4.845-4.284 2.625-0.451 7.471 0.752 8.783 1.052 0.101 0 0.101 0 0.202 0.075h0.101c-0.202-0.075-3.735-1.428-5.553-1.954-1.817-0.526-4.34-1.052-4.542-1.352 0-0.301 1.615-1.729 2.423-2.1046 0.807-0.3758 2.523-1.1273 3.836-1.2024 1.312-0.0752 1.817 0.2254 2.725 0.8266 0.909 0.6012 3.186 0.6013 5.609 1.8034 2.221 1.128 5.35 2.555 5.653 2.631-0.303-0.226-5.249-3.307-4.845-3.683 0.403-0.3756 6.949-0.5259 9.977-0.7514 3.13-0.1503 16.496-3.0812 17.808-3.1563 1.313-0.0752 2.847 0 6.263 0.2545 3.412 0 7.854-1.4865 7.854-1.4865s-3.836-1.2776-3.937-1.5782zm-68.711 17.39c-0.404 0.075-1.514 0.3-1.918 0-0.505-0.376-2.12-0.752-2.625-1.954-0.403-0.977-2.927-2.405-3.836-2.931-0.202-0.151-0.302-0.151-0.302-0.151 1.009-0.375 6.258 0.752 6.763 1.128s0 1.578 0.505 1.954c0.505 0.375 1.514 1.954 1.514 1.954h-0.101zm1.515-0.902c-0.101-0.151-0.202-0.301-0.303-0.376-0.505-0.601-1.414-0.601-1.515-1.202-0.101-0.677 0.808-1.052 1.212-1.428s3.634 0.451 3.634 0.451 6.461 2.555 6.663 3.908c0 0.375 0 0.751-0.101 1.052-0.202 0.751-0.606 1.277-0.505 1.954 0.101 0.977 0.202 2.329-1.11 2.029-1.313-0.226-2.019-2.18-4.442-3.683-1.918-1.277-2.928-2.104-3.533-2.705zm10.498-5.787c-0.302 0.376-0.706 0.902-1.009 1.428-0.707 1.353-0.606 2.33-1.514 2.405-0.909 0.075-1.818-0.226-3.231-0.827-0.706-0.3-1.413-0.526-2.12-0.751-0.706-0.226-1.11-0.376-1.11-0.376s1.615-1.127 2.624-0.827c0.909 0.301 1.414 0.226 1.818-0.075 0.403-0.376 5.148-1.954 5.552-2.029-0.101 0.075-0.505 0.451-1.01 1.052zm3.231-4.058c-0.404 0.376-1.615 1.728-1.615 1.728h-0.101c-0.404 0.076-2.726 0.527-4.24 1.203-0.909 0.376-3.231 1.277-5.654 1.879-0.201 0.075-0.504 0.15-0.706 0.225-1.918 0.451-3.735 0.752-4.846 0.526-2.725-0.526-5.047-1.653-6.864-1.879-1.818-0.225-2.221 0.151-3.635-0.451-1.413-0.601-1.918-1.202-3.331-2.104s-2.019-1.8785-2.928-2.4797c-0.605-0.3758 0.346-0.4509 0.346-0.977 0.569-0.4761 1.139-0.4761 1.139-0.4761s0.837-0.3505 2.15-0.8015c1.312-0.3757 5.425-1.962 9.237-1.4127 3.345 0.4821 5.382-0.0193 8.732 0.4358 3.611 0.4906 4.744-0.4218 8.076 1.006 3.23 1.4279 7.571 0.6473 7.571 0.6473s-2.927 2.5551-3.331 2.9309z" clip-path="url(#clipPath3031)" fill="#5c5a5a" style="display: none;" tb:tag="leak"/><g transform="translate(4.3881 -.9697)" style="display: none;" tb:tag="leak">
  <path transform="translate(-4.3881 .9697)" d="m270.53 93.395c0-0.3006 7.47-0.7515 7.47-0.7515l-7.975 0.1503s-6.461-1.9539-7.874-2.4799c-1.212-0.5261-11.319 3.3214-12.328 3.4717h-0.202l0.101-0.0752 3.533-3.0812c-0.505 0.3006-8.694 6.1772-9.905 6.6281-1.312 0.3758-11.29 1.1273-12.098 1.503-0.202 0.0752-2.782 0.0752-3.085 0.0752-1.11-0.1503-2.624-0.9018-3.331-1.1273-0.909-0.3006-2.827-1.1272-2.423-1.503s2.928-5.486 3.836-5.8618c0.808-0.3757 10.455-2.1794 10.858-2.48 0.404-0.3006 1.313-2.48 1.414-2.7054l-1.515 2.4048s-0.807 0.3758-1.716 0.4509c-0.908 0.0752-9.243 1.4279-9.243 1.4279l0.606-2.7054c-0.101 0.1503-1.009 1.8787-1.817 3.1563-0.303 0.5261-0.606 0.977-0.808 1.2776-0.807 1.0521-2.322 3.1563-3.23 3.5321-0.808 0.3757-2.625 0.1503-4.038-0.4509s-5.034-1.7757-5.942-2.0012c-0.909-0.3006-2.538-2.057-3.043-2.7334v-0.0751c-0.504-0.6764-0.605-1.9539-0.303-2.5551 0.404-0.6764 0.303-1.6534 0.606-2.7055s5.653-4.9599 5.653-4.9599-0.101 0-0.201 0.0751c-0.606 0.3006-2.928 1.503-4.039 2.1794-1.211 0.7515-2.119 1.1273-2.523 1.1273-0.101 0-0.202-0.0752-0.404-0.1504-0.606-0.526-1.817-2.029-2.726-3.3066-1.11-1.5782-4.442-4.0582-4.946-4.6594-0.505-0.6012 0.706-3.6824 0.908-3.983-0.202 0.3006-1.918 3.0812-2.322 3.457l-0.101 0.0751c-0.504 0.1503-1.817-0.3757-3.028-0.9018-1.413-0.6012-5.451-0.6763-6.36-0.977-0.909-0.3006-6.966-2.7054-7.369-2.8557 0.303 0.1503 2.523 1.4279 4.441 2.5551 0.707 0.3758 1.313 0.7516 1.717 1.0522 1.918 1.2024 0.908 0.6012 2.322 0.8266 1.312 0.2255 4.542 1.0521 6.359 0.977 1.717-0.0752 2.726 0.526 3.635 1.1273 0.908 0.6012 1.615 2.5551 4.038 4.3587 2.422 1.8036 2.725 4.8097 3.23 5.4109 0.101 0.0751 0.101 0.1503 0.101 0.2254 0.101 0.6764-1.312 1.954-1.918 3.2315-0.707 1.3528-3.029 1.2025-3.836 1.2025-0.909 0.0751-1.313 0.0751-1.716 0.4509-0.404 0.3757-2.726-0.1503-3.534-0.1503-0.908 0.0751-4.34 1.3527-5.249 0.7515s1.413-4.1333 1.312-4.8097v-0.3006c-0.101-0.3758-0.101-0.5261-0.605-1.3527-0.505-0.9018 2.624-2.7055 2.826-2.7806-0.202 0.0751-2.12 0.8267-2.524 1.1273-0.403 0.3006-0.706 0.9018-0.807 0.9769 0 0-1.413-0.6012-2.322-0.8266-0.909-0.3006-4.139-1.0522-5.048-1.3528-0.908-0.3006-2.523-1.3728-3.028-2.0492-0.505-0.6012-8.189-3.6273-8.694-4.3037-0.504-0.6012-3.723-0.5593-3.723-0.5593s1.366 1.1072 2.228 1.1072c0.569 0 0.688-0.0218 0.991 0.3539 0.302 0.3758 2.102 0.9387 3.564 1.6078 1.625 0.7443 3.878 1.3075 4.322 1.8693 1.009 1.2776 2.12 1.7486 3.028 2.3498 0.909 0.6012 1.514 1.2024 2.827 1.503 1.312 0.2255 3.129 0.4509 4.543 0.7515 1.413 0.2255 0.504 0.977 0.605 1.6534 0.101 0.6763 0.505 0.3006 0.505 0.9769 0.101 0.6764-0.303 0.977-0.707 1.3527-0.101 0.0752-0.101 0.1503-0.202 0.2255-0.302 0.6012-0.605 1.8788-1.312 2.48-0.808 0.6763-7.167 3.7575-8.48 4.1333-0.101 0-0.202 0.0751-0.303 0.0751-0.504 0.0752-1.312 0.1503-2.221 0.0752-1.918-0.0752-4.239-0.3006-5.047-0.6764-1.413-0.6012-4.24-1.7284-4.745-2.7054-0.504-0.977-4.744-2.7055-5.653-3.3067s-10.87-5.6108-11.678-5.2351c-0.807 0.3758-4.442-0.3757-8.076-0.526-1.817-0.0752-3.23-0.4509-4.744-1.1273-1.515-0.6012-3.13-1.4279-5.452-2.2545-4.442-1.6533-9.287-6.6133-9.59-6.9891 0.202 0.3006 1.615 2.5552 2.524 3.1564 0.908 0.6012 2.423 1.8036 2.423 1.8036l-2.726-0.5261s2.322 0.8267 3.634 1.1273c1.413 0.2255 1.514 1.2024 4.24 2.4048 2.827 1.1273 6.562 2.9309 7.47 3.2315 0.909 0.3006 1.817 0.5261 2.726 0.8267s7.067-0.0752 8.48 0.1503c1.413 0.2254 3.23 0.8267 3.23 1.1273s0 0.3006-1.211 1.0521c-1.212 0.7515-7.47 0.7515-7.874 0.8266-0.404 0-3.13-0.4509-4.947-0.0751-1.716 0.4509-4.442-0.3758-4.946-0.3758-0.404 0 2.725 0.8267 3.23 0.8267 0.404 0 3.533 0.1503 4.038 0.0752 0.404 0-0.404 0.3757-0.808 0.3757-0.403 0-0.807 1.0521-1.211 1.4279-0.404 0.3757-1.514 2.1042-2.322 3.1563-0.807 1.0521 0.404 3.3067 0.404 3.3067s-0.202-1.6534 0.202-2.3297c0.404-0.6764 1.11-1.7285 1.615-1.7285 0.404 0 0.303-1.3527 1.918-2.4048 1.615-1.1273 3.029-1.2024 4.745-1.2776 1.817-0.0751 13.764 3.6824 16.159 4.4339 1.708 0.536 3.292 2.0036 4.604 2.2291 1.413 0.2254 5.956 1.2776 9.086 2.1042 3.23 0.8267 8.479 4.4337 8.479 4.4337h-0.201c-0.404 0-1.313 0-2.019-0.225-0.808-0.226-3.837-1.654-4.543-1.9541l-0.101-0.0752 0.101 0.0752c0.202 0.0751 0.505 0.3001 0.908 0.5261 0.303 0.225 1.212 0.827 1.817 1.202 0.303 0.151 0.505 0.301 0.505 0.301-0.101 0-6.106-1.122-8.563-0.561s-10.073 6.874-10.073 6.874 7.971-5.036 10.817-5.537c2.494-0.439 5.148-0.207 7.648 0.09 2.537 0.301 5.037 0.787 5.219 0.787 0.403 0 1.413 0.902 2.322 1.203 0.908 0.3 4.34 2.705 4.845 3.381 0.505 0.602-1.716 5.036-1.312 4.735 0.404-0.376 1.817-4.058 2.221-4.133 0.404 0 0.504 0.3 0.908 0.601 0.505 0.3 2.524 3.156 3.029 3.156 0.404 0-0.808 0.677-1.212 1.052-0.403 0.376-0.706 1.729-0.605 2.33s-1.01 1.278-1.212 1.353c0.202-0.075 1.716-0.752 1.615-1.052 0-0.301 1.01-2.706 1.919-3.082 0.807-0.375 1.817 0.226 2.725 0.451 0.909 0.301 2.12 2.33 2.625 2.631 0.505 0.3 2.423 1.321 2.927 1.997 0.505 0.601 3.13 1.46 3.635 2.136 0.504 0.601-1.817 3.457-2.12 4.734-0.303 1.353 0.908 4.585 0.908 4.585s-0.303-2.631-0.404-3.983c-0.1-1.278 1.919-3.082 2.322-3.157 0.404 0 0.909 0.301 1.414 0.902 0.504 0.601 1.211 2.555 1.716 3.232 0.505 0.601 0.606 1.653 1.615 2.855 0.606 0.827 0.909 1.203 1.01 1.428-0.202-0.451-0.606-1.503-0.606-1.803 0-0.301-1.615-2.255-1.615-2.856-0.101-0.676-2.221-4.509-2.524-6.463-0.202-1.954 2.322-7.741 2.12-8.718-0.101-0.977-1.514-1.578-1.615-2.855-0.101-1.353 1.817-3.758 4.845-4.284 2.625-0.451 7.471 0.752 8.783 1.052 0.101 0 0.101 0 0.202 0.075h0.101c-0.202-0.075-3.735-1.428-5.553-1.954-1.817-0.526-4.34-1.052-4.542-1.352 0-0.301 1.615-1.729 2.423-2.1046 0.807-0.3758 2.523-1.1273 3.836-1.2024 1.312-0.0752 1.817 0.2254 2.725 0.8266 0.909 0.6012 3.186 0.6013 5.609 1.8034 2.221 1.128 5.35 2.555 5.653 2.631-0.303-0.226-5.249-3.307-4.845-3.683 0.403-0.3756 6.949-0.5259 9.977-0.7514 3.13-0.1503 16.496-3.0812 17.808-3.1563 1.313-0.0752 2.847 0 6.263 0.2545 3.412 0 7.854-1.4865 7.854-1.4865s-3.836-1.2776-3.937-1.5782zm-68.711 17.39c-0.404 0.075-1.514 0.3-1.918 0-0.505-0.376-2.12-0.752-2.625-1.954-0.403-0.977-2.927-2.405-3.836-2.931-0.202-0.151-0.302-0.151-0.302-0.151 1.009-0.375 6.258 0.752 6.763 1.128s0 1.578 0.505 1.954c0.505 0.375 1.514 1.954 1.514 1.954h-0.101zm1.515-0.902c-0.101-0.151-0.202-0.301-0.303-0.376-0.505-0.601-1.414-0.601-1.515-1.202-0.101-0.677 0.808-1.052 1.212-1.428s3.634 0.451 3.634 0.451 6.461 2.555 6.663 3.908c0 0.375 0 0.751-0.101 1.052-0.202 0.751-0.606 1.277-0.505 1.954 0.101 0.977 0.202 2.329-1.11 2.029-1.313-0.226-2.019-2.18-4.442-3.683-1.918-1.277-2.928-2.104-3.533-2.705zm10.498-5.787c-0.302 0.376-0.706 0.902-1.009 1.428-0.707 1.353-0.606 2.33-1.514 2.405-0.909 0.075-1.818-0.226-3.231-0.827-0.706-0.3-1.413-0.526-2.12-0.751-0.706-0.226-1.11-0.376-1.11-0.376s1.615-1.127 2.624-0.827c0.909 0.301 1.414 0.226 1.818-0.075 0.403-0.376 5.148-1.954 5.552-2.029-0.101 0.075-0.505 0.451-1.01 1.052zm3.231-4.058c-0.404 0.376-1.615 1.728-1.615 1.728h-0.101c-0.404 0.076-2.726 0.527-4.24 1.203-0.909 0.376-3.231 1.277-5.654 1.879-0.201 0.075-0.504 0.15-0.706 0.225-1.918 0.451-3.735 0.752-4.846 0.526-2.725-0.526-5.047-1.653-6.864-1.879-1.818-0.225-2.221 0.151-3.635-0.451-1.413-0.601-1.918-1.202-3.331-2.104s-2.019-1.8785-2.928-2.4797c-0.605-0.3758 0.346-0.4509 0.346-0.977 0.569-0.4761 1.139-0.4761 1.139-0.4761s0.837-0.3505 2.15-0.8015c1.312-0.3757 5.425-1.962 9.237-1.4127 3.345 0.4821 5.382-0.0193 8.732 0.4358 3.611 0.4906 4.744-0.4218 8.076 1.006 3.23 1.4279 7.571 0.6473 7.571 0.6473s-2.927 2.5551-3.331 2.9309z" clip-path="url(#clipPath5877)" fill="#5c5a5a" style=""/>
  <g fill="#8b8b8b" style="">
   <path transform="translate(-4.3881 .9697)" d="m179.31 95.378c-0.505 0.3006-1.11 0.6012-1.716 0.6764-1.11 0.1503-2.221 0-3.23-0.1503-4.139-0.6764-8.379-2.0291-11.004-4.5091-0.505-0.4509-1.009-0.9769-1.716-1.3527-1.615-0.9018-4.031-3.9565-6.05-3.8814-6.832-3.3214-14.318-2.0713-17.649-3.875 1.514 0.6013 9 1.032 10.817 1.1072 3.533 0.1503 5.694 1.6607 7.741 2.3921 1.695 0.6056 4.131 2.4535 5.04 3.0547s5.148 2.3297 5.653 3.3066c0.505 0.977 3.332 2.1043 4.745 2.7055 1.413 0.4509 5.754 0.8266 7.369 0.526z" clip-path="url(#clipPath5871)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m206.87 86.118c-0.201 1.3527-0.908 2.7054-2.019 3.7576-0.202 0.2254-0.504 0.4509-0.807 0.526-0.909 0.3758-2.019 0.1503-3.029 0.2255-0.908 0.0751-1.716 0.3757-2.624 0.4509-0.505 0.0751-1.111 0.0751-1.616 0-1.11-0.0752-1.896-0.489-3.245 0-0.843 0.6933-2.067 0.6769-3.206 0.6769-0.419 0-0.845 0.1423-0.845-0.3964 0-0.4081 0.216-0.3557 0.276-0.7107 0.202-0.6764 0.937-2.0915 1.139-2.7679 0.101-0.3757 0.531-0.7098 0.632-1.0855 0-0.1503 0-0.5261 0.202-0.6764v0.3006c0.101 0.6764-1.173 3.5692-0.265 4.1704 0.909 0.6013 3.394-0.0371 4.202-0.1122 0.908-0.0752 3.129 0.4509 3.533 0.1503 0.404-0.3758 0.808-0.3758 1.716-0.4509 0.909-0.0752 3.13 0.1503 3.836-1.2025 0.707-1.2024 2.019-2.4799 1.919-3.2314 0.201 0 0.201 0.2254 0.201 0.3757z" clip-path="url(#clipPath5865)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m213.73 79.07c-0.808 0.7515-1.918 1.3527-2.827 1.9539-1.211 0.6763-2.322 1.4278-3.533 2.1042-0.101-0.3006-0.404-0.6012-0.505-0.977 0.101 0.1503 0.303 0.1503 0.404 0.1503 0.404 0 1.312-0.3757 2.524-1.1272 1.009-0.6012 3.331-1.7285 3.937-2.1042z" clip-path="url(#clipPath5859)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m197.58 73.734c-0.1 0.0752-0.1 0.1503-0.201 0.2255-0.101 0.0751-0.202 0.0751-0.303 0.0751-0.404 0.0752-0.808 0.0752-1.313 0-1.514-0.1503-2.826-0.8266-4.34-1.1272-0.707-0.1503-1.515-0.1503-2.221-0.3006-0.606-0.0752-1.212-0.2255-1.818-0.451-0.706-0.2254-1.514-0.3757-2.22-0.6012-2.019-1.2024-4.442-2.5551-4.442-2.5551s6.562 2.5551 7.47 2.8557c0.909 0.3006 4.947 0.3758 6.36 0.977 1.312 0.6012 2.524 1.1273 3.028 0.9018z" clip-path="url(#clipPath5853)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m190.31 84.707c-1.413-0.9018-3.23-1.2024-5.047-1.503-0.707-0.0752-1.413-0.2255-1.918-0.5261-0.404-0.2255-0.707-0.5261-1.01-0.8267-0.706-0.6763-0.987-0.5494-1.693-1.2257-0.303-0.3006-0.57-0.5536-1.139-1.1072-0.943-0.3588-1.139 0-2.277-0.5535-1.139-1.1072-3.372-1.5178-4.886-2.4948-0.303-0.3758-1.175-0.6764-1.377-0.8267-0.505-0.3006-3.416-0.5535-3.416-0.5535s4.793-0.198 5.297 0.4784c0.505 0.6012 5.686 2.742 7.169 3.2018 0.765 0.2369 2.12 2.8558 3.029 3.1564s4.139 1.0521 5.048 1.3527c0.908 0.3006 2.321 0.8267 2.321 0.8267 0.101 0.1503 0 0.3757-0.101 0.6012z" clip-path="url(#clipPath5847)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m189.4 88.69c-0.101 0.3758-0.202 0.8267-0.303 0.977 0 0.6012-0.1 1.2775-0.605 1.7285-0.202 0.2254-0.505 0.3757-0.808 0.526-0.908 0.5261-1.817 0.977-2.826 1.503-0.606 0.3006-1.212 0.6764-1.918 0.9018-0.505 0.1503-1.01 0.3006-1.515 0.5261s-1.009 0.4509-1.514 0.6764c-0.909 0.3006-1.817 0.526-2.827 0.526h-0.202c-0.201-0.0751-0.201-0.2254-0.1-0.3757 0.1-0.0752 0.201-0.1503 0.302-0.1503 0.909 0 1.717 0 2.221-0.0752 0.101 0 0.202-0.0751 0.303-0.0751 1.313-0.3758 7.672-3.457 8.48-4.1334 0.707-0.6763 1.009-1.9539 1.312-2.5551z" clip-path="url(#clipPath5841)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m205.55 104.92c-1.009 0.451-2.12 0.827-3.23 1.128-0.808 0.15-1.615 0.3-2.423 0.15-0.606-0.075-1.11-0.376-1.615-0.601-0.909-0.376-1.918-0.526-2.928-0.752-1.009-0.225-2.019-0.375-3.129-0.526-0.404 0-0.808-0.075-1.211-0.15-0.505-0.075-0.909-0.301-1.414-0.451-0.908-0.376-1.716-0.751-2.524-1.202-0.706-0.451-1.413-0.902-2.019-1.428-1.009-0.902-2.927-2.0293-3.028-3.3069 0-0.3006 0.101-0.6763 0.303-0.9018 0 0.5261 0.101 1.3527 0.706 1.7285 0.909 0.6012 1.515 1.5782 2.928 2.4802s1.918 1.503 3.331 2.104c1.414 0.601 1.817 0.225 3.635 0.451 1.817 0.225 4.139 1.428 6.864 1.879 1.414 0.375 3.533 0 5.754-0.602z" clip-path="url(#clipPath5835)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m185.46 102.07v0.376c-1.514-0.075-2.928-0.451-4.341-0.977-0.606-0.376-1.514-0.977-1.817-1.202-0.303-0.226-0.707-0.4511-0.909-0.5262 0.707 0.3002 3.735 1.7282 4.543 1.9542 0.707 0.225 1.615 0.225 2.019 0.225 0.202 0.075 0.303 0.075 0.505 0.15z" clip-path="url(#clipPath5829)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m215.45 102.44c-0.808 0-1.616 0.225-2.322 0.451-2.827 0.827-5.553 1.578-8.379 2.254v-0.075c0.202-0.075 0.505-0.15 0.707-0.225 2.422-0.677 4.744-1.503 5.653-1.879 1.514-0.676 3.836-1.127 4.24-1.202 0.101 0.3 0.101 0.676 0.101 0.676z" clip-path="url(#clipPath5823)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m213.83 104.1c-0.101 0.15-0.303 0.375-0.404 0.526-0.101 0.3-0.302 0.526-0.403 0.826-0.303 0.827-0.404 1.654-0.707 2.405-0.101 0.15-0.101 0.301-0.202 0.451-0.303 0.226-0.707 0.301-1.11 0.226-0.404-0.076-0.808-0.301-1.111-0.451-0.908-0.451-1.918-0.902-2.927-1.278-0.303-0.075-0.606-0.225-0.909-0.451 0.606 0.226 1.413 0.451 2.12 0.752 1.413 0.601 2.322 0.826 3.23 0.826 0.909-0.075 0.808-1.052 1.515-2.404 0.202-0.527 0.504-0.977 0.908-1.428z" clip-path="url(#clipPath5817)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m227.36 105c-0.202-0.075-0.202-0.075-0.303-0.075-2.019-0.601-4.139-1.202-6.259-1.728-1.01-0.301-2.12-0.526-3.23-0.677-0.202 0-0.404-0.075-0.505-0.15-0.202-0.15-0.202-0.301-0.101-0.526 0.101-0.075 0.101-0.15 0.202-0.15 0.202 0.3 2.826 0.826 4.542 1.352 1.919 0.526 5.452 1.879 5.654 1.954z" clip-path="url(#clipPath5811)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m224.94 89.741c-0.101 0.3006-0.202 0.6012-0.404 0.8267-0.807 1.6533-2.019 3.1563-3.331 4.5842-0.101 0.1503-0.303 0.3006-0.505 0.3757-0.404 0.1503-0.807 0-1.11-0.0751-2.221-0.7515-1.501-0.2255-3.722-0.977-0.404-0.1503-0.807-0.2254-1.11-0.4509-0.404-0.2254-0.47-0.4122-1.091-0.6675-0.569-0.2343-0.735-0.253-1.138-0.5536-0.404-0.3006-0.57 0-0.57 0-0.201-0.2254-0.569-0.5536 0-0.5536h0.57c0.569 0 0.311 0.5723 1.219 0.8729 0.909 0.3006 1.804 0.5261 3.217 1.0521 1.413 0.6012 3.23 0.8267 4.038 0.4509 0.808-0.3757 2.423-2.48 3.23-3.5321 0.101-0.3757 0.404-0.8266 0.707-1.3527z" clip-path="url(#clipPath5805)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m234.43 87.487c-0.101 0.0752-0.202 0.1503-0.404 0.2255-1.313 0.3006-2.524 0.6763-3.836 0.9769-0.808 0.2255-1.616 0.4509-2.524 0.6012-0.404 0.0752-1.817 0.451-1.615-0.0751 0-0.0752 0-0.0752 0.101-0.0752z" clip-path="url(#clipPath5799)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m243.84 97.232-3.416 0.5536c-1.708 0-2.69 0.2731-3.901 0.4234-1.514 0.2254-3.226 0.0994-4.639 0.4752-0.707 0.1503-1.495 0.3006-2.201 0.3006-0.404 0-1.212-0.0752-1.515-0.3006 0.303 0.0751 0.606 0.0751 0.808-0.0752 0.808-0.3757 7.85-0.8509 9.061-1.2267 1.212-0.3758 5.399 0.1503 5.803-0.1503z" clip-path="url(#clipPath5793)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m271.04 92.729c-0.101 0.0751-0.101 0.1503-0.202 0.1503-0.101 0.0751-0.303 0-0.404 0-3.533-0.4509-5.982-2.2905-9.515-2.2905-1.716 0-10.818 3.3215-11.387 3.3215 0-0.5536 1.139-1.1072 2.277-1.6608 1.111-0.0751 10.145-2.5266 11.357-2.0005 1.413 0.5261 7.773 2.48 7.874 2.48z" clip-path="url(#clipPath5787)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m202.02 111.54c-1.212-0.15-2.625 0.151-3.533-0.451-0.808-0.451-1.01-1.277-1.515-1.878-0.706-0.977-2.019-1.579-2.927-2.48-0.202-0.151-0.505-0.451-0.505-0.677v-0.075c0.909 0.526 3.432 1.954 3.836 2.931 0.505 1.127 2.12 1.578 2.625 1.954 0.404 0.301 1.514 0.15 1.918 0 0 0.15 0.101 0.376 0.101 0.676z" clip-path="url(#clipPath5781)" style=""/>
   <path transform="translate(-4.3881 .9697)" d="m212.92 115.29c-0.101 0.526-0.303 1.052-0.606 1.503-0.101 0.301-0.404 0.602-0.706 0.602-0.303 0-0.404-0.151-0.606-0.301-1.111-0.977-2.221-1.879-3.13-2.931-0.202-0.225-0.403-0.526-0.706-0.751-0.404-0.451-0.909-0.752-1.414-1.203-0.908-0.676-1.716-1.503-2.422-2.329 0.605 0.601 1.615 1.427 3.533 2.705 2.423 1.503 3.028 3.457 4.442 3.682 1.413 0.226 1.211-1.052 1.11-2.029-0.101-0.676 0.303-1.277 0.505-1.954v0.301c0 0.526 0.101 0.977 0.101 1.503-0.101 0.301 0 0.751-0.101 1.202z" clip-path="url(#clipPath5775)" style=""/>
  </g>
 </g>
</svg>