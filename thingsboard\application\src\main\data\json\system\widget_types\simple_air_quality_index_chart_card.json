{"fqn": "simple_air_quality_chart_card", "name": "Simple air quality index chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_air_quality_index_chart_card_system_widget_image.png", "description": "Displays historical air quality index values as a simplified chart. Optionally may display the corresponding latest air quality index value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'air', label: 'Air Quality Index', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'air', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Air Quality Index\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 320) {\\n\\tvalue = 320;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 320) {\\n\\tvalue = 320;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":100,\"color\":\"#FFA600\"},{\"from\":100,\"to\":150,\"color\":\"#F36900\"},{\"from\":150,\"to\":200,\"color\":\"#D81838\"},{\"from\":200,\"to\":300,\"color\":\"#8D268C\"},{\"from\":300,\"to\":null,\"color\":\"#6F113A\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Air Quality Index\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:weather-windy\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":1,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"AQI\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "air", "aqi", "pollution", "emission", "smog"], "resources": [{"link": "/api/images/system/simple_air_quality_index_chart_card_system_widget_image.png", "title": "\"Simple air quality index chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_air_quality_index_chart_card_system_widget_image.png", "publicResourceKey": "4G8IKaj2u7JWrzCOaFaxd69QL4jbF3qH", "mediaType": "image/png", "data": "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", "public": true}]}