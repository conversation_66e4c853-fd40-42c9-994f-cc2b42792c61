{"fqn": "fluid_pressure_progress_bar_with_background", "name": "Pressure progress bar with background", "deprecated": false, "image": "tb-image;/api/images/system/progress_bar_with_background.svg", "description": "Displays fluid pressure reading as a horizontal progress bar with background. Allows to configure value range, bar colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 2, "resources": [], "templateHtml": "<tb-progress-bar-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-progress-bar-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.progressBarWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.progressBarWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '280px',\n        previewHeight: '180px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pressure', label: 'Pressure', type: 'timeseries' }];\n        }\n    };\n};\n\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-progress-bar-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-progress-bar-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 15 - 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"default\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":5,\"color\":\"#2B54CE\"},{\"from\":5,\"to\":10,\"color\":\"#3B911C\"},{\"from\":10,\"to\":15,\"color\":\"#F77410\"},{\"from\":15,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"tickMin\":0,\"tickMax\":25,\"showTicks\":true,\"ticksFont\":{\"family\":\"Roboto\",\"size\":11,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"ticksColor\":\"rgba(0,0,0,0.54)\",\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/pressure_progress_bar_background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"barColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":5,\"color\":\"#2B54CE\"},{\"from\":5,\"to\":10,\"color\":\"#3B911C\"},{\"from\":10,\"to\":15,\"color\":\"#F77410\"},{\"from\":15,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"barBackground\":\"rgba(0, 0, 0, 0.04)\"},\"title\":\"Pressure\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"bar\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"titleIcon\":\"mdi:water-percent\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"18px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.87)\"}"}, "tags": ["fluid pressure", "liquid pressure", "pump pressure", "hydraulic", "pump", "compressibility", "compressive stress", "pipe", "pipeline"], "resources": [{"link": "/api/images/system/pressure_progress_bar_background.png", "title": "pressure_progress_bar_background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "pressure_progress_bar_background.png", "publicResourceKey": "hvYBw5JZnN5QvljpeOy1uvuoqn9330gF", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/progress_bar_with_background.svg", "title": "progress_bar_with_background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "progress_bar_with_background.svg", "publicResourceKey": "gq388XZDwTIXn2GiVlMaQAFqjSwx6mGW", "mediaType": "image/svg+xml", "data": "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", "public": true}]}