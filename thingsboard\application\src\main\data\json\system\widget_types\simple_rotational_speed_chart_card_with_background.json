{"fqn": "simple_rotational_speed_chart_card_with_background", "name": "Simple rotational speed chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_rotational_speed_chart_card_with_background.svg", "description": "Displays historical rotational speed values as a simplified chart with background. Optionally may display the corresponding latest rotational speed value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'rotationalSpeed', label: 'Rotational speed', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'rotationalSpeed', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Rotational speed\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 4000 - 2000;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 4000) {\\n\\tvalue = 4000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 4000 - 2000;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 4000) {\\n\\tvalue = 4000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":500,\"color\":\"#2B54CE\"},{\"from\":500,\"to\":1500,\"color\":\"#3B911C\"},{\"from\":1500,\"to\":3000,\"color\":\"#F89E0D\"},{\"from\":3000,\"to\":null,\"color\":\"#F04022\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/simple_rotational_speed_chart_card_background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Rotational speed\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"360\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"units\":\"RPM\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true},\"margin\":\"0px\"}"}, "tags": ["angular speed", "spin rate", "revolutions", "rotational frequency", "spin motion"], "resources": [{"link": "/api/images/system/simple_rotational_speed_chart_card_background.png", "title": "simple_rotational_speed_chart_card_background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_rotational_speed_chart_card_background.png", "publicResourceKey": "CgzN6niavtUACOJJlIa1bi5IuuYa0tOX", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_rotational_speed_chart_card_with_background.svg", "title": "simple_rotational_speed_chart_card_with_background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_rotational_speed_chart_card_with_background.svg", "publicResourceKey": "I21Gwb89mqS2QJzvkqWfXyfEuVtmIZLp", "mediaType": "image/svg+xml", "data": "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", "public": true}]}