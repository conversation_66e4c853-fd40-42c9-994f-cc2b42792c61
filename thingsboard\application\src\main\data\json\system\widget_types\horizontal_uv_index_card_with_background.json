{"fqn": "horizontal_uv_index_card_with_background", "name": "Horizontal UV Index card with background", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_uv_index_card_with_background_system_widget_image.png", "description": "Displays the latest UV index telemetry in a scalable horizontal layout with the background image.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'uv', label: 'UV Index', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"UV Index\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.ceil(Math.random() * 4 - 2);\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 14) {\\n\\tvalue = 14;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"light_mode\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#7CC322\"},{\"from\":2,\"to\":5,\"color\":\"#F89E0D\"},{\"from\":5,\"to\":7,\"color\":\"#F77410\"},{\"from\":7,\"to\":10,\"color\":\"#F04022\"},{\"from\":10,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#7CC322\"},{\"from\":2,\"to\":5,\"color\":\"#F89E0D\"},{\"from\":5,\"to\":7,\"color\":\"#F77410\"},{\"from\":7,\"to\":10,\"color\":\"#F04022\"},{\"from\":10,\"to\":null,\"color\":\"#DE2343\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/horizontal_uv_index_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal UV Index card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":null,\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "uv", "ultraviolet", "sunburn"], "resources": [{"link": "/api/images/system/horizontal_uv_index_card_with_background_system_widget_background.png", "title": "\"Horizontal UV Index card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_uv_index_card_with_background_system_widget_background.png", "publicResourceKey": "R6L2S8jOW6CmZetSoGtkeMm9bH6BHp1z", "mediaType": "image/png", "data": "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********************************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", "public": true}, {"link": "/api/images/system/horizontal_uv_index_card_with_background_system_widget_image.png", "title": "\"Horizontal UV Index card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_uv_index_card_with_background_system_widget_image.png", "publicResourceKey": "ok4uqVguVahf4TUjfSm6tTdX1FGKnH3e", "mediaType": "image/png", "data": "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", "public": true}]}