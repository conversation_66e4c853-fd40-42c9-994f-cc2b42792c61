{"fqn": "simple_radon_level_chart_card", "name": "Simple radon level chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_radon_level_chart_card_system_widget_image.png", "description": "Displays historical radon level values as a simplified chart. Optionally may display the corresponding latest radon level value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'radon', label: 'Radon level', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'radon', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Radon level\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 75 - 37.5;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 300) {\\n\\tvalue = 300;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 75 - 37.5;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 300) {\\n\\tvalue = 300;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":100,\"color\":\"#80C32C\"},{\"from\":100,\"to\":200,\"color\":\"#FFA600\"},{\"from\":200,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Radon level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:radioactive\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"Bq/m³\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["environment", "indoor", "air", "radon"], "resources": [{"link": "/api/images/system/simple_radon_level_chart_card_system_widget_image.png", "title": "\"Simple radon level chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_radon_level_chart_card_system_widget_image.png", "publicResourceKey": "M4yLN1gHB7vcuh3x8NJnaRttEWz5xZBK", "mediaType": "image/png", "data": "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", "public": true}]}