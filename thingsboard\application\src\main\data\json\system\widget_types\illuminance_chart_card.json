{"fqn": "illuminance_chart_card", "name": "Illuminance chart card", "deprecated": false, "image": "tb-image;/api/images/system/illuminance_chart_card_system_widget_image.png", "description": "Displays a illuminance data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'illuminance', label: 'Illuminance', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'lx', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'illuminance', 'lx', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Illuminance\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"lx\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":1,\"to\":5,\"color\":\"#FFA600\"},{\"from\":5,\"to\":20,\"color\":\"#F36900\"},{\"from\":20,\"to\":50,\"color\":\"#F04022\"},{\"from\":50,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"lx\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"lx\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Illuminance\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:lightbulb-on\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "brightness", "luminance", "luminosity", "light", "light level", "light intensity", "lux", "candela", "foot-candle"], "resources": [{"link": "/api/images/system/illuminance_chart_card_system_widget_image.png", "title": "\"Illuminance chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "illuminance_chart_card_system_widget_image.png", "publicResourceKey": "Xd44pbA1Q9VT4KQ8kCLOdzAS3dM7GZr3", "mediaType": "image/png", "data": "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", "public": true}]}