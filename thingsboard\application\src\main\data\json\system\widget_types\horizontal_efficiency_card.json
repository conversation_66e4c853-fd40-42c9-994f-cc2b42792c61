{"fqn": "horizontal_efficiency_card", "name": "Horizontal efficiency card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_efficiency_card.svg", "description": "Displays the latest efficiency telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "#container {\n    overflow: auto;\n}\n\n.tbDatasource-container {\n    margin: 5px;\n    padding: 8px;\n}\n\n.tbDatasource-title {\n    font-size: 1.200rem;\n    font-weight: 500;\n    padding-bottom: 10px;\n}\n\n.tbDatasource-table {\n    width: 100%;\n    box-shadow: 0 0 10px #ccc;\n    border-collapse: collapse;\n    white-space: nowrap;\n    font-size: 1.000rem;\n    color: #757575;\n}\n\n.tbDatasource-table td {\n    position: relative;\n    border-top: 1px solid rgba(0, 0, 0, 0.12);\n    border-bottom: 1px solid rgba(0, 0, 0, 0.12);\n    padding: 0px 18px;\n    box-sizing: border-box;\n}", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'efficiency', label: 'Efficiency', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Efficiency\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 30;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"horizontal\",\"autoScale\":true,\"showLabel\":true,\"labelFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"#000000DE\",\"rangeList\":null,\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"trending_up\",\"iconColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":0,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":60,\"color\":\"#FFA600\"},{\"from\":60,\"to\":80,\"color\":\"#3FA71A\"},{\"from\":80,\"to\":null,\"color\":\"#305AD7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"36px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":0,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":60,\"color\":\"#FFA600\"},{\"from\":60,\"to\":80,\"color\":\"#3FA71A\"},{\"from\":80,\"to\":null,\"color\":\"#305AD7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Horizontal efficiency card\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"units\":\"%\",\"decimals\":0,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"showTitleIcon\":false,\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\"}"}, "tags": ["productivity", "effectiveness", "performance", "capability"], "resources": [{"link": "/api/images/system/horizontal_efficiency_card.svg", "title": "horizontal_efficiency_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_efficiency_card.svg", "publicResourceKey": "84jYbeejRH7BoXjK267MtDRxpadsi0gE", "mediaType": "image/svg+xml", "data": "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", "public": true}]}