<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="400" height="200" fill="none" version="1.1" viewBox="0 0 400 200">
 <tb:metadata><![CDATA[{
  "title": "Left drain pipe",
  "description": "Left drain pipe with configurable fluid and leak visualizations.",
  "searchTags": [
    "pipe",
    "drain"
  ],
  "widgetSizeX": 2,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "fluid-background",
      "stateRenderFunction": "var color = ctx.properties.fluidColor;\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "leak",
      "stateRenderFunction": "var leak = ctx.values.leak;\nif (leak) {\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "pipe-background",
      "stateRenderFunction": "var color = ctx.properties.pipeColor;\nelement.attr({fill: color});",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "openInSeparateDialog": false,
        "openInPopover": false
      }
    },
    {
      "id": "leak",
      "name": "{i18n:scada.symbol.leak}",
      "hint": "{i18n:scada.symbol.leak-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.leak-present}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    }
  ],
  "properties": [
    {
      "id": "fluidColor",
      "name": "{i18n:scada.symbol.fluid-color}",
      "type": "color",
      "default": "#518DA0",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "pipeColor",
      "name": "{i18n:scada.symbol.pipe-color}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata>
 <g tb:tag="clickArea">
  <rect x="100" y="64" width="286" height="72" fill="#fff" tb:tag="pipe-background"/>
  <rect x="100" y="64" width="286" height="72" fill="url(#paint0_linear_1924_305498)"/>
  <rect x="101.5" y="65.5" width="283" height="69" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
  <rect x="387.5" y="51.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/>
  <ellipse transform="rotate(-90 100 100)" cx="100" cy="100" rx="36" ry="4" fill="#5D5C5C"/>
  <path d="m76.285 94.5c4-9.2 20-22.333 24.001-24.5 0 0 1.499 18.101 1.499 30.5 0 12.399-1.499 33-1.499 33 0 6.5 1.5 9 3.999 14.5 5.089 11.196 3.5 10.5 12.5 13.5s24 2.5 21.5 11.5-32-8-34-4 16 17 10.5 21.5-17.5-10.5-21.5-10.5-4.5 10.5-10 10.5-7-11.5-16-10.5-12 13-18 17-14-2-14.5-11 20.5-7 20.5-13-39 1.5-45.5-4c-6.5-5.5-0.50001-6.5 6.5-10.5 7-4 38 5.5 39 0s-16.5 0-15-5.5 16.5-2 23-10.5 8-36.5 13-48z" fill="#518DA0" tb:tag="fluid-background"/>
  <g style="display:none" tb:tag="leak">
   <path d="m172.47 93.395c0-0.3006-7.47-0.7515-7.47-0.7515l7.975 0.1503s6.461-1.9539 7.874-2.48c1.212-0.526 11.319 3.3214 12.328 3.4717h0.202l-0.101-0.0751-3.533-3.0812c0.505 0.3006 8.694 6.1772 9.905 6.6281 1.312 0.3757 11.29 1.1272 12.098 1.503 0.202 0.0752 2.782 0.0752 3.085 0.0752 1.11-0.1503 2.624-0.9019 3.331-1.1273 0.909-0.3006 2.827-1.1273 2.423-1.503-0.404-0.3758-2.928-5.4861-3.836-5.8618-0.808-0.3758-10.455-2.1794-10.858-2.48-0.404-0.3006-1.313-2.48-1.414-2.7054l1.515 2.4048s0.807 0.3757 1.716 0.4509c0.908 0.0752 9.243 1.4279 9.243 1.4279l-0.606-2.7055c0.101 0.1503 1.009 1.8788 1.817 3.1564 0.303 0.526 0.606 0.9769 0.808 1.2775 0.807 1.0522 2.322 3.1564 3.23 3.5321 0.808 0.3758 2.625 0.1503 4.038-0.4509s5.034-1.7756 5.942-2.0011c0.909-0.3006 2.538-2.057 3.043-2.7334v-0.0751c0.504-0.6764 0.605-1.954 0.303-2.5552-0.404-0.6763-0.303-1.6533-0.606-2.7054s-5.653-4.96-5.653-4.96 0.101 0 0.201 0.0752c0.606 0.3006 2.928 1.503 4.039 2.1794 1.211 0.7515 2.119 1.1272 2.523 1.1272 0.101 0 0.202-0.0751 0.404-0.1503 0.606-0.526 1.817-2.0291 2.726-3.3066 1.11-1.5782 4.442-4.0582 4.946-4.6594 0.505-0.6012-0.706-3.6824-0.908-3.983 0.202 0.3006 1.918 3.0812 2.322 3.4569l0.101 0.0752c0.504 0.1503 1.817-0.3758 3.028-0.9018 1.413-0.6012 5.451-0.6764 6.36-0.977s6.966-2.7054 7.369-2.8557c-0.303 0.1503-2.523 1.4279-4.441 2.5551-0.707 0.3758-1.313 0.7515-1.717 1.0521-1.918 1.2025-0.908 0.6012-2.322 0.8267-1.312 0.2254-4.542 1.0521-6.359 0.977-1.717-0.0752-2.726 0.526-3.635 1.1272-0.908 0.6012-1.615 2.5552-4.038 4.3588-2.422 1.8036-2.725 4.8097-3.23 5.4109-0.101 0.0751-0.101 0.1503-0.101 0.2254-0.101 0.6764 1.312 1.9539 1.918 3.2315 0.707 1.3527 3.029 1.2024 3.836 1.2024 0.909 0.0752 1.313 0.0752 1.716 0.4509 0.404 0.3758 2.726-0.1503 3.534-0.1503 0.908 0.0752 4.34 1.3528 5.249 0.7515 0.909-0.6012-1.413-4.1333-1.312-4.8096v-0.3006c0.101-0.3758 0.101-0.5261 0.605-1.3527 0.505-0.9019-2.624-2.7055-2.826-2.7806 0.202 0.0751 2.12 0.8266 2.524 1.1272 0.403 0.3006 0.706 0.9018 0.807 0.977 0 0 1.413-0.6012 2.322-0.8267 0.909-0.3006 4.139-1.0521 5.048-1.3527 0.908-0.3006 2.523-1.3728 3.028-2.0492 0.505-0.6012 8.189-3.6274 8.694-4.3037 0.504-0.6012 3.723-0.5593 3.723-0.5593s-1.366 1.1071-2.228 1.1071c-0.569 0-0.688-0.0218-0.991 0.354-0.302 0.3757-2.102 0.9387-3.564 1.6077-1.625 0.7444-3.878 1.3075-4.322 1.8694-1.009 1.2775-2.12 1.7486-3.028 2.3498-0.909 0.6012-1.514 1.2024-2.827 1.503-1.312 0.2254-3.129 0.4509-4.543 0.7515-1.413 0.2255-0.504 0.977-0.605 1.6533-0.101 0.6764-0.505 0.3006-0.505 0.977-0.101 0.6763 0.303 0.977 0.707 1.3527 0.101 0.0752 0.101 0.1503 0.202 0.2255 0.302 0.6012 0.605 1.8787 1.312 2.4799 0.808 0.6764 7.167 3.7576 8.48 4.1334 0.101 0 0.202 0.0751 0.303 0.0751 0.504 0.0752 1.312 0.1503 2.221 0.0752 1.918-0.0752 4.239-0.3006 5.047-0.6764 1.413-0.6012 4.24-1.7285 4.745-2.7054 0.504-0.977 4.744-2.7055 5.653-3.3067s10.87-5.6109 11.678-5.2351c0.807 0.3758 4.442-0.3758 8.076-0.5261 1.817-0.0751 3.23-0.4509 4.744-1.1272 1.515-0.6012 3.13-1.4279 5.452-2.2546 4.442-1.6533 9.287-6.6132 9.59-6.989-0.202 0.3006-1.615 2.5551-2.524 3.1563-0.908 0.6013-2.423 1.8037-2.423 1.8037l2.726-0.5261s-2.322 0.8267-3.634 1.1273c-1.413 0.2254-1.514 1.2024-4.24 2.4048-2.827 1.1273-6.562 2.9309-7.47 3.2315-0.909 0.3006-1.817 0.5261-2.726 0.8267s-7.067-0.0752-8.48 0.1503c-1.413 0.2254-3.23 0.8266-3.23 1.1272s0 0.3006 1.211 1.0522c1.212 0.7515 7.47 0.7515 7.874 0.8266 0.404 0 3.13-0.4509 4.947-0.0751 1.716 0.4509 4.442-0.3758 4.946-0.3758 0.404 0-2.725 0.8267-3.23 0.8267-0.404 0-3.533 0.1503-4.038 0.0751-0.404 0 0.404 0.3758 0.808 0.3758 0.403 0 0.807 1.0521 1.211 1.4278 0.404 0.3758 1.514 2.1043 2.322 3.1564 0.807 1.0521-0.404 3.3066-0.404 3.3066s0.202-1.6533-0.202-2.3296c-0.404-0.6764-1.11-1.7285-1.615-1.7285-0.404 0-0.303-1.3527-1.918-2.4049-1.615-1.1272-3.029-1.2024-4.745-1.2775-1.817-0.0752-13.764 3.6824-16.159 4.4339-1.708 0.5359-3.292 2.0036-4.604 2.2291-1.413 0.2254-5.956 1.2775-9.086 2.1042-3.23 0.8267-8.479 4.4341-8.479 4.4341h0.201c0.404 0 1.313 0 2.019-0.226 0.808-0.225 3.837-1.653 4.543-1.9536l0.101-0.0751-0.101 0.0751c-0.202 0.0756-0.505 0.3006-0.908 0.5256-0.303 0.226-1.212 0.827-1.817 1.203-0.303 0.15-0.505 0.301-0.505 0.301 0.101 0 6.106-1.122 8.563-0.561s10.073 6.873 10.073 6.873-7.971-5.035-10.817-5.536c-2.494-0.439-5.148-0.207-7.648 0.089-2.537 0.302-5.037 0.788-5.219 0.788-0.403 0-1.413 0.902-2.322 1.202-0.908 0.301-4.34 2.706-4.845 3.382-0.505 0.601 1.716 5.035 1.312 4.735-0.404-0.376-1.817-4.059-2.221-4.134-0.404 0-0.504 0.301-0.908 0.601-0.505 0.301-2.524 3.157-3.029 3.157-0.404 0 0.808 0.676 1.212 1.052 0.403 0.376 0.706 1.728 0.605 2.33-0.101 0.601 1.01 1.277 1.212 1.352-0.202-0.075-1.716-0.751-1.615-1.052 0-0.3-1.01-2.705-1.919-3.081-0.807-0.376-1.817 0.225-2.725 0.451-0.909 0.301-2.12 2.33-2.625 2.63-0.505 0.301-2.423 1.321-2.927 1.998-0.505 0.601-3.13 1.459-3.635 2.136-0.504 0.601 1.817 3.456 2.12 4.734 0.303 1.353-0.908 4.584-0.908 4.584s0.303-2.63 0.404-3.983c0.1-1.277-1.919-3.081-2.322-3.156-0.404 0-0.909 0.3-1.414 0.902-0.504 0.601-1.211 2.555-1.716 3.231-0.505 0.601-0.606 1.654-1.615 2.856-0.606 0.827-0.909 1.202-1.01 1.428 0.202-0.451 0.606-1.503 0.606-1.804 0-0.3 1.615-2.254 1.615-2.856 0.101-0.676 2.221-4.509 2.524-6.463 0.202-1.953-2.322-7.74-2.12-8.717 0.101-0.977 1.514-1.578 1.615-2.856 0.101-1.353-1.817-3.757-4.845-4.283-2.625-0.451-7.471 0.751-8.783 1.052-0.101 0-0.101 0-0.202 0.075h-0.101c0.202-0.075 3.735-1.428 5.553-1.954 1.817-0.526 4.34-1.052 4.542-1.353 0-0.3-1.615-1.728-2.423-2.104-0.807-0.3758-2.523-1.1273-3.836-1.2024-1.312-0.0752-1.817 0.2254-2.725 0.8266-0.909 0.6012-3.186 0.6012-5.609 1.8038-2.221 1.127-5.35 2.555-5.653 2.63 0.303-0.225 5.249-3.307 4.845-3.682-0.403-0.376-6.949-0.5263-9.977-0.7518-3.13-0.1503-16.496-3.0812-17.808-3.1563-1.313-0.0752-2.847 0-6.263 0.2545-3.412 0-7.854-1.4865-7.854-1.4865s3.836-1.2776 3.937-1.5782zm68.711 17.389c0.404 0.075 1.514 0.301 1.918 0 0.505-0.376 2.12-0.751 2.625-1.954 0.403-0.977 2.927-2.405 3.836-2.931 0.202-0.15 0.302-0.15 0.302-0.15-1.009-0.376-6.258 0.752-6.763 1.127-0.505 0.376 0 1.578-0.505 1.954s-1.514 1.954-1.514 1.954h0.101zm-1.515-0.902c0.101-0.15 0.202-0.3 0.303-0.375 0.505-0.602 1.414-0.602 1.515-1.203 0.101-0.676-0.808-1.052-1.212-1.428-0.404-0.375-3.634 0.451-3.634 0.451s-6.461 2.555-6.663 3.908c0 0.376 0 0.752 0.101 1.052 0.202 0.752 0.606 1.278 0.505 1.954-0.101 0.977-0.202 2.33 1.11 2.029 1.313-0.225 2.019-2.179 4.442-3.682 1.918-1.278 2.928-2.104 3.533-2.706zm-10.498-5.786c0.302 0.375 0.706 0.902 1.009 1.428 0.707 1.352 0.606 2.329 1.514 2.404 0.909 0.076 1.818-0.225 3.231-0.826 0.706-0.301 1.413-0.526 2.12-0.752 0.706-0.225 1.11-0.375 1.11-0.375s-1.615-1.128-2.624-0.827c-0.909 0.3-1.414 0.225-1.818-0.075-0.403-0.376-5.148-1.954-5.552-2.029 0.101 0.075 0.505 0.451 1.01 1.052zm-3.231-4.058c0.404 0.375 1.615 1.728 1.615 1.728h0.101c0.404 0.075 2.726 0.526 4.24 1.202 0.909 0.376 3.231 1.278 5.654 1.879 0.201 0.075 0.504 0.151 0.706 0.226 1.918 0.451 3.735 0.751 4.846 0.526 2.725-0.526 5.047-1.654 6.864-1.879 1.818-0.225 2.221 0.15 3.635-0.451 1.413-0.601 1.918-1.202 3.331-2.104s2.019-1.8789 2.928-2.4801c0.605-0.3758-0.346-0.451-0.346-0.977-0.569-0.4761-1.139-0.4761-1.139-0.4761s-0.837-0.3506-2.15-0.8015c-1.312-0.3757-5.425-1.962-9.237-1.4128-3.345 0.4822-5.382-0.0192-8.732 0.4359-3.611 0.4906-4.744-0.4219-8.076 1.006-3.23 1.4279-7.571 0.6473-7.571 0.6473s2.927 2.5551 3.331 2.9313z" fill="#5C5A5A"/>
   <path d="m263.69 95.378c0.504 0.3006 1.11 0.6012 1.716 0.6764 1.11 0.1503 2.221 0 3.23-0.1503 4.139-0.6764 8.379-2.0291 11.004-4.5091 0.505-0.4509 1.009-0.977 1.716-1.3527 1.615-0.9018 4.031-3.9565 6.05-3.8814 6.832-3.3214 14.318-2.0713 17.649-3.875-1.514 0.6012-9 1.032-10.817 1.1072-3.533 0.1503-5.694 1.6607-7.741 2.3921-1.695 0.6055-4.131 2.4535-5.04 3.0547s-5.148 2.3297-5.653 3.3066c-0.505 0.977-3.332 2.1043-4.745 2.7055-1.413 0.4509-5.754 0.8266-7.369 0.526z" fill="#8B8B8B"/>
   <path d="m236.13 86.118c0.202 1.3528 0.908 2.7055 2.019 3.7576 0.202 0.2255 0.505 0.4509 0.807 0.5261 0.909 0.3757 2.019 0.1503 3.029 0.2254 0.908 0.0752 1.716 0.3758 2.625 0.4509 0.504 0.0752 1.11 0.0752 1.615 0 1.11-0.0751 1.896-0.4889 3.245 0 0.843 0.6934 2.067 0.6769 3.206 0.6769 0.419 0 0.845 0.1423 0.845-0.3964 0-0.4081-0.216-0.3556-0.276-0.7107-0.202-0.6764-0.937-2.0915-1.139-2.7679-0.101-0.3757-0.531-0.7097-0.632-1.0855 0-0.1503 0-0.5261-0.202-0.6764v0.3006c-0.101 0.6764 1.174 3.5693 0.265 4.1705-0.908 0.6012-3.394-0.0371-4.202-0.1123-0.908-0.0751-3.129 0.4509-3.533 0.1503-0.404-0.3757-0.808-0.3757-1.716-0.4509-0.909-0.0751-3.13 0.1503-3.836-1.2024-0.707-1.2024-2.019-2.48-1.918-3.2315-0.202 0-0.202 0.2254-0.202 0.3757z" fill="#8B8B8B"/>
   <path d="m229.27 79.07c0.808 0.7515 1.918 1.3527 2.827 1.9539 1.211 0.6764 2.322 1.4279 3.533 2.1043 0.101-0.3006 0.404-0.6012 0.505-0.977-0.101 0.1503-0.303 0.1503-0.404 0.1503-0.404 0-1.312-0.3757-2.524-1.1273-1.009-0.6012-3.331-1.7284-3.937-2.1042z" fill="#8B8B8B"/>
   <path d="m245.42 73.734c0.101 0.0752 0.101 0.1503 0.202 0.2255 0.101 0.0751 0.202 0.0751 0.303 0.0751 0.404 0.0752 0.808 0.0752 1.313 0 1.514-0.1503 2.826-0.8266 4.34-1.1272 0.707-0.1503 1.515-0.1503 2.221-0.3006 0.606-0.0752 1.212-0.2255 1.817-0.451 0.707-0.2254 1.515-0.3757 2.221-0.6012 2.019-1.2024 4.442-2.5551 4.442-2.5551s-6.562 2.5551-7.47 2.8557c-0.909 0.3006-4.947 0.3758-6.36 0.977-1.312 0.6012-2.524 1.1273-3.029 0.9018z" fill="#8B8B8B"/>
   <path d="m252.69 84.707c1.414-0.9018 3.231-1.2024 5.048-1.503 0.707-0.0752 1.413-0.2255 1.918-0.5261 0.404-0.2254 0.707-0.526 1.01-0.8266 0.706-0.6764 0.987-0.5494 1.693-1.2258 0.303-0.3006 0.57-0.5536 1.139-1.1071 0.943-0.3589 1.139 0 2.277-0.5536 1.139-1.1072 3.372-1.5178 4.886-2.4948 0.303-0.3757 1.175-0.6763 1.377-0.8266 0.505-0.3006 3.416-0.5536 3.416-0.5536s-4.793-0.198-5.298 0.4784c-0.504 0.6012-5.685 2.742-7.169 3.2019-0.764 0.2368-2.12 2.8557-3.028 3.1563-0.909 0.3006-4.139 1.0521-5.048 1.3527-0.908 0.3006-2.322 0.8267-2.322 0.8267-0.1 0.1503 0 0.3757 0.101 0.6012z" fill="#8B8B8B"/>
   <path d="m253.6 88.69c0.101 0.3757 0.202 0.8266 0.303 0.9769 0 0.6012 0.101 1.2776 0.606 1.7285 0.202 0.2254 0.504 0.3757 0.807 0.526 0.909 0.5261 1.817 0.977 2.827 1.5031 0.606 0.3006 1.211 0.6763 1.918 0.9018 0.505 0.1503 1.009 0.3006 1.514 0.526 0.505 0.2255 1.01 0.4509 1.514 0.6764 0.909 0.3006 1.818 0.5261 2.827 0.5261h0.202c0.202-0.0752 0.202-0.2255 0.101-0.3758-0.101-0.0751-0.202-0.1503-0.303-0.1503-0.909 0-1.716 0-2.221-0.0752-0.101 0-0.202-0.0751-0.303-0.0751-1.312-0.3758-7.672-3.457-8.48-4.1333-0.706-0.6764-1.009-1.9539-1.312-2.5551z" fill="#8B8B8B"/>
   <path d="m237.44 104.92c1.01 0.451 2.12 0.827 3.231 1.127 0.807 0.151 1.615 0.301 2.423 0.151 0.605-0.076 1.11-0.376 1.615-0.602 0.908-0.375 1.918-0.526 2.927-0.751 1.01-0.226 2.019-0.376 3.13-0.526 0.404 0 0.807-0.075 1.211-0.15 0.505-0.076 0.909-0.301 1.414-0.451 0.908-0.376 1.716-0.752 2.523-1.203 0.707-0.451 1.414-0.902 2.019-1.428 1.01-0.901 2.928-2.0287 3.029-3.3063 0-0.3006-0.101-0.6764-0.303-0.9018 0 0.526-0.101 1.3527-0.707 1.7285-0.908 0.6012-1.514 1.5786-2.927 2.4796-1.414 0.902-1.918 1.503-3.332 2.105-1.413 0.601-1.817 0.225-3.634 0.45-1.817 0.226-4.139 1.428-6.864 1.879-1.414 0.376-3.534 0-5.755-0.601z" fill="#8B8B8B"/>
   <path d="m257.54 102.07v0.376c1.514-0.075 2.928-0.451 4.341-0.977 0.606-0.376 1.514-0.977 1.817-1.203 0.303-0.225 0.707-0.4505 0.909-0.5257-0.707 0.3007-3.735 1.7287-4.543 1.9537-0.707 0.226-1.615 0.226-2.019 0.226-0.202 0.075-0.303 0.075-0.505 0.15z" fill="#8B8B8B"/>
   <path d="m227.55 102.44c0.808 0 1.616 0.225 2.322 0.451 2.827 0.827 5.553 1.578 8.379 2.254v-0.075c-0.202-0.075-0.505-0.15-0.706-0.225-2.423-0.677-4.745-1.503-5.654-1.879-1.514-0.676-3.836-1.127-4.24-1.202-0.101 0.3-0.101 0.676-0.101 0.676z" fill="#8B8B8B"/>
   <path d="m229.17 104.1c0.101 0.15 0.303 0.375 0.404 0.526 0.101 0.3 0.303 0.526 0.404 0.826 0.303 0.827 0.404 1.654 0.706 2.405 0.101 0.151 0.101 0.301 0.202 0.451 0.303 0.226 0.707 0.301 1.111 0.226 0.404-0.076 0.807-0.301 1.11-0.451 0.909-0.451 1.918-0.902 2.928-1.278 0.303-0.075 0.606-0.225 0.908-0.451-0.605 0.226-1.413 0.451-2.12 0.752-1.413 0.601-2.321 0.826-3.23 0.826-0.909-0.075-0.808-1.052-1.514-2.404-0.202-0.526-0.505-0.977-0.909-1.428z" fill="#8B8B8B"/>
   <path d="m215.64 105c0.202-0.075 0.202-0.075 0.303-0.075 2.019-0.601 4.139-1.202 6.259-1.729 1.01-0.3 2.12-0.526 3.23-0.676 0.202 0 0.404-0.075 0.505-0.15 0.202-0.151 0.202-0.301 0.101-0.526-0.101-0.075-0.101-0.151-0.202-0.151-0.202 0.301-2.826 0.827-4.543 1.353-1.918 0.526-5.451 1.879-5.653 1.954z" fill="#8B8B8B"/>
   <path d="m218.06 89.741c0.101 0.3006 0.202 0.6012 0.404 0.8267 0.807 1.6533 2.019 3.1563 3.331 4.5842 0.101 0.1503 0.303 0.3006 0.505 0.3757 0.404 0.1503 0.807 0 1.11-0.0751 2.221-0.7515 1.501-0.2255 3.722-0.977 0.404-0.1503 0.807-0.2254 1.11-0.4509 0.404-0.2254 0.47-0.4122 1.091-0.6675 0.569-0.2343 0.735-0.253 1.138-0.5536 0.404-0.3006 0.57 0 0.57 0 0.201-0.2254 0.569-0.5536 0-0.5536h-0.57c-0.569 0-0.311 0.5723-1.219 0.8729-0.909 0.3006-1.804 0.5261-3.217 1.0521-1.413 0.6012-3.23 0.8267-4.038 0.4509-0.808-0.3757-2.423-2.48-3.23-3.5321-0.101-0.3757-0.404-0.8266-0.707-1.3527z" fill="#8B8B8B"/>
   <path d="m208.57 87.486c0.101 0.0752 0.202 0.1503 0.404 0.2255 1.312 0.3006 2.524 0.6763 3.836 0.9769 0.808 0.2255 1.615 0.451 2.524 0.6013 0.404 0.0751 1.817 0.4509 1.615-0.0752 0-0.0751 0-0.0751-0.101-0.0751l-8.278-1.6534z" fill="#8B8B8B"/>
   <path d="m199.16 97.232 3.416 0.5536c1.708 0 2.689 0.2731 3.9 0.4234 1.515 0.2254 3.227 0.0994 4.64 0.4752 0.706 0.1503 1.494 0.3006 2.201 0.3006 0.404 0 1.211-0.0751 1.514-0.3006-0.303 0.0752-0.606 0.0752-0.808-0.0751-0.807-0.3758-7.849-0.851-9.061-1.2268-1.211-0.3757-5.399 0.1503-5.802-0.1503z" fill="#8B8B8B"/>
   <path d="m171.96 92.73c0.101 0.0751 0.101 0.1503 0.202 0.1503 0.101 0.0751 0.303 0 0.403 0 3.534-0.4509 5.982-2.2904 9.516-2.2904 1.716 0 10.817 3.3214 11.387 3.3214 0-0.5536-1.139-1.1072-2.278-1.6607-1.11-0.0752-10.145-2.5267-11.356-2.0006-1.413 0.5261-7.773 2.48-7.874 2.48z" fill="#8B8B8B"/>
   <path d="m240.98 111.54c1.212-0.151 2.625 0.15 3.533-0.451 0.808-0.451 1.01-1.278 1.515-1.879 0.706-0.977 2.019-1.578 2.927-2.48 0.202-0.15 0.505-0.451 0.505-0.676v-0.075c-0.909 0.526-3.432 1.954-3.836 2.93-0.505 1.128-2.12 1.579-2.625 1.954-0.404 0.301-1.514 0.151-1.918 0 0 0.151-0.101 0.376-0.101 0.677z" fill="#8B8B8B"/>
   <path d="m230.08 115.29c0.101 0.526 0.303 1.052 0.605 1.503 0.101 0.3 0.404 0.601 0.707 0.601s0.404-0.15 0.606-0.301c1.11-0.977 2.221-1.878 3.129-2.931 0.202-0.225 0.404-0.526 0.707-0.751 0.404-0.451 0.908-0.752 1.413-1.203 0.909-0.676 1.716-1.503 2.423-2.329-0.606 0.601-1.615 1.428-3.533 2.705-2.423 1.503-3.029 3.457-4.442 3.683-1.413 0.225-1.211-1.052-1.111-2.029 0.101-0.677-0.302-1.278-0.504-1.954v0.3c0 0.526-0.101 0.977-0.101 1.503 0.101 0.301 0 0.752 0.101 1.203z" fill="#8B8B8B"/>
  </g>
 </g>
 <defs>
  <linearGradient id="paint0_linear_1924_305498" x1="174.36" x2="174.25" y1="64" y2="136.01" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
 </defs>
</svg>