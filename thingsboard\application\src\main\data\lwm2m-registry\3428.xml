<?xml version="1.0" encoding="UTF-8"?>

<!-- BSD-3 Clause License

Copyright 2020 uCIFI Alliance

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>Air quality</Name>
		<Description1>The uCIFI air quality sensor reports measurement required to calculate Air Quality Index (AIQ. It also provides resources for average calculation as per the IQ index calculation.</Description1>
		<ObjectID>3428</ObjectID>
		<ObjectURN>urn:oma:lwm2m:ext:3428</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="1">
				<Name>PM10</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ug/m3</Units>
				<Description>Level of PM10 measured by the air quality sensor.</Description>
			</Item>
			<Item ID="2">
				<Name>PM10 24 hour average</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ug/m3</Units>
				<Description>Average level of PM10 measured by the sensor during the last 24 hours.</Description>
			</Item>
			<Item ID="3">
				<Name>PM2.5</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ug/m3</Units>
				<Description>Level of PM2.5 measured by the air quality sensor.</Description>
			</Item>
			<Item ID="4">
				<Name>PM2.5 24 hour average</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ug/m3</Units>
				<Description>Average level of PM2.5 measured by the sensor during the last 24 hours.</Description>
			</Item>
			<Item ID="5">
				<Name>PM1</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ug/m3</Units>
				<Description>Level of PM1 measured by the air quality sensor.</Description>
			</Item>
			<Item ID="6">
				<Name>PM1 24 hour average</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ug/m3</Units>
				<Description>Average level of PM1 measured by the sensor during the last 24 hours.</Description>
			</Item>
			<Item ID="7">
				<Name>CO</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Level of carbon monoxide measured by the air quality sensor.</Description>
			</Item>
			<Item ID="8">
				<Name>CO 8 hour average</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Average level of carbon monoxide measured by the sensor during the last 8 hours.</Description>
			</Item>
			<Item ID="9">
				<Name>SO2</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Level of sulfur dioxide measured by the air quality sensor.</Description>
			</Item>
			<Item ID="10">
				<Name>SO2 1 hour average</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Average level of sulfur dioxide measured by the sensor during the last 1 hour.</Description>
			</Item>
			<Item ID="11">
				<Name>SO2 24 hour average</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Average level of sulfur dioxide measured by the sensor during the last 24 hours.</Description>
			</Item>
			<Item ID="12">
				<Name>O3</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Level of ozone measured by the air quality sensor.</Description>
			</Item>
			<Item ID="13">
				<Name>O3 1 hour average</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Average level of ozone measured by the sensor during the last 1 hour.</Description>
			</Item>
			<Item ID="14">
				<Name>O3 8 hour average</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Average level of ozone measured by the sensor during the last 8 hour.</Description>
			</Item>
			<Item ID="15">
				<Name>NO2</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Level of nitrogen dioxide measured by the air quality sensor.</Description>
			</Item>
			<Item ID="16">
				<Name>NO2 1 hour average</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Average level of nitrogen dioxide measured by the sensor during the last 1 hour.</Description>
			</Item>
			<Item ID="17">
				<Name>CO2</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Level of carbon dioxide measured by the air quality sensor.</Description>
			</Item>
			<Item ID="18">
				<Name>CO2 1 hour average</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Average level of carbon dioxide measured by the sensor during the last 1 hour.</Description>
			</Item>
			<Item ID="19">
				<Name>NO</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Level of nitric oxide measured by the air quality sensor.</Description>
			</Item>
			<Item ID="20">
				<Name>NO 1 hour average</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Average level of nitric oxide measured by the sensor during the last 1 hour.</Description>
			</Item>
			<Item ID="21">
				<Name>H2S</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Level of hydrogen sulfid measured by the air quality sensor.</Description>
			</Item>
			<Item ID="22">
				<Name>H2S 1 hour average</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>ppm</Units>
				<Description>Average level of hydrogen sulfid measured by the sensor during the last 1 hour.</Description>
			</Item>
		</Resources>
		<Description2></Description2>
	</Object>
</LWM2M>
