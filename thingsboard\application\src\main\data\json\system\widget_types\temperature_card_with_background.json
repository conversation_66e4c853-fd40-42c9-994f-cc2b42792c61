{"fqn": "temperature_card_with_background", "name": "Temperature card with background", "deprecated": false, "image": "tb-image;/api/images/system/temperature_card_with_background_system_widget_image.png", "description": "Displays the latest temperature telemetry in a scalable rectangle card with the background image.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'temperature', label: 'Temperature', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"thermostat\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#224AC2\"},{\"from\":-20,\"to\":0,\"color\":\"#2B54CE\"},{\"from\":0,\"to\":10,\"color\":\"#6083EC\"},{\"from\":10,\"to\":20,\"color\":\"#F89E0D\"},{\"from\":20,\"to\":30,\"color\":\"#F77410\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":52,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#224AC2\"},{\"from\":-20,\"to\":0,\"color\":\"#2B54CE\"},{\"from\":0,\"to\":10,\"color\":\"#6083EC\"},{\"from\":10,\"to\":20,\"color\":\"#F89E0D\"},{\"from\":20,\"to\":30,\"color\":\"#F77410\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#DE2343\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/temperature_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Temperature card with background\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"°C\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["temperature", "weather", "environment"], "resources": [{"link": "/api/images/system/temperature_card_with_background_system_widget_background.png", "title": "\"Temperature card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "temperature_card_with_background_system_widget_background.png", "publicResourceKey": "4S5du5LOJHOU0GmMaoUFEQreCDm3XhnS", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/temperature_card_with_background_system_widget_image.png", "title": "\"Temperature card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "temperature_card_with_background_system_widget_image.png", "publicResourceKey": "AnBNDngRLKqv2voxm8CLFVKKIpRpXakZ", "mediaType": "image/png", "data": "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", "public": true}]}