{"fqn": "indoor_temperature_card", "name": "Indoor temperature card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_temperature_card_system_widget_image.png", "description": "Displays the latest indoor temperature telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'temperature', label: 'Temperature', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nif (value < 15) {\\n\\tvalue = 15;\\n} else if (value > 30) {\\n\\tvalue = 30;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"thermostat\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":18,\"color\":\"#234CC7\"},{\"from\":18,\"to\":24,\"color\":\"#3FA71A\"},{\"from\":24,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":52,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":18,\"color\":\"#234CC7\"},{\"from\":18,\"to\":24,\"color\":\"#3FA71A\"},{\"from\":24,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Temperature card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"°C\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["temperature", "environment", "indoor"], "resources": [{"link": "/api/images/system/indoor_temperature_card_system_widget_image.png", "title": "\"Indoor temperature card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_temperature_card_system_widget_image.png", "publicResourceKey": "1ika2Llrrd7Y1Uv6jFVVIy20M6NvzKSO", "mediaType": "image/png", "data": "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", "public": true}]}