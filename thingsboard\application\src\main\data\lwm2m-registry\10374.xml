<?xml version="1.0" encoding="utf-8"?><!--Copyright 2021 AVSystem.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, E<PERSON>EM<PERSON>AR<PERSON>, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

-->
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>Modbus Connection</Name>
		<Description1><![CDATA[This LwM2M Object is used to configure a Modbus protocol connection.]]></Description1>
		<ObjectID>10374</ObjectID>
		<ObjectURN>urn:oma:lwm2m:x:10374</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>Address</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Address uniquely defining the Modbus connection within a given physical layer. The LwM2M Client is assumed to take the role of a Modbus client.
For Modbus TCP, this is a hostname with optional port number, e.g. "***********" or "example.com:502". Port number 502 is the default if not specified.
For Modbus RTU, this is an implementation-defined unique identifier of a physical port. Suggested formats are UNIX-style device names (e.g. "/dev/tty1") or DOS-style port designations (e.g. "COM2"), but any strings may be used. Values that are valid for this LwM2M Client are listed in the "Available RTU ports" resource. The Client MAY treat configuration as invalid if Physical Layer Type is set to Modbus RTU and the Address is not equal to any of the "Available RTU ports".]]></Description>
			</Item>
			<Item ID="1">
				<Name>Physical Layer Type</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..2</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Type of the physical layer to use for this connection.
0: Modbus TCP
1: Modbus RTU (RS-232)
2: Modbus RTU (RS-485)
Additional types MAY be specified in future revisions of this object.]]></Description>
			</Item>
			<Item ID="2">
				<Name>Enable</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Enables or disables this Modbus connection.
When set to false, this Modbus connection is administratively disabled and shall not function. Upon setting it to true, the LwM2M Client shall attempt opening the relevant connection and report the result of that operation in the "State" resource.]]></Description>
			</Item>
			<Item ID="3">
				<Name>Baudrate</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>bit/s</Units>
				<Description><![CDATA[Baudrate for the RTU link layer, e.g. 9600, 115200, etc.
NOTE: 8 data bits are always used.
Mandatory if Physical Layer Type is set to Modbus RTU.]]></Description>
			</Item>
			<Item ID="4">
				<Name>Stop Bits</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>1..2</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Number of stop bits to use for the RTU link layer.
Mandatory if Physical Layer Type is set to Modbus RTU.]]></Description>
			</Item>
			<Item ID="5">
				<Name>Parity</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..2</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Parity bit type to use for the RTU link layer.
0: none
1: even
2: odd
Mandatory if Physical Layer Type is set to Modbus RTU.]]></Description>
			</Item>
			<Item ID="6">
				<Name>Hardware Control Flow Mode</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..2</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Type of hardware flow control to use for the RTU link layer.
0: none
1: Set RTS flag when sending data
2: Set RTS flag when NOT sending data
Mandatory if Physical Layer Type is set to Modbus RTU.]]></Description>
			</Item>
			<Item ID="7">
				<Name>Available RTU Ports</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[List of all valid values for the "Address" resource when Physical Layer Type is set to Modbus RTU. The Resource Instance IDs shall have no semantic meaning. The value of this resource shall be equal for all instances of this Object.
Mandatory if this LwM2M Client has at least one physical port usable for Modbus RTU.]]></Description>
			</Item>
			<Item ID="8">
				<Name>State</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..4</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[State of this Modbus connection.
0: disabled (MUST NOT be used unless the "Enable" resource is set to false)
1: connecting
2: successfully connected
3: invalid or unsupported configuration
4: connection error
If this resource reached the "connection error" state after having been successfully connected previously, the LwM2M Client SHOULD apply an implementation-specific deferred retry policy to try establishing the connection again.
If the resource reached the "connection error" state as a direct result of setting the "Enable" resource to true or changing the configuration while in the enabled state, the LwM2M Client MAY apply an implementation-specific deferred retry policy to try establishing the connection again, or it MAY stay in the "connection error" state until further reconfiguration by the LwM2M Server.
NOTE: The "connection error" state corresponds to any non-specific failure case. More specific error cases MAY be specified in future revisions of this object. LwM2M Server SHOULD interpret any unknown or invalid value reported by the LwM2M Client as equivalent to "connection error".]]></Description>
			</Item>
		</Resources>
		<Description2><![CDATA[]]></Description2>
	</Object>
</LWM2M>
