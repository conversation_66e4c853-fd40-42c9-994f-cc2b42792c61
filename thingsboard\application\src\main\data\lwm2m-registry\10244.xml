<?xml version="1.0" encoding="utf-8"?><LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>VehicleControlUnit</Name>
		<Description1><![CDATA[This Object provides the information to represent a generic VCU(vehicle control unit).]]></Description1>
		<ObjectID>10244</ObjectID>
		<ObjectURN>urn:oma:lwm2m:x:10244</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Single</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0"><Name>Vehicle UI State</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..15</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[The UI state of the vehicle. 0: idle 1: driving 2: charging 3: limp-home 4-15: reserved for future use]]></Description>
			</Item>
			<Item ID="1"><Name>Vehicle Speed</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>km/h</Units>
				<Description><![CDATA[Current speed of the vehicle.]]></Description>
			</Item>
			<Item ID="2"><Name>Vehicle Shift Status</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..3</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Current shift status of the vehicle. 0: Neutral 1: Forward 2: Reverse]]></Description>
			</Item>
			<Item ID="3"><Name>Vehicle AP Position</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..100</RangeEnumeration>
				<Units>/100</Units>
				<Description><![CDATA[Current position of the accelerator pedal.]]></Description>
			</Item>
			<Item ID="4"><Name>Vehicle Power</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>kW</Units>
				<Description><![CDATA[Current power of drive output/regenerative braking.]]></Description>
			</Item>
			<Item ID="5"><Name>Vehicle Drive Energy</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>Wh</Units>
				<Description><![CDATA[Accumulated drive energy of the vehicle.]]></Description>
			</Item>
			<Item ID="6"><Name>Vehicle Energy Consumption Efficiency</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>Wh/km</Units>
				<Description><![CDATA[Energy consumption efficiency of the vehicle.]]></Description>
			</Item>
			<Item ID="7"><Name>Vehicle Estimated Mileage</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>km</Units>
				<Description><![CDATA[Estimated mileage of current battery capacity.]]></Description>
			</Item>
			<Item ID="8"><Name>Vehicle Charge Cable Status</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Whether the charge cable is connected or not. 0: unconnected 1: connected]]></Description>
			</Item>
			<Item ID="9"><Name>Vehicle Charge Status</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..15</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Charging status of the vehicle. 1: non-charge mode 2: charging 3: charge completed 4: charging abort abnormally ]]></Description>
			</Item>
			<Item ID="10"><Name>Vehicle Charge Voltage</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>V</Units>
				<Description><![CDATA[Charging voltage]]></Description>
			</Item>
			<Item ID="11"><Name>Vehicle Charge Current</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>A</Units>
				<Description><![CDATA[Charging current]]></Description>
			</Item>
			<Item ID="12"><Name>Vehicle Charge Remaining Time</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>min</Units>
				<Description><![CDATA[Remaining charging time]]></Description>
			</Item>
			<Item ID="13"><Name>Battery Pack Voltage</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>V</Units>
				<Description><![CDATA[Voltage of the battery pack]]></Description>
			</Item>
			<Item ID="14"><Name>Battery Pack Current</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Float</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>A</Units>
				<Description><![CDATA[Current of the battery pack]]></Description>
			</Item>
			<Item ID="15"><Name>Battery Pack Remaining Capacity</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>Ah</Units>
				<Description><![CDATA[Remaining capacity of the battery pack]]></Description>
			</Item>
			<Item ID="16"><Name>Battery Pack SOC</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..100</RangeEnumeration>
				<Units>/100</Units>			
				<Description><![CDATA[SOC(state of charge) of the battery pack]]></Description>
			</Item>
			<Item ID="17"><Name>Battery Pack SOH</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..100</RangeEnumeration>
				<Units>/100</Units>
				<Description><![CDATA[SOH(state of health) of the battery pack]]></Description>
			</Item>
			<Item ID="18"><Name>Battery Cell MinVolt</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>mV</Units>
				<Description><![CDATA[Minimum voltage of the battery module (with parallel connection of cells)]]></Description>
			</Item>
			<Item ID="19"><Name>Battery Cell MaxVolt</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>mV</Units>
				<Description><![CDATA[Maximum voltage of the battery module (with parallel connection of cells)]]></Description>
			</Item>
			<Item ID="20"><Name>Battery Module MinTemp</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>Cel</Units>
				<Description><![CDATA[Minimum temperature of the battery module]]></Description>
			</Item>
			<Item ID="21"><Name>Battery Module MaxTemp</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>Cel</Units>
				<Description><![CDATA[Maximum temperature of the battery module]]></Description>
			</Item>
			<Item ID="22"><Name>Battery Connection Status</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Whether the battery is connected or not. 0: unconnected 1: connected]]></Description>
			</Item>
			
			<Item ID="24"><Name>MCU Voltage</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>V</Units>
				<Description><![CDATA[Voltage of the MCU(motor control unit)]]></Description>
			</Item>
			<Item ID="25"><Name>MCU Temperature</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>Cel</Units>
				<Description><![CDATA[Temperature of MCU(motor control unit)]]></Description>
			</Item>
			<Item ID="26"><Name>Motor Speed</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>1/min</Units>
				<Description><![CDATA[Rotational speed of the motor]]></Description>
			</Item>
			<Item ID="27"><Name>Motor Temperature</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>Cel</Units>
				<Description><![CDATA[Temperature of the motor]]></Description>
			</Item>
			<Item ID="28"><Name>Motor OT Warning</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Whether the motor is OT or not. 0: normal 1: OT warning]]></Description>
			</Item>
			<Item ID="29"><Name>MCU OT Warning</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Whether the MCU is OT or not. 0: normal 1: OT warning]]></Description>
			</Item>
			<Item ID="30"><Name>Battery Pack OT Warning</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Whether the battery pack is OT or not. 0: normal 1: OT warning]]></Description>
			</Item>
			<Item ID="31"><Name>MCU fault</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Status of MCU. 0: normal 1: level 1 minor fault 2: level 2 critical fault]]></Description>
			</Item>
			<Item ID="32"><Name>Motor Error</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Status of the battery pack. 0: normal 1: level D25 minor fault 2: level 2 critical fault]]></Description>
			</Item></Resources>
		<Description2><![CDATA[]]></Description2>
	</Object>
</LWM2M>
