<?xml version="1.0" encoding="UTF-8"?>

<!--
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-XML_509-V1_0-20220503-A.xml

OMNA LwM2M Registry
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 509.xml

NORMATIVE INFORMATION

  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

LEGAL DISCLAIMER

  Copyright 2022 Open Mobile Alliance.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

  1. Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
  3. Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived
  from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.

  The above license is used as a license under copyright only.  Please
  reference the OMA IPR Policy for patent licensing terms:
  https://www.omaspecworks.org/about/intellectual-property-rights/

-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
    <Object ObjectType="MODefinition">
        <Name>Measurement Metadata</Name>
        <Description1>This object contains measurement metadata for sensor objects. The corresponding sensor is identified with an object link. If the LwM2M Client provides measurement quality information both inside the sensor objects and in the Measurement Metadata object, it MUST ensure that identical data is reported in corresponding object instances. The LwM2M Server SHOULD use the data from the Measurement Metadata object if both are available. The LwM2M Client also MUST ensure that no two Measurement Metadata object instances link to the same sensor object instance. If the LwM2M Server receives such non-conforming data, the Measurement Metadata object instance with largest Instance ID SHOULD be used.
        </Description1>
        <ObjectID>509</ObjectID>
        <ObjectURN>urn:oma:lwm2m:oma:509</ObjectURN>
        <LWM2MVersion>1.1</LWM2MVersion>
        <ObjectVersion>1.0</ObjectVersion>
        <MultipleInstances>Multiple</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Resources>
            <Item ID="0">
                <Name>Linked Sensor</Name>
                <Operations>R</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Objlnk</Type>
                <RangeEnumeration></RangeEnumeration>
                <Units></Units>
                <Description>Link to the sensor object instance that this metadata relates to.
                </Description>
            </Item>
            <Item ID="1">
                <Name>Measurement Quality Reason Code</Name>
                <Operations>R</Operations>
                <MultipleInstances>Multiple</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration>0..1023</RangeEnumeration>
                <Units></Units>
                <Description>Measurement quality degradation reason reported by a smart sensor. A degraded measurement has Measurement Quality Indicator value 1-3 or Measurement Quality Level less than 100.
0: UNKNOWN Reason is unknown.
1: UNDER_RANGE Measured value is under measurement range. 
2: OVER_RANGE Measured value is over measurement range.
3: NOISY Measured value seems to change too quickly. 
4: STUCK_VALUE Measured value seems to change too slowly.
5: STEP_TOO_HIGH Measured value has changed too much.
6: CALIBRATION_EXPIRED Sensor calibration has expired.
7: WARMING_UP Sensor is warming up.
8: SERVICE_MODE Sensor is under service.
9: HUMAN_INTERFERENCE Human presence near the sensor.
10: NATURAL_INTERFERENCE Natural phenomenon or animal presence.
11: CONTAMINATION Contamination of the sensor.
12: HW_FAILURE Sensor hardware has failed.
13: LACK_OF_DATA_SAMPLES Data sampling is lossy.
14: OUT_OF_OPERATING_RANGE Environment conditions exceeded.
15: AUTO_CALIBRATION Auto calibration in progress. 
16-127: RESERVED_GENERIC_BLOCK Reserved generic block.
128-511: VENDOR_SPECIFIC_BLOCK Vendor specific block.
512-1023: RESERVED_VENDOR_BLOCK Reserved vendor specific block.
                </Description>
            </Item>
            <Item ID="6042">
                <Name>Measurement Quality Indicator</Name>
                <Operations>R</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration>0..23</RangeEnumeration>
                <Units></Units>
                <Description>Measurement quality indicator reported by a smart sensor.
0: UNCHECKED No quality checks were done because they do not exist or can not be applied.
1: REJECTED WITH CERTAINTY The measured value is invalid.
2: REJECTED WITH PROBABILITY The measured value is likely invalid.
3: ACCEPTED BUT SUSPICIOUS The measured value is likely OK.
4: ACCEPTED The measured value is OK.
5-15: Reserved for future extensions.
16-23: Vendor specific measurement quality.
                </Description>
            </Item>
            <Item ID="6049">
                <Name>Measurement Quality Level</Name>
                <Operations>R</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration>0..100</RangeEnumeration>
                <Units></Units>
                <Description>Measurement quality level reported by a smart sensor. Quality level 100 means that the measurement has fully passed quality check algorithms. Smaller quality levels mean that quality has decreased and the measurement has only partially passed quality check algorithms. The smaller the quality level, the more caution should be used by the application when using the measurement. When the quality level is 0 it means that the measurement should certainly be rejected.
                </Description>
            </Item>
        </Resources>
        <Description2></Description2>
    </Object>
</LWM2M>
