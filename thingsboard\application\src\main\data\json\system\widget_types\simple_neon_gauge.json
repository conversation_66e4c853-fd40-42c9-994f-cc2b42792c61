{"fqn": "digital_gauges.simple_neon_gauge_justgage", "name": "Simple neon gauge", "deprecated": false, "image": "tb-image;/api/images/system/simple_neon_gauge_system_widget_image.png", "description": "Preconfigured gauge to display any value reading as a doughnut. Allows to configure value range, gradient colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<canvas id=\"digitalGauge\"></canvas>", "templateCss": "#gauge {\n    text-align: center;\n   /* margin-left: -100px;\n    margin-right: -100px;*/\n    /*margin-top: -50px;*/\n    \n}\n", "controllerScript": "self.onInit = function() {\n    self.ctx.gauge = new TbCanvasDigitalGauge(self.ctx, 'digitalGauge');    \n}\n\nself.onDataUpdated = function() {\n    self.ctx.gauge.update();\n}\n\nself.onResize = function() {\n    self.ctx.gauge.resize();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        supportsUnitConversion: true\n    };\n}\n\nself.onMobileModeChanged = function() {\n    self.ctx.gauge.mobileModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.gauge.destroy();\n}\n\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-digital-gauge-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-digital-simple-gauge-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temp\",\"color\":\"#388e3c\",\"settings\":{},\"_hash\":0.7282710489093589,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#000000\",\"color\":\"rgba(255, 254, 254, 0.87)\",\"padding\":\"0px\",\"settings\":{\"maxValue\":100,\"minValue\":0,\"donutStartAngle\":90,\"showValue\":true,\"showMinMax\":true,\"gaugeWidthScale\":1,\"levelColors\":[],\"titleFont\":{\"family\":\"Roboto\",\"size\":12,\"style\":\"normal\",\"weight\":\"500\"},\"labelFont\":{\"family\":\"Roboto\",\"size\":8,\"style\":\"normal\",\"weight\":\"500\"},\"valueFont\":{\"family\":\"Segment7Standard\",\"style\":\"normal\",\"weight\":\"500\",\"size\":32},\"minMaxFont\":{\"family\":\"Segment7Standard\",\"size\":12,\"style\":\"normal\",\"weight\":\"500\"},\"neonGlowBrightness\":40,\"dashThickness\":1.5,\"gaugeType\":\"donut\",\"animation\":true,\"animationDuration\":500,\"animationRule\":\"linear\"},\"title\":\"Simple neon gauge\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"widgetStyle\":{},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{},\"configMode\":\"basic\"}"}, "resources": [{"link": "/api/images/system/simple_neon_gauge_system_widget_image.png", "title": "\"Simple neon gauge\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_neon_gauge_system_widget_image.png", "publicResourceKey": "r5PaqzSWXbfnpMPhCWa1bKNtjifC6BTd", "mediaType": "image/png", "data": "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", "public": true}]}