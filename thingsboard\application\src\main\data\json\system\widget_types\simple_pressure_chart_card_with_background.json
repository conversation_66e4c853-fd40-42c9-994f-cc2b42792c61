{"fqn": "simple_pressure_chart_card_with_background", "name": "Simple pressure chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_pressure_chart_card_with_background_system_widget_image.png", "description": "Displays historical pressure values as a simplified chart with background. Optionally may display the corresponding latest pressure value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pressure', label: 'Pressure', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'pressure', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < 980) {\\n\\tvalue = 980;\\n} else if (value > 1040) {\\n\\tvalue = 1040;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 80 - 40;\\nif (value < 980) {\\n\\tvalue = 980;\\n} else if (value > 1040) {\\n\\tvalue = 1040;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1000,\"color\":\"#DE2343\"},{\"from\":1000,\"to\":1020,\"color\":\"#7CC322\"},{\"from\":1020,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_pressure_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Pressure\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"compress\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"hPa\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "barometry"], "resources": [{"link": "/api/images/system/simple_pressure_chart_card_with_background_system_widget_background.png", "title": "\"Simple pressure chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_pressure_chart_card_with_background_system_widget_background.png", "publicResourceKey": "XOOzNXXjrfm30Xc8qY1JSA9hBj7Cwpha", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_pressure_chart_card_with_background_system_widget_image.png", "title": "\"Simple pressure chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_pressure_chart_card_with_background_system_widget_image.png", "publicResourceKey": "t7suIvRhLgsakUUPaeWJd9aV5RVrF7hG", "mediaType": "image/png", "data": "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", "public": true}]}