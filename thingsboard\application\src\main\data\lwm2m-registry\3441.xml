<?xml version="1.0" encoding="UTF-8"?>
<!-- BSD-3 Clause License 

    Copyright 2021 Eclipse Foundation.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, <PERSON><PERSON>IAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
-->
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M.xsd">
    <Object ObjectType="MODefinition">
        <Name>LwM2M v1.0 Test Object</Name>
        <Description1><![CDATA[This object is for use in interoperability tests of the LwM2M v1.0 technical specification. It contains resources for each available datatype.]]></Description1>
        <ObjectID>3441</ObjectID>
        <ObjectURN>urn:oma:lwm2m:ext:3441</ObjectURN>
        <LWM2MVersion>1.0</LWM2MVersion>
        <ObjectVersion>1.0</ObjectVersion>
        <MultipleInstances>Multiple</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Resources>
            <Item ID="0">
                <Name>Reset values</Name>
                <Operations>E</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type></Type>
                <RangeEnumeration />
                <Units />
                <Description>Reset all resources of this object with their initial value.</Description>
            </Item>
            <Item ID="1">
                <Name>Randomize values</Name>
                <Operations>E</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type></Type>
                <RangeEnumeration />
                <Units />
                <Description>
<![CDATA[Set random value to all resources. For multi-instance resources, the number of resource instances is also randomized.
Randomization should avoid to generate too big payload. We advice to limit value to something like : 
 - 20 characters for String,
 - 20 bytes for Opaque,
 - 10 instances for multi-instance resources. 
]]>
                </Description>
            </Item>
            <Item ID="2">
                <Name>Clear values</Name>
                <Operations>E</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type></Type>
                <RangeEnumeration />
                <Units />
                <Description>
<![CDATA[Clear all values : 
 - all multiple resource as empty resource
 - all number to 0
 - String to empty string
 - boolean to false,
 - opaque to empty byte array,
 - time to an 1st, 1970 in the UTC time zone
 - objlink to null link]]>
                </Description>
            </Item>
            <Item ID="3">
                <Name>Exec With Arguments</Name>
                <Operations>E</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type></Type>
                <RangeEnumeration />
                <Units />
                <Description>
<![CDATA[This resources can be used to test "Execute Operation" with Arguments.
Sent Arguments can be read via "Arguments List"(4) resource.
E.g. If you send an Exec /3441/0/3 with "3='stringValue',4" as arguments value,
     then /3441/0/4/3 will be 'stringValue' and /3441/0/4/4 will be an empty string.
]]>
                </Description>
            </Item>
            <Item ID="4">
                <Name>Arguments List</Name>
                <Operations>R</Operations>
                <MultipleInstances>Multiple</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>String</Type>
                <RangeEnumeration />
                <Units />
                <Description>List of Arguments from last execute on "Exec With Arguments"(3) resource. This resource is not affected by the "Randomize values"(1) executable resource.
                </Description>
            </Item>
            <Item ID="110">
                <Name>String Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>String</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be "initial value".
                </Description>
            </Item>
            <Item ID="120">
                <Name>Integer Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be "1024".
                </Description>
            </Item>
            <Item ID="130">
                <Name>Float Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Float</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be "3.14159".
                </Description>
            </Item>
            <Item ID="140">
                <Name>Boolean Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Boolean</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be "true".</Description>
            </Item>
            <Item ID="150">
                <Name>Opaque Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Opaque</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be the bytes sequence "0123456789ABCDEF" (Hexadecimal notation).
                </Description>
            </Item>
            <Item ID="160">
                <Name>Time Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Time</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be the time to an 1st, 2000 in the UTC time zone. (Timestamp value : 946684800)</Description>
            </Item>
            <Item ID="170">
                <Name>ObjLink Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Objlnk</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be a link to instance 0 of Device Object 3 (3:0).</Description>
            </Item>
            <Item ID="1110">
                <Name>Multiple String Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Multiple</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>String</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be 1 instance with ID 0 and value "initial value".</Description>
            </Item>
            <Item ID="1120">
                <Name>Multiple Integer Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Multiple</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be 1 instance with ID 0 and value "1024".</Description>
            </Item>
            <Item ID="1130">
                <Name>Multiple Float Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Multiple</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Float</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be 1 instance with ID 0 and value "3.14159".</Description>
            </Item>
            <Item ID="1140">
                <Name>Multiple Boolean Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Multiple</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Boolean</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be 1 instance with ID 0 and value "true".</Description>
            </Item>
            <Item ID="1150">
                <Name>Multiple Opaque Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Multiple</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Opaque</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be 1 instance with ID 0 and value "0123456789ABCDEF"(Hexadecimal notation of the bytes sequence).</Description>
            </Item>
            <Item ID="1160">
                <Name>Multiple Time Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Multiple</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Time</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be 1 instance with ID 0 and value 1st, 2000 in the UTC time zone (Timestamp value : 946684800).</Description>
            </Item>
            <Item ID="1170">
                <Name>Multiple ObjLink Value</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Multiple</MultipleInstances>
                <Mandatory>Optional</Mandatory>
                <Type>Objlnk</Type>
                <RangeEnumeration />
                <Units />
                <Description>Initial value must be 1 instance with ID 0 and value "3:0".</Description>
            </Item>
        </Resources>
        <Description2></Description2>
    </Object>
</LWM2M>
