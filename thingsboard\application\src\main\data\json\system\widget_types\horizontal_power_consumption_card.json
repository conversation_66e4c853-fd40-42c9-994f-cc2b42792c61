{"fqn": "horizontal_power_consumption_card", "name": "Horizontal power consumption card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_power_consumption_card.svg", "description": "Displays the latest power consumption telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "#container {\n    overflow: auto;\n}\n\n.tbDatasource-container {\n    margin: 5px;\n    padding: 8px;\n}\n\n.tbDatasource-title {\n    font-size: 1.200rem;\n    font-weight: 500;\n    padding-bottom: 10px;\n}\n\n.tbDatasource-table {\n    width: 100%;\n    box-shadow: 0 0 10px #ccc;\n    border-collapse: collapse;\n    white-space: nowrap;\n    font-size: 1.000rem;\n    color: #757575;\n}\n\n.tbDatasource-table td {\n    position: relative;\n    border-top: 1px solid rgba(0, 0, 0, 0.12);\n    border-bottom: 1px solid rgba(0, 0, 0, 0.12);\n    padding: 0px 18px;\n    box-sizing: border-box;\n}", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'powerConsumption', label: 'Power consumption', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Power consumption\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"horizontal\",\"autoScale\":true,\"showLabel\":true,\"labelFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"#000000DE\",\"rangeList\":null,\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"bolt\",\"iconColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":null,\"to\":5,\"color\":\"#3FA71A\"},{\"from\":5,\"to\":15,\"color\":\"#F36900\"},{\"from\":15,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":null,\"to\":5,\"color\":\"#3FA71A\"},{\"from\":5,\"to\":15,\"color\":\"#F36900\"},{\"from\":15,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Horizontal power consumption card\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"units\":\"kW\",\"decimals\":0,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"showTitleIcon\":false,\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\"}"}, "tags": ["power", "energy", "energy usage", "electric load", "electricity", "power efficiency", "load profile"], "resources": [{"link": "/api/images/system/horizontal_power_consumption_card.svg", "title": "horizontal_power_consumption_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_power_consumption_card.svg", "publicResourceKey": "rgmTVwHQwv65WDknq5ktc3im7eGlZzzt", "mediaType": "image/svg+xml", "data": "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", "public": true}]}