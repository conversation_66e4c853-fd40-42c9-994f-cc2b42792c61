{"fqn": "horizontal_pm10_card", "name": "Horizontal PM10 card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_pm10_card_system_widget_image.png", "description": "Displays the latest fine and coarse particulate matter (PM10) telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pm10', label: 'PM10', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM10\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"bubble_chart\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#80C32C\"},{\"from\":20,\"to\":50,\"color\":\"#FFA600\"},{\"from\":50,\"to\":150,\"color\":\"#F36900\"},{\"from\":150,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#80C32C\"},{\"from\":20,\"to\":50,\"color\":\"#FFA600\"},{\"from\":50,\"to\":150,\"color\":\"#F36900\"},{\"from\":150,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal temperature card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"µg/m³\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["environment", "particulate", "matter", "air", "pm10", "coarse particulates", "coarse particles", "particulate matter 10", "inhalable particles", "larger particulates", "dust", "airborne coarse particles"], "resources": [{"link": "/api/images/system/horizontal_pm10_card_system_widget_image.png", "title": "\"Horizontal PM10 card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_pm10_card_system_widget_image.png", "publicResourceKey": "47N91CR3wnIrqGS1sBD7iRvC9AF4CsSI", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAllBMVEUAAADf39/f39/g4ODg4ODf39/////k5OT/pgDg4OD/9N//vED/sSD/+u//3p//0oDOzs7z8/P/6L//x2D5+fnIyMj/6b//qxD/46/b29u2trbCwsL/2JBYWFjV1dW8vLzn5+erq6uQkJD/tzD/78//zW//zXD/x187Ozuenp7t7e1mZmb/wlBKSkp0dHT9/f2tra2CgoIEnlQuAAAABnRSTlMAIEDfv1C6kOEmAAADg0lEQVR42u3aiXLaMBSFYdqkh4ssb9jGNjY2OyFLl/d/uUqihTYt0IHSIfR8kzCynMnojwyBgQ4RERERERERERERERERERERERERERHRv/TuzetY7++6b13v7oPp6KZ48+Lu+859jBuQ3nd6uAWfejcSAoZcG4ZcG4ZcG4Zcm/8rZDKddjFpgWYNZI2d6OLK7AvpB4XCd6N19tCMHoCPL/gymqJ9al5wnn4dFv52hCNU4OMV3y7weEgiIrrYhmR4Go6esq6NmUwxXePlvC1JxNBzMwrdSOGglcYrhWidz4+GLMXK1feQl9EIo+nHyeRbyASjs0IC0YN5KBpQdrQUDwflyS8hIZSER0NyccLdjtibp4d4E7Je4yE+a0PcbueisJQl4Ofiw1EK5lC5o36hYEe2u28n3QxU0Ie18LQ6FqJkI/k5ZP0Rm5D45ekLzmeX74lNmskclhLtrrbEDGd2AZutejazA5nZmdBeitrm1Svp/+mO1Ngnxpl8pZaygAnpw92+DlnJLKi1eG42sSGyKBYmoi60hKjNRkpyKMQPkwC1WFrhUuy6xMP+ECWigML9TGD3a+CGWgJ7/Gy+dS7FoZCZ2wm3gXNcjgoXWsL9IW7d3269mR3uugM7VHXg40DIXAwNqMEcF+bnovaGBLsQJcEvIcf/ISoxZriwwv413R09keC3d/ZCttsQijohBCsR+7trrzhy144fX0/E+FMztwPa3Nby7Na//TP6dgmJ265vS9YeTglBEA4AXyTH1tDFmK/HEk6aIu1FdqKH1ByPJ2OkZbk5kR5/UKslT0LPLt/XkoRawm2IDhfucX9pR7l4piA4JWR3/eqfQpomKrMsMqMKVVm16bCKmiaL2seoTYFoHLdtiThyJ6KsxWGBFtPi27Xb0WJ3YeeSezYESzNeiWf2xz85ZPc0bRcSDeMsGkebkHFTmrGZG5dRlbmQXlmakGw4zmKTW8U4QikfG/52ZK8x/8d5+0Crk7/3NP5zVFbRsMmq2O5I1JbVMB5mkZl7bMdZBBuS2h0Zt5/tiaaqcBK1uw7qZ4W+luCiL6zKMfZrshLG2SFirXDRkDQ9dDLGyfyw3kWFyWLAl7pvF0OuDUOuDUOujQn5hFvQ69zfwNvsQHxvPjBwA2+0p+YDA50Pd73uW3f3/pY+VENERERERERERERERERERERERERERET0j3wFxKH86witj0MAAAAASUVORK5CYII=", "public": true}]}