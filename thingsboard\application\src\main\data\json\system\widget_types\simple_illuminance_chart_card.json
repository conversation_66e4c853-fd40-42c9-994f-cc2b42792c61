{"fqn": "simple_illuminance_chart_card", "name": "Simple illuminance chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_illuminance_chart_card_system_widget_image.png", "description": "Displays historical illuminance values as a simplified chart. Optionally may display the corresponding latest illuminance value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'illuminance', label: 'Illuminance', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'illuminance', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Illuminance\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":1,\"to\":5,\"color\":\"#FFA600\"},{\"from\":5,\"to\":20,\"color\":\"#F36900\"},{\"from\":20,\"to\":50,\"color\":\"#F04022\"},{\"from\":50,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Illuminance\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:lightbulb-on\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"lx\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "brightness", "luminance", "luminosity", "light", "light level", "light intensity", "lux", "candela", "foot-candle"], "resources": [{"link": "/api/images/system/simple_illuminance_chart_card_system_widget_image.png", "title": "\"Simple illuminance chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_illuminance_chart_card_system_widget_image.png", "publicResourceKey": "l9Bkv20BnpIcrj0wvAwq18BivgAsnXGV", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAolBMVEUAAADg4ODf39/g4ODg4OD////g4ODYGDghISHj4+NYWFisrKx0dHTx8fHHx8c9PT2QkJAvLy/64ubri5vaJkSfn588PDz98fPdNVH1xc26urqCgoJKSkrV1dXxqbTiUmllZWUXFxfnboLzt8Grq6vumqjpfY/kYHYJCQn309rsjJxDQ0ORkZGBgYHfQ13Q0NDAwMD41NmSkpLgRF1SUlLcNVGnmCfWAAAABXRSTlMA7yC/r1EOHTEAAASiSURBVHja7M9JAcAgDAAwytEf/u0OGdAlDtIAAAAAAAB+p4/I181+HrHX83b0Ngs8zmS0XCVElUiKXEbkNh+7ddMiKQyEcfz0SFWSysshHtQGvQndzQzLfv/PtqXdLLuY6dNgR8Y/gVCUl99B9ITU1gmprRNSWyektn4A5P6JI1WGCIBrXu9nlwiQhXHYNma8vzLk2j+WZPHM0gtIh/dXhjQ36ema5Y4txHug83q8vcBb1umiZ5mgs6LYsu0EEKvbZSxC94BoNBFNv1CAOIPlMmloyQxtEoT4mDxicq2Bp2CSAUIylNHR0AbBpn0gWR0qkRcQvZIDU7dCRghlRI+O2CspEywxXEBwurLYtAMkz59uhcwf4/wKogImu0IcQBEXF8IDYgl9gCY0GKOrTTtApJHbAyLSbCHD15DgxP8HERbqvfeMTTtANCFtupfekT4xhzJE1BL/QvSS0cAMLPFtEDQTTSNKEG4pDWWIKsiQf0KWqeXleTLvgyDN8w3lWPBVwoWJi4y9IE2u4Xv9DZCP628cqR/w93uwTkhtnZDaOiG1dUJq64TUVmUQn2PM/vCQnGgtxENDeCBjGZBupMDHhXBIHZ51beDDQkLif1RtkINC/rBnRjuqwkAAfZpMC6XQboUqChtX1+iDyc3N/f9fuwPEiiwEyBoCCedBRsPDnExnWsFnJ6ghWbRQkdCDF4TiixTxmWy0jBKLFAn30CBih0WIHPydbOsQR6C8BYhUW1/oP5IOY/hBxOR8RORGpBvekqPn88Npz9Kg2grblhGVZC4i3GNMKcYEb3pEjyBMfT9V6tRaNibfIKK3SS2+uvhs7eA2UCoKSCdtnDg4E+6WT0auKYdW9iH/rcjNIGKuy/iaU2wzqPhAHHzmiPnDyIMngfu97xE3Vy8mAQ/Ginwj/qP0TWGiDZrcImZjRSLmstgxHxwpOwyv6WMeBFKoonhCjhHRiMfS4BsAjmjJJ0czViQU4Nh7tUkbjToRh2Ljb8hCldeYRt1wkS/EKxTJ3wESxDPFW8TtU0TrhMKELgP36h2LngsOxtCsxIlUUj5cRNPlgrkzIJ/MiRT1IYVbcVsnXvy6zgQv9VTMYRzN3pDCzcE+kaRcVLrMfVtVBxA/ahUx9E0j3dUJZ6dGxzBPCPWWt3BcxPthzZ4ZtLnBC5QiuiFSfhpNzZNAJ4I1MuapF4ZCwlsIgmEiyQUJk3WKJBYNYtbT6hPRLUJp2mx7pEzbRSo/PPYfy7uZRCSrkj+i6egRoq8gMfQwhQiNK+fzB/EL3BR2IjdENN0tkvYWZBIRamMgzoVIQgPqxz5STiwabd0eEfQwiQgZ3AC0LX0uaEjnL8U1kRwtReTWhtxP5NHf7HdEY7FaVNogWkNxTSQrG8Ri3jrkWbyDaegfv8dCI99Cgc4pvpOHE9H2scWcoUEQlWf36ej/P6KTp5fWMAwZjt+6Z/g0nnssljCMOYtslBre5HMW+RQjmmPOImNYRVaRpbCKzI1VZG6sInNjFZkbq8j/du6EAAAQBGAgfvSPbA3EuwYrsGqEVJMxTgsZq8dyZ/eZIMXc+bqxZgAAAAAAAPzmAmkgqmF9nMW/AAAAAElFTkSuQmCC", "public": true}]}