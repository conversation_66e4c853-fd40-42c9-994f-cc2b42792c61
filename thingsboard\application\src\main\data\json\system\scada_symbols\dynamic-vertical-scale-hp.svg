<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="400" height="800" fill="none" version="1.1" viewBox="0 0 400 800"><tb:metadata xmlns=""><![CDATA[{
  "title": "HP Dynamic vertical scale",
  "description": "Dynamic vertical scale with current volume value and dynamically configurable warning and critical scale.",
  "searchTags": [
    "scale"
  ],
  "widgetSizeX": 2,
  "widgetSizeY": 4,
  "tags": [
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "highCriticalScale",
      "stateRenderFunction": "function calculateOffset(value, minValue, maxValue) {\n    const clampedValue = Math.max(Math.min(value, Math.max(minValue, maxValue)), Math.min(minValue, maxValue));\n    const normalizedValue = minValue < maxValue\n        ? (clampedValue - minValue) / (maxValue - minValue)\n        : (minValue - clampedValue) / (minValue - maxValue);\n    return normalizedValue * 653;\n}\n\nconst { value, highCriticalState } = ctx.values;\nconst {\n    minValue,\n    maxValue,\n    showHighCriticalScale,\n    activeCriticalScaleColor,\n    defaultCriticalScaleColor\n} = ctx.properties;\n\nif (showHighCriticalScale && highCriticalState !== null) {\n    element.show();\n    const offset = calculateOffset(highCriticalState, minValue, maxValue);\n    element.height(653 - offset);\n} else {\n    element.hide();\n}\n\nif (showHighCriticalScale && value !== null && highCriticalState !== null) {\n    const isActive = minValue < maxValue ? value >= highCriticalState : value <= highCriticalState;\n    element.fill(isActive ? activeCriticalScaleColor : defaultCriticalScaleColor);\n}",
      "actions": null
    },
    {
      "tag": "highWarningScale",
      "stateRenderFunction": "function calculateOffset(value, minValue, maxValue) {\n    const clampedValue = Math.max(Math.min(value, Math.max(minValue, maxValue)), Math.min(minValue, maxValue));\n    const normalizedValue = minValue < maxValue\n        ? (clampedValue - minValue) / (maxValue - minValue)\n        : (minValue - clampedValue) / (minValue - maxValue);\n    return normalizedValue * 653;\n}\n\nconst { value, highWarningState, highCriticalState: initialHighCriticalState } = ctx.values;\nconst {\n    minValue,\n    maxValue,\n    showHighCriticalScale,\n    showHighWarningScale,\n    activeWarningScaleColor,\n    defaultWarningScaleColor\n} = ctx.properties;\n\nif (showHighWarningScale && highWarningState !== null) {\n    element.show();\n    const offset = calculateOffset(highWarningState, minValue, maxValue);\n    element.height(653 - offset);\n} else {\n    element.hide();\n}\n\nif (showHighWarningScale && value !== null) {\n    let adjustedHighCriticalState = initialHighCriticalState;\n    if (!showHighCriticalScale) {\n        adjustedHighCriticalState = minValue < maxValue ? Number.MAX_SAFE_INTEGER : Number.MIN_SAFE_INTEGER;\n    }\n    if (highWarningState !== null && adjustedHighCriticalState !== null) {\n        let adjustedHighWarningState = highWarningState;\n        if (minValue > maxValue) {\n            [adjustedHighCriticalState, adjustedHighWarningState] = [adjustedHighWarningState, adjustedHighCriticalState];\n        }\n        const isActive = value < adjustedHighCriticalState && value >= adjustedHighWarningState;\n        element.fill(isActive ? activeWarningScaleColor : defaultWarningScaleColor);\n    }\n}",
      "actions": null
    },
    {
      "tag": "label",
      "stateRenderFunction": "if (ctx.properties.label) {\n    element.show();\n    ctx.api.font(element, ctx.properties.labelFont, ctx.properties.labelColor);\n    ctx.api.text(element, ctx.properties.labelText);\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "lowCriticalScale",
      "stateRenderFunction": "function calculateOffset(value, minValue, maxValue) {\n    const clampedValue = Math.max(Math.min(value, Math.max(minValue, maxValue)), Math.min(minValue, maxValue));\n    const normalizedValue = minValue < maxValue\n        ? (clampedValue - minValue) / (maxValue - minValue)\n        : (minValue - clampedValue) / (minValue - maxValue);\n    return normalizedValue * 653;\n}\n\nconst { value, lowCriticalState: lowCriticalValue } = ctx.values;\nconst {\n    minValue,\n    maxValue,\n    showLowCriticalScale,\n    activeCriticalScaleColor,\n    defaultCriticalScaleColor\n} = ctx.properties;\n\nif (showLowCriticalScale && lowCriticalValue !== null) {\n    element.show();\n    const offset = calculateOffset(lowCriticalValue, minValue, maxValue);\n    element.height(offset);\n} else {\n    element.hide();\n}\n\nif (showLowCriticalScale && value !== null && lowCriticalValue !== null) {\n    const isActive = minValue < maxValue ? value <= lowCriticalValue : value >= lowCriticalValue;\n    element.fill(isActive ? activeCriticalScaleColor : defaultCriticalScaleColor);\n}",
      "actions": null
    },
    {
      "tag": "lowWarningScale",
      "stateRenderFunction": "function calculateOffset(value, minValue, maxValue) {\n    const clampedValue = Math.max(Math.min(value, Math.max(minValue, maxValue)), Math.min(minValue, maxValue));\n    const normalizedValue = minValue < maxValue\n        ? (clampedValue - minValue) / (maxValue - minValue)\n        : (minValue - clampedValue) / (minValue - maxValue);\n    return normalizedValue * 653;\n}\n\nconst {\n    value,\n    lowWarningState,\n    lowCriticalState: initialLowCriticalState\n} = ctx.values;\nconst {\n    minValue,\n    maxValue,\n    showLowWarningScale,\n    showLowCriticalScale,\n    activeWarningScaleColor,\n    defaultWarningScaleColor\n} = ctx.properties;\n\nif (showLowWarningScale && lowWarningState !== null) {\n    element.show();\n    const offset = calculateOffset(lowWarningState, minValue, maxValue);\n    element.height(offset);\n} else {\n    element.hide();\n}\n\nif (showLowWarningScale && value !== null) {\n    let adjustedLowCriticalState = initialLowCriticalState;\n    if (!showLowCriticalScale) {\n        adjustedLowCriticalState = minValue < maxValue ? Number.MIN_SAFE_INTEGER : Number.MAX_SAFE_INTEGER;\n    }\n    if (initialLowCriticalState !== null && lowWarningState !== null) {\n        let adjustedLowWarningState = lowWarningState;\n        if (minValue > maxValue) {\n            [adjustedLowCriticalState, adjustedLowWarningState] = [adjustedLowWarningState, adjustedLowCriticalState];\n        }\n        const isActive = value > adjustedLowCriticalState && value <= adjustedLowWarningState;\n        element.fill(isActive ? activeWarningScaleColor : defaultWarningScaleColor);\n    }\n}\n",
      "actions": null
    },
    {
      "tag": "maxValue",
      "stateRenderFunction": "if (ctx.properties.minMaxValue) {\n    ctx.api.text(element, ctx.api.convertUnitValue(ctx.properties.maxValue, ctx.properties.units).toFixed(0));\n}",
      "actions": null
    },
    {
      "tag": "minMaxValue",
      "stateRenderFunction": "if (ctx.properties.minMaxValue) {\n    element.show();\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "minValue",
      "stateRenderFunction": "if (ctx.properties.minMaxValue) {\n    ctx.api.text(element, ctx.api.convertUnitValue(ctx.properties.minValue, ctx.properties.units).toFixed(0));\n}",
      "actions": null
    },
    {
      "tag": "scaleBackground",
      "stateRenderFunction": "element.fill(ctx.properties.scaleColor);",
      "actions": null
    },
    {
      "tag": "target",
      "stateRenderFunction": "function calculateOffset(value, minValue, maxValue) {\n    var clampedValue = Math.max(Math.min(value, Math.max(minValue, maxValue)), Math.min(minValue, maxValue));\n    var normalizedValue = minValue < maxValue\n        ? (clampedValue - minValue) / (maxValue - minValue)\n        : (minValue - clampedValue) / (minValue - maxValue);\n    var offset = normalizedValue * 653;\n    return offset;\n}\n\nvar minValue = ctx.properties.minValue;\nvar maxValue = ctx.properties.maxValue;\nvar showTarget = ctx.properties.showTarget;\nvar targetValue = ctx.values.targetValue;\n\nif (showTarget && targetValue !== null) {\n    element.show();\n    var offset = calculateOffset(targetValue, minValue, maxValue);\n    element.transform({translateY: -Math.min(Math.max(offset-17, 0), 619)});\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "targetBackground",
      "stateRenderFunction": "element.fill(ctx.properties.targetColor);",
      "actions": null
    },
    {
      "tag": "units",
      "stateRenderFunction": "if (ctx.properties.showUnits) {\n    element.show();\n    ctx.api.font(element, ctx.properties.unitsFont, ctx.properties.unitsColor);\n    ctx.api.text(element, ctx.api.unitSymbol(ctx.properties.units));\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "value",
      "stateRenderFunction": "if (ctx.properties.value) {\n    element.show();\n    ctx.api.font(element, ctx.properties.valueFont, ctx.properties.valueColor);\n    ctx.api.text(element, ctx.api.formatValue(ctx.values.value, {units: ctx.properties.units, decimals: ctx.properties.valueDecimals, ignoreUnitSymbol: true}));\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "valueArrowPosition",
      "stateRenderFunction": "function calculateOffset(value, minValue, maxValue) {\n    var clampedValue = Math.max(Math.min(value, Math.max(minValue, maxValue)), Math.min(minValue, maxValue));\n    var normalizedValue = minValue < maxValue\n        ? (clampedValue - minValue) / (maxValue - minValue)\n        : (minValue - clampedValue) / (minValue - maxValue);\n    var offset = normalizedValue * 653;\n    return offset;\n}\n\n\nvar minValue = ctx.properties.minValue;\nvar maxValue = ctx.properties.maxValue;\nvar value = ctx.values.value;\n\nif (value !== null) {\n    var offset = calculateOffset(value, minValue, maxValue);\n    element.transform({translateY: -offset});\n}\n",
      "actions": null
    },
    {
      "tag": "valuePointer",
      "stateRenderFunction": "element.fill(ctx.properties.arrowColor);",
      "actions": null
    },
    {
      "tag": "valueTextPositon",
      "stateRenderFunction": "function calculateOffset(value, minValue, maxValue) {\n    var clampedValue = Math.max(Math.min(value, Math.max(minValue, maxValue)), Math.min(minValue, maxValue));\n    var normalizedValue = minValue < maxValue\n        ? (clampedValue - minValue) / (maxValue - minValue)\n        : (minValue - clampedValue) / (minValue - maxValue);\n    var offset = normalizedValue * 653;\n    return offset;\n}\n\nvar minValue = ctx.properties.minValue;\nvar maxValue = ctx.properties.maxValue;\nvar value = ctx.values.value;\n\nif (value !== null) {\n    var offset = calculateOffset(value, minValue, maxValue);\n    element.transform({translateY: -offset});\n}\n",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "value",
      "name": "{i18n:scada.symbol.value}",
      "hint": "{i18n:scada.symbol.value-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_TIME_SERIES",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "temperature"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "highCriticalState",
      "name": "{i18n:scada.symbol.high-critical-state}",
      "hint": "{i18n:scada.symbol.high-critical-state-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": 85,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SERVER_SCOPE",
          "key": "highCriticalState"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "highWarningState",
      "name": "{i18n:scada.symbol.high-warning-state}",
      "hint": "{i18n:scada.symbol.high-warning-state-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": 70,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SERVER_SCOPE",
          "key": "highWarningState"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "lowWarningState",
      "name": "{i18n:scada.symbol.low-warning-state}",
      "hint": "{i18n:scada.symbol.low-warning-state-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": 30,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SERVER_SCOPE",
          "key": "lowWarningState"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "lowCriticalState",
      "name": "{i18n:scada.symbol.low-critical-state}",
      "hint": "{i18n:scada.symbol.low-critical-state-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": 15,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SERVER_SCOPE",
          "key": "lowCriticalState"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "targetValue",
      "name": "{i18n:scada.symbol.target-value}",
      "hint": "{i18n:scada.symbol.target-value-hint}",
      "group": null,
      "type": "value",
      "valueType": "DOUBLE",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": null,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SERVER_SCOPE",
          "key": "target"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    }
  ],
  "properties": [
    {
      "id": "minMaxValue",
      "name": "{i18n:scada.symbol.min-max-value}",
      "type": "switch",
      "default": false,
      "disabled": false,
      "visible": true
    },
    {
      "id": "minValue",
      "name": "{i18n:scada.symbol.min-max-value}",
      "type": "number",
      "default": 0,
      "required": true,
      "subLabel": "{i18n:scada.symbol.min-value}",
      "step": 1,
      "disabled": false,
      "visible": true
    },
    {
      "id": "maxValue",
      "name": "{i18n:scada.symbol.min-max-value}",
      "type": "number",
      "default": 100,
      "required": true,
      "subLabel": "{i18n:scada.symbol.max-value}",
      "step": 1,
      "disabled": false,
      "visible": true
    },
    {
      "id": "value",
      "name": "{i18n:scada.symbol.value}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "valueDecimals",
      "name": "{i18n:scada.symbol.value}",
      "type": "number",
      "default": 0,
      "required": true,
      "subLabel": "Decimals",
      "min": 0,
      "max": 10,
      "step": 1,
      "disabled": false,
      "visible": true
    },
    {
      "id": "valueFont",
      "name": "{i18n:scada.symbol.value}",
      "type": "font",
      "default": {
        "size": 60,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "400",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "valueColor",
      "name": "{i18n:scada.symbol.value}",
      "type": "color",
      "default": "#002878",
      "disabled": false,
      "visible": true
    },
    {
      "id": "label",
      "name": "{i18n:scada.symbol.label}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "labelText",
      "name": "{i18n:scada.symbol.label}",
      "type": "text",
      "default": "Outdoor",
      "fieldClass": "flex",
      "disabled": false,
      "visible": true
    },
    {
      "id": "labelFont",
      "name": "{i18n:scada.symbol.label}",
      "type": "font",
      "default": {
        "size": 56,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "500",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "labelColor",
      "name": "{i18n:scada.symbol.label}",
      "type": "color",
      "default": "#000000DE",
      "disabled": false,
      "visible": true
    },
    {
      "id": "showUnits",
      "name": "{i18n:scada.symbol.units}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "units",
      "name": "{i18n:scada.symbol.units}",
      "type": "units",
      "default": "°C",
      "required": false,
      "supportsUnitConversion": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "unitsFont",
      "name": "{i18n:scada.symbol.units}",
      "type": "font",
      "default": {
        "size": 56,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "500",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "unitsColor",
      "name": "{i18n:scada.symbol.units}",
      "type": "color",
      "default": "#000000DE",
      "disabled": false,
      "visible": true
    },
    {
      "id": "arrowColor",
      "name": "{i18n:scada.symbol.arrow-color}",
      "type": "color",
      "default": "#666666",
      "disabled": false,
      "visible": true
    },
    {
      "id": "showTarget",
      "name": "{i18n:scada.symbol.target}",
      "type": "switch",
      "default": false,
      "disabled": false,
      "visible": true
    },
    {
      "id": "targetColor",
      "name": "{i18n:scada.symbol.target}",
      "type": "color",
      "default": "#DEDEDE",
      "disableOnProperty": "showTarget",
      "disabled": true,
      "visible": true
    },
    {
      "id": "showHighCriticalScale",
      "name": "{i18n:scada.symbol.show-high-critical-scale}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "showHighWarningScale",
      "name": "{i18n:scada.symbol.show-high-warning-scale}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "showLowWarningScale",
      "name": "{i18n:scada.symbol.show-low-warning-scale}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "showLowCriticalScale",
      "name": "{i18n:scada.symbol.show-low-critical-scale}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "scaleColor",
      "name": "{i18n:scada.symbol.scale-color}",
      "type": "color",
      "default": "#C8DFF7",
      "disabled": false,
      "visible": true
    },
    {
      "id": "defaultWarningScaleColor",
      "name": "{i18n:scada.symbol.warning-scale-color}",
      "type": "color",
      "default": "#EBEBEB",
      "subLabel": "Default",
      "disabled": false,
      "visible": true
    },
    {
      "id": "activeWarningScaleColor",
      "name": "{i18n:scada.symbol.warning-scale-color}",
      "type": "color",
      "default": "#FAA405",
      "subLabel": "Active",
      "disabled": false,
      "visible": true
    },
    {
      "id": "defaultCriticalScaleColor",
      "name": "{i18n:scada.symbol.critical-scale-color}",
      "type": "color",
      "default": "#666666",
      "subLabel": "Default",
      "disabled": false,
      "visible": true
    },
    {
      "id": "activeCriticalScaleColor",
      "name": "{i18n:scada.symbol.critical-scale-color}",
      "type": "color",
      "default": "#D12730",
      "subLabel": "Active",
      "disabled": false,
      "visible": true
    }
  ]
}]]></tb:metadata>
<text x="206.16602" y="43.234375" fill="#000000" font-family="Roboto, sans-serif" font-size="56px" font-weight="400" text-anchor="middle" tb:tag="label" xml:space="preserve"><tspan transform="translate(0,-144)" dominant-baseline="middle">Outdoor</tspan></text><text x="195.21875" y="770" fill="#000000" font-family="Roboto, sans-serif" font-size="40px" font-weight="400" text-anchor="middle" tb:tag="units" xml:space="preserve"><tspan transform="translate(0,-144)" dominant-baseline="middle">°C</tspan></text><g stroke="#000" tb:tag="scale">
  <rect x="176.5" y="83" width="41" height="652" fill="#c8dff7" tb:tag="scaleBackground"/>
  <rect transform="scale(1,-1)" x="176.5" y="-735" width="41" height="164" fill="#ebebeb" tb:tag="lowWarningScale"/>
  <rect x="176.5" y="83.5" width="41" height="164" fill="#ebebeb" tb:tag="highWarningScale"/>
  <rect transform="scale(1,-1)" x="176.5" y="-735" width="41" height="81" fill="#666" tb:tag="lowCriticalScale"/>
  <rect x="176.5" y="83.5" width="41" height="81" fill="#666" tb:tag="highCriticalScale"/>
 </g><g fill="#666" font-family="Roboto, sans-serif" font-size="32px" font-weight="500" style="display: none;" tb:tag="minMaxValue">
  <text x="237.48438" y="97" tb:tag="maxValue" xml:space="preserve" style=""><tspan dominant-baseline="middle">100</tspan></text>
  <text x="238.89062" y="727" tb:tag="minValue" xml:space="preserve" style=""><tspan dominant-baseline="middle">0</tspan></text>
 </g><g transform="translate(0,-180)" tb:tag="valueArrowPosition">
  <path d="m168 735-42-24v48z" fill="#666" tb:tag="valuePointer"/>
 </g><g transform="translate(0,-180)" tb:tag="valueTextPositon">
  <text x="223.60547" y="739.125" fill="#002878" font-family="Roboto, sans-serif" font-size="60px" font-weight="400" tb:tag="value" xml:space="preserve"><tspan dominant-baseline="middle">26</tspan></text>
 </g><g transform="translate(0,-400)" style="display: none;" tb:tag="target">
  <rect transform="rotate(-45)" x="-379.76" y="636.31" width="22" height="22" fill="#dedede" stroke="#000" stroke-width="2" tb:tag="targetBackground" style=""/>
 </g><path d="m134.53 0s-134.53 0-134.53 134v656.72c0 5.3024 3.5817 9.2808 8 9.2808h384c4.418 0 8-3.9784 8-9.2808v-656.72c0-134-132.14-134-132.14-134h-67.86zm134.14 162.4c-2.5773 0-4.6667 2.5072-4.6667 5.6v600.8c0 3.0928 2.0893 5.6 4.6667 5.6h29.333c2.5773 0 4.6667-2.5072 4.6667-5.6v-600.8c0-3.0928-2.0893-5.6-4.6667-5.6z" fill="#000" fill-opacity="0" tb:tag="clickArea"/>
</svg>