{"fqn": "simple_vibration_chart_card", "name": "Simple vibration chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_vibration_chart_card_system_widget_image.png", "description": "Displays historical vibration values as a simplified chart. Optionally may display the corresponding latest vibration value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'vibration', label: 'Vibration', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'vibration', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Vibration\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"let factor = 1000;\\nif (prevValue < 1) {\\n    factor = 1;\\n} else if (prevValue < 10) {\\n    factor = 10;\\n} else if (prevValue < 100) {\\n    factor = 100;\\n}\\nlet value = prevValue + Math.random() * factor;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"let factor = 1000;\\nif (prevValue < 1) {\\n    factor = 1;\\n} else if (prevValue < 10) {\\n    factor = 10;\\n} else if (prevValue < 100) {\\n    factor = 100;\\n}\\nlet value = prevValue + Math.random() * factor;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0.1,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0.1,\"to\":1,\"color\":\"#FFA600\"},{\"from\":1,\"to\":10,\"color\":\"#F36900\"},{\"from\":10,\"to\":100,\"color\":\"#F04022\"},{\"from\":100,\"to\":1000,\"color\":\"#D81838\"},{\"from\":1000,\"to\":null,\"color\":\"#6F113A\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Vibration\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"vibration\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":1,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"m/s²\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "vibration", "tremor", "shake", "quiver", "jolt", "oscillation", "pulsation", "resonance"], "resources": [{"link": "/api/images/system/simple_vibration_chart_card_system_widget_image.png", "title": "\"Simple vibration chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_vibration_chart_card_system_widget_image.png", "publicResourceKey": "yRTz6ea09KTYnlh2WiB1HM2RvVaYaE8a", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAh1BMVEUAAADf39/g4ODf39/f39/g4OD////k5OTYGDjg4OAhISH1xc3j4+OQkJD64uY8PDzdNVFYWFisrKx0dHTiUmr98fPy8vLaJkXnboLsi5vHx8cvLy+enp7V1dXwqLSCgoL409rkYHbzt8HpfY9KSkpmZma6urq5ubni4uLumqjtmqjfQ13gRF1p+9dsAAAABnRSTlMAIL9AEN/GQiaNAAAE7UlEQVR42uzPQQ3AIBAAsBswFgyg4fwrXOZgTyCtgwYAAAAAAMB/7dpci08t+Wwu7xpx5ezbm1mjHPDofZYY/QhDZDEiqxF52a133YZhGArD2xkIEgQlgIMu0BLb7/+GtRu3zdAARQeDafVPkjh94KJoTUi0JiRaExKtCYnWLyDOCNgTCOcjebgXAGvmkSGEbyo3x2X9HDJM9mrHR9UAmGJr30AKMXoOCOH1vgxNK86kOpwGtgShIgkorTR4kxWcaXAXB9oxcOEmjMt6DimbnhBR7ucbFSRiZIOQLSSQWjNXy9TYaOnH0DSrgalmVVzWcwilL4hk3LOMxXCHAFLfd8QJsAWF+FAW6uhU+DT/qOshUp3SJyQRiwIQVbIPSCIAJMEhToP4AeKi7x7YA8QBanEgXL4g7jgzMpyQxnrDAWm1FzV0Wj0Rex28a+NA4P2ErB2fJU0AxoJ2W0gZzQA30sXgStumjE2pbmAt+ykEBBi3vFc7XqYnEJajhNfpP38aYzYh0ZqQaE1ItCYkWhMSrQmJ1oRcGa/F/wCEjfbk9SFapZdM49Uhb+yabZOiMAyAv2UyLZairQVB3sS7Uff//77bNJbDWbrj7I53cufzAZTYnTybBBh0Lda0a0SycJFsAGIrqoWLlBvep8dli+yps4imXLbID7EPs7JfpkhVimPChQhDskiRtdisSnoamwGEIVmkSJYCVCIteUR4SL4poiyMWAWPZJelAyeeiIbKkh13kxrtvyBiz0b6feEQdQGe3iC6XE0c9RX4jIu28Bt7cPjWRwY73QyiimQdHZK4iKWM0YsYRO0QD/S6RnQacZKWxCvwCRJzmJCjKZyz8+20fd+U/HIunFCRxLC+V8QhITn3E8CB32jsLLQOCwj06KQHPiHHHiZ0BqDD2RXcTmLHnfWBfZkeB5E1g1jdKYLm3HPuHV6oQj75HrEFstIQOFP4Ftv6HJVs4YrTIWCBUU7b2WvfT+qgNK2qtExmf8YwZFSNlWjubC0AGYpw8F2OHUCB7tpgnIWXMrf/++5EI1Urak2tgKh9Z9kO3zGKJ8v10Wsf37YPe4hDJps7hz2IIBac4YU2+hpRY5ugKTpzVqMI4sXQJDmj0UuGNnpDl3faz1frdAtzNOKuX7uwyfaLIpo3Y4Qx6AmJ5T5UczE6LqBCPS6SNHHSuVpKNTfrGXyN74t0zvSyQNRBhIpgeVHNlat9c7a8iARYvoCPlM1fE2EKPhJE4EbEcCNSp50lkJMk1Mwoi+oPiJipSAs3KMQ6JqLwwp/JfQ/2EGcndg8TCWctg2Y8a/WIFiZw5jGRM9ZBt891KF1k1rePE+k4NYeHkNl4HWEnxQPQx0Q0d5asJbk4ikRI0gweJ3L2u5PfWq+jNOYUL5Q/kntbVBERyUeomvz5uMhQJg8UsTSkb0j5eh9tHKeE2I1HKPGISE6dxX9H5wdNayOsRAUPFAHV4TsHC0ThENG0nBjfR2pEdCeIiegwT8ogr41QiRU8RASUCilIOQ63Hc+bNqTUyvZ3WKmwlN9Mb3wVrY2RlMNTP/ttawX3kKRp8tQi97IR+3/jaXxT/R9fK7xElshL5Nl4iTwbL5Fn4yXybLxEno1f7dwxDQAgEATBwIcC/4ZBAuVzmXGwBlZIN0K62TnLncqYII27pVr/b6lmJY3CAAAAAAAAnhww9gaQpe5WwgAAAABJRU5ErkJggg==", "public": true}]}