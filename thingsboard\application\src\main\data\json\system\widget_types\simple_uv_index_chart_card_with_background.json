{"fqn": "simple_uv_index_chart_card_with_background", "name": "Simple UV Index chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_uv_index_chart_card_with_background_system_widget_image.png", "description": "Displays historical UV index values as a simplified chart with background. Optionally may display the corresponding latest UV index value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'uv', label: 'UV Index', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'uv', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"UV Index\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.ceil(Math.random() * 4 - 2);\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 14) {\\n\\tvalue = 14;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.ceil(Math.random() * 4 - 2);\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 14) {\\n\\tvalue = 14;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#7CC322\"},{\"from\":2,\"to\":5,\"color\":\"#F89E0D\"},{\"from\":5,\"to\":7,\"color\":\"#F77410\"},{\"from\":7,\"to\":10,\"color\":\"#F04022\"},{\"from\":10,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_uv_index_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"UV Index\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"light_mode\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":null,\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "uv", "ultraviolet", "sunburn"], "resources": [{"link": "/api/images/system/simple_uv_index_chart_card_with_background_system_widget_background.png", "title": "\"Simple UV Index chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_uv_index_chart_card_with_background_system_widget_background.png", "publicResourceKey": "Q98eahklafJ9y99tO9S7J959VZ7EhU4I", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_uv_index_chart_card_with_background_system_widget_image.png", "title": "\"Simple UV Index chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_uv_index_chart_card_with_background_system_widget_image.png", "publicResourceKey": "y8OHOAOmAyg7f7EnEyX3YpoRkpJkjyPR", "mediaType": "image/png", "data": "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", "public": true}]}