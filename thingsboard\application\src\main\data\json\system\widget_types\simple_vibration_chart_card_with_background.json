{"fqn": "simple_vibration_chart_card_with_background", "name": "Simple vibration chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_vibration_chart_card_with_background_system_widget_image.png", "description": "Displays historical vibration values as a simplified chart with background. Optionally may display the corresponding latest vibration value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'vibration', label: 'Vibration', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'vibration', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Vibration\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"let factor = 1000;\\nif (prevValue < 1) {\\n    factor = 1;\\n} else if (prevValue < 10) {\\n    factor = 10;\\n} else if (prevValue < 100) {\\n    factor = 100;\\n}\\nlet value = prevValue + Math.random() * factor;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"let factor = 1000;\\nif (prevValue < 1) {\\n    factor = 1;\\n} else if (prevValue < 10) {\\n    factor = 10;\\n} else if (prevValue < 100) {\\n    factor = 100;\\n}\\nlet value = prevValue + Math.random() * factor;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0.1,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0.1,\"to\":1,\"color\":\"#F89E0D\"},{\"from\":1,\"to\":10,\"color\":\"#F77410\"},{\"from\":10,\"to\":100,\"color\":\"#F04022\"},{\"from\":100,\"to\":1000,\"color\":\"#DE2343\"},{\"from\":1000,\"to\":null,\"color\":\"#791541\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_vibration_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Vibration\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"vibration\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":1,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"m/s²\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "vibration", "tremor", "shake", "quiver", "jolt", "oscillation", "pulsation", "resonance"], "resources": [{"link": "/api/images/system/simple_vibration_chart_card_with_background_system_widget_background.png", "title": "\"Simple vibration chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_vibration_chart_card_with_background_system_widget_background.png", "publicResourceKey": "YKs9Yjsj775UpJdIDxLDQIbf6FcT7iC8", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_vibration_chart_card_with_background_system_widget_image.png", "title": "\"Simple vibration chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_vibration_chart_card_with_background_system_widget_image.png", "publicResourceKey": "9Js2kM6NdRwehbITmjP1MGCJklAD7jbm", "mediaType": "image/png", "data": "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", "public": true}]}