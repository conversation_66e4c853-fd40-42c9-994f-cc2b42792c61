{"fqn": "input_widgets.update_shared_string_attribute", "name": "Update shared string attribute", "deprecated": true, "image": "tb-image;/api/images/system/update_string_timeseries_system_widget_image.png", "description": "Simple form to input new string value for pre-defined shared attribute key.\nThe widget is deprecated. Use \"Update Multiple Attributes\" widget. Attribute type and string value type can be selected in widgets data key configuration.", "descriptor": {"type": "latest", "sizeX": 7.5, "sizeY": 3, "resources": [], "templateHtml": "<div tb-toast toastTarget=\"{{ toastTargetId }}\" style=\"width: 100%; height: 100%;\">\n    <form *ngIf=\"attributeUpdateFormGroup\"\n          class=\"attribute-update-form\"\n          [formGroup]=\"attributeUpdateFormGroup\"\n          (ngSubmit)=\"updateAttribute()\">\n        <div style=\"padding: 0 8px; margin: auto 0;\">\n            <div class=\"attribute-update-form__grid\" [class.!hidden]=\"!entityDetected || !isValidParameter || !dataKeyDetected\">\n                <div class=\"grid__element\">\n                    <mat-form-field class=\"mat-block\" style=\"width: 100%;\"\n                                    floatLabel=\"{{settings.showLabel ? 'auto' : 'always'}}\"\n                                    [hideRequiredMarker]=\"!settings.showLabel\">\n                        <mat-label>{{ settings.showLabel ? labelValue : '' }}</mat-label>\n                        <input matInput\n                               formControlName=\"currentValue\"\n                               [required]=\"settings.isRequired\"\n                               (focus)=\"isFocused = true\"\n                               (blur)=\"changeFocus()\"\n                               maxlength=\"{{settings.maxLength}}\"\n                               minlength=\"{{settings.minLength}}\"/>\n                        <mat-error *ngIf=\"attributeUpdateFormGroup.get('currentValue').hasError('required') && settings.isRequired\">\n                            {{requiredErrorMessage}}\n                        </mat-error>\n                    </mat-form-field>    \n                </div>\n    \n                <div class=\"grid__element\">\n                    <button mat-icon-button class=\"applyChanges\"\n                               type=\"submit\"\n                               [disabled]=\"(originalValue === attributeUpdateFormGroup.get('currentValue').value || attributeUpdateFormGroup.invalid || !attributeUpdateFormGroup.dirty) && (originalValue === attributeUpdateFormGroup.get('currentValue').value || settings.isRequired)\"\n                               matTooltip=\"{{ 'widgets.input-widgets.update-attribute' | translate }}\"\n                               matTooltipPosition=\"above\">\n                        <mat-icon>check</mat-icon>\n                    </button>\n                    <button mat-icon-button class=\"discardChanges\"\n                               type=\"button\"\n                               [disabled]=\"originalValue === attributeUpdateFormGroup.get('currentValue').value\"\n                               (click)=\"attributeUpdateFormGroup.get('currentValue').patchValue(originalValue); isFocused = false\"\n                               matTooltip=\"{{ 'widgets.input-widgets.discard-changes' | translate }}\"\n                               matTooltipPosition=\"above\">\n                        <mat-icon>close</mat-icon>\n                    </button>\n                </div>\n            </div>\n    \n            <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\" [class.!hidden]=\"entityDetected\" [innerHtml]=\"message\"></div>\n            <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\"\n                 [class.!hidden]=\"!entityDetected || dataKeyDetected\">\n                {{ 'widgets.input-widgets.no-attribute-selected' | translate }}\n            </div>\n            <div style=\"text-align: center; font-size: 18px; color: #a0a0a0;\"\n                 [class.!hidden]=\"!entityDetected || isValidParameter\">\n                {{ 'widgets.input-widgets.timeseries-not-allowed' | translate }}\n            </div>\n        </div>\n    </form>\n</div>", "templateCss": ".attribute-update-form {\n    overflow: hidden;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n}\n\n.attribute-update-form__grid {\n    display: flex;\n}\n.grid__element:first-child {\n    flex: 1;\n}\n.grid__element:last-child {\n    margin-top: 19px;\n    margin-left: 7px;\n}\n.grid__element {\n    display: flex;\n}\n\n.attribute-update-form .mat-button.mat-icon-button {\n    width: 32px;\n    min-width: 32px;\n    height: 32px;\n    min-height: 32px;\n    padding: 0 !important;\n    margin: 0 !important;\n    line-height: 20px;\n}\n\n.attribute-update-form .mat-icon-button mat-icon {\n    width: 20px;\n    min-width: 20px;\n    height: 20px;\n    min-height: 20px;\n    font-size: 20px;\n}\n\n.tb-toast {\n    font-size: 14px!important;\n}", "controllerScript": "let $scope;\nlet settings;\nlet attributeService;\nlet utils;\nlet translate;\n\nself.onInit = function() {\n    self.ctx.ngZone.run(function() {\n       init(); \n       self.ctx.detectChanges(true);\n    });\n};\n\n\nfunction init() {\n\n    $scope = self.ctx.$scope;\n    attributeService = $scope.$injector.get(self.ctx.servicesMap.get('attributeService'));\n    utils = $scope.$injector.get(self.ctx.servicesMap.get('utils'));\n    translate = $scope.$injector.get(self.ctx.servicesMap.get('translate'));\n    $scope.toastTargetId = 'input-widget' + utils.guid();\n    settings = utils.deepClone(self.ctx.settings) || {};\n    settings.showLabel = utils.defaultValue(settings.showLabel, true);\n    settings.showResultMessage = utils.defaultValue(settings.showResultMessage, true);\n    settings.isRequired = utils.defaultValue(settings.isRequired, true);\n    $scope.settings = settings;\n    $scope.isValidParameter = true;\n    $scope.dataKeyDetected = false; \n    $scope.message = translate.instant('widgets.input-widgets.no-entity-selected');\n    \n    $scope.requiredErrorMessage = utils.customTranslation(settings.requiredErrorMessage, settings.requiredErrorMessage) || translate.instant('widgets.input-widgets.entity-attribute-required');\n    $scope.labelValue = utils.customTranslation(settings.labelValue, settings.labelValue) || translate.instant('widgets.input-widgets.value');\n    \n    var validators = [];\n    if (utils.isDefinedAndNotNull(settings.minLength)) {\n        validators.push($scope.validators.minLength(settings.minLength));\n    }\n    if (utils.isDefinedAndNotNull(settings.maxLength)) {\n        validators.push($scope.validators.maxLength(settings.maxLength));\n    }\n    if (settings.isRequired) {\n        validators.push($scope.validators.required);\n    }\n    \n    $scope.attributeUpdateFormGroup = $scope.fb.group({\n        currentValue: [undefined, validators]\n    });\n\n    if (self.ctx.datasources && self.ctx.datasources.length) {\n        var datasource = self.ctx.datasources[0];\n        if (datasource.type === 'entity') {\n            if (datasource.entityType === 'DEVICE') {\n                if (datasource.entityType && datasource.entityId) {\n                    $scope.entityName = datasource.entityName;\n                    if (settings.widgetTitle && settings.widgetTitle.length) {\n                        $scope.titleTemplate = utils.customTranslation(settings.widgetTitle, settings.widgetTitle);\n                    } else {\n                        $scope.titleTemplate = self.ctx.widgetConfig.title;\n                    }\n    \n                    $scope.entityDetected = true;\n                }\n            } else {\n                $scope.message = translate.instant('widgets.input-widgets.not-allowed-entity');\n            }\n        }\n        if (datasource.dataKeys.length) {\n            if (datasource.dataKeys[0].type !== \"attribute\") {\n                $scope.isValidParameter = false;\n            } else {\n                $scope.currentKey = datasource.dataKeys[0].name;\n                $scope.dataKeyType = datasource.dataKeys[0].type;\n                $scope.dataKeyDetected = true;\n            }\n        }\n    }\n\n    self.ctx.widgetTitle = utils.createLabelFromDatasource(self.ctx.datasources[0], $scope.titleTemplate);\n\n    $scope.updateAttribute = function () {\n        $scope.isFocused = false;\n        if ($scope.entityDetected) {\n            var datasource = self.ctx.datasources[0];\n            var value = $scope.attributeUpdateFormGroup.get('currentValue').value;\n            \n            if (!$scope.attributeUpdateFormGroup.get('currentValue').value.length) {\n                value = null;\n            }\n\n            attributeService.saveEntityAttributes(\n                datasource.entity.id,\n                'SHARED_SCOPE',\n                [\n                    {\n                        key: $scope.currentKey,\n                        value\n                    }\n                ]\n            ).subscribe(\n                function success() {\n                    $scope.originalValue = $scope.attributeUpdateFormGroup.get('currentValue').value;\n                    if (settings.showResultMessage) {\n                        $scope.showSuccessToast(translate.instant('widgets.input-widgets.update-successful'), 1000, 'bottom', 'left', $scope.toastTargetId);\n                    }\n                },\n                function fail() {\n                    if (settings.showResultMessage) {\n                        $scope.showErrorToast(translate.instant('widgets.input-widgets.update-failed'), 'bottom', 'left', $scope.toastTargetId);\n                    }\n                }\n            );\n        }\n    };\n\n    $scope.changeFocus = function () {\n        if ($scope.attributeUpdateFormGroup.get('currentValue').value === $scope.originalValue) {\n            $scope.isFocused = false;\n        }\n    }\n}\n\nself.onDataUpdated = function() {\n    try {\n        if ($scope.dataKeyDetected) {\n            if (!$scope.isFocused) {\n                $scope.originalValue = self.ctx.data[0].data[0][1];\n                $scope.attributeUpdateFormGroup.get('currentValue').patchValue($scope.originalValue);\n                self.ctx.detectChanges();\n            }\n        }\n    } catch (e) {\n        console.log(e);\n    }\n}\n\nself.onResize = function() {\n\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true\n    }\n}\n\nself.onDestroy = function() {\n\n}", "settingsSchema": "", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-update-string-attribute-widget-settings", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Sin\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.23592248334107624,\"funcBody\":\"return Math.round(1000*Math.sin(time/5000));\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{},\"title\":\"Update shared string attribute\",\"dropShadow\":true,\"enableFullscreen\":false,\"enableDataExport\":false,\"widgetStyle\":{},\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{}}"}, "resources": [{"link": "/api/images/system/update_string_timeseries_system_widget_image.png", "title": "\"Update string timeseries\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "update_string_timeseries_system_widget_image.png", "publicResourceKey": "iyiEAEpcCF3Lu8Dy2XIxc2O9xir540wS", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAAAAABslHx1AAAAAmJLR0QA/4ePzL8AAAOkSURBVHja7d3bSxRRHAfw/VdG6yklCbPsRk+GhVLYDSXMohC1tczVUkxFXDULzW4a+SA9VIZRm5haIJqBmMqW2cUoddUVR1sved2dnV/nuJaX3X0Q1pjZvj/YM4fDmYfP/uZcdvbhaMgmmnpVHibRShpb/4REKg9pot+mESfIC2JC1Jgkb4BIJk0veUX0AgIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAsp6QN194+epPs1Z0vqm/Uw2QuuussMSOuYXYbe3PZJvyIeNx40T1xUTDTe+sHCK3STTfwQDGpgVU/+Vr+ox2FYyRktdEuS3UllFXVsAhtrMz9DOe5JI7htQfvIM1K3VcDYO9NZ+GtfP00Uz2uLEliDFHpqYy3qG2suaxGiDW86PVFURTD6/mxg4vQQwpRUW5ObyDZc5uVsX0+6A2s5uo9LmdtA7INIfUlo6MjFhUtY58S06TiXJa6HOsmc9aSd+pI576Lo6SuVNVEDndwErjhUs3dO85pCExrSSeqDE5K61bjSu7febvqJlzACdlbFEAAQQQQP4/yKxTRR0Q+eXKDdFsocFRMRTOqgkiP9E1rWwx6AzLLmqByE919avbajihxqVDsRCWj3rnVpYM1/lQLMRVPhw5ceNQHGTmnok7qlw7PAuJOrWOEIs+c8DNc+XxR2tdITSWd8XkPh8eHezrC2E5SXWfjzVNvxUdRn0Bf29V3sZvq2GVD82FuUYy5ukbFiBdN7MbeU+pOvvuV96xoyrT4kGJa8eaF8Sg/cHaEN9WIr/ixe9/8+7QxD0bsgITDgqPWMu2refChXIi28mApIiNzOYX7B826MEB76EtStC+SbLuSFkOiZZoaos/ewcUdYh9AodJ1gX8ovubekhO2Ms6hk8pcdMYlM+KmBPLIUWscoy1UF7Q4hjpEpopTMsq9cKYo6NCIacjV0MiY1hRELgImRQqKUBYiC5VQ4aEatqZ2MljWvGQYP6C9LAz5CirvBA+UUyYndXmSPGQ2GDjwG0fZ4hQPPh21xH2l5BP+oCoD7UrHtITIvgej3CGnIkWhAN9fELfLggh7YrNyLIQp92sWUOOq31QxG92QAABBBBAvBNy658GMgIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAokyI1xwQ7B1HNo+LGqt3HKItabzjWHOJfgM5tA7UOw6xeQAAAABJRU5ErkJggg==", "public": true}]}