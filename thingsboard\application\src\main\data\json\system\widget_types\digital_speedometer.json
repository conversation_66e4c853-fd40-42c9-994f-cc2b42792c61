{"fqn": "digital_gauges.digital_speedometer", "name": "Digital speedometer", "deprecated": false, "image": "tb-image;/api/images/system/digital_speedometer_system_widget_image.png", "description": "Preconfigured gauge to display speed. Allows to configure speed range, gradient colors, and other settings.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 3, "resources": [], "templateHtml": "<canvas id=\"digitalGauge\"></canvas>", "templateCss": "#gauge {\n    text-align: center;\n   /* margin-left: -100px;\n    margin-right: -100px;*/\n    /*margin-top: -50px;*/\n    \n}\n", "controllerScript": "self.onInit = function() {\n    self.ctx.gauge = new TbCanvasDigitalGauge(self.ctx, 'digitalGauge');    \n}\n\nself.onDataUpdated = function() {\n    self.ctx.gauge.update();\n}\n\nself.onResize = function() {\n    self.ctx.gauge.resize();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        supportsUnitConversion: true\n    };\n}\n\nself.onMobileModeChanged = function() {\n    self.ctx.gauge.mobileModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.gauge.destroy();\n}\n\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-digital-gauge-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-digital-simple-gauge-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Speed\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.7282710489093589,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < 45) {\\n\\tvalue = 45;\\n} else if (value > 130) {\\n\\tvalue = 130;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#000000\",\"color\":\"rgba(255, 254, 254, 0.87)\",\"padding\":\"0px\",\"settings\":{\"maxValue\":180,\"minValue\":0,\"donutStartAngle\":90,\"showValue\":true,\"showMinMax\":true,\"gaugeWidthScale\":0.75,\"levelColors\":[\"#008000\",\"#fbc02d\",\"#f44336\"],\"titleFont\":{\"family\":\"Roboto\",\"size\":12,\"style\":\"normal\",\"weight\":\"500\"},\"labelFont\":{\"family\":\"Roboto\",\"size\":8,\"style\":\"normal\",\"weight\":\"500\"},\"valueFont\":{\"family\":\"Segment7Standard\",\"style\":\"normal\",\"weight\":\"500\",\"size\":32},\"minMaxFont\":{\"family\":\"Segment7Standard\",\"size\":12,\"style\":\"normal\",\"weight\":\"500\",\"color\":\"#ffffff\"},\"neonGlowBrightness\":40,\"dashThickness\":1.5,\"unitTitle\":\"MPH\",\"showUnitTitle\":true,\"gaugeColor\":\"#171a1c\",\"gaugeType\":\"arc\",\"animation\":true,\"animationDuration\":500,\"animationRule\":\"linear\"},\"title\":\"Digital speedometer\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"widgetStyle\":{},\"useDashboardTimewindow\":true,\"showLegend\":false,\"actions\":{},\"configMode\":\"basic\"}"}, "tags": ["velocity", "velocimeter", "pace", "rate", "tempo", "momentum", "haste", "swiftness", "rapidity", "acceleration", "quickness"], "resources": [{"link": "/api/images/system/digital_speedometer_system_widget_image.png", "title": "\"Digital speedometer\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "digital_speedometer_system_widget_image.png", "publicResourceKey": "rrUUWw17Eby4OSGdN3aGZSM5D8k81ZIM", "mediaType": "image/png", "data": "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", "public": true}]}