{"fqn": "cards.dashboard_state_widget", "name": "Dashboard state widget", "deprecated": false, "image": "tb-image;/api/images/system/dashboard_state_widget_system_widget_image.png", "description": "Displays specified dashboard state inside widget. Advanced widget settings allows you to configure target dashboard state to be displayed.", "descriptor": {"type": "static", "sizeX": 7.5, "sizeY": 3, "resources": [], "templateHtml": "<tb-dashboard-state *ngIf=\"stateId\" [ctx]=\"ctx\" [stateId]=\"stateId\"\n                                    [defaultAutofillLayout]=\"defaultAutofillLayout\"\n                                    [defaultMargin]=\"defaultMargin\"\n                                    [defaultBackgroundColor]=\"defaultBackgroundColor\"\n                                    [syncParentStateParams]=\"syncParentStateParams\">\n</tb-dashboard-state>\n<div *ngIf=\"!stateId\" class=\"dashboard-state-widget-prompt\">\n    <div class=\"title\">Dashboard state widget</div>\n    <div class=\"subtitle\">(Specify dashboard state id in the advanced widget settings)</div>\n</div>\n", "templateCss": ".dashboard-state-widget-prompt {\n    width: 100%;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    font-size: 32px;\n    font-weight: 500;\n    color: #999;\n}\n\n.dashboard-state-widget-prompt .title {\n    font-size: 32px;\n    font-weight: 500;\n    color: #999;\n}\n\n.dashboard-state-widget-prompt .subtitle {\n    font-size: 24px;\n    font-weight: normal;\n    color: #999;\n    text-align: center;\n}", "controllerScript": "self.onInit = function() {\n    var $injector = self.ctx.$scope.$injector;\n    self.ctx.$scope.stateId = self.ctx.settings.stateId || \"\";\n    self.ctx.$scope.defaultAutofillLayout = self.ctx.settings.defaultAutofillLayout;\n    self.ctx.$scope.defaultMargin = self.ctx.settings.defaultMargin;\n    self.ctx.$scope.defaultBackgroundColor = self.ctx.settings.defaultBackgroundColor;\n    self.ctx.$scope.syncParentStateParams = self.ctx.settings.syncParentStateParams !== false;\n}\n\n\nself.onDestroy = function() {\n}\n", "settingsSchema": "", "dataKeySettingsSchema": "", "settingsDirective": "tb-dashboard-state-widget-settings", "defaultConfig": "{\"datasources\":[{\"type\":\"static\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgb(255, 255, 255)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"syncParentStateParams\":true,\"defaultAutofillLayout\":true,\"defaultMargin\":0,\"defaultBackgroundColor\":\"#fff\"},\"title\":\"Dashboard state widget\",\"dropShadow\":true,\"enableFullscreen\":false,\"widgetStyle\":{},\"widgetCss\":\"\",\"noDataDisplayMessage\":\"\",\"showLegend\":false}"}, "tags": ["embed", "embedded", "inner"], "resources": [{"link": "/api/images/system/dashboard_state_widget_system_widget_image.png", "title": "\"Dashboard state widget\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "dashboard_state_widget_system_widget_image.png", "publicResourceKey": "CKyiyFQIDFgkC4qx5lRHLAl5P7hDqhcG", "mediaType": "image/png", "data": "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", "public": true}]}