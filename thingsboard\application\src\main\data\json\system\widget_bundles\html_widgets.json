{"widgetsBundle": {"alias": "html_widgets", "title": "HTML widgets", "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAA81BMVEUAAADg4ODf39/g4ODg4ODR0dHPz8/R0dHR0dH////f39/t7e3v7+/6+vr09PT8/Pzn5+fR0dHk5OT29vbx8fH4+Pjr6+uUlJTp6emMjIyGhobLy8vGxsalpaXh4eGAgIC3t7erq6vb29u5ubnZ2dmamprDw8PAwMCysrKnp6fd3d3IyMjW1taJiYmurq6jo6Ofn5+Pj4/X19fNzc3T09OXl5eDg4NtbW16enq7u7t9fX2RkZG9vb13d3ehoaG0tLSwsLBycnLQ0NBXV1deXl5kZGTPz8+cnJxqampnZ2dbW1tSUlJOTk5hYWFHR0c3NzdAQEAykmEAAAAACXRSTlMA7yC/r+8gv69spyeXAAAWJklEQVR42uybiZaiRhiFsyc/u+ybbIIIioAKuCuttlt3Z97/aYKgpnti9jYnnZM7Z/6pQ48Wn7eKul3CF1989/23tbeykI+mb74qOL5FePStcPhgItkvv/riBw0thJV/ytYHBAHAvv6ixpcnX6pqYX8ShBWhEkNx8AdFse/syZdf1NCfHaGa1M9Da4D5Ig4+RdFcVatX+Bpo/mtUZrSFSnbAw2+LH9eql6bqW2b+74IhBcjVkV6cIVcQPPadAY7X1a7LkGWtPv4oRMPodbdEXYJKtYz6vREg+2TZWDcYeCXOa78LSIXCJ8GTfQHxjU307NWMTMiSmnuq1adNB3pDD2h65Lk8sErXU8UCRFQIptZN9hTX6y54vE8AYH3M8Lo0VFLYgrhPNxKkAKFb3SeD4UdeSwRsZA5YQn2WjQ7zd0GwiyNN93gF6cRZsPbUOJM3ieqcqlgNn60+1rcW7ZpSiNckx2wWIEi4ZBU5lJ/pxyCMZGrSY/nHSWdjbmc4lAoGLGZNbCNTcWD1KPy04CeRuVOxZG4OXd5d68sB806OFOqsriCEsJ3LD6gghIGL1k+Vq4ZPGGRBWCMsVxqjaqryLBs+bz2EN2V6tO90ZXqQNyTDC41YWfHaS4UPqeHNR5LI76YcNPMOvTKauipmhjqLRrlDU7O6SJDv4sjnIO1dnu3D0S7LN85odqp0NUaixbQVqaN4rg8pNbAK5PnL3gAqCaGW9Z0QsCAKk10Wmcoa4MmGUuE22zh1AvICxAswWDe6EgKzRjebhLEpMuPFu80R7C2Iv95uJs3HtTzT3cf8VDE4Sd3WmNq2JaSdUD+D1PMwVbAowGtZJ5JwMWtMn9Pxrn8COfagVHud6s8uA8MFA82MKkDUvEnvjObO6NkUh4+7JMC7OeINP80UvgKhho+5ytHDqdRl+OBUoZTr9KDnLOaZMB5eQCS2rtv9PEg3tJLLesrbL4rxhL4GwX7st19sEsxMeuDTsfDSQMN0cjTEZDaJHwCcYTzC3s0RtVs3O2cQYsA/Um9qJcrGALNpZNpaqARfYwFwpAasSnH9ZWvEcErLsIGdMrxLiirA9HJ2LYaZsgCi0UJwy1gufZxSGscmTrtLgwLwG613uWr9+axFchz5+SHmdAQnf+d1ZSGB3QZybAEAVy1b+PvNEezS+keyFtc3Gp+Z8A6OfPD0+/k68mAuff4Dg1wc0SRHTukPDHJxhO62jafeBwZ5NUeU1UcG+Tlr9fTkIw+tqyNWIPc+8mS/OiLoNf4jX34vjtBPMyFpix8X5HrVSpLkQ4NUjmD/nZX9mrU+KshfcYRloRLD/juw31y1aqOp9jMIJnLl7hsDJI0BhcIrMVMDKnVa7K2wLvJlFeEizMFI+Ez3umrxYSL/vCAS7QaNk8Rjw+IwZ8pGbzaiMEGCSg2dh18IF0cuSxa1wV7Ont/zdwe57mstm/OVfQYhjEkaaawhyE7Pn02UPEHFgWIRQPh9BeELEFbjOLrvjHmcerQYgsKBxCj0QRmwJFWX5QVW1NTEzuSPwskRrKYMOKhE9/sUCffJWr3Fs38GYcP1Su/z4X41ayrxOBhHVMcUBJWsJbHgUoIkLg22Iwj5TLQTYdLwPZrB+mZ7LMQmrumr54lY1JUsQil/PrML0nCSeARUegwnEn2nrGXudOQMAj1huCCg5wxNtB2OM6mOoHZzKBCttInw/CQPHRuN0sF2aBW1lbvSyHUXgrFDGiuWU4fpAIo67EMlwlopHHCree+6byzW3MMUv48jjWSv8GeQjqqoaFkpN6zP51GnbZoziW8JGgAWv8xUsIQlNPR2UZFg7iT52DMbMxDXCNdstxUoqtKEi9YFCClMPIWBUty0a65bzH0cQfl168bK3pjbhOo0U32UxheQQzzXkKRONnQl8UhbXnqzPAvUE8jGghvaNBkAohlnWjUxqDyaPi+Ju/yGaCzM5zb/S5BWpEE/GQWSK19ABKkzifylbEjDoi68FBmtDWFP/zpIEBgI4bqNVa8CoYdRc7cg7uGIHcjjOnVjQXxs8qCNLFeIgxbRn/IAxGhE1kxNCwPZJLS5HDeBTljb4XohEA4FNzTQg0d0EkvO+dxJI57Ibe4uK7uIiL+5suMEQ36+lXU6QnI4/L64goBkmVcHWO6eWQvFPmxoPDlyRcEujjA0IpIc/4vdfoKB3xXOwu8LI++YfmlbvIBo27pKUIajEgVU+3r1Z9T+ZaC8Fe63L4gkbVb//hYP0eXv5wg9F7QLyMNwaTP8NHN4AHayZK4pKyzPsTf9HKQ9RC9NZV3lKxN+XVRWu1v65bubZ+sKIltQyJnzQNhmnwMgkP4jhcZyH2FwP84HCF/mK7HIWh0Wp5smUTDQj4rNKMf+gCBR42UwOBvH9wedGnHOV2hHUdATiM8yGgcaip/eGX+/lb2Xb3e3QKil1GCBfIgkvUVJz1LUY0fZyjEUQ+RYZUqFsTBiFUGgAChPklJR+SQJBm4FnxyHhlJTPZDSAePGkofCKJCDgZY9IF6PCmmoN/n6OO5a5Hs5Ikr16U0QwpKjAmcsDeIYiSVFdwhfyAaamrT7qjefz0b1zKdaeQ+IVqYoK0Q5DhYr4rUjE0cOpITVOqOXHjkTBj2WyrpxndfyAczqyHiuzDzsvRxR1tP6ui3+EqSqTcmFUECEsKgoZ6YAvSiZyWEkjbuR3obesAdUEgF1QJR1UTXSPsJFE9NzFhJf63afVHzWYACo/VEQ4QyiN6CbWO/lSJjNdi8O9RZkOxfLSoEqLYjt5ATiCTzX1QFoL89mk5Y0W7amdAEyAHErEP6qADkFFdJ6In8GaY4akr1yFscrSJYqDJU9XkAm/rvta9l2a/Pw2dAyE1djyooEQWMonUHIxVixqfqmmw59L1YGFgAie7Y4zQ3v6QwC2qE9wC4gAK6k7Nqd1QUk63v6AzvzHvYhMo76ev3dvkMspAT0ZyBWkC+xqnbqwXNMdRsw7WLQCfKtNo3QRR18IZsl5Gl+SDbWCtInaiCBKFGACdnwEUp1XYB+nZ3M0n2PFPo4ADqx8KiFucNZNqXzVS758F6OVPUCUhsbGleufHgZs3Ac6bcDj/w5eHGvUhNZBi+C5Pt9L8OvUYwg4K2Iz/aaqvfh9UWZ4t7NEezVvpYvDz0eXol18llE/0426ed5+gB/VphTK+p7OlK1SgtYjPuTd7GJlZVk9TqeBAL+kNjy/zHExd7f7Bdn/oAjlG/b1BmEd73OnyJh1bp38otrI1AIizh0AaWIS70tvPtAFvjTop4YuFqvPKpp+K2PS2v+gZXdTbaRfwahuwePhT8hZlCfPQAQo7x26tDdXRIX3r3U20Jyt6jU2AXy0bqGOdZx2Fu9NGZ/IGvFutm6XrVAXrIAGG1RHIkhvogDLvoIhrMEEBhJUBZNwOkHFEmyiEYAgCU/AFnLXChESHVcOfgIA1znR01jysrilI+wJBC0pXEAbNWRKdhFHcU2Sed1DeUncdEZTjWaZbyzaObcDc5bPs8YO6Q4AgyCFFW0fPaWI/K88OMtiBrrnkaYY71LkVY91Q3eVaDtEr1oLHRIepmOTYIy5KB/BmGNHVlm200PVz6lqQq09KMgaGXtoZIuuxy40ligAdwuevJOX2AAIMwx1DxmQgORDmmo8QuphQGnpnpinbsRW3raYhZPslQjuZE8UcjivCT3liPpzOnzb0Fou7k36fG2mbWocapIsSV4EAoYaivBmKrn7WiImPnU21MVCJ3Uy6HcP5C48tQ3PjFs80dFYcvKa8dm49hnjqZdYwGEfeeEnCtFxcYGMIOdoCBiLDeHDmvFMQ1EOFSClC+7odsr1bKZxaE53yPKyl0eKNyqeU/sDUceF+mx9gaERNxWNvf1BkieUWatCgTlHxfBWBW60NCVIhOHw2YFggSjcmQ5QvVbyQsC7I8AUFX6QIEUsXmq8tdd8kXsF7Up1QDKu7awKsxBV6DL2yVHervshrI2ic2c5ggih1EMaLrEO4v6yv/ckUrPLeoCEnRZQPNZuI+sAkSoL+IRHk38sh862Uf6rCl0cUNvy8O6t/TPjkjlpBb3DxXIsfc5iLBlieU+twCq1TJdYkV1Iv4CUmUgWJ5BpmnVDUVqwsEhChBfirYTwFJz8LSNjtaN3fiePViPxAuIk/QYf2aIQViB9PRA1SXRielk0hMksZX26rO2MLNDWRMvkx2LdByArK3wKwj5IyIyp0ox9PGRzqaEJtKrEQMthwLgM+WEOTbK6VKnMP4M0ojbGFaCVN3QGC02d5qxo5r5g5vR/U2tmVGdZ+uXjiC7zUpGrkNrkK9N0dlnB1OLVYhabC2ZrSS2P1wfHMzI9htdpOez9Qz15dVeJysQUlkPAIjCesAfZgB7C0BabdplbVgvq1WKodluk3cAnKwoDckCgHZQZrLF7jnpzLvQmqOgBQeh1kqgPaHKbijlsNsLjHrcb5aAxavnOtD5Jnumb+1r9ejX+1ocWzQYlIBKJEoXppLnAIVjLFkcQeZygcBhePG3fwonmJmLwO+Uz5buqtIH5NQkebZ8/1O+kk0UALZbCqrlnXu9i1Y1qm6KHtDTAaZKUAxXpgkU/7V9rQrkljDpsI7fRNTiErLWO1CJdzbZqc2aU/D3t0MBlVPwVpyukkWVRiTc1rWbP/kdIvbLL0PJ21yA47/1UyAASPgndPOqRdG/dMRH8DMR9mZPzVa5olI95nb06nIkjcC9dTtr8b1FSP8CRKoTVQOdTDC4ivWk298hVtDtZyC6mQZ31m1H2rvZ/ArCFYdI4AgsPYOQmHvygCSwche1lxin9lLnz0cYDCUAJ1CUrW7kLliTHIe76rYj4saxxevQaieprhDtRF7NKxDKPMUf0OqypwHAVNaANtP9TKRa8sQG7FEYO2IRiHSvylpFpZ96cH9dQS4avMixKl5AJNlVeHEfjPZRBcIOAokGQhgaQ4EAxkxJor4bBTPazIxkhyqHqF9j1YNqPwCQnePJNXbiwP11BblYYqzm9ReVv4B0MYCG3P95jpTxpzaZwnQyAD/pglVlraLly10vhUIPh4QCAGJeTiCipcNddduRRuajwzn9GqQVDz4DUSYqNCcKNGUbbMEoQNSianE0F8qJZMVPk0vwAMIL4K667Yh9rPGbJfUapLdb4CcQtW4BwGJiAS3VuVCiOVPHgXckrqF3nJjrZ4/GRqyWFfpIk/axfA90uIT76wpylfP0VJjyGgS8w9OniABn3QQAa/giI83Np00TOo5XxrGXlxytZZ+OdRAnn467tvtyeJrhhCeX9tQ+EXA33Xak8oTSfnErIF5eWjmmPIazTFGIot2SB9XGFYZft7g4ngFgTkGYz0/coG3ubMhtR7A/cZsTLow5+HX5T0w5Q0y4v64g3/Lof+DeeBL54nsERT/6I64A7DfFs7rax39WF/vy9NTxD7WP/vT0l19/9cX/+qm9e91REwjAMNzTnw+YgzOMoiKCIIjgCdGqtWratE16/xdUFNvapkfb7tLWN3EzjjHjE5nFTSRbvR49fqj9wZ48uqNFHj00GP5czHx4R4s8MfEHK6+Zv5NFNIY/G3v44E4W0fCn0x7cxSI3yA1SdoPcIDfIPUJyGwgdBCGD1gZoaJC5k7q+L8B91/WHL7K267prayuBpJg3KwrRYip3BhlM6qj5gOHVld8LXcehkM5275jreT31ciE8CbiR45CKQqjnpAdwdz3/BKIJBiDZAsUD+tjCGaKJqh5aiGz/BWxbG6gLyNieky9BBnatshDemZhq53kTcYRQT9CBFkYAvgR5Xd3NDjXeIxgbMopqk/1e2N7ep2FcjD6D7Ab7nlvMV3WzA1JCSYBRQillhBoS6jg6f1GKHm8Exwflab6ykIv+5vPI+26QG+QGuUFukM/7ZyCMlT9YGRg7T5e3L1ef61WDiGcuIBe74Wx0LDb7oxBAMmqYfLTGVwpWSdUgTvcA0Naz+tZ/thn7ttlcPSNQ01XLEKvBXwihwGDjALTZ2mjgy+UnkHzacCmMQ2OaA9pucSgg5r6xG1YJ0g/DbfcC0m/uEY2ml5BhdxF3XdVfeo1NZi5a49kqUfFy/KxVrxCk22yONheQN/um0Y/jS8h4YzE36m1c8FZH67osXyXWZifaq6RCEI9S6/LQWjjd7TLvXEImLRNAvmmDzvrZyj7ukd5qOZu1wgpBPt8jDdXozszdJWTQ5QjDoLuHaMViM0D5jpimJqsMwXp1ICfILI5jASBtTcebgeyPBv1ujS5ah8YqkZ2lN27wykD4NAHobiwBe2oBND5ATAXZd2i9f8wBwNpvFq4C9xZv5gTOtO9PU+j7Rd8mlYH8YITgmGKX38wlrELnkV/t+xBJ8LORCkKsyWSaX7JclLHI/KojJpWDiKkCBgk+5K5QZnc5vtaROJC/DtGH5HdBFiaKdgTnAreDU9xbc3wr79chrNMyfxNE+TgWK5TRQ72EkIGzPUO03klogKbcHY8zAO3C+bwTAuIQxy6tAATsxOngnJejHG9tvIfU1ijyOYzpVCempwMdEHNimZA7S8nXY3Y9JOnsdSDwOi9ADu1DCHHoJPLavxAHQ5QND6SE6Dv5BciKAgiHBeR8aFkuitbG1ZDNaLbxoS1HzU0gV93uQTRbzU37SkjmoUy9kUDntIKFL0BiFOXJR4gZc/ZLe2RpGaO+2nW5vpzKVUMYg01gPFuoqyD6hKEsrqF8kdsQX4IMUJTaHyEQAy/iv7TZ5bMGHW0ajWVDrjygXwxbM3oNhC0kysTb5mw2ezV7I1+OisHT5Ux9D1LkTMJfhTzrpkE6PEGm3XkxJFdAVJ/jsvNmv3hHch9F+y9BCDs9Q/81CItWcTJ9cYK83kzt2L7mt5an4TsQHgNgz8XnEApkEYp2V0MOCyqnExC/2XrD5cgFmD1r9YMrIO5yfUygNv8aBNvxa7vzjH8GWbsZIR13bk9sBt+6AvLli++ZZLgCQvkpCmKgjOKcIjjH855J3z+kZDlQvUCB8TzTAZgKuPdPv+f+/o/xx26QG+QGuUFukM+7Qf5TCGGf3f+7IJoBDCmKehyfFBhf+fd5lKngEzHFh8x7g/A2sAYzGFKhCCRAZfnq8joUBYjBTnNEUapIcY+1BTEAqsCUKQE4CYGkOD5b2vp9QVSk6gnZphFNRU0gkdk8EQCNauu6ZddqKkxtmbUTZ7jtpVYW1kJ9WzMi1n4data6FpnAfG2K9jwwbb3N/eC+IEi1pA4rW1slRI/SWhtIh8it9uvMV2HPMG0wfeggtQIOW3+t04hGILaYIxCANUdYyyOkPkUi7w1Cwwg00pMCkjuw9VBwHchOkB7npO7Yug3QzyAh1AXE1jhHvjXvE8LCAHKb+zwVVpj6Mp+nHDDtNKpzO8hoO5vT9MULcYJkdmaT4HRo5cnQKiE01LUk04xEn5N2dm8QGASoc0NSwizLAOEWw3FGL+4LCV2YUMUcJaAkCwSF5Oo4VYcyQQkArpjFiaTQYdbv/zzyI2n1yp5Hfi5W3RPiJ/0bH1H+RshDhj8bKyB3schjA3+w8pr5O1nk0UOT4Y9VXjN/R4s80f5c5TXzd7HIO9jqsoYL1vqIAAAAAElFTkSuQmCC", "description": "Visualize HTML based on a configurable template or function and device attributes or time series values.", "order": 21000, "externalId": null, "name": "HTML widgets"}, "widgetTypeFqns": ["cards.html_card", "cards.html_value_card", "cards.markdown_card"]}