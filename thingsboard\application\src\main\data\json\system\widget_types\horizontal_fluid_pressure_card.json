{"fqn": "horizontal_fluid_pressure_card", "name": "Horizontal pressure card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_pressure_card.svg", "description": "Displays the latest fluid pressure telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "#container {\n    overflow: auto;\n}\n\n.tbDatasource-container {\n    margin: 5px;\n    padding: 8px;\n}\n\n.tbDatasource-title {\n    font-size: 1.200rem;\n    font-weight: 500;\n    padding-bottom: 10px;\n}\n\n.tbDatasource-table {\n    width: 100%;\n    box-shadow: 0 0 10px #ccc;\n    border-collapse: collapse;\n    white-space: nowrap;\n    font-size: 1.000rem;\n    color: #757575;\n}\n\n.tbDatasource-table td {\n    position: relative;\n    border-top: 1px solid rgba(0, 0, 0, 0.12);\n    border-bottom: 1px solid rgba(0, 0, 0, 0.12);\n    padding: 0px 18px;\n    box-sizing: border-box;\n}", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pressure', label: 'Pressure', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = prevValue + Math.random() * 15 - 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"layout\":\"horizontal\",\"autoScale\":true,\"showLabel\":true,\"labelFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"#000000DE\",\"rangeList\":null,\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"compress\",\"iconColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":0,\"to\":5,\"color\":\"#305AD7\"},{\"from\":5,\"to\":10,\"color\":\"#3FA71A\"},{\"from\":10,\"to\":15,\"color\":\"#F36900\"},{\"from\":15,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":0,\"to\":5,\"color\":\"#305AD7\"},{\"from\":5,\"to\":10,\"color\":\"#3FA71A\"},{\"from\":10,\"to\":15,\"color\":\"#F36900\"},{\"from\":15,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Horizontal pressure card\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"units\":\"bar\",\"decimals\":0,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"showTitleIcon\":false,\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\"}"}, "tags": ["fluid pressure", "liquid pressure", "pump pressure", "hydraulic", "pump", "compressibility", "compressive stress", "pipe", "pipeline"], "resources": [{"link": "/api/images/system/horizontal_pressure_card.svg", "title": "horizontal_pressure_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_pressure_card.svg", "publicResourceKey": "WZaK0BA1C79Afq5Va5s70BoON8X3sLFy", "mediaType": "image/svg+xml", "data": "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", "public": true}]}