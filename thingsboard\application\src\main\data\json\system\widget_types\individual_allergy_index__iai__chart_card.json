{"fqn": "individual_allergy_index_iai_chart_card", "name": "Individual allergy index (IAI) chart card", "deprecated": false, "image": "tb-image;/api/images/system/IAI-value-chart-card.svg", "description": "Indicates the concentration of airborne allergens, including pollen and mold spores, which can trigger allergic reactions in sensitive individuals by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'IAI_level', label: 'IAI', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: null, decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'IAI_level', null, 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Individual Allergy Index\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 12) {\\n\\tvalue = 12;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#3FA71A\"},{\"from\":2,\"to\":6,\"color\":\"#80C32C\"},{\"from\":6,\"to\":9,\"color\":\"#F36900\"},{\"from\":9,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 12 - 6;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 12) {\\n\\tvalue = 12;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 8 - 4;\\nif (value < -15) {\\n\\tvalue = -15;\\n} else if (value > 15) {\\n\\tvalue = 15;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"IAI\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:flower-pollen\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "air", "aqi", "pollution", "emission", "smog"], "resources": [{"link": "/api/images/system/IAI-value-chart-card.svg", "title": "IAI-value-chart-card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "IAI-value-chart-card.svg", "publicResourceKey": "bfsbF1v1F5dbBGVNYVK3PMWgB41yLztt", "mediaType": "image/svg+xml", "data": "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", "public": true}]}