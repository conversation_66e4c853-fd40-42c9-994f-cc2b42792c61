<?xml version="1.0" encoding="utf-8"?>
<!--Copyright 2020 Sierra Wireless. 

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, <PERSON>XEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

-->
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>Reboot Status</Name>
		<Description1><![CDATA[This object provides features allowing to monitor Reboot and Reset Factory operation from LWM2M Device object.]]></Description1>
		<ObjectID>10371</ObjectID>
		<ObjectURN>urn:oma:lwm2m:x:10371</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Single</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>Reboot State</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..2</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[ 	Containing the state of reboot operation. At device start-up the value should be 0 (booted). Once a reboot process is scheduled (e.g. exec on /3/0/4), this value must be set to 1 (rebooting), it is adviced to do that before to send response of Reboot resource (/3/0/4) to avoid any race conditions. If rebooting failed for any reason value must be set to 2(reboot error). If reboot succeeds, at device start up, reboot state should be set to 0 (booted) as always.]]></Description>
			</Item>
			<Item ID="1">
				<Name>Reboot Error Message</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Containing the error message of the last reboot failure. This value MUST be set to an empty string on exec on /3/0/4, ideally before to send the response for Reboot resource (3/0/4) to avoid any race conditions]]></Description>
			</Item>
			<Item ID="2">
				<Name>Reset Factory State</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..3</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[ 		Containing the state of reset factory operation. At start-up the value should be 0 (initial-state). Once the reset factory process is scheduled (exec on /3/O/5), this value must be set to 1 (resetting), it is advised to do that before to send response of Reset Factory resource (/3/0/5) to avoid any race conditions. If reset fails for any reason value must be set to 2 (reset error). If reset succeeds, state should be set to 3 (reset). If the reset factory need a reboot, reboot state(/10371/0/0) resource SHOULD also be updated accordingly. If reset factory need a reboot, in case of successful reset, the value MUST be set to 3 (reset) at device start-up. If it helps implementation, this is tolerated to have this resource set to 3 (reset) at first device start-up.]]></Description>
			</Item>
			<Item ID="3">
				<Name>Reset Factory Error Message</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Containing the error message of the last reset factory failure. This value MUST be set to an empty string on exec on /3/0/5, ideally before to send the response for Reset Factory resource (3/0/5) to avoid any race conditions]]></Description>
			</Item>
		</Resources>
		<Description2><![CDATA[This LWM2M object provides features to monitor Reboot(/3/0/4) and Reset Factory(/3/0/5) operation available on LWM2M Device (3) Object, allowing to know if a reboot or a reset factory is a success or a failure and why it failed. This is strongly recommended to use separated response for those resources (/3/0/4 and /3/0/5) and wait for response ACK before to execute/schedule the operation (reboot or reset factory). This prevents LWM2M server to consider the request as a failure in case response is lost. This situation could be annoying as server could decide to resent the request later which could bring to unexpected reboot or reset factory.]]></Description2>
	</Object>
</LWM2M>
