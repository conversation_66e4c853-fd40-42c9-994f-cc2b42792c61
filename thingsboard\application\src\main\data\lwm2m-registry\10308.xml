<?xml version="1.0" encoding="utf-8"?>

<!-- BSD-3 Clause License

Copyright 2019 AT&T.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, <PERSON>ECIAL, <PERSON><PERSON>EMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.openmobilealliance.org/tech/profiles/LWM2M.xsd">
    <Object ObjectType="MODefinition">
        <Name>AT&amp;T Connectivity Extension</Name>
        <Description1><![CDATA[The AT&T Connectivity Extension Object will be used to gather information describing the UE and the operating state of the UE. The AT&T Connectivity Extension Object is an open and standard object used by AT&T.]]></Description1>
        <ObjectID>10308</ObjectID>
        <ObjectURN>urn:oma:lwm2m:x:10308:2.0</ObjectURN>
        <LWM2MVersion>1.0</LWM2MVersion>
        <ObjectVersion>2.0</ObjectVersion>
        <MultipleInstances>Multiple</MultipleInstances>
        <Mandatory>Optional</Mandatory>
        <Resources>
            <Item ID="1">
                <Name>ICCID</Name>
                <Operations>R</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>String</Type>
                <RangeEnumeration>
                </RangeEnumeration>
                <Units></Units>
                <Description><![CDATA[T he ICCID resource is used to identify the UICC used by the IoT device or module for authentication by the network. Reference ITU-T recommendation E.118]]></Description>
            </Item>
            <Item ID="2">
                <Name>IMSI</Name>
                <Operations>R</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>String</Type>
                <RangeEnumeration>
                </RangeEnumeration>
                <Units></Units>
                <Description><![CDATA[The IMSI resource is used to log the IMSI used by the UE to identify itself to the network. The IMSI is defined in 3GPP TS 23.003 and is a combination of MCC (Mobile Country Code), MNC (Mobile Network Code), and MSIN (Mobile Subscriber Identification Number).]]></Description>
            </Item>
            <Item ID="3">
                <Name>MSISDN</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>String</Type>
                <RangeEnumeration>
                </RangeEnumeration>
                <Units></Units>
                <Description><![CDATA[The MSISDN resource will indicate the MSISDN as assigned, if any, to the SIM card. In the case that no MSISDN is assigned to the SIM card the response should be zero.]]></Description>
            </Item>
            <Item ID="4">
                <Name>APN Retries</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration>
                </RangeEnumeration>
                <Units></Units>
                <Description><![CDATA[The APN Retry multi instance resource will maintain the number of retries the device can attempt to activate a context on a particular APN. This resource should be defaulted to 2 retries. Should a device integrator wish to manipulate this the integrator should be able to do so.]]></Description>
            </Item>
            <Item ID="5">
                <Name>APN Retry Period</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration>
                </RangeEnumeration>
                <Units>s</Units>
                <Description><![CDATA[The APN retry period multi instance resource is the time in seconds between attempting a context activation on the APN. This should be defaulted to 0. Note that the IoT Module will be limited by the number retries and the periodicity of the retries by the DAM requirements in Chapter 28 of the AT&T 13340. The resource is multi-instance to accommodate different periods of retries on different APNs.]]></Description>
            </Item>
            <Item ID="6">
                <Name>APN Retry Back-Off Period</Name>
                <Operations>RW</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration>
                </RangeEnumeration>
                <Units>s</Units>
                <Description><![CDATA[The APN Retry Back-Off Period is the period between a burst of attempts to activate a context on a particular APN. This resource is intended to be a longer period than the APN retry period to account for limitations on the numbers of times an authentication request can be made with the network. This should be defaulted to 86400 seconds (1 Day). This resource is also a multiple instance resource to accommodate different retry periods amongst different APNs.]]></Description>
            </Item>
            <Item ID="7">
                <Name>SINR</Name>
                <Operations>R</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration>&#60;7 to &#62;12.5</RangeEnumeration>
                <Units></Units>
                <Description><![CDATA[This is the signal to noise ratio resource. This measurement should be measured over 10 sampled at a 400ms sample rate. Once the 10 samples are averaged the measurement should be reflected in the Signal to Noise Ratio resource. Reference: 3GPP TS 38.215, clause 5.1.]]></Description>
            </Item>
            <Item ID="8">
                <Name>SRXLEV</Name>
                <Operations>R</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration>
                </RangeEnumeration>
                <Units></Units>
                <Description><![CDATA[This resource indicates the cell selection receive value. See definition in 3GPP TS 36.304.]]></Description>
            </Item>
            <Item ID="9">
                <Name>CE_LEVEL</Name>
                <Operations>R</Operations>
                <MultipleInstances>Single</MultipleInstances>
                <Mandatory>Mandatory</Mandatory>
                <Type>Integer</Type>
                <RangeEnumeration>0,1,2</RangeEnumeration>
                <Units></Units>
                <Description><![CDATA[This resource indicates the Coverage Enhancement Level defined for LTE-M and NB-IoT. The Coverage Enhancement Level is on a range from 0-2. AT&T NB-IoT supports CE MODE A and B (CE LEVEL 0,1,2) and AT&T LTE-M supports CE MODE A (CE Level 0,1).]]></Description>
            </Item>
        </Resources>
        <Description2 />
    </Object>
</LWM2M>
