<?xml version="1.0" encoding="UTF-8"?>

<!-- BSD-3 Clause License

Copyright 2019 Zumtobel Group. 

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, <PERSON><PERSON>IAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON><PERSON>NTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://openmobilealliance.org/tech/profiles/LWM2M-v1_1.xsd">
  <Object ObjectType="MODefinition">
		<Name>oA Basic Control</Name>
		<Description1><![CDATA[The 'oA Basic Control' represents a common basic interface for a Control Function. This allows to have vendor specific (executing) control functions using diverse object numbers to be interfaced for the basic functionality in a common way. The object provides the basic functionality required for an OpenAIS control object.]]></Description1>
		<ObjectID>3387</ObjectID>
		<ObjectURN>urn:oma:lwm2m:ext:3387</ObjectURN>
		<LWM2MVersion>1.1</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="4001">
				<Name>ObjectVersion</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[LWM2M Object versioning label.]]></Description>
			</Item>
			<Item ID="901">
				<Name>Documentary Description</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration>0..256 bytes</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Resource to hold a documentary text description of the object.]]></Description>
			</Item>
			<Item ID="800">
				<Name>Control Function Status</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0: disabled
				1: normal
				2: remote
				3: local
				255: non_responsive</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Current behaviour status of the control function:\n* **0: disabled** (The control function is disabled (see Object_Enabled). The control logic is still enabled and processes incoming events and updates the current state, but doesn't create outgoing events. No sensor or actuator mimicking actions are performed when in disabled mode.)\n* **1: normal** (The local control is fully operational, a superior control object can override the actual control using the northbound interface, but has no guarantee that a sensor or User Interface event overrides this without any warning.)\n* **2: remote** (No actuator actions related to sensor signals received via the eastbound interface are performed. The Remote state is e.g. used to allow the superior control to handle the actuators individually through the westbound interface, using its own algorithms.)\n* **3: local** (Set to local, a control object will no longer execute requests coming through the northbound interface. The sensor mimicking and the control status setting remains operational. This operating mode isolates the control object from superior control objects, and may be used e.g. to support troubleshooting and error triage handling.)\n* **255: non responsive** (internal failure)]]></Description>
			</Item>
			<Item ID="812">
				<Name>Ghost Status</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicates if the control function is in ghost mode. In ghost mode, no actuator commands are sent out, all operation is kept internal. The northbound interface with the mimicked sensor and status values are provided. This ghost status may be used e.g. as a backup control object that can jump in when the operational control object fails and stops setting the superseded status.]]></Description>
			</Item>
			<Item ID="801">
				<Name>Control Object Mode</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>1: normal
				2: remote
				3: local</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Configures the mode of this control function instance, see Control_Function_Status.]]></Description>
			</Item>
			<Item ID="802">
				<Name>Ghost Configuration</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration>0..65535</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Configures if this control function instance is in ghost mode (see Ghost_Status).]]></Description>
			</Item>
			<Item ID="813">
				<Name>Supersede Heartbeat Time</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description><![CDATA[The default time another active controller is assumed to take over (see Supersede_Heartbeat).]]></Description>
			</Item>
			<Item ID="803">
				<Name>Controlled Application Group ID</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration>0..65535</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Application Group ID of the controlled actuators.]]></Description>
			</Item>
			<Item ID="806">
				<Name>Push-Button Event Configuration</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Unsigned Integer</Type>
				<RangeEnumeration>0..255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Configures the reaction on button events: Bitwise: 0 (LSB): switch-on, 1: switch-off, 2: darker, 3: brighter, 4: save \n**Reaction on CLICK event:**\n* If the switch-on and switch-off bits are not set, the CLICK event is ignored.\n* If only the switch-on bit is set, the CLICK event always switches to On-State.\n* If only the switch-off bit is set, the CLICK event always switches to Off-State.\n* If the switch-on and switch-off bits are set, the CLICK event toggles between Off-State and On-State.\n\t\t\t\t\t\n**Reaction on HOLD event:**\n* If the darker and brighter bits are not set, the HOLD event is ignored.\n* If only the darker bit is set, the HOLD event always starts a dim darker process.\n* If only the brighter bit is set, the HOLD event always starts a dim brighter process.\n* If the darker and brighter bits are set, the HOLD event is allowed to toggle the dimming direction for each new dimming process.\n\t\t\t\t\t\n**Reaction on DOUBLE-CLICK event:**\n* If the save bit is not set, the DOUBLE-CLICK event is ignored.\n* If the save bit is set, the DOUBLE-CLICK event saves the current situation.]]></Description>
			</Item>
			<Item ID="807">
				<Name>Presence Sensor Event Configuration</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Unsigned Integer</Type>
				<RangeEnumeration>0..255</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Configures the reaction on presence sensor events: Bitwise: 0 (LSB): presence, 1: absence \n* The presence bit enables or disables activities based on the presence event.  \n *Note: This setting may allow to set the 'only off' behavior.*\n* The absence bit enables or disables activities based on the absence event.]]></Description>
			</Item>
			<Item ID="909">
				<Name>Executing Object</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Corelnk</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Link to the Object Instance in CoRE Link Format [RFC6690](https://tools.ietf.org/html/rfc6690)]]></Description>
			</Item>
			<Item ID="805">
				<Name>Supersede Heartbeat</Name>
				<Operations>W</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Unsigned Integer</Type>
				<RangeEnumeration>0..65535</RangeEnumeration>
				<Units>s</Units>
				<Description><![CDATA[This resource is used by active control functions (in the same application group) to take over the control functionality. By writing this resource, the IP address of the origin CoAP client is added to the Active-Controller-List.\nIf no heartbeat is received after the specified time the not sending controller is taken out of the active controller list again, if there are no more active controllers this controller takes over: This is a fall-back to normal or local operation, if superseding control fails/gets lost.\n\t\t\t\t\nIf the Ghost Configuration is TRUE, the Control Function Status changes to 'ghost' if a controller is active.\n\t\t\t\t\nIf operation mode is set to remote, it will be reset to normal if the active controller list gets empty.]]></Description>
			</Item>
			<Item ID="905">
				<Name>Debug Mode Enabled</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Enables the object test mode. The physical representation of the object will be disabled. Stimuli can be injected to test the object remotely using inject test event.]]></Description>
			</Item>
			<Item ID="906">
				<Name>Inject Test Event</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Unsigned Integer</Type>
				<RangeEnumeration>0..65535</RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Defines the test event to be injected into the system when debug mode is enabled. Event definition is vendor specific.]]></Description>
			</Item>
			<Item ID="934">
				<Name>Execute Inject</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[When executed, the test event defined in resource 906 will be injected]]></Description>
			</Item>
			<Item ID="924">
				<Name>Object Enabled</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Boolean</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource allows to disable an object instance. \nIf an object instance is disabled it does process incoming events but does not create outgoing events.]]></Description>
			</Item>
			<Item ID="811">
				<Name>Active-Controller-List</Name>
				<Operations>R</Operations>
				<MultipleInstances>Multiple</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[IP Address of the Controller that has taken over control. The list also contains the time the other control function is considered to be active. This time can be refreshed by periodic execution of the Ghost_Heartbeat.\n\t\t\t\t\nThis list only has entries if the control function is in ghost mode (see Control_Function_Status). When the Remaining_Active_Time of one entry expires, the entry is removed. If this results in an empty list, the Control_Function_Status changes the state to the mode configured in Control_Object_Mode if the state is not 'disabled'.]]></Description>
			</Item>
		</Resources>
		<Description2><![CDATA[]]></Description2>
	</Object>
</LWM2M>
