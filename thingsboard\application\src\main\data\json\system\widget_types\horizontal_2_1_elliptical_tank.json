{"fqn": "horizontal_2_1_elliptical_tank", "name": "Horizontal 2:1 elliptical tank", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_2_1_elliptical_tank_system_widget_image.png", "description": "Widget indicates the level of liquid in Horizontal 2:1 elliptical tank.", "descriptor": {"type": "latest", "sizeX": 4, "sizeY": 4, "resources": [], "templateHtml": "<tb-liquid-level-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-liquid-level-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.liquidLevelWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.liquidLevelWidget.update();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true\n    };\n};\n\nself.onDestroy = function() {\n}\n\nself.actionSources = function() {    \n    return {        \n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false        \n        }    \n    };\n}", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-liquid-level-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-liquid-level-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"return Math.floor(Math.random() * 101);\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"tankSelectionType\":\"static\",\"selectedShape\":\"Horizontal 2:1 Elliptical\",\"shapeAttributeName\":\"tankShape\",\"tankColor\":{\"type\":\"range\",\"color\":\"#242770\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E73535DE\"},{\"from\":20,\"to\":null,\"color\":\"#242770\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E73535DE';\\n  }\\n}\\nreturn '#242770';\"},\"datasourceUnits\":\"%\",\"layout\":\"percentage\",\"volumeSource\":\"static\",\"volumeConstant\":500,\"volumeAttributeName\":\"volume\",\"volumeUnits\":\"L\",\"volumeFont\":{\"family\":\"Roboto\",\"size\":14,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"volumeColor\":\"rgba(0, 0, 0, 0.18)\",\"units\":\"%\",\"widgetUnitsSource\":\"static\",\"widgetUnitsAttributeName\":\"units\",\"liquidColor\":{\"type\":\"range\",\"color\":\"#7A8BFF\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E27C7CDE\"},{\"from\":20,\"to\":null,\"color\":\"#7A8BFF\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FF0000DE\"},{\"from\":20,\"to\":null,\"color\":\"rgba(0,0,0,0.87)\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FF0000DE';\\n  }\\n}\\nreturn '#000000DE';\"},\"showBackgroundOverlay\":true,\"backgroundOverlayColor\":{\"type\":\"range\",\"color\":\"rgba(255, 255, 255, 0.76)\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FFEFEFDE\"},{\"from\":20,\"to\":null,\"color\":\"#FFFFFFC2\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FFEFEFDE';\\n  }\\n}\\nreturn '#FFFFFFC2';\"},\"showTooltip\":true,\"showTooltipLevel\":true,\"tooltipUnits\":\"%\",\"tooltipLevelDecimals\":0,\"tooltipLevelFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipLevelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.76)\",\"rangeList\":[],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"showTooltipDate\":true,\"tooltipDateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"tooltipDateFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipDateColor\":\"rgba(0, 0, 0, 0.76)\",\"tooltipBackgroundColor\":\"rgba(255, 255, 255, 0.76)\",\"tooltipBackgroundBlur\":3,\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Liquid level\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"titleFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"1.5\"},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"showTitleIcon\":false,\"titleIcon\":\"water_drop\",\"iconColor\":\"#5469FF\",\"decimals\":0,\"enableDataExport\":false,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\"}"}, "tags": ["reservoir", "container", "vessel", "storage unit", "cistern", "canister", "vat", "basin", "repository", "bin", "hopper"], "resources": [{"link": "/api/images/system/horizontal_2_1_elliptical_tank_system_widget_image.png", "title": "\"Horizontal 2:1 elliptical tank\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_2_1_elliptical_tank_system_widget_image.png", "publicResourceKey": "9maGo0kNN8zZDNGAtWDFRLE4DdtdSTyR", "mediaType": "image/png", "data": "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", "public": true}]}