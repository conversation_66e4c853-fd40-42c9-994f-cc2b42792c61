{"fqn": "noise_level_chart_card", "name": "Noise level chart card", "deprecated": false, "image": "tb-image;/api/images/system/noise_level_chart_card_system_widget_image.png", "description": "Displays a noise level data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'noise', label: 'Noise level', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'dB', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'noise', 'dB', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Noise level\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value;\\nif (!prevValue) {\\n    value = Math.random() * 120;\\n} else {\\n    value = prevValue + Math.random() * 40 - 20;\\n}\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 120) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"dB\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":70,\"color\":\"#FFA600\"},{\"from\":70,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value;\\nif (!prevValue) {\\n    value = Math.random() * 120;\\n} else {\\n    value = prevValue + Math.random() * 40 - 20;\\n}\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 120) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"dB\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 20 - 10;\\nif (value < -20) {\\n\\tvalue = -20;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"dB\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Noise level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"bar_chart\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "sound level", "acoustic level", "decibel level", "volume", "loudness", "ambient noise", "sound intensity", "acoustic intensity"], "resources": [{"link": "/api/images/system/noise_level_chart_card_system_widget_image.png", "title": "\"Noise level chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "noise_level_chart_card_system_widget_image.png", "publicResourceKey": "mprTBJgVvRTJnxpj0PHtN6CM5j2K9jg6", "mediaType": "image/png", "data": "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", "public": true}]}