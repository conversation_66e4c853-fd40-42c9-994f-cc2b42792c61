<?xml version="1.0" encoding="UTF-8"?>

<!--
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-XML_21-V2_0-20211123-A.xml
   Path: http://www.openmobilealliance.org/release/ObjLwM2M_OSCORE/

OMNA LwM2M Registry
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 21.xml

NORMATIVE INFORMATION

  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

LEGAL DISCLAIMER

  Copyright 2021 Open Mobile Alliance.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

  1. Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
  3. Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived
  from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.

  The above license is used as a license under copyright only.  Please
  reference the OMA IPR Policy for patent licensing terms:
  https://www.omaspecworks.org/about/intellectual-property-rights/

-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.openmobilealliance.org/tech/profiles/LWM2M-v1_1.xsd">
  <Object ObjectType="MODefinition">
		<Name>LWM2M OSCORE</Name>
		<Description1><![CDATA[This LwM2M Object provides the keying material and related information of a LwM2M Client appropriate to access a specified LwM2M Server using OSCORE. One Object Instance MAY address a LwM2M Bootstrap-Server. These LwM2M Object Resources MUST only be changed by a LwM2M Bootstrap-Server or Bootstrap from Smartcard and MUST NOT be accessible by any other LwM2M Server.
Instances of this Object are linked from Instances of Object 0 using the OSCORE Security Mode Resource of Object 0. Instances of this Object MUST NOT be linked from more than one Instance of Object 0.]]></Description1>
		<ObjectID>21</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:21:2.0</ObjectURN>
		<LWM2MVersion>1.1</LWM2MVersion>
		<ObjectVersion>2.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0"><Name>OSCORE Master Secret</Name>
				<Operations></Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource MUST be used to store the pre-shared key used in LwM2M Client and LwM2M Server/Bootstrap-Server, called the Master Secret.  ]]></Description>
			</Item>
			<Item ID="1"><Name>OSCORE Sender ID</Name>
				<Operations></Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource MUST store an OSCORE identifier for the LwM2M Client called the Sender ID. ]]></Description>
			</Item>
			<Item ID="2"><Name>OSCORE Recipient ID</Name>
				<Operations></Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource MUST store an OSCORE identifier for the LwM2M Client called the Recipient ID. ]]></Description>
			</Item>
			<Item ID="3"><Name>OSCORE AEAD Algorithm </Name>
				<Operations></Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource MUST be used to store the encoding of the AEAD Algorithm as defined in Table 10 of RFC 8152. The AEAD is used by OSCORE for encryption and integrity protection of CoAP message fields.  ]]></Description>
			</Item>
			<Item ID="4"><Name>OSCORE HMAC Algorithm </Name>
				<Operations></Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource MUST be used to store the encoding of the HMAC Algorithm used in the HKDF. The encoding of HMAC algorithms are defined in Table 7 of RFC 8152. The HKDF is used to derive the security context used by OSCORE. 
 ]]></Description>
			</Item>
			<Item ID="5"><Name>OSCORE Master Salt</Name>
				<Operations></Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource MUST be used to store a non-secret random value called the Master Salt. The Master Salt is used to derive the security context used by OSCORE. ]]></Description>
			</Item>
	  		<Item ID="6"><Name> OSCORE ID Context</Name>
				<Operations></Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource MUST be used to store an OSCORE identifier called ID Context. This identifier is used to identify the Common Context and derive the security context used by OSCORE.]]></Description>
			</Item></Resources>
			<Description2><![CDATA[]]></Description2>
	</Object>
</LWM2M>
