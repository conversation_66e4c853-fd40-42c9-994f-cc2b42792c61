{"fqn": "simple_flooding_level_chart_card", "name": "Simple flooding level chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_flooding_level_chart_card_system_widget_image.png", "description": "Displays historical flooding level values as a simplified chart. Optionally may display the corresponding latest flooding level value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'flooding', label: 'Flooding level', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'flooding', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Flooding level\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 2 - 1;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 5) {\\n\\tvalue = 5;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 2 - 1;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 5) {\\n\\tvalue = 5;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":1,\"color\":\"#234CC7\"},{\"from\":1,\"to\":3,\"color\":\"#F36900\"},{\"from\":3,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Flooding level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"flood\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":1,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"m\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "flood", "flooding", "water height", "flood depth", "flood stage", "inundation level", "water rise", "overflow level", "flood peak", "high water mark"], "resources": [{"link": "/api/images/system/simple_flooding_level_chart_card_system_widget_image.png", "title": "\"Simple flooding level chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_flooding_level_chart_card_system_widget_image.png", "publicResourceKey": "mHE8YmlWBYoBM2arJBuwub4NTi8TjH2F", "mediaType": "image/png", "data": "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", "public": true}]}