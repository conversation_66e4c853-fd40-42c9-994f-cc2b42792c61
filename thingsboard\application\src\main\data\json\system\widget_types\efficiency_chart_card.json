{"fqn": "efficiency_chart_card", "name": "Efficiency chart card", "deprecated": false, "image": "tb-image;/api/images/system/efficiency_chart_card.svg", "description": "Displays efficiency data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'efficiency', label: 'Efficiency', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: '%', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'efficiency', '%', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Efficiency\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 30;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"52px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":60,\"color\":\"#FFA600\"},{\"from\":60,\"to\":80,\"color\":\"#3FA71A\"},{\"from\":80,\"to\":null,\"color\":\"#305AD7\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 30;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#D12730\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0,0,0,0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#198038\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Efficiency\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"trending_up\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"margin\":\"0px\"}"}, "tags": ["productivity", "effectiveness", "performance", "capability"], "resources": [{"link": "/api/images/system/efficiency_chart_card.svg", "title": "efficiency_chart_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "efficiency_chart_card.svg", "publicResourceKey": "PLCkKSGEHxb4cZTC8QPrKoHs7VXGYBfw", "mediaType": "image/svg+xml", "data": "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", "public": true}]}