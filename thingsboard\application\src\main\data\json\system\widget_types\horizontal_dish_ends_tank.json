{"fqn": "horizontal_dish_ends_tank", "name": "Horizontal dish ends tank", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_dish_ends_tank_system_widget_image.png", "description": "Widget indicates the level of liquid in Horizontal dish ends tank.", "descriptor": {"type": "latest", "sizeX": 4, "sizeY": 4, "resources": [], "templateHtml": "<tb-liquid-level-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-liquid-level-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.liquidLevelWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.liquidLevelWidget.update();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true\n    };\n};\n\nself.onDestroy = function() {\n}\n\nself.actionSources = function() {    \n    return {        \n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false        \n        }    \n    };\n}", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-liquid-level-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-liquid-level-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"return Math.floor(Math.random() * 101);\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"tankSelectionType\":\"static\",\"selectedShape\":\"Horizontal Dish Ends\",\"shapeAttributeName\":\"tankShape\",\"tankColor\":{\"type\":\"range\",\"color\":\"#242770\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E73535DE\"},{\"from\":20,\"to\":null,\"color\":\"#242770\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E73535DE';\\n  }\\n}\\nreturn '#242770';\"},\"datasourceUnits\":\"%\",\"layout\":\"percentage\",\"volumeSource\":\"static\",\"volumeConstant\":500,\"volumeAttributeName\":\"volume\",\"volumeUnits\":\"L\",\"volumeFont\":{\"family\":\"Roboto\",\"size\":14,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"volumeColor\":\"rgba(0, 0, 0, 0.18)\",\"units\":\"%\",\"widgetUnitsSource\":\"static\",\"widgetUnitsAttributeName\":\"units\",\"liquidColor\":{\"type\":\"range\",\"color\":\"#7A8BFF\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E27C7CDE\"},{\"from\":20,\"to\":null,\"color\":\"#7A8BFF\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FF0000DE\"},{\"from\":20,\"to\":null,\"color\":\"rgba(0,0,0,0.87)\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FF0000DE';\\n  }\\n}\\nreturn '#000000DE';\"},\"showBackgroundOverlay\":true,\"backgroundOverlayColor\":{\"type\":\"range\",\"color\":\"rgba(255, 255, 255, 0.76)\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FFEFEFDE\"},{\"from\":20,\"to\":null,\"color\":\"#FFFFFFC2\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FFEFEFDE';\\n  }\\n}\\nreturn '#FFFFFFC2';\"},\"showTooltip\":true,\"showTooltipLevel\":true,\"tooltipUnits\":\"%\",\"tooltipLevelDecimals\":0,\"tooltipLevelFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipLevelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.76)\",\"rangeList\":[],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"showTooltipDate\":true,\"tooltipDateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"tooltipDateFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipDateColor\":\"rgba(0, 0, 0, 0.76)\",\"tooltipBackgroundColor\":\"rgba(255, 255, 255, 0.76)\",\"tooltipBackgroundBlur\":3,\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Liquid level\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"titleFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"1.5\"},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"showTitleIcon\":false,\"titleIcon\":\"water_drop\",\"iconColor\":\"#5469FF\",\"decimals\":0,\"enableDataExport\":false,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\"}"}, "tags": ["reservoir", "container", "vessel", "storage unit", "cistern", "canister", "vat", "basin", "repository", "bin", "hopper"], "resources": [{"link": "/api/images/system/horizontal_dish_ends_tank_system_widget_image.png", "title": "\"Horizontal dish ends tank\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_dish_ends_tank_system_widget_image.png", "publicResourceKey": "zE43W5tCWP33QAEkWqmJVNXmvvM2Uc2C", "mediaType": "image/png", "data": "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", "public": true}]}