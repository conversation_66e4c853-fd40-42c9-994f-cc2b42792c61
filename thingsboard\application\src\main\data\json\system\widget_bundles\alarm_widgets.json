{"widgetsBundle": {"alias": "alarm_widgets", "title": "Alarm widgets", "image": "data:image/png;base64,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", "description": "Visualization of alarms for devices, assets, and other entities.", "order": 3000, "externalId": null, "name": "Alarm widgets"}, "widgetTypeFqns": ["alarm_widgets.alarms_table", "alarm_count"]}