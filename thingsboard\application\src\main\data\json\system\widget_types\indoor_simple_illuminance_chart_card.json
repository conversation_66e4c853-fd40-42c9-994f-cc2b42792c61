{"fqn": "indoor_simple_illuminance_chart_card", "name": "Indoor simple illuminance chart card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_simple_illuminance_chart_card_system_widget_image.png", "description": "Displays historical indoor illuminance values as a simplified chart. Optionally may display the corresponding latest indoor illuminance value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'illuminance', label: 'Illuminance', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'illuminance', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Illuminance\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 400 - 200;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 400 - 200;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":100,\"color\":\"rgba(0, 0, 0, 0.76)\"},{\"from\":100,\"to\":300,\"color\":\"#FFA600\"},{\"from\":300,\"to\":500,\"color\":\"#F36900\"},{\"from\":500,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Illuminance\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:lightbulb-on\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"lx\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["environment", "illuminance", "indoor", "brightness", "luminance", "luminosity", "light", "light level", "light intensity", "lux", "candela", "foot-candle"], "resources": [{"link": "/api/images/system/indoor_simple_illuminance_chart_card_system_widget_image.png", "title": "\"Indoor simple illuminance chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_simple_illuminance_chart_card_system_widget_image.png", "publicResourceKey": "sUMG60pPiBLrHeM23CyhgXpEp69JiI91", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAApVBMVEUAAADg4ODf39/g4ODg4OD////g4ODzaQAhISHj4+NYWFg8PDx0dHSsrKzHx8eQkJDx8fH5tID+7N/0fCD3oWAvLy/+9u+6urr7x5+CgoJKSkrw8PDV1dX0chBlZWX2jkD2j0AWFhb3l0/82r+enp6dnZ3948/0cxChoaH6vZAICAj4q3AlJSX3oV9DQ0Pf39/Q0ND4qm/1hTD7z6+goKCSkpJCQkJ25ZykAAAABXRSTlMA7yC/r1EOHTEAAASuSURBVHja7M85AcAgEAAwjm9mx7/PVgYciYMUAAAAAACA59QW83a9/o/Y43oraulrJLBbmSOFyBKZIocROc3Hbt2stg4DYRhefWFGo7+NMLKMwZCCMSHt4pxz/5d2lLgUipUsHbnVi2E0ePVgG9wgtdUgtdUgtdUgtfULILHDkSpDEoDrn3WuDQ4gA6WxbTzj9ZUh17jevBh8ZugJpIZnV4acXIqX65withBrgc7my5oB1nDehnzdNuQ9o9iw6TzgjWHc1iJ0FwhwmYimvyhAtMJtqNALqV6Ch7h1s3BBi4IlUUEBEhSd0VEv4rFpH8g8UW5KTyB5BA2m7g4Z4ekMZ9ER20x6IxhiaIFoeDLYtANkXjq9Qj7G5RkkC5jMHaIBchi0yAoxhCjIeeqVIodNO0DSe3J3yJLS+xbSP4aI9vYbxLOnaK1lFNrj1UqX7Jhi6RuJgVnKEJ8t7guShx8VVM/evQyC00TTiBKEhUJfhmQFKbKfkNsmDBYi9ToIwrI4lGOPR3kubFxk7AU5zTOO1EPIxz+FI/UL/n4PVoPUVoPUVoPUVoPUVoPUVpUQZv8DIFYFIhLNx4Z4RSEa8zYSuSNDWMj59TSSOzBE0/B1jtQdFvKfHbPrVRQGwvDV5G2hfLVSkYga/MrJybnY3f//3xapdOthGzHmEEh4EuOMV/M4dKYasZOTBXk6V5EgJIeYnWYqwln0zWumIoJxemxJNBsRvo5TO3pzQY8EwUxE4oA1dLsvYdve4Y+nJ7KN4/6QCs9xLFjIzYMk6DthMDGRNMkZY7ngj2e7TO9bUESngJWc+qrrHxLRl6O2yYdSAz1KFkTx2nz3ll2XcZE3lklKfcqcvytyBFbUkMFgkmsT1ZIMGTB0HEXm8cpLskTuTOLcd2cJ+Zsi6l77ZydiYnVoXvo1kdhem9Zs7ZQ48PYltm1XT0HI8mDDXxTJuibUWMkbmkgCGdEFyF4TEbkNy8CzNLxwwZr6GwlWimQTmCE3XERCVUbkgMyeCUC3n+wdkVaRdPvmIRQ2PLPECYbBo10QiDM3SRIalYEiCoUyIhUKulPjt6m/ckQOqHQbSvKwdreBaMtIN6yk1/Gp+EWypuS7CLCq1f7DaUUBaKcjFTKSwC/ysQvJIWEsDxnbpERvqJTDROQV8i4iYVDaFZGdiGmQ/ILS/qugeMyjjTin9BZpOkykRkadSK0yecmAzCeiFa5AQT4itqUfxS9SQJERsRxQ+UToaAacB89lfAwR2SgURVFhX1z+uUE6Io/jtwKKgT8yLGOIFLBkpKW2Iiuo3tQyb6g0efCs5jFELquWCmr1pyv+C7B7pH7YI+3EqvDpOZVilIb494g9I0ezza+3RAOf7SeFK7KHukVH+g/b0rv3xhUxvbgqQMm2chOTI1K0Xgp76pOwMCYPo4tQpgDspY2rlXZEpEJtGvfRb8eTtTeKiIuUuhc/JR3Wjsn/Gx+HT9oxE5Hn7ZiJyO5pO2Yi8oxFZBGZC4vI1FhEpsYiMjUWkamxiPxt584JAIZhAAbazjN3L3+epZE6dwxEQKcRcpoduVrYMRqsg9Z6ZlQ2mAe9WRE199/lqAAAAAAAALjNB+/brAXegFpbAAAAAElFTkSuQmCC", "public": true}]}