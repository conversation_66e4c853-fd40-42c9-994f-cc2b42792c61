{"fqn": "horizontal_temperature_card", "name": "Horizontal temperature card", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_temperature_card_system_widget_image.png", "description": "Displays the latest temperature telemetry in a scalable horizontal layout.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"thermostat\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#234CC7\"},{\"from\":-20,\"to\":0,\"color\":\"#305AD7\"},{\"from\":0,\"to\":10,\"color\":\"#7191EF\"},{\"from\":10,\"to\":20,\"color\":\"#FFA600\"},{\"from\":20,\"to\":30,\"color\":\"#F36900\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#234CC7\"},{\"from\":-20,\"to\":0,\"color\":\"#305AD7\"},{\"from\":0,\"to\":10,\"color\":\"#7191EF\"},{\"from\":10,\"to\":20,\"color\":\"#FFA600\"},{\"from\":20,\"to\":30,\"color\":\"#F36900\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal temperature card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"°C\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["temperature", "weather", "environment"], "resources": [{"link": "/api/images/system/horizontal_temperature_card_system_widget_image.png", "title": "\"Horizontal temperature card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_temperature_card_system_widget_image.png", "publicResourceKey": "dqgV2tfkhFHJZetU1J60xAJrLjU7KSBQ", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAsVBMVEUAAADf39/u7u7x8fHr6+vl5eXf39/f39/r6+vq6ur////z8/MjTMc+Ys7IyMjj4+Pk6fitvOqRpePPz8/x8fGtra2QkJDCwsLy9Pv5+fnI0vHV1dW2trZYWFg9PT3n5+cwV8vt7e1aeNXW3fV1j9zb29tog9laedW8vLxmZma6x+5MbtGCgoIvLy88PDyfsOeDmt+enp50dHRKSkrU1NSRpuNMbtIhISHd3d0xV8qdnZ2QKY5sAAAACnRSTlMAIN/vz6AQML+/B+y1FwAAA4lJREFUeNrt2glzmkAYxnHTptfLsssNCgoqGPCsR8/v/8H6LjTVaqsZ66TGeX4ZDbDMZP+yDpPRFgAAAAAAAAAAAAAAAAAAAAAAAAAAAMBzenv3wr1tae8+GC+dd3/Xat29phvw+q71hm7Cm9Y93YRXrVd0ExBybRBybRBybRBybW47ZGiKPKL/IurY9pAalm13dofs8a8te2zRrr+EmOZIOLTVZT4d4cV0GXYm2Mgi9lEwk6salpmJSZ3BA81Jp0Is4TjZgLaU31UexYqUkiqWPG/eJk8SxbHnSUX+WilFkmJ+5vGzdYQYOLkQD7z9SYiPjim+RdTIzcgS+ppM+KSPI26MnhASmQPaIbtE3elaBvNpb9VdynUwl/F82qVeFfe+z+V0LgOfvvCunK6qJZ3roU5whLCITL0oooyfG6MRUWYT2ULYepJ65HTIMNsPUbOgmgYB9cgPeDdYrqpg5vV4bNrz+UATomgWrCo6lxCL+u+Ljr44Q72+OK1hi9FDVgfmxHjEfELI4jCkp5S3DVktp0uleM+bedVvIXp5ncuyomaBWTQWgthiO117MOK0SPD60obOU64IHS6tYF3Jx5BZtVZqXa10yLya+d7cl71qpkO+97pL+je5GNQrqLkO5sHbqENbJ0LMBzGgfd5Ol7dzwGse3va8f8PvcusyIUPBPtPfqYAuzBSsQ0yvKYcuEzIR2oKe0cBkHWLDjDuOhAyfHmKL2reIHvXpj/oG7Wi3+ZHSP7JM7tgJcfaXRvT4Eo/z/ETISDTG9ChpZs3T3Gx4tvqH2/puyr/aZTP92NcnuFQPGf12nXlOR/5zq3nlR2L0x3uNHvl8IiQXjc5uSOom7cRVUhKFRmj4sfwqwzRWYT8xEtclfdjw9YarSqOUxUaW7XM6JtsFntc59uHd/1NzxewTIQvRiHZDvrqJUci0KJqQhEI3LVJ3UxJXuXEd0ncNHbJJiiItSnLTM+7sIjO1vH7Tmw+ZMGmfowdMwcknQqKJ0BzahoSyKH0jCeM0JCpC3/C5IyzT0k0oMUpX1SHtRHIIH2uXYRGHCZ0T0hjwjm3qDYv2NQOZQ6dCKMozMbHpmOOTbBdS0QVYw+hvA9x3GHKGPh1l9Ol53fp/iC8RQq4NQq4NQq4NQq7N/e18PH0zXxho3d17xkv3/t0tfakGAAAAAAAAAAAAAAAAAAAAAAAAAAAA4Jn8AJ01hSMevW95AAAAAElFTkSuQmCC", "public": true}]}