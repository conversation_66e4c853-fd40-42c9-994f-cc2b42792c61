{"fqn": "temperature_card", "name": "Temperature card", "deprecated": false, "image": "tb-image;/api/images/system/temperature_card_system_widget_image.png", "description": "Displays the latest temperature telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'temperature', label: 'Temperature', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"thermostat\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#234CC7\"},{\"from\":-20,\"to\":0,\"color\":\"#305AD7\"},{\"from\":0,\"to\":10,\"color\":\"#7191EF\"},{\"from\":10,\"to\":20,\"color\":\"#FFA600\"},{\"from\":20,\"to\":30,\"color\":\"#F36900\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":52,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#234CC7\"},{\"from\":-20,\"to\":0,\"color\":\"#305AD7\"},{\"from\":0,\"to\":10,\"color\":\"#7191EF\"},{\"from\":10,\"to\":20,\"color\":\"#FFA600\"},{\"from\":20,\"to\":30,\"color\":\"#F36900\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Temperature card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"°C\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["temperature", "weather", "environment"], "resources": [{"link": "/api/images/system/temperature_card_system_widget_image.png", "title": "\"Temperature card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "temperature_card_system_widget_image.png", "publicResourceKey": "ODWstIWFtVF0DYNSyS4rAR0gsRixTjI7", "mediaType": "image/png", "data": "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", "public": true}]}