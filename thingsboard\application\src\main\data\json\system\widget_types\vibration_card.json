{"fqn": "vibration_card", "name": "Vibration card", "deprecated": false, "image": "tb-image;/api/images/system/vibration_card_system_widget_image.png", "description": "Displays the latest vibration telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'vibration', label: 'Vibration ', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Vibration\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"let factor = 1000;\\nif (prevValue < 1) {\\n    factor = 1;\\n} else if (prevValue < 10) {\\n    factor = 10;\\n} else if (prevValue < 100) {\\n    factor = 100;\\n}\\nlet value = prevValue + Math.random() * factor;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"vibration\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":0.1,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0.1,\"to\":1,\"color\":\"#FFA600\"},{\"from\":1,\"to\":10,\"color\":\"#F36900\"},{\"from\":10,\"to\":100,\"color\":\"#F04022\"},{\"from\":100,\"to\":1000,\"color\":\"#D81838\"},{\"from\":1000,\"to\":null,\"color\":\"#6F113A\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":28,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":0.1,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0.1,\"to\":1,\"color\":\"#FFA600\"},{\"from\":1,\"to\":10,\"color\":\"#F36900\"},{\"from\":10,\"to\":100,\"color\":\"#F04022\"},{\"from\":100,\"to\":1000,\"color\":\"#D81838\"},{\"from\":1000,\"to\":null,\"color\":\"#6F113A\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Vibration card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"m/s²\",\"decimals\":1,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "vibration", "tremor", "shake", "quiver", "jolt", "oscillation", "pulsation", "resonance"], "resources": [{"link": "/api/images/system/vibration_card_system_widget_image.png", "title": "\"Vibration card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "vibration_card_system_widget_image.png", "publicResourceKey": "d0XWCaXaxep6dWsDLaxcoXof71Iq6H7f", "mediaType": "image/png", "data": "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", "public": true}]}