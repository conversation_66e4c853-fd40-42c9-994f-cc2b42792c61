{"fqn": "pm10_card", "name": "PM10 card", "deprecated": false, "image": "tb-image;/api/images/system/pm10_card_system_widget_image.png", "description": "Displays the latest fine and coarse particulate matter (PM10) telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pm10', label: 'PM10', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM10\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"bubble_chart\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#80C32C\"},{\"from\":20,\"to\":50,\"color\":\"#FFA600\"},{\"from\":50,\"to\":150,\"color\":\"#F36900\"},{\"from\":150,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":32,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#80C32C\"},{\"from\":20,\"to\":50,\"color\":\"#FFA600\"},{\"from\":50,\"to\":150,\"color\":\"#F36900\"},{\"from\":150,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Indoor PM10 card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"µg/m³\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["environment", "particulate", "matter", "air", "pm10", "coarse particulates", "coarse particles", "particulate matter 10", "inhalable particles", "larger particulates", "dust", "airborne coarse particles"], "resources": [{"link": "/api/images/system/pm10_card_system_widget_image.png", "title": "\"PM10 card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "pm10_card_system_widget_image.png", "publicResourceKey": "WKp8PBA1nS3kKz9f2W0wS2rzKgsdd4eU", "mediaType": "image/png", "data": "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", "public": true}]}