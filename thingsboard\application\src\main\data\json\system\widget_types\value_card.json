{"fqn": "cards.value_card", "name": "Value card", "deprecated": false, "image": "tb-image;/api/images/system/value_card_system_widget_image.png", "description": "Displays a single entity attribute or the latest telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'temperature', label: 'Temperature', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"thermostat\",\"iconColor\":{\"type\":\"constant\",\"color\":\"#5469FF\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":52,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"valueColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Value card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"°C\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "resources": [{"link": "/api/images/system/value_card_system_widget_image.png", "title": "\"Value card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "value_card_system_widget_image.png", "publicResourceKey": "hElZIXmH3gZr0d59JGFQkNYXClz3ymkf", "mediaType": "image/png", "data": "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", "public": true}]}