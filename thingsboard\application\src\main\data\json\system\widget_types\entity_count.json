{"fqn": "entity_count", "name": "Entity count", "deprecated": false, "image": "tb-image;/api/images/system/entity_count_system_widget_image.png", "description": "Displays the number of entities based on the selected alias and optional filter. The number of entities refreshes periodically and not in real-time.", "descriptor": {"type": "latest", "sizeX": 3.5, "sizeY": 1.5, "resources": [], "templateHtml": "<tb-count-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\"\n    alarmElseEntity=\"false\">\n</tb-count-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.countWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.countWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '220px',\n        previewHeight: '100px',\n        embedTitlePanel: true,\n        hideDataSettings: true\n    };\n};\n\nself.actionSources = function() {\n    return {\n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false\n        }\n    };\n}\n\nself.onDestroy = function() {\n};\n", "settingsSchema": "", "dataKeySettingsSchema": "", "settingsDirective": "tb-entity-count-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-entity-count-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"count\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"return 150;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"],\"assignedToCurrentUser\":false,\"assigneeId\":null}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgb(255, 255, 255)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"showLabel\":true,\"label\":\"Devices\",\"labelFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.54)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":20,\"iconSizeUnit\":\"px\",\"icon\":\"devices\",\"iconColor\":{\"type\":\"constant\",\"color\":\"rgba(255, 255, 255, 1)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIconBackground\":true,\"iconBackgroundSize\":36,\"iconBackgroundSizeUnit\":\"px\",\"iconBackgroundColor\":{\"type\":\"constant\",\"color\":\"rgb(241, 141, 23)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":20,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"24px\"},\"valueColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showChevron\":false,\"chevronSize\":24,\"chevronSizeUnit\":\"px\",\"chevronColor\":\"rgba(0, 0, 0, 0.38)\",\"layout\":\"column\"},\"title\":\"Entity count\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"\",\"decimals\":null,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.54)\"}"}, "tags": ["total", "tally"], "resources": [{"link": "/api/images/system/entity_count_system_widget_image.png", "title": "\"Entity count\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "entity_count_system_widget_image.png", "publicResourceKey": "J9EfD64hqqBRkdvUVhDwZfaZbCGS2Ev6", "mediaType": "image/png", "data": "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", "public": true}]}