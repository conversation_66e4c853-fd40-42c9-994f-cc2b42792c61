<svg width="600" height="1e3" fill="none" version="1.1" viewBox="0 0 600 1e3" xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg">
 <tb:metadata><![CDATA[{
  "title": "Sand filter",
  "description": "Sand filter with configurable filtration mode option and various states.",
  "searchTags": [
    "filter",
    "sand"
  ],
  "widgetSizeX": 3,
  "widgetSizeY": 5,
  "stateRenderFunction": "var running = ctx.values.running;\nif (running) {\n    ctx.api.enable(ctx.tags.filterMode);\n} else {\n    ctx.api.disable(ctx.tags.filterMode);\n}",
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "var color = ctx.properties.sandFilterColor;\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "clickArea",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'click');"
        }
      }
    },
    {
      "tag": "filterMode",
      "stateRenderFunction": "var defaultBorderColor = ctx.properties.defaultBorderColor;\nvar activeBorderColor = ctx.properties.activeBorderColor;\nvar defaultLabelColor = ctx.properties.defaultLabelColor;\nvar activeLabelColor = ctx.properties.activeLabelColor;\nvar boxBackground = ctx.properties.modeBoxBackground;\n\nvar running = ctx.values.running;\nvar filtrationMode = ctx.values.filtrationMode;\n\nvar filtrationMap = {};\nvar bottomShift = 104;\nvar rightShift = 226;\nvar middleShift = 121;\n\nvar filterModeSet = element.remember('filterModeSet');\n\nvar i = 0;\nif (ctx.properties.filtrationMode) {\n    i++;\n    filtrationMap[i] = 'filter';\n}\nif (ctx.properties.wasteMode) {\n    i++;\n    filtrationMap[i] = 'waste';\n}\nif (ctx.properties.backwashMode) {\n    i++;\n    filtrationMap[i] = 'backwash';\n}\nif (ctx.properties.recirculateMode) {\n    i++;\n    filtrationMap[i] = 'recirculate';\n}\nif (ctx.properties.rinseMode) {\n    i++;\n    filtrationMap[i] = 'rinse';\n}\nif (ctx.properties.closedMode) {\n    i++;\n    filtrationMap[i] = 'closed';\n}\n\nif (!filterModeSet) {\n    element.remember('filterModeSet', true);\n    var clone = element.children()[0];\n    setFilterModeColors(clone);\n    element.clear();\n    \n    var filterMode = Object.values(filtrationMap);\n    var lastToMiddle = filterMode.length % 2;\n    \n    filterMode.forEach((mode, index, arr) => {\n        var template = clone.clone();\n        var x = (index % 2) * rightShift;\n        var y = Math.floor((index % filterMode.length) / 2) * bottomShift;\n        if (index === filterMode.length-1 && lastToMiddle) {\n            x = middleShift;\n        }\n        template.attr({'class': mode}).css('cursor', 'pointer').translate(x, y);\n        ctx.api.text(template.findOne('text'), capitalizeFirstLetter(mode));\n        template.click((event) => click(event, getFilterModeKey(mode)));\n        element.add(template);\n    })\n}\n\nif (isFinite(filtrationMode)) {\n    if (element.findOne('.active')) {\n        setFilterModeColors(element.findOne('.active'));\n    }\n    setFilterModeColorsByMap(filtrationMode, running);\n}\n\nfunction click(event, index) {\n    var filtrationMode = ctx.values.filtrationMode;\n    if (ctx.values.running && isFinite(filtrationMode)) {\n        ctx.api.disable(element.children());\n        var newValue = +index;\n        if (newValue === filtrationMode) {\n            newValue = 0;\n        } else {\n            setFilterModeColorsByMap(filtrationMode);\n        }\n        ctx.api.setValue('filtrationMode', newValue);\n        ctx.api.callAction(event, 'filtrationModeUpdateState', newValue, {\n            next: () => {\n                setFilterModeColorsByMap(newValue ? newValue: filtrationMode, newValue);\n                ctx.api.enable(element.children());\n            },\n            error: () => {\n                setFilterModeColorsByMap(newValue);\n                ctx.api.setValue('filtrationMode', filtrationMode);\n                ctx.api.enable(element.children());\n            }\n        });\n    }\n}\n\nfunction getFilterModeKey(value) {\n  return Object.keys(filtrationMap).find(key => filtrationMap[key] === value);\n}\n\nfunction setFilterModeColorsByMap(mode, active = false) {\n    var filterBox = element.findOne('g.'+filtrationMap[mode])\n    if (filterBox) {\n        return setFilterModeColors(filterBox, active);\n    }\n}\n\nfunction setFilterModeColors(filterBox, active = false) {\n    if (filterBox) {\n        if (active) {\n            filterBox.addClass('active');\n        } else {\n            filterBox.removeClass('active');\n        }\n        var borderColor = active ? activeBorderColor : defaultBorderColor;\n        var labelColor = active ? activeLabelColor : defaultLabelColor;\n        filterBox.children()[0].fill(boxBackground);\n        filterBox.children()[1].stroke(borderColor);\n        filterBox.children()[2].fill(borderColor);\n        ctx.api.font(filterBox.findOne('text'), null, labelColor);\n    }\n}\n\nfunction capitalizeFirstLetter(string) {\n    return string.charAt(0).toUpperCase() + string.slice(1);\n}",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "running",
      "name": "{i18n:scada.symbol.running}",
      "hint": "{i18n:scada.symbol.running-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.running}",
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "running"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "filtrationMode",
      "name": "{i18n:scada.symbol.filtration-mode}",
      "hint": "{i18n:scada.symbol.filtration-mode-hint}",
      "group": null,
      "type": "value",
      "valueType": "INTEGER",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": 0,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SERVER_SCOPE",
          "key": "filtrationMode"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "filtrationModeUpdateState",
      "name": "{i18n:scada.symbol.filtration-mode-update}",
      "hint": "{i18n:scada.symbol.filtration-mode-update-hint}",
      "group": null,
      "type": "action",
      "valueType": "INTEGER",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": {
        "action": "SET_ATTRIBUTE",
        "executeRpc": {
          "method": "setState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "setAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "filtrationMode"
        },
        "putTimeSeries": {
          "key": "state"
        },
        "valueToData": {
          "type": "VALUE",
          "constantValue": false,
          "valueToDataFunction": "/* Convert input boolean value to RPC parameters or attribute/time-series value */\nreturn value;"
        }
      },
      "defaultWidgetActionSettings": null
    },
    {
      "id": "click",
      "name": "{i18n:scada.symbol.on-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": null,
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    }
  ],
  "properties": [
    {
      "id": "filtrationMode",
      "name": "{i18n:scada.symbol.filter-mode}",
      "type": "switch",
      "default": true,
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "wasteMode",
      "name": "{i18n:scada.symbol.waste-mode}",
      "type": "switch",
      "default": true,
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "backwashMode",
      "name": "{i18n:scada.symbol.backwash-mode}",
      "type": "switch",
      "default": true,
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "recirculateMode",
      "name": "{i18n:scada.symbol.recirculate-mode}",
      "type": "switch",
      "default": true,
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "rinseMode",
      "name": "{i18n:scada.symbol.rinse-mode}",
      "type": "switch",
      "default": true,
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "closedMode",
      "name": "{i18n:scada.symbol.closed-mode}",
      "type": "switch",
      "default": true,
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "sandFilterColor",
      "name": "{i18n:scada.symbol.sand-filter-color}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "modeBoxBackground",
      "name": "{i18n:scada.symbol.mode-box-background}",
      "type": "color",
      "default": "#FFFFFF",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "activeBorderColor",
      "name": "{i18n:scada.symbol.border-color}",
      "type": "color",
      "default": "#198038",
      "required": null,
      "subLabel": "Active",
      "divider": true,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "column-xs",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "defaultBorderColor",
      "name": "{i18n:scada.symbol.border-color}",
      "type": "color",
      "default": "#0000001F",
      "required": null,
      "subLabel": "Default",
      "divider": false,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "activeLabelColor",
      "name": "{i18n:scada.symbol.label-color}",
      "type": "color",
      "default": "#000000C2",
      "required": null,
      "subLabel": "Active",
      "divider": true,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "column-xs",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "defaultLabelColor",
      "name": "{i18n:scada.symbol.label-color}",
      "type": "color",
      "default": "#00000061",
      "required": null,
      "subLabel": "Default",
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata>
 <path d="m0 424.62c0-157.62 201.79-157.62 201.79-157.62h200s198.21 0 198.21 157.62v382.47c0 60.751-49.249 109.91-110 109.91h-380c-60.751 0-110-49.157-110-109.91v-382.47z" fill="#fff" tb:tag="background"/>
 <path d="m0 424.62c0-157.62 201.79-157.62 201.79-157.62h200s198.21 0 198.21 157.62v382.47c0 60.751-49.249 109.91-110 109.91h-380c-60.751 0-110-49.157-110-109.91v-382.47z" fill="url(#paint0_linear_1830_321985)"/>
 <path d="m1.5 424.62c0-39.068 12.108-68.28 30.308-90.168 18.222-21.914 42.622-36.568 67.307-46.36 49.396-19.594 99.607-19.597 102.67-19.597h195.71c2.203 0 52.596 2e-3 102.39 19.599 24.884 9.793 49.534 24.449 67.956 46.364 18.4 21.889 30.656 51.099 30.656 90.162v382.47c0 59.921-48.575 108.41-108.5 108.41h-380c-59.924 0-108.5-48.487-108.5-108.41v-382.47z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <rect x="80" y="971" width="440" height="29" rx="7" fill="#fff"/>
 <rect x="80" y="971" width="440" height="29" rx="7" fill="url(#paint1_linear_1830_321985)"/>
 <rect x="81.5" y="972.5" width="437" height="26" rx="5.5" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m136.83 917h326.34l53.829 54h-434l53.829-54z" fill="#fff"/>
 <path d="m136.83 917h326.34l53.829 54h-434l53.829-54z" fill="url(#paint2_linear_1830_321985)"/>
 <path d="m86.613 969.5 50.839-51h325.1l50.839 51h-426.77z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m395 267c2.21 0 4-1.791 4-4l1e-3 -24c0-2.209-1.791-4-4-4h-189c-2.209 0-4 1.791-4 4v24c0 2.209 1.791 4 4 4h189z" fill="#647484"/>
 <path d="m395 267c2.21 0 4-1.791 4-4l1e-3 -24c0-2.209-1.791-4-4-4h-189c-2.209 0-4 1.791-4 4v24c0 2.209 1.791 4 4 4h189z" fill="url(#paint3_linear_1830_321985)"/>
 <path d="m395 265.5c1.381 0 2.5-1.119 2.5-2.5l1e-3 -24c0-1.381-1.119-2.5-2.5-2.5h-189c-1.381 0-2.5 1.119-2.5 2.5v24c0 1.381 1.119 2.5 2.5 2.5h189z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m395 167c2.21 0 4-1.791 4-4l1e-3 -24c0-2.209-1.791-4-4-4h-189c-2.209 0-4 1.791-4 4v24c0 2.209 1.791 4 4 4h189z" fill="#647484"/>
 <path d="m395 167c2.21 0 4-1.791 4-4l1e-3 -24c0-2.209-1.791-4-4-4h-189c-2.209 0-4 1.791-4 4v24c0 2.209 1.791 4 4 4h189z" fill="url(#paint4_linear_1830_321985)"/>
 <path d="m395 165.5c1.381 0 2.5-1.119 2.5-2.5l1e-3 -24c0-1.381-1.119-2.5-2.5-2.5h-189c-1.381 0-2.5 1.119-2.5 2.5v24c0 1.381 1.119 2.5 2.5 2.5h189z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m215 231c0 2.209 1.791 4 4 4h162c2.209 0 4-1.791 4-4v-60c0-2.209-1.791-4-4-4h-162c-2.209 0-4 1.791-4 4v60z" fill="#93979B"/>
 <path d="m215 231c0 2.209 1.791 4 4 4h162c2.209 0 4-1.791 4-4v-60c0-2.209-1.791-4-4-4h-162c-2.209 0-4 1.791-4 4v60z" fill="url(#paint5_linear_1830_321985)"/>
 <path d="m216.5 231c0 1.381 1.119 2.5 2.5 2.5h162c1.381 0 2.5-1.119 2.5-2.5v-60c0-1.381-1.119-2.5-2.5-2.5h-162c-1.381 0-2.5 1.119-2.5 2.5v60z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m215 131c0 2.209 1.791 4 4 4h162c2.209 0 4-1.791 4-4v-60c0-2.2091-1.791-4-4-4h-162c-2.209 0-4 1.7909-4 4v60z" fill="#93979B"/>
 <path d="m215 131c0 2.209 1.791 4 4 4h162c2.209 0 4-1.791 4-4v-60c0-2.2091-1.791-4-4-4h-162c-2.209 0-4 1.7909-4 4v60z" fill="url(#paint6_linear_1830_321985)"/>
 <path d="m216.5 131c0 1.381 1.119 2.5 2.5 2.5h162c1.381 0 2.5-1.119 2.5-2.5v-60c0-1.3807-1.119-2.5-2.5-2.5h-162c-1.381 0-2.5 1.1193-2.5 2.5v60z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m165 13c0 2.2091 1.791 4 4 4h35.18c4.771 0 9.435 1.4223 13.394 4.0852l43.638 29.35c4.401 2.9598 7.698 7.2939 9.375 12.325l0.501 1.5047c0.545 1.6334 2.073 2.7351 3.795 2.7351h49.617c2.209 0 4-1.7909 4-4v-24.5c0-8.8366-7.163-16-16-16h-36.182c-2.856 0-5.689-0.5097-8.365-1.5053l-52.406-19.489c-2.676-0.99551-5.509-1.5053-8.365-1.5053h-30.182c-6.627 0-12 5.3726-12 12v1z" fill="#93979B"/>
 <path d="m165 13c0 2.2091 1.791 4 4 4h35.18c4.771 0 9.435 1.4223 13.394 4.0852l43.638 29.35c4.401 2.9598 7.698 7.2939 9.375 12.325l0.501 1.5047c0.545 1.6334 2.073 2.7351 3.795 2.7351h49.617c2.209 0 4-1.7909 4-4v-24.5c0-8.8366-7.163-16-16-16h-36.182c-2.856 0-5.689-0.5097-8.365-1.5053l-52.406-19.489c-2.676-0.99551-5.509-1.5053-8.365-1.5053h-30.182c-6.627 0-12 5.3726-12 12v1z" fill="url(#paint7_linear_1830_321985)"/>
 <path d="m166.5 13c0 1.3807 1.119 2.5 2.5 2.5h35.18c5.07 0 10.024 1.5112 14.231 4.3405l43.638 29.35c4.676 3.1448 8.179 7.7498 9.961 13.096l0.501 1.5047c0.341 1.0208 1.296 1.7094 2.372 1.7094h49.617c1.381 0 2.5-1.1193 2.5-2.5v-24.5c0-8.0081-6.492-14.5-14.5-14.5h-36.182c-3.034 0-6.044-0.5416-8.888-1.5993l-52.405-19.49c-2.51-0.9333-5.166-1.4112-7.843-1.4112h-30.182c-5.799 0-10.5 4.701-10.5 10.5v1z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m247 167v6.857c0 3.945 3.198 7.143 7.143 7.143h6.714c3.945 0 7.143-3.198 7.143-7.143v-6.857h-21z" fill="url(#paint8_linear_1830_321985)"/>
 <path d="m248.5 168.5v5.357c0 3.117 2.526 5.643 5.643 5.643h6.714c3.117 0 5.643-2.526 5.643-5.643v-5.357h-18z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m332 167v6.857c0 3.945 3.198 7.143 7.143 7.143h6.714c3.945 0 7.143-3.198 7.143-7.143v-6.857h-21z" fill="url(#paint9_linear_1830_321985)"/>
 <path d="m333.5 168.5v5.357c0 3.117 2.526 5.643 5.643 5.643h6.714c3.117 0 5.643-2.526 5.643-5.643v-5.357h-18z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m586 131 1e-3 -60h-201v60h201z" fill="#93979B"/>
 <path d="m586 131 1e-3 -60h-201v60h201z" fill="url(#paint10_linear_1830_321985)"/>
 <path d="m584.5 129.5 1e-3 -57h-198v57h198z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <rect x="587.5" y="51.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/>
 <path d="m215 131 1e-3 -60h-201v60h201z" fill="#93979B"/>
 <path d="m215 131 1e-3 -60h-201v60h201z" fill="url(#paint11_linear_1830_321985)"/>
 <path d="m213.5 129.5 1e-3 -57h-198v57h198z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <rect x="1.5" y="51.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/>
 <rect x="587.5" y="252.5" width="11" height="97" rx="5.5" fill="#D9D9D9" stroke="#727171" stroke-width="3"/>
 <path d="m427 271h60v-40h-60v40z" fill="#93979B"/>
 <path d="m427 271h60v-40h-60v40z" fill="url(#paint12_linear_1830_321985)"/>
 <path d="m428.5 269.5h57v-37h-57v37z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m586 331v-60h-99v60h99z" fill="#93979B"/>
 <path d="m586 331v-60h-99v60h99z" fill="url(#paint13_linear_1830_321985)"/>
 <path d="m584.5 329.5v-57h-96v57h96z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m487 331s-25.431-1.431-42-18-18-42-18-42h60v60z" fill="#93979B"/>
 <path d="m487 331s-25.431-1.431-42-18-18-42-18-42h60v60z" fill="url(#paint14_linear_1830_321985)"/>
 <path d="m485.5 329.36c-0.659-0.071-1.507-0.176-2.512-0.325-2.591-0.386-6.221-1.07-10.359-2.262-8.297-2.391-18.531-6.795-26.568-14.833-8.038-8.037-12.442-18.271-14.833-26.568-1.192-4.138-1.876-7.768-2.262-10.359-0.149-1.005-0.254-1.853-0.325-2.512h56.859v56.859z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m427 231v-60h-42v60h42z" fill="#93979B"/>
 <path d="m427 231v-60h-42v60h42z" fill="url(#paint15_linear_1830_321985)"/>
 <path d="m425.5 229.5v-57h-39v57h39z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m427 171s25.431 1.431 42 18 18 42 18 42h-60v-60z" fill="#93979B"/>
 <path d="m427 171s25.431 1.431 42 18 18 42 18 42h-60v-60z" fill="url(#paint16_linear_1830_321985)"/>
 <path d="m428.5 172.64c0.659 0.071 1.507 0.176 2.512 0.325 2.591 0.386 6.221 1.07 10.359 2.262 8.297 2.391 18.531 6.795 26.568 14.833 8.038 8.037 12.442 18.271 14.833 26.568 1.192 4.138 1.876 7.768 2.262 10.359 0.149 1.005 0.254 1.853 0.325 2.512h-56.859v-56.859z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
 <path d="m201.79 0s-201.79 0-201.79 167.5v820.9c0 6.628 5.3726 11.601 12 11.601h576c6.6269 0 12-4.9729 12-11.601v-820.9c0-167.5-198.21-167.5-198.21-167.5h-101.79zm201.21 203c-3.866 0-7 3.134-7 7v751.01c0 3.866 3.134 7 7 7h43.999c3.866 0 7-3.134 7-7v-751.01c0-3.866-3.134-7-7-7z" fill="#000" fill-opacity="0" tb:tag="clickArea"/>
 <g tb:tag="filterMode">
  <g filter="url(#filter0_ii_1830_321985)">
   <path d="m74 470c0-6.627 5.3726-12 12-12h186c6.627 0 12 5.373 12 12v56c0 6.627-5.373 12-12 12h-186c-6.6274 0-12-5.373-12-12v-56z" fill="#fff"/>
   <path d="m75.5 470c0-5.799 4.701-10.5 10.5-10.5h186c5.799 0 10.5 4.701 10.5 10.5v56c0 5.799-4.701 10.5-10.5 10.5h-186c-5.799 0-10.5-4.701-10.5-10.5v-56z" stroke="#198038" stroke-width="3"/>
   <circle cx="179.5" cy="483" r="10" fill="#198038"/>
   <text x="178.02383" y="512.61719" fill="#000000" font-family="Roboto, sans-serif" font-size="28px" font-weight="500" text-anchor="middle" xml:space="preserve"><tspan dominant-baseline="middle">Filter</tspan></text>
  </g>
  <g filter="url(#filter1_ii_1830_321985)">
   <path d="m74 574c0-6.627 5.3726-12 12-12h186c6.627 0 12 5.373 12 12v56c0 6.627-5.373 12-12 12h-186c-6.6274 0-12-5.373-12-12v-56z" fill="#fff"/>
   <path d="m75.5 574c0-5.799 4.701-10.5 10.5-10.5h186c5.799 0 10.5 4.701 10.5 10.5v56c0 5.799-4.701 10.5-10.5 10.5h-186c-5.799 0-10.5-4.701-10.5-10.5v-56z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
   <circle cx="179.5" cy="587" r="10" fill="#000" fill-opacity=".12"/>
   <text x="179.43652" y="617.61719" fill="#727272" font-family="Roboto, sans-serif" font-size="28px" font-weight="500" text-anchor="middle" xml:space="preserve"><tspan dominant-baseline="middle">Backwash</tspan></text>
  </g>
  <g filter="url(#filter2_ii_1830_321985)">
   <path d="m74 678c0-6.627 5.3726-12 12-12h186c6.627 0 12 5.373 12 12v56c0 6.627-5.373 12-12 12h-186c-6.6274 0-12-5.373-12-12v-56z" fill="#fff"/>
   <path d="m75.5 678c0-5.799 4.701-10.5 10.5-10.5h186c5.799 0 10.5 4.701 10.5 10.5v56c0 5.799-4.701 10.5-10.5 10.5h-186c-5.799 0-10.5-4.701-10.5-10.5v-56z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
   <circle cx="179" cy="691" r="10" fill="#000" fill-opacity=".12"/>
   <text x="177.64941" y="722.61719" fill="#727272" font-family="Roboto, sans-serif" font-size="28px" font-weight="500" text-anchor="middle" xml:space="preserve"><tspan dominant-baseline="middle">Rinse</tspan></text>
  </g>
  <g filter="url(#filter3_ii_1830_321985)">
   <path d="m316 470c0-6.627 5.373-12 12-12h186c6.627 0 12 5.373 12 12v56c0 6.627-5.373 12-12 12h-186c-6.627 0-12-5.373-12-12v-56z" fill="#fff"/>
   <path d="m317.5 470c0-5.799 4.701-10.5 10.5-10.5h186c5.799 0 10.5 4.701 10.5 10.5v56c0 5.799-4.701 10.5-10.5 10.5h-186c-5.799 0-10.5-4.701-10.5-10.5v-56z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
   <circle cx="421" cy="483" r="10" fill="#000" fill-opacity=".12"/>
   <text x="420.99316" y="512.75586" fill="#727272" font-family="Roboto, sans-serif" font-size="28px" font-weight="500" text-anchor="middle" xml:space="preserve"><tspan dominant-baseline="middle">Waste</tspan></text>
  </g>
  <g filter="url(#filter4_ii_1830_321985)">
   <path d="m316 574c0-6.627 5.373-12 12-12h186c6.627 0 12 5.373 12 12v56c0 6.627-5.373 12-12 12h-186c-6.627 0-12-5.373-12-12v-56z" fill="#fff"/>
   <path d="m317.5 574c0-5.799 4.701-10.5 10.5-10.5h186c5.799 0 10.5 4.701 10.5 10.5v56c0 5.799-4.701 10.5-10.5 10.5h-186c-5.799 0-10.5-4.701-10.5-10.5v-56z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
   <circle cx="421.5" cy="587" r="10" fill="#000" fill-opacity=".12"/>
   <text x="420.14062" y="617.61719" fill="#727272" font-family="Roboto, sans-serif" font-size="28px" font-weight="500" text-anchor="middle" xml:space="preserve"><tspan dominant-baseline="middle">Recirculate</tspan></text>
  </g>
  <g filter="url(#filter5_ii_1830_321985)">
   <path d="m316 678c0-6.627 5.373-12 12-12h186c6.627 0 12 5.373 12 12v56c0 6.627-5.373 12-12 12h-186c-6.627 0-12-5.373-12-12v-56z" fill="#fff"/>
   <path d="m317.5 678c0-5.799 4.701-10.5 10.5-10.5h186c5.799 0 10.5 4.701 10.5 10.5v56c0 5.799-4.701 10.5-10.5 10.5h-186c-5.799 0-10.5-4.701-10.5-10.5v-56z" stroke="#000" stroke-opacity=".12" stroke-width="3"/>
   <circle cx="421.5" cy="691" r="10" fill="#000" fill-opacity=".12"/>
   <text x="421.45215" y="721.61719" fill="#727272" font-family="Roboto, sans-serif" font-size="28px" font-weight="500" text-anchor="middle" xml:space="preserve"><tspan dominant-baseline="middle">Closed</tspan></text>
  </g>
 </g>
 <defs>
  <filter id="filter0_ii_1830_321985" x="70" y="454" width="218" height="88" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
   <feFlood flood-opacity="0" result="BackgroundImageFix"/>
   <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="4" dy="4"/>
   <feGaussianBlur stdDeviation="3"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
   <feBlend in2="shape" result="effect1_innerShadow_1830_321985"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="-4" dy="-4"/>
   <feGaussianBlur stdDeviation="2"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
   <feBlend in2="effect1_innerShadow_1830_321985" result="effect2_innerShadow_1830_321985"/>
  </filter>
  <filter id="filter1_ii_1830_321985" x="70" y="558" width="218" height="88" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
   <feFlood flood-opacity="0" result="BackgroundImageFix"/>
   <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="4" dy="4"/>
   <feGaussianBlur stdDeviation="3"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
   <feBlend in2="shape" result="effect1_innerShadow_1830_321985"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="-4" dy="-4"/>
   <feGaussianBlur stdDeviation="2"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
   <feBlend in2="effect1_innerShadow_1830_321985" result="effect2_innerShadow_1830_321985"/>
  </filter>
  <filter id="filter2_ii_1830_321985" x="70" y="662" width="218" height="88" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
   <feFlood flood-opacity="0" result="BackgroundImageFix"/>
   <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="4" dy="4"/>
   <feGaussianBlur stdDeviation="3"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
   <feBlend in2="shape" result="effect1_innerShadow_1830_321985"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="-4" dy="-4"/>
   <feGaussianBlur stdDeviation="2"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
   <feBlend in2="effect1_innerShadow_1830_321985" result="effect2_innerShadow_1830_321985"/>
  </filter>
  <filter id="filter3_ii_1830_321985" x="312" y="454" width="218" height="88" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
   <feFlood flood-opacity="0" result="BackgroundImageFix"/>
   <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="4" dy="4"/>
   <feGaussianBlur stdDeviation="3"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
   <feBlend in2="shape" result="effect1_innerShadow_1830_321985"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="-4" dy="-4"/>
   <feGaussianBlur stdDeviation="2"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
   <feBlend in2="effect1_innerShadow_1830_321985" result="effect2_innerShadow_1830_321985"/>
  </filter>
  <filter id="filter4_ii_1830_321985" x="312" y="558" width="218" height="88" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
   <feFlood flood-opacity="0" result="BackgroundImageFix"/>
   <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="4" dy="4"/>
   <feGaussianBlur stdDeviation="3"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
   <feBlend in2="shape" result="effect1_innerShadow_1830_321985"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="-4" dy="-4"/>
   <feGaussianBlur stdDeviation="2"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
   <feBlend in2="effect1_innerShadow_1830_321985" result="effect2_innerShadow_1830_321985"/>
  </filter>
  <filter id="filter5_ii_1830_321985" x="312" y="662" width="218" height="88" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
   <feFlood flood-opacity="0" result="BackgroundImageFix"/>
   <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="4" dy="4"/>
   <feGaussianBlur stdDeviation="3"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
   <feBlend in2="shape" result="effect1_innerShadow_1830_321985"/>
   <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
   <feOffset dx="-4" dy="-4"/>
   <feGaussianBlur stdDeviation="2"/>
   <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
   <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
   <feBlend in2="effect1_innerShadow_1830_321985" result="effect2_innerShadow_1830_321985"/>
  </filter>
  <linearGradient id="paint0_linear_1830_321985" x1="600" x2=".1075" y1="594.29" y2="584.58" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint1_linear_1830_321985" x1="90" x2="97.344" y1="985.5" y2="1040.6" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" stop-opacity=".2" offset="0"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint2_linear_1830_321985" x1="517" x2="85.673" y1="944.34" y2="910.15" gradientUnits="userSpaceOnUse">
   <stop stop-color="#727171" offset="0"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".26388"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".41759"/>
   <stop stop-color="#fff" stop-opacity="0" offset=".49829"/>
   <stop stop-color="#727171" stop-opacity=".1" offset=".58094"/>
   <stop stop-color="#727171" stop-opacity=".35" offset=".71855"/>
   <stop stop-color="#727171" offset="1"/>
  </linearGradient>
  <linearGradient id="paint3_linear_1830_321985" x1="209.51" x2="389.39" y1="248.21" y2="254.63" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".2" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".18" offset=".090959"/>
   <stop stop-color="#020202" stop-opacity=".1" offset=".1"/>
   <stop stop-color="#020202" stop-opacity=".08" offset=".20513"/>
   <stop stop-color="#020202" stop-opacity=".05" offset=".21555"/>
   <stop stop-color="#020202" stop-opacity=".02" offset=".36962"/>
   <stop stop-color="#fff" stop-opacity=".01" offset=".37768"/>
   <stop stop-color="#fff" stop-opacity=".01" offset=".62413"/>
   <stop stop-color="#020202" stop-opacity=".02" offset=".6313"/>
   <stop stop-color="#020202" stop-opacity=".05" offset=".77601"/>
   <stop stop-color="#020202" stop-opacity=".08" offset=".7898"/>
   <stop stop-color="#020202" stop-opacity=".1" offset=".9"/>
   <stop stop-color="#020202" stop-opacity=".18" offset=".90846"/>
   <stop stop-color="#020202" stop-opacity=".2" offset="1"/>
  </linearGradient>
  <linearGradient id="paint4_linear_1830_321985" x1="209.51" x2="389.39" y1="148.21" y2="154.63" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".2" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".18" offset=".090959"/>
   <stop stop-color="#020202" stop-opacity=".1" offset=".1"/>
   <stop stop-color="#020202" stop-opacity=".08" offset=".20513"/>
   <stop stop-color="#020202" stop-opacity=".05" offset=".21555"/>
   <stop stop-color="#020202" stop-opacity=".02" offset=".36962"/>
   <stop stop-color="#fff" stop-opacity=".01" offset=".37768"/>
   <stop stop-color="#fff" stop-opacity=".01" offset=".62413"/>
   <stop stop-color="#020202" stop-opacity=".02" offset=".6313"/>
   <stop stop-color="#020202" stop-opacity=".05" offset=".77601"/>
   <stop stop-color="#020202" stop-opacity=".08" offset=".7898"/>
   <stop stop-color="#020202" stop-opacity=".1" offset=".9"/>
   <stop stop-color="#020202" stop-opacity=".18" offset=".90846"/>
   <stop stop-color="#020202" stop-opacity=".2" offset="1"/>
  </linearGradient>
  <linearGradient id="paint5_linear_1830_321985" x1="397.19" x2="202.97" y1="177.94" y2="183.78" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint6_linear_1830_321985" x1="397.19" x2="202.97" y1="77.935" y2="83.775" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint7_linear_1830_321985" x1="328" x2="165" y1="28.5" y2="28" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".14902"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39183"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".53726"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".70165"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".90734"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint8_linear_1830_321985" x1="247" x2="268" y1="170.64" y2="170.44" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint9_linear_1830_321985" x1="332" x2="353" y1="170.64" y2="170.44" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".18316"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".35085"/>
   <stop stop-color="#fff" stop-opacity=".25" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".08" offset=".64825"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".82318"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint10_linear_1830_321985" x1="417.32" x2="417.57" y1="66.697" y2="135.31" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint11_linear_1830_321985" x1="46.323" x2="46.569" y1="66.697" y2="135.31" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint12_linear_1830_321985" x1="491.3" x2="422.71" y1="237.43" y2="238.67" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint13_linear_1830_321985" x1="502.92" x2="503.42" y1="266.7" y2="335.3" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint14_linear_1830_321985" x1="440" x2="499.31" y1="317.5" y2="257.31" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint15_linear_1830_321985" x1="391.75" x2="392.93" y1="166.7" y2="235.29" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
  <linearGradient id="paint16_linear_1830_321985" x1="474" x2="414.69" y1="184.5" y2="244.69" gradientUnits="userSpaceOnUse">
   <stop stop-color="#020202" stop-opacity=".35" offset="0"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".23574"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".39937"/>
   <stop stop-color="#fff" stop-opacity=".3" offset=".49829"/>
   <stop stop-color="#fff" stop-opacity=".12" offset=".59721"/>
   <stop stop-color="#020202" stop-opacity=".12" offset=".76303"/>
   <stop stop-color="#020202" stop-opacity=".35" offset="1"/>
  </linearGradient>
 </defs>
</svg>
