{"fqn": "co2_card", "name": "CO2 card", "deprecated": false, "image": "tb-image;/api/images/system/co2_card_system_widget_image.png", "description": "Displays the latest CO2 level telemetry in a scalable rectangle card.", "descriptor": {"type": "latest", "sizeX": 3, "sizeY": 3, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'co2', label: 'CO2 level', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"CO2 level\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 160 - 80;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 400) {\\n\\tvalue = 400;\\n} else if (value > 1600) {\\n\\tvalue = 1600;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"square\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"co2\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":600,\"color\":\"#3FA71A\"},{\"from\":600,\"to\":1000,\"color\":\"#80C32C\"},{\"from\":1000,\"to\":1500,\"color\":\"#F36900\"},{\"from\":1500,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":null,\"to\":600,\"color\":\"#3FA71A\"},{\"from\":600,\"to\":1000,\"color\":\"#80C32C\"},{\"from\":1000,\"to\":1500,\"color\":\"#F36900\"},{\"from\":1500,\"to\":null,\"color\":\"#D81838\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"CO2 card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"ppm\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "co2", "carbon dioxide", "carbon emission", "carbon footprint", "carbon output", "carbon pollution", "carbon capture", "carbon offset", "carbon reduction", "carbon neutral", "climate gas", "fossil fuel emission", "carbon cycle"], "resources": [{"link": "/api/images/system/co2_card_system_widget_image.png", "title": "\"CO2 card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "co2_card_system_widget_image.png", "publicResourceKey": "MkRTjYHJgbUMx9o4gqAQy7CpulegfGRT", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAolBMVEXg4ODf39/g4ODg4OAAAAD////h4eGAwyyg0mC/4ZXv9+WQy0av2Xvz8/MhISGqqqr3+/LIyMiIxzm3t7eenp7f8Mr5+fmg0mHCwsJYWFiwsLA8PDysrKzb29vV1dXOzs7u7u7P6LCkpKTn5+fn9NjY7L3P6bDH5aMvLy+8vLx0dHSYzlOQkJC43YjQ6a+CgoKo1m5mZmZLS0vH5aK43Ym33Yg4UfnhAAAABXRSTlPvIL+vAC9A4IoAAAXcSURBVHja7NNha5xAEMZxr8mzepods6cXkRVJICV7dolR6Pf/ap0RLSm9BEKhtx7+3+zAIuyPuYtud98itfZudrfRLnrE6tPMuNG4gh75Z4WrKLoWiNoggbVBQmuDhNYGCa0NElobJLQ2SGhtkNDaIKH1bxA1f6yV/uNE6zXO1eRYCgjSWiJ7AtATUQ+gdHJKDSmcq64xFxBE2UG1ndPwVKicPAx5ns3qIPxY0eRwnWzFacOnomK5U72tT/Ad0NSstWMTKKRzmGqpWVhASWqBWFvwvkrSqAd2Np5MoBCLKTNDzITymCGGDDSVmhpFOagHxi5QiMOUmiAltTzaHgukIcnzOhoHRVIdJmR6eks53CAsK44OeL8RqXF1J6u51J+9irnsHtgfZDjir7QdzWm0ikXe9OTZ4U7GtDOEr1szaGhHBQucUX1+AUgaH7LsED8BWSzD/ty3NVEt7/aWXAm0VhrAFVbJtetlV6OoO6JaoRuw9N8gFXDcPzMk44EhgfcZRLpGyF3M/UCgfQp5qr6/g+yTJDnECLSPIPfxlEDkFIiUrA6Cnwn3lgIvy/D8VlXr28iZ7uKX19cjAu1LkGOapvhSGh/28HA5iITfmV/smMGK7SAMhle/3LsIQSIiAavoqu//gmPaOYcZZnVWzcIPaUP8hX64KYn4Rae/Hgk384fA0WCU9pTI/38G3pSCRaYMYD2yHLDKVv7uUrIAMBNdhTFErvswkVeTcO0/9NN4iRCf3KEnH22sApRsFRXrqiaEFchnKlj7EUYVrK6MBty74DsoTM+J9EBno0EUIBVvkQMxTkZPmJ14rlZOlEf+FrFsMZE5SWu4glrR4pM3ErWhCv8WqThKFSubxGEiM4nIW4TDLRI0cm3FgjxR5TmRGHG22RDkJZJ7so8scQ4rNWQTyXkQJl4ipx3DeldovYNiZx4RicwcO6vEXAYHHGOa3lBGUVZCHMKrqxLAjMoaYYQTIFY5TIpZ6gpqAikLeZs0lopPCDjU58g0hs/iKt2nyCc8P3zwxRbxxhbxxhbxxhbxxhbxxhbxxhbxxhbxxhbxxhbxxhbxxhbxxhbxxhbxxhb5YtcMdhuEYQB6imjsHKJIEWckUg4IVGnb///aHOLgtGJUPaC0Gr6Q13irX5wGVPXd4hR5t/jPIn7QOoAwzFp3BVvioVfPorZI3zZLGMtl35izinYLI/Fe1BYB13CMyWR84G/G5yZ1RbBpnIbe5F8HahoQ68xXGkw9XMm3VbtRV6SnOrs4uNCSs1gWcvy6iVdPiU9aUlUkUH02r3xZr6WBzxc2/Cp2JABNhtnbP5lG/RzSP/Ozh+NEpH4ZDNyIXLjPosqk1lBwo7rB0aTTJVMIB8eHyMCDI0UgrjjXOaYd1a6lTqljiX/SjIjkU2F6YMOMzO23TBwiIhVBWvku+YiIkY4tiuPdnzWo5yFW65mdcDkvE/ZIEcC4aMh7Yl8E70QQ+PRu97gpBt2RIgp4S9zUiyI6v9xYzt5iLAb68I6g473+ogif331ROAhL4qEi4nGxSgUkk9c+I1rJMc3ZzKGCCBWK5LGesz97Iu2GiBKRlbsKIlKAowJ4A0kFcvxKr553xNcRmZS8UeyAW8VC2vKwZm6IePlMCFcQufE68/EojyQ9Gzh+fxvFNhbAkHliI1xBRMf689M68A2SDUdOcJCuaO9EHLC/KdkR1xCxGG8hemqb4ikXtW6zIDjiKbLUke/kRk9u9afIXENE9dhwXCz3KIbU3TFyYSKCOY/ZCdcQUWBw0ejWE/mysJeEyPiVea3vipJHbBYew8IGccqDIWXQ4BgRCQtg91gJlyJLorBJ/GFfB0WRzfvRKXKKnCJLDMaEB+4+UmQrTpFT5CPiFHm3OEXeLU6R3/bupAhgIAZi4HgP/8Ih/FGGhuJSMxAC0RhCYwiNITSG0BhCYwiNITSG0BhCYwiNITSG0BhCYwiNITSG0BhC08kdobMGrKfvfXYqA+bTbyqndv9dVp0PmUfKf9rJDFUAAAAASUVORK5CYII=", "public": true}]}