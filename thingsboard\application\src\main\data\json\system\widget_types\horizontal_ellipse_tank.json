{"fqn": "horizontal_ellipse_tank", "name": "Horizontal ellipse tank", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_ellipse_tank_system_widget_image.png", "description": "Widget indicates the level of liquid in Horizontal ellipse tank.", "descriptor": {"type": "latest", "sizeX": 4, "sizeY": 4, "resources": [], "templateHtml": "<tb-liquid-level-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-liquid-level-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.liquidLevelWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.liquidLevelWidget.update();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true\n    };\n};\n\nself.onDestroy = function() {\n}\n\nself.actionSources = function() {    \n    return {        \n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false        \n        }    \n    };\n}", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-liquid-level-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-liquid-level-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"return Math.floor(Math.random() * 101);\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"tankSelectionType\":\"static\",\"selectedShape\":\"Horizontal Ellipse\",\"shapeAttributeName\":\"tankShape\",\"tankColor\":{\"type\":\"range\",\"color\":\"#242770\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E73535DE\"},{\"from\":20,\"to\":null,\"color\":\"#242770\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E73535DE';\\n  }\\n}\\nreturn '#242770';\"},\"datasourceUnits\":\"%\",\"layout\":\"percentage\",\"volumeSource\":\"static\",\"volumeConstant\":500,\"volumeAttributeName\":\"volume\",\"volumeUnits\":\"L\",\"volumeFont\":{\"family\":\"Roboto\",\"size\":14,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"volumeColor\":\"rgba(0, 0, 0, 0.18)\",\"units\":\"%\",\"widgetUnitsSource\":\"static\",\"widgetUnitsAttributeName\":\"units\",\"liquidColor\":{\"type\":\"range\",\"color\":\"#7A8BFF\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E27C7CDE\"},{\"from\":20,\"to\":null,\"color\":\"#7A8BFF\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FF0000DE\"},{\"from\":20,\"to\":null,\"color\":\"rgba(0,0,0,0.87)\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FF0000DE';\\n  }\\n}\\nreturn '#000000DE';\"},\"showBackgroundOverlay\":true,\"backgroundOverlayColor\":{\"type\":\"range\",\"color\":\"#FFFFFFC2\",\"rangeList\":[{\"from\":0,\"to\":20,\"color\":\"#FFEFEFDE\"},{\"from\":20,\"to\":null,\"color\":\"#FFFFFFC2\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FFEFEFDE';\\n  }\\n}\\nreturn '#FFFFFFC2';\"},\"showTooltip\":true,\"showTooltipLevel\":true,\"tooltipUnits\":\"%\",\"tooltipLevelDecimals\":0,\"tooltipLevelFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipLevelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.76)\",\"rangeList\":[],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"showTooltipDate\":true,\"tooltipDateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"tooltipDateFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipDateColor\":\"rgba(0, 0, 0, 0.76)\",\"tooltipBackgroundColor\":\"rgba(255, 255, 255, 0.76)\",\"tooltipBackgroundBlur\":3,\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Liquid level\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"titleFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"1.5\"},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"showTitleIcon\":false,\"titleIcon\":\"water_drop\",\"iconColor\":\"#5469FF\",\"decimals\":0,\"enableDataExport\":false,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\"}"}, "tags": ["reservoir", "container", "vessel", "storage unit", "cistern", "canister", "vat", "basin", "repository", "bin", "hopper"], "resources": [{"link": "/api/images/system/horizontal_ellipse_tank_system_widget_image.png", "title": "\"Horizontal ellipse tank\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_ellipse_tank_system_widget_image.png", "publicResourceKey": "8iPll3ppQZXEIy0GAnEEgQbuL1BHtydo", "mediaType": "image/png", "data": "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", "public": true}]}