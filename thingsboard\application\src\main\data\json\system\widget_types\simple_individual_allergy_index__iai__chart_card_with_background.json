{"fqn": "simple_individual_allergy_index_iai_chart_card_with_background2", "name": "Simple individual allergy index (IAI) chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/IAI-simple-chart-value-card-with-background.svg", "description": "Displays the concentration of airborne allergens, including pollen and mold spores, which can trigger allergic reactions in sensitive individuals as a simplified chart. Optionally may display the corresponding latest concentration of allergens value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'IAI_level', label: 'IAI', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'IAI_level', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Air Quality Index\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 12) {\\n\\tvalue = 12;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 10 - 5;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 12) {\\n\\tvalue = 12;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":2,\"color\":\"#3B911C\"},{\"from\":2,\"to\":6,\"color\":\"#7CC322\"},{\"from\":6,\"to\":9,\"color\":\"#F77410\"},{\"from\":9,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageUrl\":\"tb-image;/api/images/system/simple-IAI-value-and-chart-card-background.png\",\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"IAI\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:flower-pollen\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":null,\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "air", "aqi", "pollution", "emission", "smog"], "resources": [{"link": "/api/images/system/IAI-simple-chart-value-card-with-background.svg", "title": "IAI-simple-chart-value-card-with-background.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "IAI-simple-chart-value-card-with-background.svg", "publicResourceKey": "ES65x1GQISM2bCoRA0tlvNraRCLc0dTP", "mediaType": "image/svg+xml", "data": "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", "public": true}, {"link": "/api/images/system/simple-IAI-value-and-chart-card-background.png", "title": "simple-IAI-value-and-chart-card-background.png", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple-IAI-value-and-chart-card-background.png", "publicResourceKey": "yEfN67EqwwLqAM9Nzm3UYNQBmNEE8jIQ", "mediaType": "image/png", "data": "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", "public": true}]}