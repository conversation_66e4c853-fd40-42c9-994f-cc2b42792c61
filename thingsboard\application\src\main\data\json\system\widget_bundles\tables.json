{"widgetsBundle": {"alias": "tables", "title": "Tables", "image": "data:image/png;base64,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", "description": "Contains tables to display alarms, entities, and their telemetry.", "order": 4000, "externalId": null, "name": "Tables"}, "widgetTypeFqns": ["cards.entities_table", "alarm_widgets.alarms_table", "cards.timeseries_table", "control_widgets.persistent_table"]}