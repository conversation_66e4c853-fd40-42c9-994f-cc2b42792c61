{"fqn": "horizontal_cylinder_tank", "name": "Horizontal cylinder tank", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_cylinder_tank_system_widget_image.png", "description": "Widget indicates the level of liquid in Horizontal cylinder tank.", "descriptor": {"type": "latest", "sizeX": 4, "sizeY": 4, "resources": [], "templateHtml": "<tb-liquid-level-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-liquid-level-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.liquidLevelWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.liquidLevelWidget.update();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '250px',\n        previewHeight: '250px',\n        embedTitlePanel: true\n    };\n};\n\nself.onDestroy = function() {\n}\n\nself.actionSources = function() {    \n    return {        \n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false        \n        }    \n    };\n}", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "settingsDirective": "tb-liquid-level-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-liquid-level-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Random\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"return Math.floor(Math.random() * 101);\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"tankSelectionType\":\"static\",\"selectedShape\":\"Horizontal Cylinder\",\"shapeAttributeName\":\"tankShape\",\"tankColor\":{\"type\":\"range\",\"color\":\"#242770\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E73535DE\"},{\"from\":20,\"to\":null,\"color\":\"#242770\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E73535DE';\\n  }\\n}\\nreturn '#242770';\"},\"datasourceUnits\":\"%\",\"layout\":\"percentage\",\"volumeSource\":\"static\",\"volumeConstant\":500,\"volumeAttributeName\":\"volume\",\"volumeUnits\":\"L\",\"volumeFont\":{\"family\":\"Roboto\",\"size\":14,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"volumeColor\":\"rgba(0, 0, 0, 0.18)\",\"units\":\"%\",\"widgetUnitsSource\":\"static\",\"widgetUnitsAttributeName\":\"units\",\"liquidColor\":{\"type\":\"range\",\"color\":\"#7A8BFF\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#E27C7CDE\"},{\"from\":20,\"to\":null,\"color\":\"#7A8BFF\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":24,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"valueColor\":{\"type\":\"range\",\"color\":\"#000000DE\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FF0000DE\"},{\"from\":20,\"to\":null,\"color\":\"rgba(0,0,0,0.87)\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FF0000DE';\\n  }\\n}\\nreturn '#000000DE';\"},\"showBackgroundOverlay\":true,\"backgroundOverlayColor\":{\"type\":\"range\",\"color\":\"#FFFFFFC2\",\"rangeList\":[{\"from\":null,\"to\":20,\"color\":\"#FFEFEFDE\"},{\"from\":20,\"to\":null,\"color\":\"#FFFFFFC2\"}],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#FFEFEFDE';\\n  }\\n}\\nreturn '#FFFFFFC2';\"},\"showTooltip\":true,\"showTooltipLevel\":true,\"tooltipUnits\":\"%\",\"tooltipLevelDecimals\":0,\"tooltipLevelFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipLevelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.76)\",\"rangeList\":[],\"colorFunction\":\"var percent = value;\\nif (typeof percent !== undefined) {\\n  if (percent < 20) {\\n      return '#E27C7CDE';\\n  }\\n}\\nreturn '#7A8BFF';\"},\"showTooltipDate\":true,\"tooltipDateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"tooltipDateFont\":{\"family\":\"Roboto\",\"size\":13,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"100%\"},\"tooltipDateColor\":\"rgba(0, 0, 0, 0.76)\",\"tooltipBackgroundColor\":\"rgba(255, 255, 255, 0.76)\",\"tooltipBackgroundBlur\":3,\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Liquid level\",\"useDashboardTimewindow\":true,\"displayTimewindow\":true,\"configMode\":\"basic\",\"titleFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"1.5\"},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"showTitleIcon\":false,\"titleIcon\":\"water_drop\",\"iconColor\":\"#5469FF\",\"decimals\":0,\"enableDataExport\":false,\"enableFullscreen\":false,\"borderRadius\":\"0px\",\"actions\":{},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"dropShadow\":true,\"margin\":\"0px\",\"widgetStyle\":{},\"widgetCss\":\"\",\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"pageSize\":1024,\"noDataDisplayMessage\":\"\"}"}, "tags": ["reservoir", "container", "vessel", "storage unit", "cistern", "canister", "vat", "basin", "repository", "bin", "hopper"], "resources": [{"link": "/api/images/system/horizontal_cylinder_tank_system_widget_image.png", "title": "\"Horizontal cylinder tank\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_cylinder_tank_system_widget_image.png", "publicResourceKey": "3NOwcXemssFOlfbnXOyCpqKHIbzyC3SM", "mediaType": "image/png", "data": "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", "public": true}]}