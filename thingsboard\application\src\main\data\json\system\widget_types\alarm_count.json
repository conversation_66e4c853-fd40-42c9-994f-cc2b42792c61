{"fqn": "alarm_count", "name": "Alarm count", "deprecated": false, "image": "tb-image;/api/images/system/alarm_count_system_widget_image.png", "description": "Displays the number of alarms based on the selected filter.", "descriptor": {"type": "latest", "sizeX": 3.5, "sizeY": 1.5, "resources": [], "templateHtml": "<tb-count-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\"\n    alarmElseEntity=\"true\">\n</tb-count-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.countWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.countWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '220px',\n        previewHeight: '100px',\n        embedTitlePanel: true,\n        hideDataSettings: true\n    };\n};\n\nself.actionSources = function() {\n    return {\n        'cardClick': {\n            name: 'widget-action.card-click',\n            multiple: false\n        }\n    };\n}\n\nself.onDestroy = function() {\n};\n", "settingsSchema": "", "dataKeySettingsSchema": "", "settingsDirective": "tb-alarm-count-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-alarm-count-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"count\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = Number((prevValue + Math.random() * 4 - 2).toFixed(0));\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"],\"assignedToCurrentUser\":false,\"assigneeId\":null}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgb(255, 255, 255)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"showLabel\":true,\"label\":\"Total\",\"labelFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.54)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":20,\"iconSizeUnit\":\"px\",\"icon\":\"warning\",\"iconColor\":{\"type\":\"constant\",\"color\":\"rgba(255, 255, 255, 1)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIconBackground\":true,\"iconBackgroundSize\":36,\"iconBackgroundSizeUnit\":\"px\",\"iconBackgroundColor\":{\"type\":\"range\",\"color\":\"rgba(0, 105, 92, 1)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"rgba(0, 105, 92, 1)\"},{\"from\":1,\"to\":null,\"color\":\"rgba(209, 39, 48, 1)\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"family\":\"Roboto\",\"size\":20,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"24px\"},\"valueColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showChevron\":false,\"chevronSize\":24,\"chevronSizeUnit\":\"px\",\"chevronColor\":\"rgba(0, 0, 0, 0.38)\",\"layout\":\"column\"},\"title\":\"Alarm count\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"\",\"decimals\":null,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null},\"titleColor\":\"rgba(0, 0, 0, 0.54)\"}"}, "tags": ["alert", "alerts"], "resources": [{"link": "/api/images/system/alarm_count_system_widget_image.png", "title": "\"Alarm count\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "alarm_count_system_widget_image.png", "publicResourceKey": "51rgho5bPH25oMUEo8dEAZdzhv5iPaar", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAolBMVEXg4ODf39/g4OAAAADg4ODf39/////g4OD8/Pz+/v7RJzD29vb6+vr7+/v09PTt7e3z8/P4+Pjx8fHv7+/ok5fXQkqXl5chISHk5OTIyMi6urqpqamgoKDDw8P55OXurrI8PDzc3NyxsbF0dHRYWFjlhYrUND3V1dXroKT88fLR0dHzycvwu76QkJDieH7eY2pKSkovLy/219jieH1mZmbTND12Tj45AAAABnRSTlPvIL8Ar7DvmsykAAAEx0lEQVR42u2aiXLaMBBATdMuUmU5yEBJgEDanL3v//+1rpDI2khA087U63RfjGRJa4k3a5sMpjgZPCuGfef54KQYFGMFPUfVqPG8hifAGE+r3ufDo4piCE+CoYgwQ0S4ISLcEBFuiAg3RIQbIsINEeGGiHBDRLghItwQEW6ICDdEhBsiwg0R4YaIcENEuCEi3BARbogIN0SEGyLCDRHhhohwQ0S48X+IaDiKBibsiGit/hit/i06J0IatkRsaWPZxoZR3OIgheKYjUHbkoLj/jawOWOjsDQ3rUfTUXcIQ5c9IqhROlPlMf5lwm6saYx2KC4dJSg6BhsKzMZTBK3hSqV0XsQ6p9ic9EexximtMyJaOdMfDURjTnIiyrqe/d5fOauyIgb6ha4wJRmRsoKekRXRyvVPxKUiGkVGEFnfvswwXQMzRiiik4xYEpm+zHILDS4Wy8UFeE7jtfUK/i15Ea2seRB5uQeI0Bs/ew1mcWGw7kKkNjYrUj9aZP7m4o1Zzg1W512IOKVSEZUVufm+XyRu89dwOl/OmWQEdFbkHuD+gMgSNy+yNK+7EcllxLpUZEqXflbk3QIv+HdLc7FYvOlCxFkNuiWitc5dIx8AeU8iKSa8DHRBnd61NOREPoXm5wcRZmBGVJIRlRFZAawxKatbriK/efud+AtkisWkRyKZUwsVosjbKVMR91sZWQMyuW9c77vz1NAVlBGAY3etT9Dgcypy/mU2uz6DzsiLQCqyAs/NDXhWicjZ7Mt88fUaOoJOrWMZmQDEa2TDZFfk7uoO4GoGHUGf7PpIRlbQ4m32Yr+8hI4gETgi8g1arFKR8+uvl3fQFXT7PfKBOJ00uZlmRK5+zD5CJ1BGNBw+tfLALlezGjrgkZ8jKe18nAF8nGHRDfTf79+KzK7Pz7u+2J2Fg9fIbd5jCk2Wlyw+EA/etdbTnMfPNbQ56/5fFLVHpGf4jMDTEMl+r+VGvXqqsO8LOq1c1bPHCuWe736t6VdKdGXKVkbo3KpGJfQFbUeVs9mMqBJNxuPT8SnRbKS940MhcaOOXCgu9whoYTyw3nikIt7EOlONPNWorkeBUFe176lDGxuhDANYhMjQsymo8qMRf1gaQathSZ3bmCqO163p8LlufKybnlooosrSObMX95iBdOR4jHtEqEOPRIRyosLjfB/msLKhahYIFiECxzF486IOfPliU1Fo+ENiUSI0l2/EJeKiYeUQZ2NJK/mmRQ0EMiJqg03xevinwm4Thd1xI9R2YBtfKhqIsc3jFM2llI8uYwD2UMAGmlQhwSMRQVSfCBqpCOgNyuuEDaEq9oYOKg9BR+5O125ovTtCA1TRHKEH0EOnIqTixx8ARaXC2pcB5RsQ+o6zEwR4dLuNTZoqzrpdAFqRcS6gfJAIETUASIl2qBGrGA5JCNAOJKPUBiROQiHxXcBma8ydLpKIJMSc6WYrzkz7YY94eAeNYJonjEWzzHK0FwV20Nsh/T//FLBPiAg3RIQbIsINEeGGiHBDRLghItwQEW6ICDdEhBsiwg0R4YaIcENEuCEi3BARbogIN0SEGyLCDRHhhohwQ0S4ISLcGBZFz37Zn0cNi2djeALUL4pBUfc+J2qMGieD58O+UzwbnPwC9rQ9R9RHLAMAAAAASUVORK5CYII=", "public": true}]}