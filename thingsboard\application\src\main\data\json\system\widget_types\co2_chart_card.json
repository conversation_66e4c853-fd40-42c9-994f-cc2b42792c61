{"fqn": "co2_chart_card", "name": "CO2 chart card", "deprecated": false, "image": "tb-image;/api/images/system/co2_chart_card_system_widget_image.png", "description": "Displays a CO2 level data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'co2', label: 'CO2 level', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'ppm', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'co2', 'ppm', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"CO2 level\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 160 - 80;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 400) {\\n\\tvalue = 400;\\n} else if (value > 1600) {\\n\\tvalue = 1600;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"ppm\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":600,\"color\":\"#3FA71A\"},{\"from\":600,\"to\":1000,\"color\":\"#80C32C\"},{\"from\":1000,\"to\":1500,\"color\":\"#F36900\"},{\"from\":1500,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 160 - 80;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 400) {\\n\\tvalue = 400;\\n} else if (value > 1600) {\\n\\tvalue = 1600;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"ppm\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"ppm\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"CO2 level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"co2\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "co2", "carbon dioxide", "carbon emission", "carbon footprint", "carbon output", "carbon pollution", "carbon capture", "carbon offset", "carbon reduction", "carbon neutral", "climate gas", "fossil fuel emission", "carbon cycle"], "resources": [{"link": "/api/images/system/co2_chart_card_system_widget_image.png", "title": "\"CO2 chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "co2_chart_card_system_widget_image.png", "publicResourceKey": "yLbXPg1sN6Sz924VVp29CrOgduQmHtwA", "mediaType": "image/png", "data": "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", "public": true}]}