{"fqn": "simple_noise_level_chart_card", "name": "Simple noise level chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_noise_level_chart_card_system_widget_image.png", "description": "Displays historical noise level values as a simplified chart. Optionally may display the corresponding latest noise level value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'noise', label: 'Noise level', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'noise', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Noise level\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value;\\nif (!prevValue) {\\n    value = Math.random() * 120;\\n} else {\\n    value = prevValue + Math.random() * 40 - 20;\\n}\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 120) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value;\\nif (!prevValue) {\\n    value = Math.random() * 120;\\n} else {\\n    value = prevValue + Math.random() * 40 - 20;\\n}\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 120) {\\n\\tvalue = 0;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":50,\"color\":\"#80C32C\"},{\"from\":50,\"to\":70,\"color\":\"#FFA600\"},{\"from\":70,\"to\":90,\"color\":\"#F36900\"},{\"from\":90,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Noise level\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"bar_chart\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"dB\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "sound level", "acoustic level", "decibel level", "volume", "loudness", "ambient noise", "sound intensity", "acoustic intensity"], "resources": [{"link": "/api/images/system/simple_noise_level_chart_card_system_widget_image.png", "title": "\"Simple noise level chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_noise_level_chart_card_system_widget_image.png", "publicResourceKey": "tLrzTHAMsW3nvmUp9XVCove83vGJ5m5L", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAAAhFBMVEUAAADf39/g4ODf39/f39/g4OD////k5OSAwyzg4OAhISE8PDyQkJDHx8fy8vJ0dHSv2Xvv9+UvLy9YWFienp6Qy0asrKyg0mCIxzm6urqrq6v3+/JmZmaCgoLV1dXP6bDf8MrH5aPA4ZaYzlO43YhLS0vn9NhKSkrX7L2w2Xuo1m729vZb/Cn5AAAABnRSTlMAIL9AEN/GQiaNAAAEq0lEQVR42uzPwQ2AIBAAsBMQcx8WYf/9TJzAJ5B2gwYAAAAAAMB/7dpc+xq15LO5vGvElbNvb2SNMvoBZonsR0iRxYisRuRlt25WbQdhMAzPvkBiIqgUFOusrPu/w2Olm7N/OtzsZcGXSpN29ExKZ2tBZmtBZmtBZmtBZusXIUa4iwx3vQuSvQHNE65qwvc2xl3icNffQVz+snEFlPX/28dA2H+FsGJA1CUCUgbymCy57YJci7nWD4GSo3dDyL5DZBc7IS54EYN4OHFjetWQPiCHeNkRHDIThVhZ3wshdj8gFlyHGBf06YTUCnO0MXXcBcl92YLtEX5ciH4+CAon1m4BYj0hTUJtcCwS+IKcizApmxQcQSTEKSASP0MQAytx7oM/IWTqublAvQuSuI0lVCYclXozQqhD8Dpa4XJCZEdmVXak7oJY2McyvnEuZNrLbJB0jkkU9OKQgLijyZhKvxX0spxnPFMpAPbAkRAT7vsLiG0NmyJnPKb1rzVbCzJbCzJbCzJbCzJbCzJbCzJbE0Isb9qeD9HIZ7I9G2KRD5dJS2ShB0NMOF1jEaHnQjwrPqJwPBbyjz2zW04WBsLw2WYnIcHwI6loRaFWvf8L/Db5JAyFItYplRmeAwzGcfLM7pvI+M4DaJA8mKtIKFq3caTmKcJ42LqXfD1PkZAzaBGLuYiodbJuFi98QZqSzEMkiDixvakkXMIXYvFqIuotSWS3lUKp3usDY2MT0i3Ja4kkESdE0n7ztr0yweNgF/NYQYdYqGdFdA6eXHemV9nxkRTHUim5bR0MTGzrUfLJebyBHiTfPiWiK4OYHv+7HFJEc/nikuIKoEotZba/EwUe1IMEPGHExtSSfzQfU+xBEZ2SBqlkQBxJg8Zl3hVZocNP9qNE2BxxULPmyciujMJ3+y0ytP0p6Ga8yAXTwhmcSQrxmsMe8dgrorUurn6ylzWX3fAyIWAcLOQ8EoIuYbIOthG16WgRpFUSBiuwCprGGWZQcypyLwIWg1doGNpD41s/qS1nMBa22YVhIMGhNoLHb+PD7kUqLF1Q0NQaKVIFWiJpY9n/75eHiShhoOQHT+DHkIp4e0DkjHj2pXCFsWiblxLRi7juOwx1Fms3ikVIIJ5Q+RwrcjiubOd3RS6IJ5quRVaEGSoIiBhasCQIJDzNWBHbP6XuiLiUd3Ytsx96xthAH1OJnPcUg1R3RHLXRo1IURRnejkM/qSdgKGMaBf2a1tEI+6BaIU9Q/Nt1EUIfUwpQutMbxcXCvhW5GAt+xnYZn9dxC+WusotMncZL/2e3BG52M/0sht47p6gIsbl/IS4cqGoyM0f3hUa7fQakcKQZR/yY8BjChFapslKdGGHCjHLjB37cyTz58gNc4Iuajd0XEwiAvsUiUyD5ejHzcleXb2Ile7zkILvFAwyRdhPReHbPi+KVpbtVK5zoAtBgz8vhxd5jr8vx6+J7Pjn3XLMQySAu8xC5D6LyCIyFxaRV2MReTUWkVdjEXk1FpF/7dwxDcAwDABB11YjZSmR8ucXChkT647BE/jTCDlNo5BssA4a48uo2WAe9M8not77t1RZnUZhAAAAAAAAWxauHfe/D+cw4gAAAABJRU5ErkJggg==", "public": true}]}