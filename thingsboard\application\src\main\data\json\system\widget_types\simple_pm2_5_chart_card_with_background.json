{"fqn": "simple_pm2_5_chart_card_with_background", "name": "Simple PM2.5 chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_pm2_5_chart_card_with_background_system_widget_image.png", "description": "Displays historical fine particulate matter (PM2.5) values as a simplified chart with background. Optionally may display the corresponding latest PM2.5 value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pm2.5', label: 'PM2.5', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'pm2.5', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM2.5\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 120 - 60;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 120 - 60;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#7CC322\"},{\"from\":10,\"to\":35,\"color\":\"#F89E0D\"},{\"from\":35,\"to\":75,\"color\":\"#F77410\"},{\"from\":75,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_pm2_5_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"PM2.5\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"bubble_chart\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"µg/m³\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["environment", "pm2.5", "particulate", "matter", "air", "fine particulates", "fine particles", "particulate matter 2.5", "airborne particles", "microscopic particles", "respirable particles", "aerosol particles", "dust particles"], "resources": [{"link": "/api/images/system/simple_pm2_5_chart_card_with_background_system_widget_background.png", "title": "\"Simple PM2.5 chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_pm2_5_chart_card_with_background_system_widget_background.png", "publicResourceKey": "TCFew1J4tXdWGGxjNTwgtw7EszDm9aOb", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_pm2_5_chart_card_with_background_system_widget_image.png", "title": "\"Simple PM2.5 chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_pm2_5_chart_card_with_background_system_widget_image.png", "publicResourceKey": "NIhKNrvrC81i10rozLvnSIMsX8RvN5dI", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAMgAAACgCAMAAAB+IdObAAABqlBMVEUAAADn5+cdHiJpamxGR0ne3t6xsbOYmJtnaWyFh4nf4ufi4uLg4OJgYGTf398yOTnj4+bj4+YcHSAbHSAYHBz6+vv4+fng4eLk5ebj4+Tp6uvP0NDJysro6Onm5ufOzs/r6+zS09PV1tbe39/Mzc3U1NXi4uPg4OHLy8zY2dnR0dLa2tvX19jd3d58wyLb3Nzs7O329vcfICLx8fErLS81NjkpKi0uLzEvMTT09fUlJinz8/Tv7/Dt7u46Oz0ZGh0nKCuio6U3OTsjJCfHyMk+P0IyNDacnZ+nqKpWV1g7PD+ampxISUu1tbccHBwhIiUVFhqfoKIcHSFlZmhiZGVMTU9DREfL5apycnNdXmF4eXtAQkXy8vPAwMGWl5mPkJKMjY5PUFPq89+JioykpaeTlJaFhol0dXdaW15FR0mBgoWsra9gYWMQEBTDxMS9vr9YWVu6u7yxsrR+gIGLyj2a0FdSU1a73o98fX9JS01wcHJsbXCDxy/y9u23uLna7MSqqqyur7HD4pyr2HPi8NKj1GWz2oHj7NiTzUnb7cWp1XHT57ro7OLs8eeDIQJdAAAAFXRSTlMAICD+ICAgICAgIEC/QBAQ37+/v0Cx7+2MAAAYcklEQVR42uyUbWvaUBiGMzJr3dbtY19YS0s3GWxjv+HAJCc7+kGqoilLInk1kZImEBIFMf9890kq6WBqy7Zv5xJfQTgX1/McSSAQCAQCgUAgEAgEAoFAIBAIBAKB4Om8bWzl1QMv/gWNxsFBs9lsHR62Wk1wsJ3mY1ot/OUPvNzwTuK8fnO8hXa7ffrAyYaLDee/c7aLy8vLs7OL9udP97SvOKamBYbBCFnHvdWqF8e9EnyO4yJcr9dhSG3SVxRW4RhWoHmL5djzxsulLN/Mwc1gMvFVNU2nw6kaDY4kqXH8Pz1qzi+uP971KFEcS/Myy2D9PoVCXBR45e/8CzQoxdMmEKlgOhfJPM/TNA2vixvOADoTP1LTYTcfptFkciTt7wH2eOyOwYHHMYKE9oOIa+qM0bAIKQ25AfeJ4UGpTQGCVCKM6bphuUEGiyzLNJSRZRkm8mASwWOaz4bTNIp8WXpSkOf3qB0qj/PTb1cIYhPdCDwvCyzD0G2c2LYxYHyiwqIoyhQEvyFICUQQxHSDJMuSIEiSTMN0LbnK3IeIOuzOunmaqpEvPdtjf5Daoe5xen31/b6wCTMsbeElgWtZukL6ODAt4hBCfDP4QPUJqDzKIvBAEY7r8jLjBUQwWxBRp/CY8SSqGkl/67EnRj1YH3721lh1IxiPtSzJEtd0GE6q0DDEuQkN10ThkCoIKU2Y42CyKiw8Ms3jIvCY++o073ZGt6N8yFWkLR5gq8djkZ0GtcjJ16u7VUiYjiDLpYfjLHB16brjKDbtM6YQCg/GGIpwk7ILRHQepNIwTctyE628ubDpfNFno86Ik8NE2htk537stqhF2l/er2LKHNNKMOFjT55P5MQyLNPBfOmGw4gNS13HXVbtCOArUooAaBgwwWxVV/Dcx301uv3RuR11OqMcJr9Is/PeJKIoCuAmfoYB0aLI6FSgE3AUqVonSqQyMDQxUjN1pYkLKCEmTRqXf/zonvvuu9xBmULxuKDG5f04b2NcgHh24Jolg88adiM7l6+61beAPMOwvievXp18/zmdJp8+vn8Px4fn7+BAIc/NBKNF/xXbsN28+tTau4/vMAtl3WPvSi4Op9Fp3Ak7nU6vM4njBcilN7l14+QcJMffzgj/7tu3H+41ipe8bdf16/VarbJTLpd3KnXfdbe38/ltegP5TSvKoYNcRfCreGvt5PC8vPkb6M/fvF6tVq8MDnu9sPf6NSSnCincNv/omhKGsCjbYSH33+xdvlrAMHyfJBWSVGoMQSBhyLzuRQemOb9spyBwXLnyNArbIdbJJN3InTUZDn/HL0LKYrDj4Z3GZRTic2gkOzuAsEMh6kDIYSGILWSbKq1U4CAI8rvdbmPnmhz+UAgQTFm/kvX6oEJKVz3Th4uQpCIOhejEWijEk+TzfxeCzJrtkFZ8fKiQNRg8Nrs2RJMZVqCPN7RC8hiDb4ZOg8G8YobLEiwS4yj968gjSxyAUPabYRhikaQhazdCjNWxbcBx5xGuvqYPGrGtBqs8DwIa4kaokBKyzIEQ15ZZ1kKuXdvdb7Z72IBPFbLaoMVoH9lhBxh7j0pUCLLt0eg820HBTHrzE/y6OAiiDggkLvx1W8h1cRCk2QxfHkYKWa8OlMGNrAMRx+WrlzweLo2Qhkf7bQrCE6vUAIQcFOtQhjq4EDAIQpV04uNvKch6kTW+MlwIHI1SEaMvmNmDUZIADKkG8cTREMellMPl+NbBhVTZwVMrfPkjGp8TIoeHUFY3srfXoFu8PSSK/FbDhBcMk/tAU+QAhH+HCTsEAQUdpVzIdXJw9nf3e5Mfx+OpQs5zEp6nkjuPaMbwFQbDNA4EDhqlLpBFh5wbXIVJje8EUog0gj6Ovx29OC/E1LEmRCbX3iNkDy8EkYnlwYGIAxB2MEQd3AWnsgi5ga/74ev4dDydngOiJ4gwVkJk3zKRVU+ngtmEZUNmB8ILhBjiEAYOUHIIhBw27c7kMDp6cfHiGRA/pxn8TH6e+Dmnm1SJkSQNxz2JEtcxGSVJcpItodD2hUIu8d6bRx00REA8PtKXOQjCDM4ySC/GvDoT4sdBOMhJRsFoNAwGThJEgMyCwHXCuBu3Gg6lFREkUwKKPU7ookKxQyRIQe9Y6pCF7vvkMFczhaSXyJXJaTQ+Gp4FCYMgaJUUgu+iyElCGnvSCtxZUHUG+EYJZs7yCESOkwIVgQnFQwSEZ1bRMtihhchdmaIQcxoSgoJHQXDgSxakFFAGCsHSGAIStfpo4ASNIF1+cYMk6TeyIXDQQscCybu0buu8nerMKoLBdcBhIXkuhBe4QHbkWOfgxjU+mh7hUcQwA4K0COIrpNtNgj4go8jphjMjcMORQ6nGJ6NWtBSitxTasHDhqlUAMRJTjSkEUcdcgkbYoZAdOdYZcR3B88bpEIUsn1p+q9XvwjHKKSSKkq4DSCOY4UeAwJHokPtBIwNCDjrc4dj2zf4DAW9ZVAhD7LxSyhwCBiAm4qiK4ubNm2OsdJOlkGEQhDl/cCWXgtCwCOJESeASpBHHDqc6wCzLgtzH5bdhzjksEAyMHC6HLyfq0JAEEHIrpGzPEFEg5TKtkOypNQqwOZVkgSgkR5BBEDkEiVuD2cx1ujNsYn13GGYsERyI9kBHIXXj0E+G5v6lC0QDiC3EQDiogyDXlQEIJhYg04wrSveklPsZ9PUg7NtxngzRRNcZhK4bUvoOrZNRK4jcFRAzshoCSOpzIRzIvxAvrxBVIKIAgyG0ZeHRfOZix9i1EjnRzxf74AEQjBIj8+2WJRDjKLLjH4g4rMJOKqlCHDS1iDGOFLLicxRJ/gNS4HeYG1n4WIikt95UIXxJRKCgPRcOAGykEdyzxniefaqQbIZRbFCIPEJ5BIjHDlojLLGfs9KFFDh/Tyy+tyNwmHVvGrKk8Tcw6Hn2HOKddVncIPpwjiAFORRYIrdF3rLEIfHEQYXYkwMHOeZVecee7nNJRJAIz7NXQhz+slEfCvHswBCS1BWy+AiLYh1SCPogBwq5yQ4wdNs6Go/Hp8fHqyCq2bQPhlzCEcIzniA1AzF7lokWYhlwWDcXwvcR6gN/iexfDImib8c/DuN4IhAvC8J1/DfEF0jFNGIK+Qciz+HkEbHMLHXQ32H2L9mEjw8PJ/EEz+XPhOhjn80d9Ly0WACkzhBeIy6WOkUc+hBLbr1pSNUsdOugn6sknkCBZ/KhhXgMycBs1ggUAqHHWn4dIzMQc8miQtQiEH1+pUudIHCU0SU7+MbIed3phe0QuSCOQiZkw0IUgqlVyNMqMY3Q+iCH5F9HPeXQQvBHxZGSgNFsQtJTyFkf0zeH3CdIqchXX4LUaH2IQyF84ZU62AEGH4UWsmMcDJGETaTdOxui2+/GEDjkssWQSs1HH15hQaKFqOPGsy+v+ltg2KUOiXxeFwm9Nnd3d9dqBIjcf0NwjwekXikDYgpB+BDXFNQBRuXVgwPKxacycN6kyKXB8XL37r27aKQ3hxSyIDK1Noa8MZBiARBZIvat41gHQVzX9nGjcxB3t7Y+TA92Z2mIFILwk60bj++1dpthL2xaSGEZRM/0DSCIOuj5ictrvebTRQuZW2TvTRXy6+A971j9g1ggkPBEUwVy7/HufrvZbjLEY0j2I+tNG2FHSZeI3LVgMa0IRgoBBI63B6/kI8jngyfGIRBWMOMWcq+1j2WCrwTxMiHO/1wY2WGfk+L5Cf9nLijmKKFKRKIPFqWQt2UrefrgaP7hVgqZM7a2tu61Wnfx9W7rDyvnwtQ0EMRxZvwM4oMqKjjWx0SFNokZtZHWqB0MDlLrqz6qoqLT+mgHdFo7Ii9Fv7P/vdvcniaMgv6tbZNGuV93927v9oKAbMGx84FdcxxSs0MU3nRpgxJxDaJ/LsMwB4/o0fQRM5/aFVQOJ7IxQEEK3TAMg9Bli+zbAoS0UxAeCw/s5ShWoxmEIZrGEv1TCUEnJsIBz3rI80DoafyI/pEpJjBHjlUOgmAmdAuFP4DslEUn8KjmgoNz2VPUFJrkUc9lOPYoMQaXDubj9lEtan4wnfS8HBzMUSHlyuXyTNkNvcL/t4iAUHkdq71ooMq+eYiWoYQ4TGXt1Zuo9kwv9E5XZaGBwr3CHAqC3aqiOXITM+WZABZxhzhE/m+MCIgqS1N4UEPGRzE7MhxkDVMgXIrDucvxNeJoxwswndGleIE5xg0HQJhkdgIcRPIHkJ1yyGwdGbziQDvy+fGRw6eQo6iwEAq4VSueGzl5cj6+1jh1NYre2uP3ienphCOvZHPkZoKgDI4wGDLj0r+HiFCYYpUqTJ9WIHDs/OiINsiR92Ec1x6MmULnXNQit7obQ8FnNFv5ESICNnwetxFdzJGzQfAGFEFAf4fMkPSfFx00yBk2CUDIIKOHj1DSeDSKa0tLUbwECDbIR53wthfuLlTAYfwIJJV4lzIICIw0BhS4ITjK5S1B+k3G+NfsBOFOM5GjKkR0hBy7Gb8agz7iRYPMxS1J3JkjTwLJyMiu+BFzvLtyVeMwRz6PjjcMkTm62a613HGc3pfm3zR8tdPPGNMHXafXXZ9EbRrOpUkOqy7rJIbCo/HNMdKJsJYYZE4ZRDgUSI5BWtX4+afxfON2uVQqebcNB5F6nue64ECwZ1hkERhA6fwFSdPZyCjtrDu9tW9OFyAwCdcTaDiEQQByCZZQdYWPYcsYRGroJ5gDTVUgILkTx55XKr2cv7L0uOS9y5EU5rjvkTlcK0b2CkfTcVb7w3XHWfszyLJTzyhR/dhcn5x0OlSYRtFZUq1T2rNC3gTQCi9X8PYJRYi1qYFBDAdOfHr/4sXSlRxp3ivdZg4GUShDzGGDbDrOCl66+LKz1F8ZyEHHabJpBivEsTL4QRXDye9rXWeRQShrVMnWcRgEHe7l2hhXrJ7F8Zubtbg2YgyiOMSzRgFiwFjtmzCK4oAKbuiCxPOzQGALCvVVp6vbiGN6WeSXHpwOp77SwUBd4ziLXYdcsd7Dy9okXGq92+msXQAHbXvgsttpnfVW4ieggEBSuXmvWHvG2wMln7IjRE4YXfEuwlpKNBh6nu8DZF8KpN8jp2p2nLXhNAjFT7eD9iqzfFGehVPqXAcfAWX5/Pf17+cvbDjfCYR3LlHOpUo8Yw/iT4BQIEmlWi9YsUHMXIMNIumuqKE48EHBi7D4EPkFC0S0jCD50nM2+mmLIH66fQAwSMeha4Cgz60iznvO6oVNZ2Nzs+coDgYBCe8eexPxngxNokDYHgBR7RaLQCbfFclnWHiYqk4VfS8B2WuD9NEmCE6fAqnr0wO4lnr5slu7lr5sBeUQeOSF82sOrLQIEOJIirb79DbRaA6lXSUuuaMQYoYQGwStZWVzkL2q2C1bjRDymSAbTme5Tl96CgRPPR0b+JCMsPkLSBM9Frrd8+cn1+t1cSyIbTI2dvp6/FDNEtkmsp1Mk1ghgQfD0KEtxTFCoh3MRdpFJyAZwd5Jg3zBSWORPg6yQCYhBHwS6cYiKhOei9vohpUUiol0kh0k8uXjKBMDglsVafPZFIPstUFWnY1hblgKZM3pCUhdeZYNgkEEIMwBEOEAiCrrNoLXarrLKBBzMEiql8pbVMpEwgFNYWtmETtNi1kg/K0vWyDNzBjp4smADAuIcMgeRd730IgiTP94BwDTSA1KtmLxLJBBDJTFgSs1SLVIGmIOGwQEdUR8R1yLumMcO3juqZFjlWKkSRcwyHACchYgzEEYANEYOuNqR8E7vQKRCAM6Rzr3WrZFKtR29V4wmIMFz4JrwbeyQKjJXTUgJCB87Gg7ddT7rzi/KCDDv4MQh1Q69SL20wD2OEISlKT6TBjCYceIcHBmb4EUqcfyoyyLQE3V/faIg0EoG141oYKUkEA2nKYNIq7FBkk2Veu6ObKthzFxiEwRXSg41K0Qsd4RBcle+/Ww+Ov7nm+DiHb3BwM0UkBw3O83m0lW1ad0rElOps80+2oKg04LCeM60nfOsYRjDwxyN76cU6ZgjjTFqLX4BmWPgYaFXr0CUhTAuJkgelphg1h5O+yku+aV+kp6Vqi2MOpA5zAHhq6bf4xfi0sJhuEYZYk50hxsB5J+gS3IuYLyFhYhimwQDJaLlCIupueFhsOkigelYID54F2zZprAMAd3ugKSS8vmkEtHoyjys0GkariFRToSPyLLIOJXxCHLuh/n0X4mke1wSmIPAcmwCDh+v3Lch0HgWGGQAZLaetKs1+1GD5aX6/3s4ieVdQBiOJK6IGRvIlMDIQ5ti9gcW1kEHBbJOIlWsCEvFSNCsh3ZnkU9r3YrcGgI2aCvBBI9oBvfsiOEQX6ngUEEhDEgTEUKPpWtLBDbrfBmR2UErusoDr0LjrdhSkalExTJTH4FSWeIOGLPAoeOJllzzMMYWEehqa7iEBDB2R4Fg4hB9iar8JzmypwDKLLXErKC3bRPBg8Wc1A4aauxsECHP3hkgYBi+yBpgyQc1Gb1bIKbjMJjOoPYK+0s5uAzKkI0snBA2EI6i/XfmfKQ5oB+jY/h7WAICBnkkBjE2khtFklOaBKmsDnGUxw8MweHGIRA8gIyWyrNgiNIg2wvQgwFlFQ+eWYLDo4MFjVatvMxhgyHgqEk2RWkul7mMCB0PRCoQmKDnBUQ0vC2d8+IZ+03IDqkrUZrg7BkVGcQbr+IEBgE1xliQ9jyfBpGEPIG5ODkjkJdQGRUt0BMaJgRI80h5WbxKBtkVI/pmoOJkzT4wVQxiihxDAXkgDGJbuA2PQscWSDMYYt3I4pbpTiYhLN2iEGY2ZweaT++WPTdwIUE5OCB++fOUpO2cX8obwDCQzEgOnQtmmuf6CrzlXyl1Wi3243227eNRqsBVfJ5rELT23aj8fZtG39In65/uq4kv5ZDSX2IK+n/aEC4kk5ATz5Up3zX1aU3AYF+u9U7dZc3npX4pimUbPdhERwLb/vQ+JNHTrTauNn76VXcYv9sYWlpfv79+wX8iobpW/7MhOfOTpS9i25xGnc/3rk2d602hfnQy4sT5bBQjXDDbTUMChOl2cLsxAQW3W/cKKGoVsa7sucF/r3py3QzK+4WeXyviNuRbuGu1pfYqhX5NB4iT/H+GkRwDAweP9s5txcnYiiMq8UH9cEriLgqa9X1ykLIZEIeJjFxqh1nyRidwW6oOhRxu1rdtVCsgn+7XzqIog9e9jU/pmGmE0q+OeekzO27vIHz17vr1+9sXOjfXV9fW9t/+wSv18MZ4BWsHWCZ8fnNoiwyIVxVVeEj2SARbFDvLWutbKGFc4T62jdFmnDjpKtkNassqYjCFhDMWJ43kErTfFmG9/BLtM2ynPpcr86pEtQJ/18hWOvm2Qv9zc2ra+Px7Xvn+9f2t2BB8fDxZHe+O5l8mcNvojfaC0curWazSs5A5YRNynctviXEGOecTTOfJFQzSDUVuhDOBQmisRhhGaKVFANGs1w3bVuWcN4YPhvttA2ipJNMFxmkHCAiAMm1qupbF8fjm3i9HMYnMAx4OZl/gmvGm+05HFngmjFsy9RYJwWUADeg9cIjIaxwhqVZxhVPhA06lJUSLReyklZwYYklilJBmWWc86ypEQakZnj8ui19pnXRND4voOcAQlYdLuFhuI2rmFMeoJJRj/uw1IAFxevgGtL7sv2mBynPRwueFQl1sxVS28VOzhgCYpOmbAbIcY3qkMQ5STNljCDWMA7xlhAXFqsMeqeYazlGvmyHi6UviiLrrjPy9Pca+ZOSTkLXdqfkN3Dr5taVB2vXPmx9eD/egrlJsJ+AI8UEKp7tvHs6evp55AftjmdkBohKGe31mqnnxjKlw0AU40wQBy0m0SkzRIbEIkIoqlKaMgTKORPiQxONwTe+u7yYcI7eQin0PHT634WA7lwWjwPc2Ni8evvKTcyJ8M35ur8F1wlYmzyEoc7L+Xz7WZm3eFNlVC+HdWZD/jteTLNl79209ikyWxiMmkjDODXGIg7WOEOqUFEZqjvFaFHsiMYq6axSLNUFag65pJGS1oZwGYTrzKET/ycE66vJ917/Rn8dtyYffPj46NF9+JsEdxDYZbyAdcju9pe9Mi98DSOWUlOBw0yIHnqdDYe+LVmekgpIogRqgWnqJASwvKBO6ClVQhCECRBqUDqGcqU4Dw9j+jz3QSH2OiIDR2BLdeq/hGAnItJd69m8ffP9VxjOQMKLFw9RIDAKeflqdw7rpb1lnmR1Pc3yKSNUYfF1uWjzga+T5PsfhrGCGYwIkZC2zZiiYRMlE2RWGCsay4RRTCGRENNpE3QQIjuh7uy5zijs8EE4fjTwq7vXMfDDzqvjCBZ8utXQ/ODHBlb+RPcLP3HyUCQSiUQikUgkEolEIpFIJBKJRCKRyF/zDX0X2zf4RO/tAAAAAElFTkSuQmCC", "public": true}]}