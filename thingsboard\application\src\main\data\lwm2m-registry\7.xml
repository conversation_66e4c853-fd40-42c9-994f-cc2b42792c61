<?xml version="1.0" encoding="UTF-8"?>

<!--
FILE INFORMATION

OMA Permanent Document
   File: OMA-SUP-XML_7-V1_0-20201110-A.xml
   Path: http://www.openmobilealliance.org/release/ObjLwM2M_Conn_Stat/

OMNA LwM2M Registry
   Path: https://github.com/OpenMobileAlliance/lwm2m-registry
   Name: 7.xml

NORMATIVE INFORMATION

  Information about this file can be found in the latest revision of

  OMA-TS-LightweightM2M-V1_0_3
  OMA-TS-LightweightM2M_Core-V1_1_1

  This is available at http://www.openmobilealliance.org/release/LightweightM2M/

  Send comments to https://github.com/OpenMobileAlliance/OMA_LwM2M_for_Developers/issues

LEGAL DISCLAIMER

  Copyright 2020 Open Mobile Alliance.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions
  are met:

  1. Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.
  3. Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived
  from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
  COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.

  The above license is used as a license under copyright only.  Please
  reference the OMA IPR Policy for patent licensing terms:
  https://www.omaspecworks.org/about/intellectual-property-rights/

-->

<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>Connectivity Statistics</Name>
		<Description1><![CDATA[This LwM2M Objects enables client to collect statistical information and enables the LwM2M Server to retrieve these information, set the collection duration and reset the statistical parameters.]]></Description1>
      <ObjectID>7</ObjectID>
		<ObjectURN>urn:oma:lwm2m:oma:7</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Single</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>SMS Tx Counter</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicate the total number of SMS successfully transmitted during the collection period.]]></Description>
			</Item>
			<Item ID="1">
				<Name>SMS Rx Counter</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicate the total number of SMS successfully received during the collection period.]]></Description>
			</Item>
			<Item ID="2">
				<Name>Tx Data</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicate the total amount of data (IP / non-IP) transmitted during the collection period expressed in kilobytes.]]></Description>
			</Item>
			<Item ID="3">
				<Name>Rx Data</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Indicate the total amount of data (IP / non-IP) received during the collection period expressed in kilobytes.]]></Description>
			</Item>
			<Item ID="4">
				<Name>Max Message Size</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>B</Units>
				<Description><![CDATA[The maximum IP message size that is used during the collection period.]]></Description>
			</Item>
			<Item ID="5">
				<Name>Average Message Size</Name>
        <Operations>R</Operations>
        <MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>B</Units>
				<Description><![CDATA[The average IP message size that is used during the collection period.]]></Description>
			</Item>
      <Item ID="6">
        <Name>Start</Name>
        <Operations>E</Operations>
        <MultipleInstances>Single</MultipleInstances>
        <Mandatory>Mandatory</Mandatory>
        <Type></Type>
        <RangeEnumeration></RangeEnumeration>
        <Units></Units>
        <Description><![CDATA[Reset resources 0-5 to 0 and start to collect information, If resource 8 (Collection Period) value is 0, the client will keep collecting information until resource 7 (Stop) is executed, otherwise the client will stop collecting information after specified period ended.
        Note:When reporting the Tx Data or Rx Data, the LwM2M Client reports the total KB transmitted/received over IP bearer(s), including all protocol header bytes up to and including the IP header. This does not include lower level retransmissions/optimizations (e.g. RAN, header compression) or SMS messages.]]></Description>
      </Item>   
			<Item ID="7"><Name>Stop</Name>
				<Operations>E</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type></Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Stop collecting information, but do not reset resources 0-5.]]></Description>
			</Item>
			<Item ID="8"><Name>Collection Period</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description><![CDATA[The default collection period in seconds. The value 0 indicates that the collection period is not set.]]></Description>
			</Item></Resources>
		<Description2><![CDATA[Note: When reporting the Tx Data or Rx Data, the LwM2M Client reports the total KB transmitted/received over IP bearer(s), including all protocol header bytes up to and including the IP header. This does not include lower level retransmissions/optimizations (e.g. RAN, header compression) or SMS messages.]]>
		</Description2>
	</Object>
</LWM2M>
