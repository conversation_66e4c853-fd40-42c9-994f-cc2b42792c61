{"fqn": "simple_volatile_organic_compounds_chart_card_with_background", "name": "Simple volatile organic compounds chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_volatile_organic_compounds_chart_card_with_background_system_widget_image.png", "description": "Displays historical volatile organic compounds (VOCs) values as a simplified chart with background. Optionally may display the corresponding latest VOCs value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'voc', label: 'VOCs', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'voc', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"VOCs\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 2000) {\\n\\tvalue = 2000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 2000) {\\n\\tvalue = 2000;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":500,\"color\":\"#7CC322\"},{\"from\":500,\"to\":1000,\"color\":\"#F89E0D\"},{\"from\":1000,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_volatile_organic_compounds_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"VOCs\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:molecule\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"ppb\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["environment", "indoor", "air", "vocs", "voc", "organic solvents", "hydrocarbons", "emissions", "fumes", "gaseous organics", "contaminants", "air pollutants"], "resources": [{"link": "/api/images/system/simple_volatile_organic_compounds_chart_card_with_background_system_widget_background.png", "title": "\"Simple volatile organic compounds chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_volatile_organic_compounds_chart_card_with_background_system_widget_background.png", "publicResourceKey": "qMc8sTDp5b2XBHEnmuQBisPFk6jIxAC9", "mediaType": "image/png", "data": "iVBORw0KGgoAAAANSUhEUgAAAQoAAACACAMAAADESdfxAAADAFBMVEW3y9S7zti4zNa90du5zNa1ydK2ytO6ztfC1uC0yNGzx9G/1N+80Nu80NmyxtC7z9rB1d+2ydS7z9m90dy80Nq3ytS/0tu5zdfB1+K3ydW+0t24yta6ztmxxc7B1eC/096+0ty1yNO/0t3A1N6wxM2/09zA1uG4zNfA1N25zdi0x9LD2OK9096/1eC3y9a+1N+5y9e2ytWvw8zD1+G80t26zdjC2OPA1uC+1N66zde2yNPC1t+xxc+70dyuwsvE2eO1ydS7ztnA1eC0yNO2ytLG3Oa1ydHF2+XE2uO6zNW90tzK4OrI3+nI3ei80ty8ztbN5O7+//+zxc+0xc3E1+CxwcnW7veuvsatvcXX6/HI3ea5ytO2x8+ktLuzyNGntrzZ8vrY8PnL3+i5zNSdq7HM4+2yxM2ouL6bqa/L4eu1xs+1x9GQnaPb9PzT6/XQ5/HK4ey4ydGtwMiqucDm+fzI2+Sjsrj8///2///R6fPO5vC80Nixw8ytvMKqu8KWo6ru/v7j9vnj7/Pg7fHE2uWgr7aSn6X5///V7fXl8fTR5eyzw8qerbPf+f28ztqwwMff9vvm8/bO4+zH3efL3uWOmqDz/P3v+/3q+fzb8ffP5u/Q4emoucGKl53G2uPG19+ap63y/v/r/f7S6fHp/P3a7PHW5uvM4enJ2uDC0tnAz9a8ytGgrrSXpazo9fjf8/jc6e6/0dmqvcTm+/3i+Pzf8fXU6O7Y5+2ltr7c9v3Z7vW7zdiyxtGUoqje9Prj8/fP3+XI3+jV4+f0+vvq9/nc7/W3xcukmpLi+/7D1NuZqbC1qaDM3OK+zdO4v8Kwpp7a5erI1Nq6w8fCwLyqoJju9/nD1d+5x820wMWms7jHvLPu8/T49OvUzsiuur/Mw7uzubu0srC3rqb8+/i/1uHM1dfo4Nbi29PKx8K7uLPBuLC7sqmuqaTr7u307uTS3eDu5dvEx8aFkZaek4uRiICFe3Th5+nu6uTg4d1vaGLa4ePK0NLDy82akIh6cmv39vJ397y/AABIQUlEQVR42mRYB2wcRRQdn2/H3gUOH3gC1mFgE0eEBR3eGAhZThjEccQECNgYOyS2gQQbbEtgJ4EAtkMNxYjQm2x6h9CLRS8CRO+9C9GbRBcgxHt/djkBf2f+/zNrJN7b9//MRe20006zMWHMZmNwo2E2rQEBk099Q0OhoaEBAVYo1ItlsEQoZAqZTD2eDBLYwStXHrjn9tso5jTPc32TStfusfec7efttef8fY455pijjz58n0Pmz59/yLmPHrbVbjPWO9btb1/T0dE+3rt48aKBtDKREzmeM+PWi2dMrB48o33ACQLfcYPAdVzX82kO3c5ZGCJ8FERZJo6sMLmH4OP5n9XJSP5MMmV5oLcsCBuJJUwICYIeGUAXhAjO2Lm3P50mDZypi/Y9Zf9tU5vssEFl5R41c+btvuf8+fscss8+hxwxf6/ddm+urlmnetOaph0rd2xRLb0jHWOdnR3t7W1gYEmkdeQ4gevhKbkYnps+/urjZz6zbKgN+4HrBo7rOzA/8l2QQaSAj6WgkQVzn0BJgcO95C13iJ8m78s5LKAqysIoExETgEjw9AyWDutf/v7NkQL44Hjk8asPOvqIWpFAffUpRx11ceXw1NTwosodanas2WxW7RZzt9B6iy02nLH+rFkbVWbah/u6unp6lnWuaVs8tERpxy85LswDB/jyHiKfAM/MKxbefWGx/6aOjA89OJHr1mU9xCyowQQc4KSrIy++BZt88KgMnUuZkQ2yLylzrgIVM1GmIPEWPjyN+G1ALBC+9/h3X38US+OlU+46Yu8t9959jpfx1NaXLH/inoOnjS9btmak38sMtFRuuGNt7UylS8NjfUe+3dM3uqYdDCxq0RrYul2iBg3dQOoFJARIuVFySqiGux+enj1x4ZKZox3A7ZILRN93syXQgIzgkcGQ0QKgrQMzRBmjrmPGKbATemLP9/Bw6r/lgUAa7LCVgZQkcGaEExZI4fcvXvuoIeNBCM8dtXze7Imx2UvmzNt4n6P3OeyAmppZ0264fb8VO1TOUsVS59RUV0/fso72trahRYsW4bMD/K5AzIeY6WzKV0GypO14xXl5f8VFquLYnhGqIsCEY21EDlA5htgFaZ141AeQ0+Jgc5l01pff5Opo5CJUUhTCQpz9o4i4NjJCAxyNRLA5wLe+9cJ4QZrlu1fMv+OZtUu7hp2ZBxz26AHn7H77tFLv7Dv6JiZAwDgUUNSQgNvtiQIChqQGAE+iGNokSZAZOMzNww+fGbmVK2vcyiV9BddB44Qg8Nr2C2dg0ZDjA7STxYgi4k8KhJMKwCRPEu1OuVWUlUJT1IQlIqkOYQGQGRmsSatEQMwIG8gKGSbuUXc5z0xOrl7a1RHNWq++c2xsDE1gaKio5ppFrnYscHiMchAxSJoY6gEQSU6pBFa8wCntftyKrOvqCw4u+jXjd+BMKYENmjQC5/lPf/vt05v6dRRZTRAosyjWiWwCNJIyO+XCsNVhsxBU/KdZYpAA+HKTgEckCTbK0VnAhCFdcMp9E5M//DB4+c197VVzW6ZVzJ3btOKNKvQNWq/napLBmUCnL5sVC0nATIykeOr4u+938Oa0hcpRTRM7kYSSTzaiEni54d1P3nvvsy8+agOO8gen3JkhcMkIS5zdYWZ9DsucfaPkJkEOEkISFhgtEUyYJZYhGZYJwbvyqonzf5hcfejJfWP9M3eb1h95O+x1ppsBPkLsbW3F/7ugp7MTH72E788JLXACpbjAxacPJHcPvvJSg1C69uyUU6o4dqqVZYFz1EBqkMIv++773YcvPvXNWxG+fFQX4tBkBmRRSC447ShDlwQWd4hEGPQqOT3orAkNDMKBpYGDxpsDAxL42E67ePjB8wcvP+Ptnp6OadNb0Uk3P8B4AWkS4PWFVjcSMoQPnBqJOTID2xiECz6kgxsVx19zv0O79MIK7Gxe7IukOOA1k2/33ffzH//6+amPXBa9g2k0O0NEyPLEQHM+F9ZyOcGdkMEdvKQwVFkMuGNKk8C0WrAFwdbAULCjIROrQWRR7wH4YxdV3bxq7c1g4sgjh1sc4N96hZGKKNmbQjTQWsgQoTVsEilcskU+8F52SYRY6fSrq7XjQyQVK+fgb3VTZyf3I8c4RSbff/LeVz/9/NOPb/dHoMdWSERS6AU8QxJRCEzpEZLIwQlClFUEmbAGBsAIqbCnBRkp4BSFExKsL+CxObi4beU5nUuXHtkDKg49dESDgj2asqQioAzwFzwiW9taHY0IrPAO2UCAR2QrlGWsDJrvrnfnwjR2YOrC65iktuob0VlIgncJbfRN73/49YvPPvvNB06kfFQFSTDCRp0JpQXIDOWbC1p6PsKDfWsYxBSvE2QCo9wr6ZKiyBTo2SzpyzxQGMJEy+vHL6/pufntvtHRnqWrBztqPXed5rQr9WGBeQHhtqJriLKxG5QQLWjfUsEN2SMhYguXz0BLcDR2TrtGw5zaqp56R+uiE2G3qKNXXv3qq6++fKEf5yigAL4GcIfosjETSX+wxs2kR1izJFkubIGQBLp6hH+dnsRug5ABWigGoYJMSDjxssr9FvbffPIH7UOL28ZWTS7VuqbakIbyfQGp5wf9vf0ZwrX7wFki9JgHkYYUitYkY9bV9yiHTLhF/9pbFA4O7U9vm0ChYDOy/eLJF1544a3FftaACxPVaY22aYjTjtCYKJfLAXCZjUQEZSqSXJWv2YQvToZtETR6e5fi0ZGYR1UwPvZGf4WnT7956eiigSjjtXZNTt7R1BTJGdktP6mIPXBL3USf6W+1eoA24iS2ouhCEPrYVQuX1xC06GjFBevJG9XUOYzIfToeLwORMdlIZw0LIwQPWIaawhAyMElGGNaFdZHIJGdVIElMhfxRqMhD+XZp75UyxRdYF3joxTz7wJDTrr92fD1sHDu0anQxMOOpX/rDMxXKTeq+RA0Egp17qBPIwcT4AyLE2rdLBnrAbLrz9BTf6SJATztljjYao2q/vnrtFx3yoC0nWW18HfKtYX1EOZOrU2HORCEAqkQElAZdDpSkCd2ucoghE7bN8m+N2BfKR6d0CvKAmFisBU7agms7KhC6d35udYfPWzXHHYOTY1XSM8vHRRGlwN/VQN3f2u8BsbQJAg9sozDI/UULblxCRlL33VpJYoQNX11wHZsDrHLBlOdHETJOJwsnBWPIC6jQ0EUujKACY4A6NECJCS/ygKNSQq44kcXvbIHwelkWRQEDCY9NEYXlgZNmYzd5oDpaTmhfwLPC2f30VaPxNQLPel0/rHUgDFlZbdBhOkj8MGj1KHwsi4wBygMD0Bf88u1vf7w5oPXud17HTgHqKAH2TVBVAtr92iaMVkigrCxYYo6GqUAKUyJDeSiDS1ZILoiekqCjMVhaEhLCWDQq+YeqRBgkAaMgtyl4EPE/Iw0whBPbhozX7el5tTee3EMJoBK6Xa+mpn3t+VMpYAVOIuWJgQEufDggiAYGHKMEvxYiABjjpX2P+uyrj9+KNrr41nXBAuBKGfi8b1oCUk2dO/mOMQoFgUNUaR+TLCijoAETmjoNURiTy4bgwWQN+Ejgc/7DSkxTwlQYF0iZBzukNxQSJgpeIVGEh2GN8bEbxnGByBSbdxhJvXVkwN9QASTiNtVE7rLJVUCCBRmISaFpz5XgDEREGRTtrlZcnbjvJ+889doHA+feebByjNCkNEhYcWGFIV5YRWVfQYcUgaMMvCnq2EyIaQ2kwEI6wgYpoIPWmDBhu4dEvEbMCxVlSWTQKtgsSQKfpDboHhA90CWt4pHz1lSgAPRJTa0meP5yL4hbg1M9PUS6dnJqE3QIMoDNgF40AfAaIjG5gf5+reTEjJulfvnXz5996psP0g9dXEGsVvRwtSubmBjAV9PnTjiyaafhgGXJhApVDk9o4HMGnx2O3goglEpolFaJABMybG4UlZBctCUHATR6KiNplAl8mTY8fW9nFZjwm+fgUPBuXNXqulyWXLdplmF/mDj/wW6FxLFT+/YGwRSVoX2TDQYGsmSD2Ais+OerX3750fghdx0AhuSoMHig/sfPAWK1s2YdVLffoZlopRTeWSqKhp4loYAZW425vFI8RkUdDHW2JGCJQmI2LDNKJJHUBnsEExRE+QB9gCwU6DjYGcmG6y04sfNYjx2zeoS/JW4cbJXLAphwmzc3lIHrHjrZtaXLj87pkwRJNWL8wbNBEAFVbKr45CuvtK370H0QheFSK8vGadcoKEJzqnTT2E4IoVHJf0RSZJo6k2KGggB60KaAkeXSmEAPlVDBZTIbEbCleKGoj7slqwP4KQxJYiskeqBPLFhywniLz+rYoTegGq4fHHHZNR3kzdOBWAqh8/xV3Vpo8DWjvUFwUcSSILMmiKJoiWI5EHak1SVnNfNz02QP/tpbUklNKF0xbaoVqWA3MSF1wC8bKVFRCHVwGTbGXYKOwPNcMJfdsoV5ldy3y0cHuwUJsD1BpgSXGVVh520tbWZX1zmpqdfpZiu4bPWwBx1QIHr7GZETmz5jcqrK97WrdYkylj0KpMh2WLTYgshkEXwgQZGs89CpFQrv4peGtKy44Fhl8CDHM6PYV0QkDcQLB888r0zeyAKSCLmDYsknqMmDIiN2wWmlQQ9VwOz10p4XBTgPWdIlynS4QgSdG3R7j122JoUa2GrmCI9K4L9sbSf0L23Sb55uirECgnT74OqMRmb0zkDvggMEEYn2NeESY4QrUUuKYDdY/6qHqhGxX55qvVPmEKyxhZBqQrsgDxgpnVLWkMYujxepPCuEqsBjhQDwSRrjZxdNxyslTIggaDwrOGxrsNAFfblEAv6wdK+/tv1Y4N5qXi/QYsMp3X7oskCDDJhunp5F5hh8feP7LSdPHslv7musiJ0NQFqGNskpQaWnYdOPOWv58kvSInqKQNROu/BROMG+BEnVjqM7KWtax9E6y0s+RPeUToGHpZOXniBBqBAvGYeYsvdtYQETUW7aHIEwIOC9B5IO4bJtsmWOLQgCf6sdWiPH5QPAy7rkBzdyvRsLxF6cqI1U++rBhhQybRyF6WuaoUPHY6ThKjA3PeOglUctb0rbL443TGDGnHBNiqhNDLyycsIl6rLlLA8GMy0bIZkQHeTTqjG3HU4UkUNjo5JQV6ZBMh6m8uBWJV0zMekUMmggBhxQEaTDXXDi+IKdXT2neSSiCIJdebsePwOXqJ1RCK5p3sDYK7PPDgnk6a7JkyFdR86D5MYQkgnbCogCcYu5A87p9106t0X9x1IGfVMZmwsF6wyNWVaSPXqbIqbx/ZVYOq/SwAnLpcBBbjvpnbZOwE9e3tl7hfBgf4JaJrxCogaM2OFuXYJ3QQVmsOuJNy3WgbvxVrO59gN7Ktx0ecll9J38vDO1w3oAVEqAeNtWDTZUETTNqkMyRSrEECsWnt7vLe5Y41nYfJe8XHFBrURow8bqNcNUQLJHAqzlVSpknkpDFiQllW9UCngJ25YGwddhMvJJc49U0BriJvFAfYE8FBJhiBQ4yAQHbwbXPt3WEjg1J/VKn/TRKAC7dNODHgqCDTI1r5JNwJAMHdozU5u+889QciIWDTfs7wbxSY9MnXPFhXtFo11dwwkuRDp4/E5PMaoEfkX1stl2lWhBIifvm5hbYBOawNOoGvPbNaZwaCADahr8djErwk5eCRH2ik1H/DRBTpdMGmTB8Mgj7WlXb9w04mpQwYrweSRcttqV+0PRpJp3Mb5RATYxjCYlANq/dnC41ighQikBjxUBiNMb7HXg8ocOS3eM9g1XVVakVAJOspYLrksW2uKdWTnhIMimjZa0JZADNSFLagRMzFWoGHTlfLoxHebZlCwZW4gqrMVUFCwbXsKFHBVCAmMGJEAdPDmA/W8urbS3jSqKzhtPPE2oSTCevArMMmWZ2C4jBmPSCYttTEhDYgEpTQsEaCkESELagFgKpWHfVdayr2LfxCI2EfYdBIgPbJ9AICH4B0h84pz7ZgjixvPefW8M5Jx37jJD+l5ad2ynv//QJKOhJc9ZPkPkoXfGfJ/h4Nv1pdYAPemnhQpz/NsePKNDiwaSpjlRNBe6f/NxIz2HXtC3euKo4eWDV/dp6J7QPJMJbnjCsb00DGxade4VOoYCDnRTQpLR250zkgSxIkac3UPQQxqoCjOLJapAQLxtHrooB0ZHmiLKZIBPm0YPLfTZl151y3DH0f2rxjp9j9VAUgXgdjw9vwYlEW6HU2kCLu5KXkR2hESoBjxsnL1j21JQYJjBIIYbQf/o6Kp8ZOm9e06ec+zcknOOa6/UvJmCe+M2x04XDs4e575k3RFYpF9wNtpCEUekB3wrCDk4zBqhYzshljb6iANjkYSQQGFgh2a9zbZKWPjfQxcHtFJlsIH/8XsqSmdnueWXoxvmNmbLK+pbsx2SGWkNH2ijhx9Z60cNSkHVFUoFTV4r0IlwwoJ87Y4v4XlCgFg2wLD34MhQfsAjpH3iaSLe7fRNm0Z7E/QcmDcJc6PJGLTMfg90GeypLFJBmB8xcw8SsBAcIWYJE2NMFwfWanSYNs3L/EU6JDbKoEEiBARgBh9+WR60y1c9O+GVDxg8qAtF0hcu0i46d9o2EBL5nqfqGfZM0honzw0+Zi6RKM/Y8UBG6oBtbcwKqt1GR07ae5ddzPEuV9MCXvds3rKlnREoHNBv/gdaKKBXbtzWIRtOApsCMA4nx7Fr2AqgiANDrIJaGBxYww/GWsJELXUdyzyJiy2+j8CbKMBHTJjieSpipFXm0PnhV6sd/8j21DQkgVaywclnLPhR9qZTQEgE9Pl6bPG9Cq7I1Exr8WHBs9bOfHmqTc8cYKE+clKf49mJ1DO5Lsw0VblkyyWrMtoctvPR7Q5nQc6BtmRqnXhCjGNmXHRBCqg4kMsANDhWMKwDRAORi6Vu4ITCj4V3VcyXhgfzQUAYAxdMFy2qosx4aD39xlq3Y0V7jy6/wRaKdEAdOO5dkBI+fR7wj4L48w/FAlvqpPnw4mgeHp9/cBvULmu33r6jxxn4NxtavUt3+ffw86s2zV5SyRuWbjgRc2KOGRz10NqD6BsjdnMLDl1uwRUvhBeAC0aHjfQAVoBfhsBQAUWkT6EGPykBeNZPqoEk4GKROKrVe+VNrr/ijsnObKPVQTGADKkTPhKj/9wjnkRFtlBdoAq8EBJhA2xyg1BjzF0788KpPL3isjsGqzVsG1Fz2DXDEkkqNJb7jW6ZPa5fwQuevM1dZCIlK9O3rdNJiUnvpj7dwDhhGDB2YoxBENugg/CNSmpYYbZQPEx/zamTTxsIDnkxBxbEWDB9vpvySzdMDfsr2ocd5kn3hKwQMTPiktTwxbXSUVmQfDOFLW2EWdjgBmNgIfHZG6/Z8WZOH9kerTd3T/VgVL/rnrt4hhmDaqfNs+PnrIRz510Fh9ty0pgMAz3HbstSBcNcLFIiNy0ySDGQEnywC0GEMQsLFWEuksHJSpNllwjDxIYhAVOLGYKvJFt8R5u98us565jBsQGvkfWxROFogIMIc+RBFw9fOy0vCbzDH2pGdOTZyDN9Q7jYLQlytfraeeuOSjdcY2nEZ5b7Ef1U4ra75L7Z8ZGVTu91h2KDYnEMXDNUJs8jfPEX2aCBMtfhSsvNA5E2KYgaLGZMYDow0EFMLhAlpIIcvE1hADyo6MTcwptJpkr0DC0ESJltVPaNhyesFSN7TLNI+FEHRiQEX/pmVgbr4Zkx8yyZqQcmGuSdGtGbYdFwQitXXbjjTYe+6IEmrXLm0AFuJIEvQ66+aXzL4DF3nc6F7Mm28XPVtZOyJ9QlNznI5XKMqQqUUjuOayAkxheJng0F56Am8rAoCqYJM7JeUBd8i08KWj6aar9c9jDf/+TW4WPa03gT2cHeGcbAgECyoSep8elr1wA/1LBbPS9ULJqHligBbJriY65uX7DPmnfe6UxjXyyAu7THswjFYhAYc+3C4JYNW75/Ik2IZk4sszfShesYs+nxtotv6JQXG5ugAFdQA/wAzVcMq8VNMgIuak1RBVNlmSNJgBAws4Vgv4CwaJEKVInGD0/cFB8zMr2HvG/BZd4wk4NsCNiYvrhmrSfv1A6uxAkRSaeMrGXRC+GWEKmFVe32oQBbOH/HNW4i6pSLQjW2ZGlGbRs4+42O33NbLseTxhaNoAVo1VnncU4Dg7M23GizEfwrlZibQcAYqdmsHRo0xDVSYiE00hf6EAUiQ8oFHjUYGI0GhhbyQOQX3oMmBjunAD5qeF7D9yIPrhWBEZ8JIcq655/PoLCsg+sOdqiA1P5tBbmXZ9nQWSJXU/Pv7FwCXjGHqa1QqUVwh3myWqeKx9Vz4t3LzuxRZkfLrmHEraw5TxxRxKI+ZJQrlhjRmkxYWvQQOzXtiCaSoUkqqAq0VWACVobxHQRfw3gdDXSZjTKU4bw2t/HgzdM785GbxiiRbjLkm9WkUP50ITxs7F1R/32opCgC5kGSo9Uho4NLmlkrNPDzqTC0NuGuqrGXwjf8aMIANZnr7hgf37zSRRpM+RgWL3fouj2cRRP8iSr4CVx32CJR2nWxjGP4yBAB5IAcETNAaFaqCD5vMFF28WnjVARHGVyADQ/hAVU88fDEbqPemNX79+9fd0ZRBO1jCBkBnsCHhV8/z2MPreV1N8mTmoOU9qQjdnsG2/Uc9pzQKtmCZ+KRa4/I2fYwoVEK/d2eYeBftUsdgUQ+eqq+acPs6H4ExChxeZv4ejLrOhR50QLfNU7yBeYNLf+IG5s9rWtujKhoOmQjsdjamcXDdBGgogth0YImODV8zObPAPwb779p+Ui2K2t9f/xff36z2jxmRrg4RqQBDNgPPyIEhDtJuJtzD8kOKAkCRsGKwdGhPsmb2i7p5NzVOrzUUaj+mmS49YynUyaIjLsGwg1vOJmh2fWzgwW57ZIIztqpzCFEgNdEiYJDb9G0iRRhqeawlMbgo8Yk4dQC5otmzeoqMzTKxrr8Fkxayw4fvXXk++whvDuf2NrXzk55vrruol++wx9KeqG0C4wUZgGPMRBaX80PAHJo71WhBHBj0ei7K6GI5ZYHVqB3DW5IBzXccfbMESowmPUhhQGHR066XOIzBv/b24Bl+cj4uZtWgQxSIEbw1a1HkAVXcNNLdWEIc80eP7GG16y5lAOuYAHNBv0FUlE2CZNtRBlc+C2+g2i0ssyOvLx93lvdu9kaY0r8+4/PPvvtp108+kmWAAehh8G2n0aPhQmqcGxwknQM8DUbvV2vbg/1DXu2U7IDOW2cvQJ83i7cMnOz5WoRfKVvQEJFeCBgjGAMizvvVg5WK88c33DJslyCTgvevfvO6xJqRCZywwyyNBtasmqsECexq1Wt6cSKYaIgCcjDKpfZUVEWIAEJooG6QU1AFOwnG41spN6be3kkO8VykT3qm1/xJ3FIkiQAfIQmPQbyWuSr+TG7BBL2rMasgjSgFjdYevUonjYs3Ccu/s4lqZOEX3JKTnzzzAN5rbA+pG8XowrXhL0SHCSk9+KVBnj1kvUbzllisCaE7ZNZ52mueF8mw4CSPVmrGr+MYSF2wEC8HcpwVIwfUKEWLKYIefhqoWxCGUIFZOG1/Aa7SiTGT5459kxnEq1jI4rCgbm5OTKB6hl6GGEBIkXi4Yeb10jLvFMlRlksBZomz8j5ZaPtVcpLi4LmPXxDsRNSwocTr5n5MrsdmCv7eHKaCrJeVLum/9GdCsepXDd/9Zb146O7KSeFDHomzuNKLKFDySS0yi1dgxfHbpp+FmLERZPGxoJUSPlkxfTN67lGC1KAGwl478T3T97sjjExYIPZQaRAFjAEIVzTQIV27pR1oY2ufokJEKKW1Ogua99Rz0WAjAV/feqiSEmAEJMYRdfP73il4DrVfSypehZ2hAz8JMd85eu8I71Drj27/vrRjGKJxAfs1CcPw800GjgZ19yGGBR26SrDk6opFTNtBLUF5IrtlryaMg9e0lt50AQvvo7CT+Q99frJx8Vj7JkiosaIWT5iYRCSBSkY+uezeNh6/4o8HGsYp50G20MZrgQ7HJmUVrqodYnSpwPQavLaFzoL/T0WVi4ZMEeLmSu4eE4HNGJTrkL/ee7sYIZBIJiXL1k74C6aowganDnJhigMkgIFWKHPAgvsMWpN2vamBfzSX2KCKPihIUlAANRB5bWTn3Imw/RlJAVgnMDSodTLIET5wABCfrwmKJWC4Mh6DDUQLjLkbneMDi1nCiUBlD1N6VIJC65VUSkiV/xFjz1j5pWdloAUEbcpjgYXV+5Xd+WIRg4a1nPchvUoJnQJbsmx522UL/MbJCvGkIQPvYSTpiwkRSBZxAtNBR4UqOhqtaR2NBoNaIMsgARWjlAah8KLJx+ntmqLS4YDZG96JnmkYM0EeFIh0f/sI8MBjn1ZT0w9BKiVuw2NDu5jW6qUJEggRqJg4cCsXRBCD/jww5q3dPX8vBWjJUpQJEGPNTfwnE5NKJXcUNX71q/fVM25Sv4N9aPWcJeuUhhp6ddVkkMXuYEU+G/GrGo1+BaDgyrwW+SAcvCyJCTKsqNUr718Qi+iA8beKULRJAdRGAWAD7Sh5L/AYeHQ4bPzwyVwUD80KNlEeHi9PdSPrj9QgSqVQBpiwtFFTI5TKvLwi0XXAT/mHMGGymfw7F5wCI0hQSjEUXLz9D+6U3AYvJxzV295d8PmnrygzDw0NU0HlxL26OKj+HHgmaWroYx8zW2SI0wwqKLb8jv5/3UgC77C98tkJQIb/PtYYD/xzuHcalsUQQ4kUUYhzaYUoAXwwIuEhPrhGcvGWVeqARgpLq2PDp7geogFgMaIuaTIA01+wSLThSqJr7lmceifxrN7t1t0i0CPSRSTd4gHeZN7BCVY6fcOXn/rhtGenIL19awZSOSgjDTSFTc4JD5WSagoMLIQu9sZIOiuqQivnPVYMHxcoZeFg/i48caJo/eQCiFkBHQCWMgB3SDYcALIQAdi+s75o0t2SbWrQFZA/azkSA1YQGJURQgCIVGEAgiEUigRPGbizBsqtFpR337KzJuCRUxgOGZ68j1XjCveztPrGxm/dbzdR79S2OoYxPkUt4KlBMDMyjEUxdRa3m2Cj+3K8tlRNTyGSEOevBEaaDDlEXzorYkjxmxSIAnTRkbgEGDAJQxA9XZaLUr6grMnA9V93IZK3H/I0FA9MwwiIH+wwagAfnaLtgtiqASKvijH7XILd4tut8rvVqkVJubfObUg6CkXOoCNGX/3Tp+WgJJ7Pcfde+vsUAbfeejksUQBnHJ5szDEKHEd4+AmAiSm4NQC5AFVHN1osXru4jE/NNg2QBwWZGFFPS+ePDklzxiRFApJm3BwcQiYBfkK1cgCZTH44fk1QenER68Y6+zv3y/v2awTIKuoKX+HMuD50lA6ikYHyiVLBl+e4lhejbXqPWvmFOjIEOA6KfDMxf30cC0GAC1ffezdWx+rF9Te1alOuQnL8X5KjPmkt8RtKg5Mm92UhQUismwjGi1GRiSvsRtSMIr3Hrt6UofgQHhgReCHQwADSioCpcLURubE/S67TC17/NETTtm2dc2YBcBFwCQgXjV8pSiLWlEDNM+qm6dSxAA0edwCNd14HRgrN3Py2e/sXMBOd3LXEPXRSQkHHIBVqFIFnPHVs6/euqni7lPd6hVSFYkaElsMk8XEwZGMbMdP0/IbeOxCjCAwvAiMgI2IL6W84K366qNs6SmFDodKQC+hhQqWRJssOOyZkCCKS7uXHr7iudOWja5/6oGbT1s3aSkXyLqBHWa6ZaFE1phgUhWANb9d5bvBBISSx8nXa05e5Yu9l+04JS/fQBwlYPJXnuiCnTzOMckUOZcjLdO+93OQMdiHP0nIkKVFFiQV4d8t5F0qGznebrqGC/wCpKJDIoNxAQtDllCLZDz21MSAAwokJKgJqRdgg2oIqAKEfQn/gcwxK1b29C9bVa1XKs9ec2i1On3hNWetnYzyRe0yCmhA3g0fEzASZzFfxNqIVn5NXOCDMAv1TMAvd/fGZ1+7b0FKg9zk+O1tLumCx4+ANRxxud8H715+9z0f9+yxbPPgfnmgpTFhJAZHKHI5C5vmFskAFf9Qca3NURRRdCcdTTQzTpxpZqkR3XHiauJGLU00CVTc0nLdNYgJGsCAMQ8TIUoSxRBIUEmQtw8UUdD4QIgKKqCCKPh+llpllX6wLMsq/4HlX/Cc27uovdPdtx8bck6fe7tnNsscaEJuLRAemBI4WlMJ/V134ekjBFFnDhDlYAJE4CSFF2hwtRM2NzdlMk2FQjaTzVbWVKfT0eFVlq6svXP80SUP3zsnWVx7ixUXH8U8rrPRC7LoWEgQQWuxHTyMgAHsjrNr9XiZLnLlciG/eB5UOEXorBzQcgaz07B9/eLFv+abC/m5gUPJQAQo/p8cR7i4wbtBWiy9GyrcBLZRbqGg4krspnJmSDb1v3hs+p4WDwyAjDpKA3soRYCdwLYbL21qyuQLWewRnYXO6nSglBCMfAhnrOZc5e33JuY/fP1ZdQgVCAFIHGRNfyEjgq4oW+InQ9LHa3NNOTormF1rYPUlyrvBTOTJYVtThWA084U6vsthiQHdtW7x4vUvOmedLaJANpXhy3hIqYODRjPIUMWVIomLxSnkEJlY+836dev2tr+nuWkiHsiWUWe3tt44t9CUz+cy+XxmwYLa2kA59FlES2RulQicezYm5s2rnlzUYnnzL7nkvIRnCRGoXI8Rok74IBmu50IYsDzHSJ3TPEpVZf2rPa0tDGm0Hl69pUJTCNolgK/7BRslQCpROWRDbIJLP7948bqDF7700hLwySEHvP1HOVKQNU7egMwTuKwKVcE4gQL3HPCNy/q3rZ967ZkHb+nUPE3iZzTP3ZzJ33prPpNbnl1QW52WmOTVMRTM430Ulpp7Q5LrvmfjXWCnYyIN8Zed+8ntNyV1BfcG9waPNVXPhMW0PddGSxaWFUskbp+FKAGiLBdINIbVltXX13PhOc/d9DrnOtpxyQ/ha9E8QaKpaqa3Pf/6OeMD7zw0RwjjbOGNEwlfJnMIlxnDumxwXA8niDkMkhImQMiV8/bunSjc/cR40IAAkFs42P9sLpfP1JAB/pt46mKZoMn1TspCw4QhgfDQwL1g5IGRGlnw8nM+uS9huSACo673FJ2FeLV24TKkxBVCigTZrna5koX0ZUIWqSBV+t5PB8oUYweQvfHNbZCMLKvBwxJN0hNmFw0O7f72g51Lfn9nfEm5I2FSnwFP1GIYeZhNWghkshLYQ8ECfQNHCVz59S9ev2X16u6Wzmz/2oaGBYHSmvMRJsBBUk5MUIGcJEgAfb/OQxdOi671Q88yxIEHRua6AA2AZfhaRJl1A7QP3ChYaxpkwXbBiIZJ21JcZ43A7mRrEpqUaAfJdsBJML51jfIE8My2Ia01Oh2i50uLtzhVuaHBL9ee/Pz5dYuPLvl95a4VnrPBIVkctGAaWTgV6DD76waGGXSRY8d1EgwVKHCixBMIbJW5sfOOPPfycz/e3rkgAkCgh5sk68zBkbkCOgAzKFDJQcEzjHgovV0PAd/CvgJXnYAr5uCPOWXVSYdNMlBryB9EkAWN8wgoAGLOB4/aK1SWETTWU1MAGHbUXRQGMNhfLB4r2ECvHWRdxKcyC0c7CoE1s2/93m+Prut66JG72o1qPGFDCmPQE1ka3zH9wrnHaHl28Rwl9xpq+7KtL+M/5nh/QcS7CmwfcAdsHSID4w24uaoDCbiRIBEsXGTwgOu7bgTDRUvztgcuJF2LL0OWUQvAjoXWZ1wBl+vAdGytaGtta9vztZOtLKdnaBeLr2xHAa52a7s/fb/ZdzS+aHcipxGu2K80YNU82zF664L2dqd/7LV9OxvU7vXv3dmuPU74N1klg+cU02TlifTOUGE+3CvjbVcSfGxuH9i49bnV480BjpPUAhLjImygBxsuj9NJxgLyQom4VISJaz88AXC5pc/CNKsMJZTho+ikBhNYYhxKiz6BEuKw0a8AWdtgAgJBpTMNSUXxyHpphxeK+JZPf0wOdj2/bf2B7x9QHCMXlYMTo3lnRbsVLJ9+bd/JSmjk9PSaW6Aj6OsMAZYCVKMJqcWk2IQt2YU9HrFwpsKpgbficqhM6mteWrly5Zr6gouIcBkihLkkPsihiUJwKQUXpsdN0kgbGOcd2g8xbO5bRF9AN8CRgMTNl5eh30YiJa74BHHjAiGofFSggq7vXlfZorlj2PQbANa2Unxn9tEf1+R6v//+yZHBWwNAqP/y4MhQ7S33tG/Qau2xqX1dCxRYbT6+Z76jDYlnsmL2SKDAZ1FSi8uQgsxYIarAVYePM0hJHR5ktlS42Y6CC1GYBPDYOS2qQPjgVmDRLwAYa4wQSQOtPRst2872PUBclnQzOcnzb7qpHPR4tkmuIkeeC4ieTSZMhQxVZDMtnpgAJmpBFvtk7TXhZF/f5HW3XdFQufPgwbUbVtzTTnm8cvrA1LcNmIL5J0+UEy99B21PScQU5CIj+paSmWe8RpkqAde4gFyIKHiXAfi83Spvm5xYZFMODJPwD4ZIEzElMpgdkHdK9ARmSTNHbrHduX1DGhjRhdIFEOTknJsSlhYelIjA5sXoYMSiDXhWDa9c5kEg2rF9TV2ABF+cJL339OjExEJ9yZpryt4MVrzdDqR0qO0gIhtopTC3eWxmvoKlTJOgHVoETFsqY5FdYcRQIiTIXbi5+5bbTVQg5KqOkcFmPJer450UIwUZQIIGeDoQA/wU9wYYTG8OPOjaDUsHgVeCAbNn00t0Bf5cyUO3JvZWZB8rJ/A1VIFCie3qyqznKQ7YyleghpVGUs7R9d/3Tqr3tzzx0RrIQStirj44O/XBojTREvfO4x9LkCF+A9yAF+zIJXIUxiyZYARDKmTr4CWf39TJgykkcKFG+zrqCd5i3CzePRA6KfAYLDWbQgUTgXd236Htqr7JIhEEwiRB00rclCgHXpLh+zZ/H0hEaZ9cKFhSat2Q4dFQyFEhplAUkVKeVn+smzo1dP2PW7eu6rlfi4Rqdo5NTfdHiomYaqdPzgc+Bb9gSRYUXwLfo1i0WAiktEoMcUYiwTsNyuAySbJVoJBPJ1o7ekfnFsUgEcI1ipAMu+QWtoHNM0D85zLtN/dN+qJ5BgFlG0LISwW//gIOIq1cn0N+xGUylPjUCQXSVKAqpAMjmmQRlu+8um7dVF+me+vLz63qWQGpqPjFE1PTu2s1eUCB6btnWywBFhCrkCIFs8xivUG8R3qkbdhIJMQ95AG+PJSYZ4IkObEsf7J3JCteYcIka4mPNireUv17SnAJHeZvPa4bL52opxCocSYt0hek+OPuci9SHlGCCXLloyZkWD6XzL9xs4/JkfJ9dqlAa4yrSEW/bjvw2WhL99bnNo7v0iBiaHpqbGe1IjBgJ6nRWNd8Q4wuAmTiiHELqbVxHTTM22CziQMmPIKpjAX3B2QKQW4k3cE+Hpd4cLC4gdLysIGSBWTyQBMFEXtovTXu2OmlEyGDIDaIUvI1EcOwym6GMBQ9gZfPF/CqUPshOcFIdT7wUPsBWriomVArVXv0aH91tuGWlftXjS9rD6Llxw7MdtUE6j/J6T8QWcSGFBicRSoEfRCYlmGoSAdLDboZK4qPqJJ8kQaIgok1dLBouO9Lxftqnq8lHJgDkrG4AZR2AFMeHlBu2/btoWlp5VMPkmTtAd/DHy5a1AAvRglBHCI4hpGPll9dAJ42X5KOqAfGkJlvDm8Zf+SO9hW7Vj70XbvKvzA725UlGJMEZfpY13yDvsSQLtklTpySKT5kGAkYQuAgPFXRHZCBPjmPR0q8rqUyeC3q7R26zTx2qXM1hSCHIgrC08TrKoFNlLAOHXF128hSrDBx+y6yoLX5G9GCIsrx7Sntt9LkCCGriGUIOkK/OldrqSgCMTa50eRizzOH3ll17wag/WHFg28HmdOzB75dyxECC0rw+g/Elix7MQYYIoKANk1WpOB/CVTISEJ20DqhgskiE0mSgMzC1fm+3sF6+gT1jwughQQPtZwMuBsySdl/pF37I9trRNcS+7QA5gqziih5XXbOTRa7I3awByXAsx36VfnYU5EdhpGCKEI4T+emr7oH7lBR4ETqw/d+a9h+fPaF5bXpgFwV110T0LHTc8xmYpTxf9jCTmmkxA5LQw9jhRwpESctiEIeA/LJA4UhOwZ8I9N3avJGikELE8iStFDBwCdab9Wwlfpi/wrfH+2rZp8iE3j85VMZktgTAnXQgi/GVdBUHESnookOaCHOVTpihehCrn13x7Ijy6ATTmh7962fxmaP9acDSkKQoiJjfrT8QINVXPpIpBAgnXGfkmWMEh2mpYtUQBC8kvy8qg6iAHqGTbqECY03Lj01cam2cSsl6LWcj6kKSRIAWNPx7T3771L2ZF8VVYEuk1SIIlKC10COdAW+AeDEIVlQwC29NBv9OF9p+RHmgwsQ8cYzj++/U2tohOCrN7196MTu6iDwg8i8VBypwI8w9sEH80kCmQlQw0r/j41AWmIYPYiYStEjIXEiySDBe3A+1UFIoDA8xgQW5GL78EhT67868DVtn01owSx/EferPUt8NTnc1NYmi6hYkAcTD/giJACPVTJRViG0YBgFHCQM2ax/ttKld0QKvV88c3hVTwv6MRiEzR1Pv/b4e9WKQENhwEeFRCqWTzV4JdyCGBM0zSIHMiAGL2k4JZKoCioCGVpg8nDJg3lzjiIhngv49ROnlmbbtEu7xAEqHw0uM01tjg5Vj99p2x29Gf5u5AAYhALY6CJSxTbF3+aUJRKEyxRjOsQRx+jIN1REgA4u1+74auCdFX6gwhhvqe3/bHZs7yP3CC0BHAgQqAaDK/p2eo6CVtiZLsLnUMkUxHyD9JvO0qhRBTfNa5GReHTwzCn6KWTyYCKj1o2jw0vzbY2lSEmVS9IKBncJahwUhertlb4aHM6SCjMlgua5qKZlE2QcimJSquLssnIsL4HGEEUqjCLwlOvUIXXS+di73U/cFbVhCKmm/9js8a7MjpVeGFL/YDTAZEITQvJTr3j0jpBDICMi1nSRj4hVugTfdBqDg2k2EvQOz+JxwhUyTCpumQwV2kigsePJvlyj2NIDFkiHNudlW1ZdMSgc3tIWLuzNAQtwI8zB/WVv4DAgMQveULER4c/eK+yUBEkpUgBdyHooa9/dtGTVEkWiYrhD7vTx4y+sjaJNPYq+kwrgL0zpdJQOwUV0GqIgOqEGHawFM3NscJe6eIkhhSJLUAVZ4I4hDOAlz6Vtw4U8nAUPgv2B4d5bq7CHUgT0e/TBMLoHaix0Yxi1+XsGlHqgN5fiJPymbRGBx3EsXhHFYUzcYX0YQQLEqfFRvQYDuJAaAdzP5VWYeuPo20fuxM/EfLwti/3z2PKaOCQV6Ri6CmK+JR0xujJSZqf6ryUneIUESIOaMEXEHsU4akiQZokHqaIElAAeknVyp4XEO06QwGRTF1QEMre8heCiWUIURaEUC7+VxyBYEWxijGb2O+Gtw89GArQe6EQKDHNkIQIf0m2QK5TKwpegVD24SlEWqLMZ54sdhwZ6rFQoQSLKHsT+ubsWXpRufqwnIqcRxJCKWaVSUQpIu6bLuNroUgEyeICP4Cr5BPto0xnYH6FiIUoSB7GK/sDtk09eTcBkcCQDUrutrVpunHLDp4aa2+QOSpyBNVPxpGg2gJmNLY253oVcYdkZImGDsYBMBT47YAdRTD5SaMQK34Kw0Afbp2zyMzs+7H7nnnQUC3FNoydmp3cuqJE3ZH9+iHqCyuKaOEWGU6kUttbsgTfLI1IDvOkIdZyOjRzQBQLkgmEyaDJyMd3SmZCjFHgwTyvNk+lSRBCLa85TNKvNfcMdTSERgoPWNmBVZqcELvoJ0cw8MT/MDS9EB1eeDKQIiFmwsyP2MYCEpkyJFL4S6MRcZvRkdjzWs+pBRAFOSV3U8fTxEycbwEsK8+M9fz8SwgTaOE4jg2X5EV1jFwRpQARuaAfLzRkKVxpWEJ9BTjmAJ879t0/BUAkTK82HdHKgFDGUkvbRUKqVuwS9Ye5npzqq2xgyJe4RPclgxGukKCDWtd13hJuHh0JwQZemHkIeg0Ligj6AR8SQZhmnkGI5W+Gz/qiqJkxVv7vpt/3LVD3mAVLT8heOj3W9ElMFmFwVzvzyOH5iFb2iip4CVmJMbNq3syUdxOCC4geLEfEGoEswsx/Ai2JghUuZIemhKqgHy6WP2Hwsxcrl0zYjA25+rbZP7OaO6aKRJyeam+Hr1AN8QLD5UQpmIzrQaHhkV31huAM9GAN+KgGoUrQiIwTMqiIw4yJVMcax0OBCp1JffXN41Z2dDqfFcdXCz//4469f3qsl2phvjD986w5OBjPpmhhcR4aKrn0tKpVOpdMcoN/AimuRpY+Y0xg3ZERGDtSNkYlp/cPU2YXGUUVxPLJCF26KF1NLycNMmMvGgc4yD126U8oSGAxtd9O1okRNFWNpNUXSVlJr/ahGY6HVBhP1wSZQbUTrB9aCX/TFL7QoKlp9EB/1QRTxQRB8EMHf/yQBz8yce+7HDP3/7/+cnd1C2wML+qIhMzHgqQurVPQaHfbBqW+SCKDOi2fIGmIBahrtPnmBhoiYNi4uTfUPTe5y9AQVnOaETIxgTmIQDoEg4+kncWCf16x59eRXp37csnO/LXYf33n4/hv4R8u/W+uWb+QhFy7t5rGsBli8nmcZMZuef++agVS0JAB3NLEkQTXESydyEIKBW9ygCWIrmLQ6e8TC6o/0vDJaVsAGoamit2XloJccaQO0xffGg7OjobCUaLQb/W3J2zYeOqw0vH4k3jz+YCqFaC5YRjiIcQDkkiyw2AwYThjFTXxGX0B3N4dGR7cWzg2feOft8zfc8Mvv3/z0Q+BWVvCwmZuvVEXBUBa6MoaSJ5euHkj7k1oaeBzAAlhBFxIFaQ3WzWyQPCJM8ZYbAxpS28OXL7FBatiXTqxSuQoRwEFvo6XiWFFdbDQarbb0AYaDF0d9v3K5wTZnRNp9hcZI/xen4ubkS4E/MECdKZ0DM8+4gBsC5+JieSowVp2ZOTZxY6X27PjS3PDIwonZz08/88Iff//+17/fXOKeEBw4i/mp611wiT0yTeMkII+0efi9axPVhTgwUEMdsAA+cZKkYNYHCm3t/7VDZ8qSyrJcpIpeMcFf0fCDsH6TbUkafVYohR0SkEKjBRdWHPjimC3Mjm8qlCFIASriIoODRtyfZf1JFr/wYmVw8qViFbCcsNYgK3BY8QvLM4zISy3R++dvmziWRNETB96enntwdPad0wsbQ3L515+/+fK3r4PQG87zUxXhVxdLiBHHk0vrBqwyBK2iFWhGLCHUSdfXJJZECrEUMSpMFrYGrqgVGGT08eEhRXC1Wq12q1sBPaWgrY+GRhsiMt4T2sTQkXUmJ7dXXB/kFA0ogI04K8gIlwFw8c2H3J0HmskKBxwuEXQlienaKbYJM9hwBT/X7T22Vls/cvjo0vjCyNiY1xNq//z5/Z9fJ0GrjY3zdwykIYgCKUUfyDSf3trjGBGn9mQ0wTmQ1mIHEYza9psRpUaLkZFaP1HVEBWrn5sWkBHdRqu3Cw2cLfHRl8EDh5qu6kOWtffPTT67tsxi4iyJMxjI+vPM9smdm9jiRkd9sbxxDhCJyxPN+NQ2Ulw4+YAPjhXPnfzo+JEdIKS3ffro3IF9O25cG2xObwSxS7klAq1rnrw75eY0wFqq6QBL56apjqIhSWPughQyI3jEEBxIaUANI8ItE1OSjHGgIFHYs7b3Kn1i6BdZ6gMeVUBEt6+dSQ8tGMkyOOjnUyPD0MRAVhS8eJ6rFHGusQARedZfxIKXRIt7d0NFqV5gCq8wYk49H2vA4xiw0w3NzNx+6h6NxD4a2vXBiY27Roevr0ATBlpbpdyi2XbhWOKlB1bzEOjBPfbyNUEBuGEpdTW7LU0hDSc60+D1KUvPqBByCJBjTCFtzzr7lbIFAzgOhNDq09ntNqDCrm5Gm4mRGI8RDt95cd+aEiawwpEZ9ZWykN83dRdUbE3i3P542ssi0Aqr2kQeMnBATaoXHr9l4sbYmNsw3Ons3zmw5Zm3P7gJvHYPqC2IrLfn26fB6RLFofA+cOyfTteFRAvNPPgTNFOjERXUDk8XyOtXwNc81SIljWBEC3WQIOihta4lDnCVdksVAiXg2i3lhHHAlYOYSN468ebTswtbijzPszx3eeaEPAZhseHS08WJ8U2qAN5UEZelk6XGRYQwtJkRnun351978ZbeiDuLzRDR2X4TOzh8eHofjwKplAENCbF1F796KqTGg3nHM5IP3+3RItMQJ1kG/phc8bxgKaakgBzcsOMTrgA/iEGVVrV2WRk9K3+X20UUraxV6TbakkILEtR2EYRx0ZXPjQrgOxiIBw9MHlxTuLyeuwwaCldkrqApv747OjC+EQo4JGEjIkCBW903w6CSsXj+8otTFRvbPsL/QzTkEy/d33r29FZwAVRTNBZBxYXLtwVaFmGxje05u7XirFxiXsWVO2sB4n0KX5QKl4olSUPG64aV0hR+gkgwl0LFOgTR2610u90WgLtoQaVRflUSecNZUIgMVADiXDZ44PMTD7UH87rLC8eUOJK9dqR44OJmRblDxInJ2y+7iAsrfQwZz51cPH58iwdQc7gDEZuSUDqtSUeWlnYSgZSuFRYalZuX39hNSJ/DIyzAv/XuDkdr5SEKYh1d6QKgjwhiLnLBIxgPeIxCaomBschaaoVerqkMMjSAHnAA58BjOFkBBxIEId457X8++MDs6NUVDyvOw4aBh5j7Tg08OLcNlRQMmtVpo8iVXNpSV3JEZx6feWTvbQK8YXh/Z2ybT0LksQio1Q/OvsRaLcdhgBNHYebS9ZEPWuK9TSVjZ89USnHGxSqTEUmSelf1GkghXXRAjeWFhlYtQTNqanI95IWsCxPQYGqwnBAbmLzxgCKKOi2KkM+ADDcHJ8fXX9F0TLnC1XMQQsqeicrC7LZ6aUKoW6EgVMfQyVVd88LjT008TA2obh451Bm7aX3q2XdWBC8EC0cPb0JBVR+WSQzgl6jmn17rxYk90Yh998MdQbPcxKjqhJizIcEnggqfShg4r1ByoeeNAQ4GOHtaxgUUdFeKZFf4Bd8MuPI54sfT1EVB3ZRRZ4AXjOLKZr1ez5scNJyLj67bNTsiqbu8LKO8Wfq8qaTwtnUlgJuvzL8+MVWL3KYnOoc626rrE4FjAkVrjRtaOttB/1EV1EYQNGniw6k+KPMrSiEhhqfPXBVhoopRnV7FCBdkREEhZTr11EwZQ7ianEgIGCW0pwsR4F9mgaudFcuJkYsPicF0Dg8gNyMSDZkNuH2zkxt3NEsHQ4yWkNJ8Zu+OfbOderOsNxlgirG8jAYHo6ofjEqOc5/yc10lhI3DI/s7230KTuakgipR2QT9Z0dPE2MMAdCIoDd/t5FmbGh94Bd/kWBdZ970g7ZorQQTgtZDsmkFWTCAWSsiCEVKj16ouKSJ1cwQfqA3cEZBVhgLwNeeg6yol/oQWca4c+7i2HUDg0xS/Zp12j2339uZ7eT1QTdITS11NMvBEg4ABxdj858cOb672RwaOXRorErCls5Q40hvgq1c/Cukw/RNKeQJM1Xwbjg5pf32Sh0rFtumn7tCWthKTN9YAba6gSCJYpCyPgkO6EkANJcDvCKdZoG5/6q6qlAroiiq6MfI/AwWg2BgHISjCB6dUQa9eMFCRcUubNQPsQMLExsTGxOxE9sPu7Ab7O7Exlxr7zuo597ZZ5+9z328tWbtfcYnj5eHxaFP1mJZB2IhCRYE+wImHxGAJBW4y1IQesUmxsPWoTrVDFnChVzdOyMaTu8Vx9hnYgjGQA9BRUNCvGzWjhu3fVr7wIxv0L1X7aRckPXIAcApaKEEyKMxE/tkNSPDCvJ640YKaQFx4z57q4/mLY/Wo59F2M91XuiFAuHweECTF2qAnpSIksCDJOUjjxCgTPx7XLAAtDsAPzsEIuQGsw/7qKKPKslWNGDAH99p+qIC+cKKWXIEyOHx1nNONjFZ5EAVBoJ4wYmzduvkqd1mlHeN+qJX4hHRM0bAJyyEXEWICLxVE8c4xFQRiIpte6EVdwhZHPVGz63FpMBWTYEi+mwPAaMiBzJdXqsGCx3lU58HCwukSgWKgr0ybZNgoBrIENzaKCl+2AoAhgng8SJsXZhKUcu9w+oMDwE1joH40baOjaYvMmFMJSD4iGzEJMVjtxxVyDbs3uSQK1MM37yDDpznAJTYrJdg7TuWSu3RKxoJB4pabrm35u1U5nzEBCd+jlea9GGNqO+pxyRevpaR5jmJRvwyPGRPPX68rbzwQYMTpwSo0INDFQESVBZwpDmqCKQniA9PDCCSCvGgDTvp5KR2jV1sll1cX9SvuGzj+JN9QuNTCKwRAw5LxL7psvlMt44Fx/ftPrstKiMOTQkwBYAYAAATWE46wi0T9zEObFpC3LVrbXspBoagnsz8KVXFZYST1g015Gu1lWFQepAk07H29a1bT99v0N4JATEGVWhVsEtiUAmETFWw+LUlcOZRQPwsCdG8QZ8IFamJJk3v3LhrsPjIm6fPLuXr0tNOX2VDYOUIhQ8Tth27u8eExnP69u1bOykfxCVMXCK0UQnPWM8znkCxLoUEM3vogqUqB40x2mZdV3q4xLRZsL9MwjRjdFQtiYIHyYjDUVUAdUJBUAr484bvnt59cAkLkiF9FVTo03U1VoUenrlzE8hBAnyBDwPMPmlQ+HyFFR9hElJWTe/d+NLNpg/f3X5wbkq3eMekEBUCGqRkMGfGjR3Qs92c7t171UuCOPZCz4M1jmVjDSolB88KevEWTFwER4cG48kbqvOWyyaLHVPympSWgC1CdslpoxTqKUsvkV7CDKbkc9PfN058PPF8WwBhIIUOTFVog8C/LYmfKqAj/cGvZEQUhng5k5E41B4InJogLwDbfW+nZb+bvrty5fT9g9NK7ZgUmVg4CEODa+vY9j2bNWzSpGG9IAmNJxnAB1skxTovFrTO5LiwJTzbZsgWz1rgcUIEzYHjhekwxHKZXxgFIOIPcvJxiVYUVYILadEJmKAVLuAlj9/dvvLp26cTZ6mIRIWRB42BjxLoiRWUCr7QdLflq6ZSgDgehXRMJQEAl4OWS53IzOwd0yc/Of/rI6iYkH/mwhADCVqzeNbFaSPbDuvetnmZLD/CnOMjFzZkYws2bBiEpAID99wK9EbzVjQkQ6GQBL5sfHRGUakOUhTgJ/7V41REWlXNVT/gw9dnkUSwIk8G9LBNsLh2++7pEyeu3C+KGLcgm4AKVAXbhBaFHhaDbn69fflc42ppHagIqIcwXemAL1gNTYO9099e+XD6xvu5I5vNXBhJmOkus85M6zCnSfelpYOYu53up1o8MGJDYOXC8SiBTyW4EOExQ9oIPwxZht2CGej/cEU4U+bnLVmC8f+qSBswXZmcyEVPJ1hWEcGv+3H3xo3L69kplAqqohoqQ2sDMhBR+JkjTR++eXLj/qWAupDmAA4UGS3eMgzI4SR+FIYNd0w/fPny9/JdpraeObOeVEZk2uI/NzaWb9KFLcJyp1WC/moG1gMRFsRYysJ6YIitYNHEoxkBbUmIhSyOtgqc+EhHC3bXd5JODTN6SXlIETWXBd2gecBAAqX4QH5q3bprkEQSUCYM5qEkpE3CVAByNoTdTfH3Y9/dPv/qejYVQHrnQx06R7lARV3YEL+4cmx9+Xq7N0zYvWAOQFtTb/XW1j279mqEMwM7uJtWR04b6tLzJAzk+CQJGD9/4mwHAlA3irPurFY54AjPnle+JP3mTGKLykNY02VAD/idqIKc6EgpIwUopdyFAuEDM4qCA5bHZDy2adMv50+cf3n+nq9SiHPfdLpI6UjnlJOo7pjRVw8POrJtWt6FwzJhmNm9s8WmZofaBgn1obsj7FZCIoqEPigAesSUCBsaIKVK9g1ZGHmkggARajuuRWwt15DF5tX5UVFsoFyzuKy62oUZJwOMl9BK4Z4kTRC+UgEPhqrQ54Zq+vxo8MpeeHjr9LdvPx+cvleN+BWF0Vuaeggzrrj0RVP3KhQ1JO5QOW7SZ9WkNu2Xj1paL8gaJIlWrA4giv5+RQYcCXFI4OsADTc2GDq/tgVMouM8e2sz5QFm9rykmicJ0YAIAiuGVCS0CDASMKRBl9BLmvMZAzSAGw7ykaeSiMH3+URIHoj02u0Hn779+nD6wSUBrXqITBRpjSgAWuWBQSUDgS78nfB622vmqVatRs1N3Rq7xIXKEvdyYBK8woomVBO0LkIqsl4mchne+SGrMs5GvOdUy5SD/TwwIsUwZmd1hphQ0BaGlpBtcxi801LgQjwHzLnKED2wYWo78fLwAYJqIBUc7ITZDa8+/Lzy8sblbbkWIarm2+pNTaERAH3VhiYgi5W2Zuuyxft36DmyeeBQB8hnmM2EmPihlEW4ohPuID+yF8jF41g0ZGAG2PXtwtXLutKlotasCErj4NWVzVhywZwTyDIzp1phgM0CyJlNSEkCh5JoLqzAy+OzKOR6ZML0aSH5cfnFjbvv16kmorQciBpWAoqHAfpcqLFjmw6ZWaH/ku3b9wxu5f6BLYOTUoChTGhU9aE0ZBgBGyCh7rx5vSJnM7zfWB5bn9fL4GYD44GdRZQVYtf2wX1YAjQXHjnM5LosB1wtJZSHemwliRg+lv0BE/W094nhhR4AAAAASUVORK5CYII=", "public": true}, {"link": "/api/images/system/simple_volatile_organic_compounds_chart_card_with_background_system_widget_image.png", "title": "\"Simple volatile organic compounds chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_volatile_organic_compounds_chart_card_with_background_system_widget_image.png", "publicResourceKey": "Jy5Hr0o4xtx56RzDt8SjYtmh6O38zpVt", "mediaType": "image/png", "data": "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", "public": true}]}