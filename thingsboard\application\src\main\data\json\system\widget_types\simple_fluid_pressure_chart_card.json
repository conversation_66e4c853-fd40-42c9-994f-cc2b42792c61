{"fqn": "simple_fluid_pressure_chart_card", "name": "Simple pressure chart card", "deprecated": false, "image": "tb-image;/api/images/system/simple_pressure_chart_card_(1).svg", "description": "Displays historical fluid pressure values as a simplified chart. Optionally may display the corresponding latest fluid pressure value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pressure', label: 'Pressure', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'pressure', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Pressure\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 15 - 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 15 - 7;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 20) {\\n\\tvalue = 20;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0px\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":5,\"color\":\"#305AD7\"},{\"from\":5,\"to\":10,\"color\":\"#3FA71A\"},{\"from\":10,\"to\":15,\"color\":\"#F36900\"},{\"from\":15,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Pressure\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"compress\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":\"0px\",\"units\":\"bar\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true},\"margin\":\"0px\"}"}, "tags": ["fluid pressure", "liquid pressure", "pump pressure", "hydraulic", "pump", "compressibility", "compressive stress", "pipe", "pipeline"], "resources": [{"link": "/api/images/system/simple_pressure_chart_card_(1).svg", "title": "simple_pressure_chart_card.svg", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_pressure_chart_card_(1).svg", "publicResourceKey": "4PS4W0NssnQ2aO72NN5dyGlMfAhVM6TH", "mediaType": "image/svg+xml", "data": "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", "public": true}]}