<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="400" height="200" fill="none" version="1.1" viewBox="0 0 400 200"><tb:metadata xmlns=""><![CDATA[{
  "title": "HP Control panel",
  "description": "Sends the command to the device or updates attribute/time series when the user pushes the button. Widget settings will enable you to configure behavior, how to fetch the initial state, and what to trigger when power on/off states.",
  "searchTags": [
    "control"
  ],
  "widgetSizeX": 2,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "element.fill(ctx.properties.background);",
      "actions": null
    },
    {
      "tag": "label",
      "stateRenderFunction": "if (ctx.properties.label) {\n    element.show();\n    ctx.api.font(element, ctx.properties.labelFont, ctx.properties.labelColor);\n    ctx.api.text(element, ctx.properties.labelText);\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "offButton",
      "stateRenderFunction": "var offButtonChildren = element.children();\nif(ctx.values.value){\n  ctx.api.enable(element);\n  offButtonChildren[0].fill(ctx.properties.defaultButtonColor);\n} else {\n  ctx.api.disable(element);\n    offButtonChildren[0].fill(ctx.properties.activeButtonColor);\n}\n\noffButtonChildren[1].stroke(ctx.properties.borderButtonColor);\n\nif (!ctx.properties.label) {\n    element.transform({translateY: -37});\n}",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.disable(element);\nctx.api.callAction(event, 'offUpdateState', undefined, {\n  next: () => {\n     ctx.api.setValue('value', false);\n  },\n  error: () => {\n     ctx.api.enable(element);\n  }  \n});"
        }
      }
    },
    {
      "tag": "offLabel",
      "stateRenderFunction": "ctx.api.text(element, ctx.properties.offLabelText);",
      "actions": null
    },
    {
      "tag": "onButton",
      "stateRenderFunction": "var onButtonChildren = element.children();\nif(ctx.values.value){\n    ctx.api.disable(element);\n    onButtonChildren[0].fill(ctx.properties.activeButtonColor);\n} else {\n    ctx.api.enable(element);\n    onButtonChildren[0].fill(ctx.properties.defaultButtonColor);\n}\nonButtonChildren[1].stroke(ctx.properties.borderButtonColor);\nif (!ctx.properties.label) {\n    element.transform({translateY: -37});\n}",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.disable(element);\nctx.api.callAction(event, 'onUpdateState', undefined, {\n  next: () => {\n     ctx.api.setValue('value', true);\n  },\n  error: () => {\n     ctx.api.enable(element);\n  }  \n});"
        }
      }
    },
    {
      "tag": "onLabel",
      "stateRenderFunction": "ctx.api.text(element, ctx.properties.onLabelText);",
      "actions": null
    }
  ],
  "behavior": [
    {
      "id": "value",
      "name": "On/Off state",
      "hint": null,
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "On",
      "falseLabel": "Off",
      "stateLabel": "On",
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "value"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "onUpdateState",
      "name": "On update state",
      "hint": null,
      "group": null,
      "type": "action",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": {
        "action": "SET_ATTRIBUTE",
        "executeRpc": {
          "method": "setState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "setAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "value"
        },
        "putTimeSeries": {
          "key": "state"
        },
        "valueToData": {
          "type": "CONSTANT",
          "constantValue": true,
          "valueToDataFunction": "/* Convert input boolean value to RPC parameters or attribute/time-series value */\nreturn value;"
        }
      },
      "defaultWidgetActionSettings": null
    },
    {
      "id": "offUpdateState",
      "name": "Off update state",
      "hint": null,
      "group": null,
      "type": "action",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": {
        "action": "SET_ATTRIBUTE",
        "executeRpc": {
          "method": "setState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "setAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "value"
        },
        "putTimeSeries": {
          "key": "state"
        },
        "valueToData": {
          "type": "CONSTANT",
          "constantValue": false,
          "valueToDataFunction": "/* Convert input boolean value to RPC parameters or attribute/time-series value */\nreturn value;"
        }
      },
      "defaultWidgetActionSettings": null
    }
  ],
  "properties": [
    {
      "id": "background",
      "name": "{i18n:scada.symbol.background-color}",
      "type": "color",
      "default": "#DEDEDE",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "label",
      "name": "{i18n:scada.symbol.label}",
      "type": "switch",
      "default": true,
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "labelText",
      "name": "{i18n:scada.symbol.label}",
      "type": "text",
      "default": "Heat pump",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "flex",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "labelFont",
      "name": "{i18n:scada.symbol.label}",
      "type": "font",
      "default": {
        "size": 56,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "500",
        "style": "normal"
      },
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "labelColor",
      "name": "{i18n:scada.symbol.label}",
      "type": "color",
      "default": "#000000DE",
      "required": null,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "onLabelText",
      "name": "{i18n:scada.symbol.on-label}",
      "type": "text",
      "default": "On",
      "required": true,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "flex",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "offLabelText",
      "name": "{i18n:scada.symbol.off-label}",
      "type": "text",
      "default": "Off",
      "required": true,
      "subLabel": null,
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "flex",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "activeButtonColor",
      "name": "{i18n:scada.symbol.button-color}",
      "type": "color",
      "default": "#999999",
      "required": null,
      "subLabel": "Active",
      "divider": true,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "defaultButtonColor",
      "name": "{i18n:scada.symbol.button-color}",
      "type": "color",
      "default": "#DEDEDE",
      "required": null,
      "subLabel": "Default",
      "divider": true,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    },
    {
      "id": "borderButtonColor",
      "name": "{i18n:scada.symbol.button-color}",
      "type": "color",
      "default": "#999999",
      "required": null,
      "subLabel": "Border",
      "divider": null,
      "fieldSuffix": null,
      "disableOnProperty": null,
      "rowClass": "",
      "fieldClass": "",
      "min": null,
      "max": null,
      "step": null
    }
  ]
}]]></tb:metadata>
<rect width="400" height="200" rx="8" fill="#dedede" tb:tag="background"/><rect x="1" y="1" width="398" height="198" rx="7" stroke="#000" stroke-opacity=".87" stroke-width="2"/><text x="198.7832" y="57.511719" fill="#000000" font-family="Roboto, sans-serif" font-size="56px" font-weight="400" text-anchor="middle" tb:tag="label" xml:space="preserve"><tspan dominant-baseline="middle">Heat pump</tspan></text><g tb:tag="onButton">
  <rect x="29" y="100" width="156" height="72" rx="9.8143" fill="#999" style="stroke-width:.95761"/>
  <path d="m29.957 109.03c0-4.4589 3.9482-8.0734 8.8189-8.0734h136.45c4.8707 0 8.8189 3.6144 8.8189 8.0734v53.938c0 4.4589-3.9482 8.0743-8.8189 8.0743h-136.45c-4.8707 0-8.8189-3.6153-8.8189-8.0743z" shape-rendering="crispEdges" stroke="#999" stroke-width="1.9147"/>
  <text x="107.44609" y="138.7375" fill="#000000" font-family="Roboto, sans-serif" font-size="34px" font-weight="500" text-anchor="middle" tb:tag="onLabel" xml:space="preserve"><tspan dominant-baseline="middle">On</tspan></text>
 </g><g tb:tag="offButton">
  <rect x="214" y="100" width="156" height="72" rx="9.8143" fill="#dedede" style="stroke-width:.95761"/>
  <path d="m214.96 109.03c0-4.4589 3.9482-8.0734 8.8189-8.0734h136.45c4.8707 0 8.8189 3.6144 8.8189 8.0734v53.938c0 4.4589-3.9482 8.0743-8.8189 8.0743h-136.45c-4.8707 0-8.8189-3.6153-8.8189-8.0743z" shape-rendering="crispEdges" stroke="#999" stroke-width="1.9147"/>
  <text x="290.68408" y="139.33516" fill="#000000" font-family="Roboto, sans-serif" font-size="34px" font-weight="500" text-anchor="middle" tb:tag="offLabel" xml:space="preserve"><tspan dominant-baseline="middle">Off</tspan></text>
 </g>
</svg>