{"fqn": "simple_ground_temperature_chart_card_with_background", "name": "Simple ground temperature chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_ground_temperature_chart_card_with_background_system_widget_image.png", "description": "Displays historical ground temperature values as a simplified chart with background. Optionally may display the corresponding latest ground temperature value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'temperature', label: 'Ground temperature', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'temperature', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Ground temperature\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#224AC2\"},{\"from\":-20,\"to\":0,\"color\":\"#2B54CE\"},{\"from\":0,\"to\":10,\"color\":\"#6083EC\"},{\"from\":10,\"to\":20,\"color\":\"#F89E0D\"},{\"from\":20,\"to\":30,\"color\":\"#F77410\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_ground_temperature_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Ground temperature\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"thermostat\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"°C\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["weather", "environment", "soil temperature", "terrestrial temperature", "subsurface temperature", "earth temperature", "below surface temperature", "surface temp", "soil warmth", "land temperature", "geothermal reading", "ground warmth"], "resources": [{"link": "/api/images/system/simple_ground_temperature_chart_card_with_background_system_widget_background.png", "title": "\"Simple ground temperature chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_ground_temperature_chart_card_with_background_system_widget_background.png", "publicResourceKey": "l07rHCllZu7cfhuIkRv593jCfRxFtHdW", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_ground_temperature_chart_card_with_background_system_widget_image.png", "title": "\"Simple ground temperature chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_ground_temperature_chart_card_with_background_system_widget_image.png", "publicResourceKey": "7kwY8anZ4kUcJo0yhJbla4f3cUwcfxcQ", "mediaType": "image/png", "data": "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", "public": true}]}