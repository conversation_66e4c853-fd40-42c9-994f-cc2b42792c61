<svg xmlns="http://www.w3.org/2000/svg" xmlns:tb="https://thingsboard.io/svg" width="400" height="200" fill="none" version="1.1" viewBox="0 0 400 200"><tb:metadata xmlns=""><![CDATA[{
  "title": "HP Horizontal circuit breaker",
  "description": "Horizontal circuit breaker with various states.",
  "searchTags": [
    "energy",
    "power",
    "ev"
  ],
  "widgetSizeX": 2,
  "widgetSizeY": 1,
  "tags": [
    {
      "tag": "background",
      "stateRenderFunction": "element.attr({fill: ctx.properties.backgroundColor});",
      "actions": null
    },
    {
      "tag": "breaker",
      "stateRenderFunction": null,
      "actions": {
        "click": {
          "actionFunction": "var initial = ctx.values.initialState;\nvar action = initial ? 'offUpdateState' : 'onUpdateState';\n\nctx.api.callAction(event, action, undefined, {\n  next: () => {\n     ctx.api.setValue('initialState', !initial);\n  }\n});"
        }
      }
    },
    {
      "tag": "breaker-trigger",
      "stateRenderFunction": "element.fill(ctx.properties.disabledColor);\nif (ctx.values.initialState) {\n    element.transform({translateX: 0});\n} else {\n    element.transform({translateX: -160});\n}",
      "actions": null
    },
    {
      "tag": "critical",
      "stateRenderFunction": "element.attr({fill: ctx.properties.criticalColor});\nif (ctx.values.critical) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = ctx.values.critical && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'criticalClick');"
        }
      }
    },
    {
      "tag": "label",
      "stateRenderFunction": "if (ctx.properties.label) {\n    element.show();\n    var label = ctx.values.initialState ? ctx.properties.onLabel : ctx.properties.offLabel;\n    ctx.api.font(element, ctx.properties.labelFont, ctx.properties.labelColor);\n    ctx.api.text(element, label);\n} else {\n    element.hide();\n}",
      "actions": null
    },
    {
      "tag": "label-box",
      "stateRenderFunction": "var color = ctx.properties.disabledColor;\nif (ctx.values.initialState) {\n    color = ctx.properties.enabledColor;\n}\nelement.attr({fill: color});",
      "actions": null
    },
    {
      "tag": "warning",
      "stateRenderFunction": "element.attr({fill: ctx.properties.warningColor});\nvar warning = ctx.values.warning && !(ctx.values.warning && ctx.values.critical)\nif (warning) {\n    element.show();\n} else {\n    element.hide();\n}\n\nvar elementCriticalAnimation = element.remember('criticalAnimation');\nvar criticalAnimation = warning && ctx.values.criticalAnimation;\n\nif (elementCriticalAnimation !== criticalAnimation) {\n    element.remember('criticalAnimation', criticalAnimation);\n    if (criticalAnimation) {\n        ctx.api.cssAnimate(element, 500).attr({opacity: 0.15}).loop(0, true);\n    } else {\n        ctx.api.resetCssAnimation(element);\n    }\n}\n",
      "actions": {
        "click": {
          "actionFunction": "ctx.api.callAction(event, 'warningClick');"
        }
      }
    }
  ],
  "behavior": [
    {
      "id": "initialState",
      "name": "{i18n:scada.symbol.on-off-state}",
      "hint": "{i18n:scada.symbol.on-off-state-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": "{i18n:scada.symbol.on}",
      "falseLabel": "{i18n:scada.symbol.off}",
      "stateLabel": "",
      "defaultGetValueSettings": {
        "action": "GET_ATTRIBUTE",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": "SHARED_SCOPE",
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": null,
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "onUpdateState",
      "name": "{i18n:scada.symbol.on-update-state}",
      "hint": "{i18n:scada.symbol.on-update-state-hint}",
      "group": null,
      "type": "action",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": {
        "action": "EXECUTE_RPC",
        "executeRpc": {
          "method": "setState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "setAttribute": {
          "scope": "SERVER_SCOPE",
          "key": "state"
        },
        "putTimeSeries": {
          "key": "state"
        },
        "valueToData": {
          "type": "CONSTANT",
          "constantValue": true,
          "valueToDataFunction": "/* Convert input boolean value to RPC parameters or attribute/time-series value */\nreturn value;"
        }
      },
      "defaultWidgetActionSettings": null
    },
    {
      "id": "offUpdateState",
      "name": "{i18n:scada.symbol.off-update-state}",
      "hint": "{i18n:scada.symbol.off-update-state-hint}",
      "group": null,
      "type": "action",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": {
        "action": "EXECUTE_RPC",
        "executeRpc": {
          "method": "setState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "setAttribute": {
          "key": "state",
          "scope": "SERVER_SCOPE"
        },
        "putTimeSeries": {
          "key": "state"
        },
        "valueToData": {
          "type": "CONSTANT",
          "constantValue": false,
          "valueToDataFunction": "/* Convert input boolean value to RPC parameters or attribute/time-series value */\nreturn value;"
        }
      },
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warning",
      "name": "{i18n:scada.symbol.warning}",
      "hint": "{i18n:scada.symbol.warning-state-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.warning}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "MAJOR",
            "MINOR",
            "WARNING",
            "INDETERMINATE"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "warningClick",
      "name": "{i18n:scada.symbol.warning-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.warning-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "critical",
      "name": "{i18n:scada.symbol.critical}",
      "hint": "{i18n:scada.symbol.critical-state-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.critical}",
      "defaultGetValueSettings": {
        "action": "GET_ALARM_STATUS",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "scope": null,
          "key": "state"
        },
        "getTimeSeries": {
          "key": "state"
        },
        "getAlarmStatus": {
          "severityList": [
            "CRITICAL"
          ],
          "typeList": null
        },
        "dataToValue": {
          "type": "NONE",
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;",
          "compareToValue": true
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    },
    {
      "id": "criticalClick",
      "name": "{i18n:scada.symbol.critical-click}",
      "hint": "{i18n:scada.symbol.on-click-hint}",
      "group": "{i18n:scada.symbol.critical-state}",
      "type": "widgetAction",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": null,
      "defaultGetValueSettings": null,
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": {
        "type": "doNothing",
        "targetDashboardStateId": null,
        "openRightLayout": false,
        "setEntityId": false,
        "stateEntityParamName": null
      }
    },
    {
      "id": "criticalAnimation",
      "name": "{i18n:scada.symbol.warning-critical-state-animation}",
      "hint": "{i18n:scada.symbol.warning-critical-state-animation-hint}",
      "group": null,
      "type": "value",
      "valueType": "BOOLEAN",
      "trueLabel": null,
      "falseLabel": null,
      "stateLabel": "{i18n:scada.symbol.animation}",
      "defaultGetValueSettings": {
        "action": "DO_NOTHING",
        "defaultValue": false,
        "executeRpc": {
          "method": "getState",
          "requestTimeout": 5000,
          "requestPersistent": false,
          "persistentPollingInterval": 1000
        },
        "getAttribute": {
          "key": "state",
          "scope": null
        },
        "getTimeSeries": {
          "key": "state"
        },
        "dataToValue": {
          "type": "NONE",
          "compareToValue": true,
          "dataToValueFunction": "/* Should return boolean value */\nreturn data;"
        }
      },
      "defaultSetValueSettings": null,
      "defaultWidgetActionSettings": null
    }
  ],
  "properties": [
    {
      "id": "label",
      "name": "{i18n:scada.symbol.label}",
      "type": "switch",
      "default": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "onLabel",
      "name": "{i18n:scada.symbol.label}",
      "type": "text",
      "default": "ON",
      "disabled": false,
      "visible": true
    },
    {
      "id": "offLabel",
      "name": "{i18n:scada.symbol.label}",
      "type": "text",
      "default": "OFF",
      "disabled": false,
      "visible": true
    },
    {
      "id": "labelFont",
      "name": "{i18n:scada.symbol.label}",
      "type": "font",
      "default": {
        "size": 42,
        "sizeUnit": "px",
        "family": "Roboto",
        "weight": "400",
        "style": "normal"
      },
      "disabled": false,
      "visible": true
    },
    {
      "id": "labelColor",
      "name": "{i18n:scada.symbol.label}",
      "type": "color",
      "default": "#1A1A1A",
      "disabled": false,
      "visible": true
    },
    {
      "id": "backgroundColor",
      "name": "{i18n:scada.symbol.background-color}",
      "type": "color",
      "default": "#FFFFFF",
      "disabled": false,
      "visible": true
    },
    {
      "id": "enabledColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#FFFFFF",
      "subLabel": "{i18n:scada.symbol.enabled}",
      "divider": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "disabledColor",
      "name": "{i18n:scada.symbol.colors}",
      "type": "color",
      "default": "#666666",
      "subLabel": "{i18n:scada.symbol.disabled}",
      "disabled": false,
      "visible": true
    },
    {
      "id": "warningColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#FAA405",
      "subLabel": "{i18n:scada.symbol.warning}",
      "divider": true,
      "disabled": false,
      "visible": true
    },
    {
      "id": "criticalColor",
      "name": "{i18n:scada.symbol.alarm-colors}",
      "type": "color",
      "default": "#D12730",
      "subLabel": "{i18n:scada.symbol.critical}",
      "disabled": false,
      "visible": true
    }
  ]
}]]></tb:metadata>
<rect x="1" y="1" width="398" height="198" fill="#fff" stroke="#1A1A1A" stroke-width="2" tb:tag="background"/><circle cx="49" cy="100" r="19" stroke="#1a1a1a" stroke-width="2"/><circle cx="351" cy="100" r="19" stroke="#1a1a1a" stroke-width="2"/><path d="m0 100h31" stroke="#1A1A1A" stroke-width="6"/><path d="m369 100h31" stroke="#1A1A1A" stroke-width="6"/><g tb:tag="breaker">
  <path d="m101 51h198v97c0 0.552-0.448 1-1 1h-196c-0.552 0-1-0.448-1-1v-97z" fill="#fff" stroke="#1A1A1A" stroke-width="2" tb:tag="label-box"/>
  <text x="200.83984" y="114.56055" fill="#000000" font-family="Roboto, sans-serif" font-size="40px" font-weight="400" text-anchor="middle" tb:tag="label" xml:space="preserve"><tspan dominant-baseline="start">ON</tspan></text>
  <rect x="261" y="25" width="38" height="150" rx="1" fill="#999" stroke="#1A1A1A" stroke-width="2" tb:tag="breaker-trigger"/>
 </g><g fill="#d12730" style="display: none;" tb:tag="critical">
  <rect width="84" height="84" rx="4" fill="#fff" style=""/>
  <rect width="84" height="84" rx="4" style=""/>
  <rect x="2" y="2" width="80" height="80" rx="2" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m44.559 27.562-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g><g fill="#faa405" style="display: none;" tb:tag="warning">
  <path d="m35.955 2.2112c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" fill="#fff" style=""/>
  <path d="m35.955 2.2112c1.4741-2.9482 5.6813-2.9482 7.1554 0l35.528 71.056c1.3298 2.6596-0.6042 5.7889-3.5777 5.7889h-71.056c-2.9735 0-4.9075-3.1292-3.5777-5.7889z" style=""/>
  <path d="m37.72 3.1061c0.7371-1.4741 2.8407-1.4741 3.5778-1e-5l35.528 71.056c0.6649 1.3298-0.3021 2.8944-1.7888 2.8944h-71.056c-1.4868 0-2.4538-1.5646-1.7889-2.8944z" stroke="#000" stroke-opacity=".87" stroke-width="4" style=""/>
  <path d="m42.092 32.618-0.4688 20.059h-4.0234l-0.4883-20.059zm-5.1172 26.211c0-0.7161 0.2344-1.3151 0.7031-1.7968 0.4818-0.4948 1.1459-0.7422 1.9922-0.7422 0.8334 0 1.4909 0.2474 1.9727 0.7422 0.4817 0.4817 0.7226 1.0807 0.7226 1.7968 0 0.6901-0.2409 1.2826-0.7226 1.7774-0.4818 0.4818-1.1393 0.7226-1.9727 0.7226-0.8463 0-1.5104-0.2408-1.9922-0.7226-0.4687-0.4948-0.7031-1.0873-0.7031-1.7774z" fill="#000" fill-opacity=".87" style=""/>
 </g>
</svg>