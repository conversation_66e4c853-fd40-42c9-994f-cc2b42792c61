<?xml version="1.0" encoding="utf-8"?>
<!--Copyright 2021 <PERSON> (Malaysia) Berhad. 

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
notice, this list of conditions and the following disclaimer in the
documentation and/or other materials provided with the distribution.
3. Neither the name of the copyright holder nor the names of its
contributors may be used to endorse or promote products derived
from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

-->
<LWM2M xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.openmobilealliance.org/tech/profiles/LWM2M.xsd">
	<Object ObjectType="MODefinition">
		<Name>Data Monitoring</Name>
		<Description1><![CDATA[Generic interface to set up data monitoring like sensor/log data and mechanism to receive alerts.]]></Description1>
		<ObjectID>10377</ObjectID>
		<ObjectURN>urn:oma:lwm2m:x:10377</ObjectURN>
		<LWM2MVersion>1.0</LWM2MVersion>
		<ObjectVersion>1.0</ObjectVersion>
		<MultipleInstances>Multiple</MultipleInstances>
		<Mandatory>Optional</Mandatory>
		<Resources>
			<Item ID="0">
				<Name>Monitoring Name</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource is the Data Monitoring operation's name or string reference declared by device. It cannot be modified externally. Device can support multiple monitoring operation by declaring multiple instances of Data Monitoring object.]]>
				</Description>
			</Item>
			<Item ID="1">
				<Name>Monitoring Description</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource describes the monitoring operation including data type and supported settings/options, if any.]]>
				</Description>
			</Item>
			<Item ID="2">
				<Name>Monitoring Settings</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource allows the monitoring operation to accept settings or operation options. The available settings, if supported, should be described using Monitoring Description resource.]]>
				</Description>
			</Item>
			<Item ID="3">
				<Name>Data</Name>
				<Operations>R</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource contains the current value of actual data being monitored/sampled in device. The data and data type is formatted as CBOR payload.]]>
				</Description>
			</Item>
			<Item ID="4">
				<Name>Data Sampling Start Mask</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>String</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource defines when to start data sampling. Data Sampling Start Mask needs to adhere to following string format:
"YYMMDDHHmmss"
YY: year, e.g 21
MM: month, 01 to 12
DD: day, 01 to 31
HH: hour, 00 to 23
mm: minute, 00 to 59
ss: second, 00 to 59

Any of the above can be replaced by character '-' for "don't care" masking.

Example:
1) "211027201801" data sampling will start at exactly 20:18:01 on 2021/10/27.
2) "------201801" data sampling will start at the next 20:18:01.
3) "------------" data sampling will start immediately.
4) "------01000000" data sampling will start at next 1st of the month at 12am.

If this resource is not present, data sampling starts at device's specific implementation.
]]></Description>
			</Item>			
			<Item ID="5">
				<Name>Data Sampling Interval</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description><![CDATA[This resource represents the time interval between consecutive data samplings. After the sampling starts (via the start mask, if present), the next sampling will run after Data Sampling Interval resource value has elapsed.
If this resource is not present, data sampling occurs at device's specific implementation.
]]></Description>
			</Item>
			<Item ID="6">
				<Name>Data Sampling Run Period</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units>s</Units>
				<Description><![CDATA[This resource defines the period to run this data sampling. Data Sampling will stop when the Run Period has elapsed from the start time. Depending on the Data Sampling Start Mask's "don't care" field, sampling may re-start again.
				]]></Description>
			</Item>		
			<Item ID="7">
				<Name>Reference A</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource contains the value of reference data that is to be compared with Data resource in conjunction with Comparison A resource. The comparison result is flagged by Status resource. When Status resource is made observable, this effectively becomes a mechanism to signal the server on a hit. The reference data and data type is formatted as CBOR payload.]]>
				</Description>
			</Item>
			<Item ID="8">
				<Name>Comparison A </Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource represents the logical statement to compare Data resource against Reference A resource. The statements are encoded as following:
0: No Comparison
1: Data > Reference A
2: Data >= Reference A
3: Data < Reference A
4: Data <= Reference A
5: Data equals Reference A
6: Data not equal Reference A
]]></Description>
			</Item>
			<Item ID="9">
				<Name>Reference B</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource contains the value of reference data that is to be compared with Data resource in conjunction with Comparison B resource. The comparison result is flagged by Status resource. When Status resource is made observable, this effectively becomes a mechanism to signal the server on a hit. The reference data and data type is formatted as CBOR payload.]]>
				</Description>
			</Item>
			<Item ID="10">
				<Name>Comparison B </Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource represents the logical statement to compare Data resource against Reference B resource. The statements are encoded as following:
0: No Comparison
1: Data > Reference B
2: Data >= Reference B
3: Data < Reference B
4: Data <= Reference B
5: Data equals Reference B
6: Data not equal Reference B
]]></Description>
			</Item>
			<Item ID="11">
				<Name>Reference C</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource contains the value of reference data that is to be compared with Data resource in conjunction with Comparison B resource. The comparison result is flagged by Status resource. When Status resource is made observable, this effectively becomes a mechanism to signal the server on a hit. The reference data and data type is formatted as CBOR payload.]]>
				</Description>
			</Item>
			<Item ID="12">
				<Name>Comparison C </Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Optional</Mandatory>
				<Type>Integer</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[This resource represents the logical statement to compare Data resource against Reference B resource. The statements are encoded as following:
0: No Comparison
1: Data > Reference C
2: Data >= Reference C
3: Data < Reference C
4: Data <= Reference C
5: Data equals Reference C
6: Data not equal Reference C
]]></Description>
			</Item>
			<Item ID="13">
				<Name>Monitoring Results</Name>
				<Operations>RW</Operations>
				<MultipleInstances>Single</MultipleInstances>
				<Mandatory>Mandatory</Mandatory>
				<Type>Opaque</Type>
				<RangeEnumeration></RangeEnumeration>
				<Units></Units>
				<Description><![CDATA[Monitoring Results resource is a serialized accumulation of comparison results of data and the references, represented by a read-only serialized Opaque (Binary).
This resource use CBOR format encoded as following: 
1. array(Result Index 1, Result Timestamp 1, Result Value 1)
2. array(Result Index 2, Result Timestamp 2, Result Value 2)
...
n. array(Result Index n, Result Timestamp n, Result Value n)	

Result value is an integer containing bit flags described as per following:
bit 0: 0(Comparison A statement is false), 1(Comparison A statement is true)
bit 1: 0(Comparison B statement is false), 1(Comparison B statement is true)
bit 2: 0(Comparison C statement is false), 1(Comparison C statement is true)
other bits: unused
New result will be added when there is a change of current Result Value. Result Timestamp is a 32-bit integer representing the number of seconds since Jan 1st, 1970 in the UTC time zone, sampled when the change occurs. Result Index is the running number of the recorded changes. 
Number of available results is up to device's implementation and maximum result that can be accumulated should be stated in Monitoring Description resource.
]]></Description>
			</Item>
		</Resources>
		<Description2 />
	</Object>
</LWM2M>
