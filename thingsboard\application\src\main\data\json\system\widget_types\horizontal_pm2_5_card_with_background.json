{"fqn": "horizontal_pm2_5_card_with_background", "name": "Horizontal PM2.5 card with background", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_pm2_5_card_with_background_system_widget_image.png", "description": "Displays the latest fine particulate matter (PM2.5) telemetry in a scalable horizontal layout with the background image.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'pm2.5', label: 'PM2.5', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM2.5\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 120 - 60;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 500) {\\n\\tvalue = 500;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"bubble_chart\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#7CC322\"},{\"from\":10,\"to\":35,\"color\":\"#F89E0D\"},{\"from\":35,\"to\":75,\"color\":\"#F77410\"},{\"from\":75,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":10,\"color\":\"#7CC322\"},{\"from\":10,\"to\":35,\"color\":\"#F89E0D\"},{\"from\":35,\"to\":75,\"color\":\"#F77410\"},{\"from\":75,\"to\":null,\"color\":\"#DE2343\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/horizontal_pm2_5_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal temperature card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"µg/m³\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["environment", "pm2.5", "particulate", "matter", "air", "fine particulates", "fine particles", "particulate matter 2.5", "airborne particles", "microscopic particles", "respirable particles", "aerosol particles", "dust particles"], "resources": [{"link": "/api/images/system/horizontal_pm2_5_card_with_background_system_widget_background.png", "title": "\"Horizontal PM2.5 card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_pm2_5_card_with_background_system_widget_background.png", "publicResourceKey": "D6pCCUkae5vhRyeXfPSYxyjmqM5TDtEe", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/horizontal_pm2_5_card_with_background_system_widget_image.png", "title": "\"Horizontal PM2.5 card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_pm2_5_card_with_background_system_widget_image.png", "publicResourceKey": "8dtZ45dZEDseGAxhcki9L9R6z2ClDWb5", "mediaType": "image/png", "data": "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", "public": true}]}