{"fqn": "indoor_pm2_5_chart_card", "name": "Indoor PM2.5 chart card", "deprecated": false, "image": "tb-image;/api/images/system/indoor_pm2_5_chart_card_system_widget_image.png", "description": "Displays a indoor fine particulate matter (PM2.5) data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'pm2.5', label: 'PM2.5', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'µg/m³', decimals: 0 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'pm2.5', 'µg/m³', 0);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"PM2.5\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 150) {\\n\\tvalue = 150;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":35,\"color\":\"#80C32C\"},{\"from\":35,\"to\":75,\"color\":\"#FFA600\"},{\"from\":75,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 150) {\\n\\tvalue = 150;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nif (value < -10) {\\n\\tvalue = -10;\\n} else if (value > 10) {\\n\\tvalue = 10;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"µg/m³\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"PM2.5\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:broom\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["environment", "pm2.5", "particulate", "matter", "air", "fine particulates", "fine particles", "particulate matter 2.5", "airborne particles", "microscopic particles", "respirable particles", "aerosol particles", "dust particles"], "resources": [{"link": "/api/images/system/indoor_pm2_5_chart_card_system_widget_image.png", "title": "\"Indoor PM2.5 chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "indoor_pm2_5_chart_card_system_widget_image.png", "publicResourceKey": "S97jgbNqJubX5L9DSPLp8gdRb2W8yttp", "mediaType": "image/png", "data": "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", "public": true}]}