{"fqn": "simple_temperature_chart_card_with_background", "name": "Simple temperature chart card with background", "deprecated": false, "image": "tb-image;/api/images/system/simple_temperature_chart_card_with_background_system_widget_image.png", "description": "Displays historical temperature values as a simplified chart with background. Optionally may display the corresponding latest temperature value.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 2, "resources": [], "templateHtml": "<tb-value-chart-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-chart-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueChartCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.valueChartCardWidget.onLatestDataUpdated();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.valueChartCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.valueChartCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '300px',\n        previewHeight: '150px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'temperature', label: 'Temperature', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)'}\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent) {\n            return [{ name: 'temperature', label: 'Latest', type: 'timeseries'}];\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-value-chart-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "", "hasBasicMode": true, "basicModeDirective": "tb-value-chart-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Temperature\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 40 - 20;\\nif (value < -60) {\\n\\tvalue = -60;\\n} else if (value > 60) {\\n\\tvalue = 60;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"layout\":\"left\",\"autoScale\":true,\"showValue\":true,\"valueFont\":{\"family\":\"Roboto\",\"size\":28,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\",\"lineHeight\":\"32px\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":null,\"to\":-20,\"color\":\"#224AC2\"},{\"from\":-20,\"to\":0,\"color\":\"#2B54CE\"},{\"from\":0,\"to\":10,\"color\":\"#6083EC\"},{\"from\":10,\"to\":20,\"color\":\"#F89E0D\"},{\"from\":20,\"to\":30,\"color\":\"#F77410\"},{\"from\":30,\"to\":40,\"color\":\"#F04022\"},{\"from\":40,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/simple_temperature_chart_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}}},\"title\":\"Temperature\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":null,\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"thermostat\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"18px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":true,\"decimals\":0,\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null,\"units\":\"°C\",\"displayTimewindow\":true,\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1697382151041,\"endTimeMs\":1697468551041},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"timewindowStyle\":{\"showIcon\":false,\"iconSize\":\"24px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":true}}"}, "tags": ["temperature", "weather", "environment"], "resources": [{"link": "/api/images/system/simple_temperature_chart_card_with_background_system_widget_background.png", "title": "\"Simple temperature chart card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_temperature_chart_card_with_background_system_widget_background.png", "publicResourceKey": "uvvhCxvSkwU4AqFjla3bqmO77RsGXaEi", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/simple_temperature_chart_card_with_background_system_widget_image.png", "title": "\"Simple temperature chart card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "simple_temperature_chart_card_with_background_system_widget_image.png", "publicResourceKey": "z2TGUA75y41D95iJ36opzzIm2pGcqLpd", "mediaType": "image/png", "data": "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", "public": true}]}