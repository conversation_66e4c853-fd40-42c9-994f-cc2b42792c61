{"fqn": "wind_speed_chart_card", "name": "Wind speed chart card", "deprecated": false, "image": "tb-image;/api/images/system/wind_speed_chart_card_system_widget_image.png", "description": "Displays a wind speed data by combining the latest and aggregated values with an optional simplified chart.", "descriptor": {"type": "timeseries", "sizeX": 4.5, "sizeY": 3.5, "resources": [], "templateHtml": "<tb-aggregated-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-aggregated-value-card-widget>\n", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDataUpdated();\n};\n\nself.onLatestDataUpdated = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onLatestDataUpdated();\n}\n\nself.onResize = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onResize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onEditModeChanged();\n}\n\nself.onDestroy = function() {\n    self.ctx.$scope.aggregatedValueCardWidget.onDestroy();\n}\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        previewWidth: '400px',\n        previewHeight: '300px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        hasAdditionalLatestDataKeys: true,\n        defaultDataKeysFunction: function() {\n            return [\n                 { name: 'speed', label: 'Wind Speed', type: 'timeseries', color: 'rgba(0, 0, 0, 0.87)', units: 'm/s', decimals: 1 }\n            ];\n        },\n        defaultLatestDataKeysFunction: function(configComponent, configData) {\n            return configComponent.createDefaultAggregatedValueLatestDataKeys(configData, 'speed', 'm/s', 1);\n        }\n    };\n}\n", "settingsForm": [], "dataKeySettingsForm": [], "latestDataKeySettingsForm": [], "settingsDirective": "tb-aggregated-value-card-widget-settings", "dataKeySettingsDirective": "", "latestDataKeySettingsDirective": "tb-aggregated-value-card-key-settings", "hasBasicMode": true, "basicModeDirective": "tb-aggregated-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"Main building\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Wind Speed\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 16 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 26) {\\n\\tvalue = 26;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m/s\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]},\"latestDataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Latest\",\"color\":\"#4caf50\",\"settings\":{\"position\":\"center\",\"font\":{\"size\":52,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"1\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0.2,\"color\":\"#7191EF\"},{\"from\":0.2,\"to\":3.4,\"color\":\"#5B7EE6\"},{\"from\":3.4,\"to\":8,\"color\":\"#4B70DD\"},{\"from\":8,\"to\":10.8,\"color\":\"#305AD7\"},{\"from\":10.8,\"to\":17.2,\"color\":\"#234CC7\"},{\"from\":17.2,\"to\":24.5,\"color\":\"#F04022\"},{\"from\":24.5,\"to\":null,\"color\":\"#D81838\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.9408410830697858,\"funcBody\":\"var value = prevValue + Math.random() * 16 - 8;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 26) {\\n\\tvalue = 26;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m/s\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta percent\",\"color\":\"#f44336\",\"settings\":{\"position\":\"rightTop\",\"font\":{\"size\":14,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"20px\"},\"color\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[{\"from\":null,\"to\":0,\"color\":\"#198038\"},{\"from\":0,\"to\":0,\"color\":\"rgba(0, 0, 0, 0.87)\"},{\"from\":0,\"to\":null,\"color\":\"#D12730\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":true},\"_hash\":0.06392321853157967,\"funcBody\":\"var value = prevValue + Math.random() * 6 - 3;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -25) {\\n\\tvalue = -25;\\n} else if (value > 25) {\\n\\tvalue = 25;\\n} \\nreturn value;\",\"aggregationType\":null,\"units\":\"%\",\"decimals\":0,\"usePostProcessing\":null,\"postFuncBody\":null},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Delta absolute\",\"color\":\"#607d8b\",\"settings\":{\"position\":\"rightBottom\",\"font\":{\"size\":11,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"rangeList\":[],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showArrow\":false},\"_hash\":0.44695098620509865,\"funcBody\":\"var value = prevValue + Math.random() * 4 - 2;\\nif (value < -6) {\\n\\tvalue = -6;\\n} else if (value > 6) {\\n\\tvalue = 6;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":\"m/s\",\"decimals\":1,\"usePostProcessing\":null,\"postFuncBody\":null}]}],\"timewindow\":{\"hideInterval\":false,\"hideLastInterval\":false,\"hideQuickInterval\":false,\"hideAggregation\":false,\"hideAggInterval\":false,\"hideTimezone\":false,\"selectedTab\":1,\"history\":{\"historyType\":2,\"timewindowMs\":60000,\"interval\":43200000,\"fixedTimewindow\":{\"startTimeMs\":1691927717318,\"endTimeMs\":1692014117318},\"quickInterval\":\"CURRENT_MONTH_SO_FAR\"},\"aggregation\":{\"type\":\"AVG\",\"limit\":25000}},\"showTitle\":true,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":null,\"padding\":\"0\",\"settings\":{\"stack\":false,\"fontSize\":10,\"fontColor\":\"#545454\",\"showTooltip\":true,\"tooltipIndividual\":false,\"tooltipCumulative\":false,\"hideZeros\":false,\"grid\":{\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1,\"color\":\"#545454\",\"backgroundColor\":null,\"tickColor\":\"#DDDDDD\"},\"xaxis\":{\"title\":null,\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"min\":null,\"max\":null,\"title\":null,\"showLabels\":true,\"color\":\"#545454\",\"tickSize\":null,\"tickDecimals\":0,\"ticksFormatter\":\"\"},\"shadowSize\":4,\"smoothLines\":false,\"comparisonEnabled\":false,\"xaxisSecond\":{\"axisPosition\":\"top\",\"title\":null,\"showLabels\":true},\"showLegend\":true,\"legendConfig\":{\"direction\":\"column\",\"position\":\"bottom\",\"sortDataKeys\":false,\"showMin\":false,\"showMax\":false,\"showAvg\":true,\"showTotal\":false,\"showLatest\":false},\"customLegendEnabled\":false,\"showSubtitle\":true,\"subtitle\":\"${entityName}\",\"subtitleFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"subtitleColor\":\"rgba(0, 0, 0, 0.38)\",\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"400\",\"lineHeight\":\"16px\"},\"dateColor\":\"rgba(0, 0, 0, 0.38)\",\"showChart\":true,\"chartColor\":\"rgba(0, 0, 0, 0.87)\",\"background\":{\"type\":\"color\",\"color\":\"#fff\",\"overlay\":{\"enabled\":false,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Wind Speed\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"configMode\":\"basic\",\"actions\":{},\"showTitleIcon\":true,\"titleIcon\":\"mdi:windsock\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"titleFont\":{\"size\":16,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\",\"lineHeight\":\"24px\"},\"iconSize\":\"24px\",\"titleTooltip\":\"\",\"widgetStyle\":{},\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"useDashboardTimewindow\":false,\"displayTimewindow\":true,\"decimals\":0,\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"24px\",\"icon\":null,\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"400\",\"style\":\"normal\",\"lineHeight\":\"16px\"},\"color\":\"rgba(0, 0, 0, 0.38)\",\"displayTypePrefix\":false},\"titleColor\":\"rgba(0, 0, 0, 0.87)\",\"borderRadius\":null}"}, "tags": ["weather", "environment", "wind", "speed", "airspeed", "flow", "gust"], "resources": [{"link": "/api/images/system/wind_speed_chart_card_system_widget_image.png", "title": "\"Wind speed chart card\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "wind_speed_chart_card_system_widget_image.png", "publicResourceKey": "OuNnz4GDuPuc0IZCk7oCE4icquLcftyf", "mediaType": "image/png", "data": "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", "public": true}]}