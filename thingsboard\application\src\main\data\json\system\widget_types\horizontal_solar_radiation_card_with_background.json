{"fqn": "horizontal_solar_radiation_card_with_background", "name": "Horizontal solar radiation card with background", "deprecated": false, "image": "tb-image;/api/images/system/horizontal_solar_radiation_card_with_background_system_widget_image.png", "description": "Displays the latest solar radiation telemetry in a scalable horizontal layout with the background image.", "descriptor": {"type": "latest", "sizeX": 5, "sizeY": 1, "resources": [], "templateHtml": "<tb-value-card-widget \n    [ctx]=\"ctx\"\n    [widgetTitlePanel]=\"widgetTitlePanel\">\n</tb-value-card-widget>", "templateCss": "", "controllerScript": "self.onInit = function() {\n    self.ctx.$scope.valueCardWidget.onInit();\n};\n\nself.onDataUpdated = function() {\n    self.ctx.$scope.valueCardWidget.onDataUpdated();\n};\n\nself.typeParameters = function() {\n    return {\n        maxDatasources: 1,\n        maxDataKeys: 1,\n        singleEntity: true,\n        horizontal: true,\n        previewWidth: '420px',\n        previewHeight: '90px',\n        embedTitlePanel: true,\n        supportsUnitConversion: true,\n        defaultDataKeysFunction: function() {\n            return [{ name: 'radiation', label: 'Solar Radiation', type: 'timeseries' }];\n        }\n    };\n};\n\nself.onDestroy = function() {\n};\n", "settingsForm": [], "dataKeySettingsForm": [], "settingsDirective": "tb-value-card-widget-settings", "hasBasicMode": true, "basicModeDirective": "tb-value-card-basic-config", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Solar Radiation\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.2392660816082064,\"funcBody\":\"var value = prevValue + Math.random() * 500 - 250;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1100) {\\n\\tvalue = 1100;\\n}\\nreturn value;\",\"aggregationType\":null,\"units\":null,\"decimals\":null,\"usePostProcessing\":null,\"postFuncBody\":null}],\"alarmFilterConfig\":{\"statusList\":[\"ACTIVE\"]}}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":false,\"backgroundColor\":\"rgba(0, 0, 0, 0)\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"0px\",\"settings\":{\"labelPosition\":\"top\",\"layout\":\"horizontal\",\"showLabel\":true,\"labelFont\":{\"family\":\"Roboto\",\"size\":16,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"labelColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"showIcon\":true,\"iconSize\":40,\"iconSizeUnit\":\"px\",\"icon\":\"mdi:radioactive\",\"iconColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#5579E5\"},{\"from\":0,\"to\":250,\"color\":\"#7CC322\"},{\"from\":250,\"to\":500,\"color\":\"#F89E0D\"},{\"from\":500,\"to\":1000,\"color\":\"#F77410\"},{\"from\":1000,\"to\":null,\"color\":\"#DE2343\"}],\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"valueFont\":{\"size\":36,\"sizeUnit\":\"px\",\"family\":\"Roboto\",\"weight\":\"500\",\"style\":\"normal\"},\"valueColor\":{\"type\":\"range\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\",\"rangeList\":[{\"from\":0,\"to\":0,\"color\":\"#5579E5\"},{\"from\":0,\"to\":250,\"color\":\"#7CC322\"},{\"from\":250,\"to\":500,\"color\":\"#F89E0D\"},{\"from\":500,\"to\":1000,\"color\":\"#F77410\"},{\"from\":1000,\"to\":null,\"color\":\"#DE2343\"}]},\"showDate\":true,\"dateFormat\":{\"format\":null,\"lastUpdateAgo\":true,\"custom\":false},\"dateFont\":{\"family\":\"Roboto\",\"size\":12,\"sizeUnit\":\"px\",\"style\":\"normal\",\"weight\":\"500\"},\"dateColor\":{\"type\":\"constant\",\"color\":\"rgba(0, 0, 0, 0.38)\",\"colorFunction\":\"var temperature = value;\\nif (typeof temperature !== undefined) {\\n  var percent = (temperature + 60)/120 * 100;\\n  return tinycolor.mix('blue', 'red', percent).toHexString();\\n}\\nreturn 'blue';\"},\"background\":{\"type\":\"image\",\"imageBase64\":\"tb-image;/api/images/system/horizontal_solar_radiation_card_with_background_system_widget_background.png\",\"imageUrl\":null,\"color\":\"#fff\",\"overlay\":{\"enabled\":true,\"color\":\"rgba(255,255,255,0.72)\",\"blur\":3}},\"autoScale\":true},\"title\":\"Horizontal solar radiation card\",\"dropShadow\":true,\"enableFullscreen\":false,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"units\":\"W/m²\",\"decimals\":0,\"useDashboardTimewindow\":true,\"showLegend\":false,\"widgetStyle\":{},\"actions\":{},\"configMode\":\"basic\",\"displayTimewindow\":true,\"margin\":\"0px\",\"borderRadius\":\"0px\",\"widgetCss\":\"\",\"pageSize\":1024,\"noDataDisplayMessage\":\"\",\"showTitleIcon\":false,\"titleTooltip\":\"\",\"titleFont\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1.6\"},\"titleIcon\":\"\",\"iconColor\":\"rgba(0, 0, 0, 0.87)\",\"iconSize\":\"14px\",\"timewindowStyle\":{\"showIcon\":true,\"iconSize\":\"14px\",\"icon\":\"query_builder\",\"iconPosition\":\"left\",\"font\":{\"size\":12,\"sizeUnit\":\"px\",\"family\":null,\"weight\":null,\"style\":null,\"lineHeight\":\"1\"},\"color\":null}}"}, "tags": ["weather", "environment", "solar", "radiation", "sunlight"], "resources": [{"link": "/api/images/system/horizontal_solar_radiation_card_with_background_system_widget_background.png", "title": "\"Horizontal solar radiation card with background\" system widget background", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_solar_radiation_card_with_background_system_widget_background.png", "publicResourceKey": "yqPKZGzXhsNDjpUQEW1xoG3KmDCmvRzy", "mediaType": "image/png", "data": "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", "public": true}, {"link": "/api/images/system/horizontal_solar_radiation_card_with_background_system_widget_image.png", "title": "\"Horizontal solar radiation card with background\" system widget image", "type": "IMAGE", "subType": "IMAGE", "fileName": "horizontal_solar_radiation_card_with_background_system_widget_image.png", "publicResourceKey": "iPWXDs57i9eGWABxmtbVXHZfZMv5LC1a", "mediaType": "image/png", "data": "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", "public": true}]}